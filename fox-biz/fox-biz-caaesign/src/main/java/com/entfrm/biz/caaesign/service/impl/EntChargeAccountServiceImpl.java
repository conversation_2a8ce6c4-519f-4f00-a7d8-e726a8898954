package com.entfrm.biz.caaesign.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import com.baomidou.mybatisplus.annotation.TableName;
import com.entfrm.biz.activiti.service.WfConfigService;
import com.entfrm.biz.activiti.entity.TQhAllRelation;
import com.entfrm.biz.activiti.service.TQhAllRelationService;
import com.entfrm.biz.activiti.service.TaskService;
import com.entfrm.biz.activiti.dto.LeaveDto;
import com.entfrm.biz.feign.consumables.ConsumablesFeignService;
import com.entfrm.biz.feign.consumables.dto.ConsumablesResult;
import com.entfrm.biz.feign.consumables.dto.UserInfoDto;
import com.entfrm.biz.system.util.JsonUtils;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.entfrm.biz.caaesign.mapper.EntChargeAccountMapper;
import com.entfrm.biz.caaesign.entity.EntChargeAccount;
import com.entfrm.biz.caaesign.service.EntChargeAccountService;
import com.entfrm.core.base.util.PinyinUtil;
import com.entfrm.core.data.util.SerialNumberTool;
import cn.hutool.core.date.DateUtil;
import lombok.AllArgsConstructor;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.runtime.ProcessInstance;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import com.entfrm.core.base.enums.TaskStatusEnum;
import java.util.Map;
import com.entfrm.core.data.constant.CommonConstant;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.io.Serializable;
import com.entfrm.biz.caaesign.entity.EntChargeItems;
import com.entfrm.biz.caaesign.service.EntChargeItemsService;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import java.util.Collection;
import com.entfrm.biz.activiti.dto.WfConfigDto;
import java.lang.reflect.Field;
import com.google.common.collect.Maps;
import org.activiti.engine.task.Task;
import com.entfrm.core.security.util.SecurityUtil;
import com.entfrm.core.base.annotation.SignNode;
import java.util.Arrays;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.bean.BeanUtil;

/**
 * <AUTHOR>
 * @date 2022-12-28 13:42:41
 *
 * @description 經管類系統賬號申請表Service业务层
 */
@Service
@AllArgsConstructor
public class EntChargeAccountServiceImpl extends ServiceImpl<EntChargeAccountMapper, EntChargeAccount> implements EntChargeAccountService {
	private final RuntimeService runtimeService;
	private final org.activiti.engine.TaskService actTaskService;
	private final WfConfigService wfConfigService;
	private final TQhAllRelationService allRelationService;
    private final RabbitTemplate rabbitTemplate;
	private final TaskService taskService;

    private final EntChargeItemsService entChargeItemsService;
    @Autowired
    private ConsumablesFeignService consumablesFeignService;

    	/**
	 * 启动流程
	 *
	 * @param entChargeAccount
	 * @return
	 */
	@Override
	public Boolean startProcess(EntChargeAccount entChargeAccount) {
        entChargeAccount.setWorkStatus(Integer.parseInt(TaskStatusEnum.CHECKING.getStatus()));
        String tableName = EntChargeAccount.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
        String procDefKey = wfConfigDto.getProcDefKey();
        String businessKey = procDefKey + CommonConstant.BREAK_BUSINESS_KEY + entChargeAccount.getId();
        Field fields[] = entChargeAccount.getClass().getDeclaredFields();
        Map variables = Maps.newHashMap();
        //設置填單人
        variables.put("maker", SecurityUtil.getUser().getUsername());
        variables.put("serialNo", entChargeAccount.getSerialno());
        variables.put("workflowName", wfConfigDto.getFormName());
        Arrays.stream(fields).forEach(field -> {
                SignNode signNode = field.getAnnotation(SignNode.class);
                if (ObjectUtil.isNotEmpty(signNode)) {
                    if (signNode.type().equals(SignNode.Type.COMMON)) {
                        variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entChargeAccount, field)) ? "AUTO_SIGN" : ReflectUtil.getFieldValue(entChargeAccount, field));
                    } else if (signNode.type().equals(SignNode.Type.HUIQIAN)) {
                        variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entChargeAccount, field)) ? Arrays.asList("AUTO_SIGN") : Arrays.asList(ReflectUtil.getFieldValue(entChargeAccount, field).toString().split(",")));
                    }
                }
            }
        );
        ProcessInstance pi = runtimeService.startProcessInstanceByKey(procDefKey, businessKey,variables);
        entChargeAccount.setProcessId(pi.getProcessInstanceId());
        this.updateById(entChargeAccount);

        TQhAllRelation relation = allRelationService.getOne(new QueryWrapper<TQhAllRelation>().eq("serialno",entChargeAccount.getSerialno()));
        relation.setWorkStatus(entChargeAccount.getWorkStatus());
        relation.setProcessId(pi.getProcessInstanceId());
        Task task = actTaskService.createTaskQuery().processInstanceId(pi.getProcessInstanceId()).singleResult();
        relation.setNodename(task.getName());
        relation.setDeptNo(entChargeAccount.getApplyDeptNo());
        allRelationService.updateById(relation);
        //添加mq
        rabbitTemplate.convertAndSend("SignDirectExchange", "Sign.Direct.Routing", BeanUtil.beanToMap(relation));
        return Boolean.TRUE;
	}

    /**
     * 插入一条记录（选择字段，策略插入）
     *
     * @param entChargeAccount 实体对象
     */
    @Override
    public boolean save(EntChargeAccount entChargeAccount) {
        boolean rs = super.save(entChargeAccount);
        saveChild(entChargeAccount);
	    String tableName = EntChargeAccount.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
        TQhAllRelation relation = new TQhAllRelation();
        relation.setDtoName(tableName);
        relation.setMakerNo(entChargeAccount.getMakerNo());
        relation.setMakerName(entChargeAccount.getMakerName());
        relation.setSerialno(entChargeAccount.getSerialno());
        relation.setWorkflowid(wfConfigDto.getProcDefKey());
        relation.setWfName(wfConfigDto.getFormName());
        relation.setClassPackage(entChargeAccount.getClass().getName());
        relation.setWorkStatus(0);
        allRelationService.save(relation);
        return rs;
    }
	@Override
	public Boolean updateAndCheck(EntChargeAccount entChargeAccount, LeaveDto leaveDto) {
		try {
			this.updateById(entChargeAccount);
			taskService.checkTask(leaveDto);
		} catch (Exception e) {
			return false;
		}
		return true;
	}
    /**
     * 插入一条记录（选择字段，策略插入）
     *
     * @param entChargeAccount 实体对象
     */
    @Override
    public boolean updateById(EntChargeAccount entChargeAccount) {
        // 删除子表信息关联
        entChargeItemsService.remove(new QueryWrapper<EntChargeItems>().eq("pid", entChargeAccount.getId()));
        boolean rs = super.updateById(entChargeAccount);
        // 新增子表信息管理
        if(rs){
            // 新增子表信息管理
            saveChild(entChargeAccount);
        }
        return rs;
    }
    /**
     * 新增子表記錄
     *
     * @param entChargeAccount 表單对象
     */
    public void saveChild(EntChargeAccount entChargeAccount) {
        List<EntChargeItems> entChargeItemsLists = entChargeAccount.getEntChargeItemsLists();
        if (entChargeItemsLists != null) {
            for (EntChargeItems entChargeItems : entChargeItemsLists) {
                entChargeItems.setId(null);
                entChargeItems.setPid(entChargeAccount.getId());
                entChargeItemsService.save(entChargeItems);
            }
        }
    }

    /**
     * 删除（根据ID 批量删除）
     *
     * @param idList 主键ID列表
     */
    @Override
    public boolean removeByIds(Collection<? extends Serializable> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return false;
        }
        for (Serializable id : idList) {
            EntChargeAccount entChargeAccount = getById(id);
            // 删除子表信息关联
            entChargeItemsService.remove(new QueryWrapper<EntChargeItems>().eq("pid", entChargeAccount.getId()));
            }

        return SqlHelper.retBool(getBaseMapper().deleteBatchIds(idList));
    }
    /**
     * 根據ID獲取信息
     *
     * @param id
     * @return
     */
    @Override
    public EntChargeAccount getById(Serializable id) {
        EntChargeAccount entChargeAccount = super.getById(id);
        setChilds(entChargeAccount);
        return entChargeAccount;
    }

    /**
     * 獲取子表列表（${childTable.tableComment}）
     */
    public void setChilds(EntChargeAccount entChargeAccount){

    List<EntChargeItems> entChargeItemsLists = entChargeItemsService.list(new QueryWrapper<EntChargeItems>().eq("pid", entChargeAccount.getId()));
    entChargeAccount.setEntChargeItemsLists(entChargeItemsLists);
    }


    @Override
    public EntChargeAccount getBySerialNo(String serialNo) {
        EntChargeAccount entChargeAccount = getOne(new QueryWrapper<EntChargeAccount>().eq("serialno", serialNo));
        setChilds(entChargeAccount);
        return entChargeAccount;
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public Boolean sendChService(String serialNo) {
        try {
            List<UserInfoDto> userInfoDtoList = new ArrayList<>();
            EntChargeAccount entChargeAccount = getBySerialNo(serialNo);
            String applyType="";
            if(entChargeAccount.getApplyContent().equals("new")){
                applyType="1";
            }else if(entChargeAccount.getApplyContent().equals("modify")){
                applyType="2";
            }else if(entChargeAccount.getApplyContent().equals("delete")){
                applyType="3";
            }
            //拋轉從表數據
            for (EntChargeItems entChargeItemsList : entChargeAccount.getEntChargeItemsLists()) {
                UserInfoDto userInfoDto = new UserInfoDto();
                userInfoDto.setUserName(entChargeItemsList.getChargeEmpNo());
                userInfoDto.setNickName(entChargeItemsList.getChargeEmpNam());
                userInfoDto.setDepartCode(entChargeItemsList.getChargeDeptNo());
                userInfoDto.setDepartName(entChargeItemsList.getChargeDeptNam());
                userInfoDto.setPhone(entChargeItemsList.getChargeDeptTel());
                userInfoDto.setEmail(entChargeItemsList.getChargeMail());
                if(entChargeItemsList.getYnChargeMail().equals("Y")){
                    userInfoDto.setIpControl("0");
                }else if(entChargeItemsList.getYnChargeMail().equals("N")){
                    userInfoDto.setIpControl("1");
                }
                userInfoDto.setLoginIp(entChargeItemsList.getChargeIp());
                userInfoDto.setSite(entChargeItemsList.getChargeEmpPlant());
                userInfoDto.setRoleName(entChargeItemsList.getChargeRole());
                userInfoDto.setType(applyType);
                userInfoDtoList.add(userInfoDto);
            }
            String s = JsonUtils.toJsonString(userInfoDtoList);
            String base = new Base64().encodeToString(s.getBytes("UTF-8"));

            ConsumablesResult<UserInfoDto> consumablesResult = consumablesFeignService.userManagement(base);
            if(200 != consumablesResult.getCode()){
                log.error("拋轉數據失敗"+serialNo+consumablesResult.getMsg());
            }
        } catch (BeansException e) {
            log.error("拋轉數據失敗"+e.getMessage() ,e);
        } catch (UnsupportedEncodingException e) {
            log.error("數據加密失敗"+e.getMessage() ,e);
        }
        return true;
    }
}
