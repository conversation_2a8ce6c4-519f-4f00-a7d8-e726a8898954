package com.entfrm.biz.caaesign.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import com.baomidou.mybatisplus.annotation.TableName;
import com.entfrm.biz.activiti.service.WfConfigService;
import com.entfrm.biz.activiti.entity.TQhAllRelation;
import com.entfrm.biz.activiti.service.TQhAllRelationService;
import com.entfrm.biz.activiti.service.TaskService;
import com.entfrm.biz.activiti.dto.LeaveDto;
import org.springframework.stereotype.Service;
import com.entfrm.biz.caaesign.mapper.EntMonthEndMapper;
import com.entfrm.biz.caaesign.entity.EntMonthEnd;
import com.entfrm.biz.caaesign.service.EntMonthEndService;
import com.entfrm.core.base.util.PinyinUtil;
import com.entfrm.core.data.util.SerialNumberTool;
import cn.hutool.core.date.DateUtil;
import lombok.AllArgsConstructor;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.runtime.ProcessInstance;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.transaction.annotation.Transactional;
import com.entfrm.core.base.enums.TaskStatusEnum;
import java.util.Map;
import com.entfrm.core.data.constant.CommonConstant;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.io.Serializable;
import com.entfrm.biz.caaesign.entity.EntMonthItems;
import com.entfrm.biz.caaesign.service.EntMonthItemsService;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import java.util.Collection;
import com.entfrm.biz.activiti.dto.WfConfigDto;
import java.lang.reflect.Field;
import com.google.common.collect.Maps;
import org.activiti.engine.task.Task;
import com.entfrm.core.security.util.SecurityUtil;
import com.entfrm.core.base.annotation.SignNode;
import java.util.Arrays;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.bean.BeanUtil;

/**
 * <AUTHOR>
 * @date 2022-08-09 15:21:08
 *
 * @description 結報月度統計表Service业务层
 */
@Service
@AllArgsConstructor
public class EntMonthEndServiceImpl extends ServiceImpl<EntMonthEndMapper, EntMonthEnd> implements EntMonthEndService {
	private final RuntimeService runtimeService;
	private final org.activiti.engine.TaskService actTaskService;
	private final WfConfigService wfConfigService;
	private final TQhAllRelationService allRelationService;
    private final RabbitTemplate rabbitTemplate;
	private final TaskService taskService;

    private final EntMonthItemsService entMonthItemsService;

    	/**
	 * 启动流程
	 *
	 * @param entMonthEnd
	 * @return
	 */
	@Override
	public Boolean startProcess(EntMonthEnd entMonthEnd) {
        entMonthEnd.setWorkStatus(Integer.parseInt(TaskStatusEnum.CHECKING.getStatus()));
        String tableName = EntMonthEnd.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
        String procDefKey = wfConfigDto.getProcDefKey();
        String businessKey = procDefKey + CommonConstant.BREAK_BUSINESS_KEY + entMonthEnd.getId();
        Field fields[] = entMonthEnd.getClass().getDeclaredFields();
        Map variables = Maps.newHashMap();
        //設置填單人
        variables.put("maker", SecurityUtil.getUser().getUsername());
        variables.put("serialNo", entMonthEnd.getSerialno());
        variables.put("workflowName", wfConfigDto.getFormName());
        Arrays.stream(fields).forEach(field -> {
                SignNode signNode = field.getAnnotation(SignNode.class);
                if (ObjectUtil.isNotEmpty(signNode)) {
                    if (signNode.type().equals(SignNode.Type.COMMON)) {
                        variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entMonthEnd, field)) ? "AUTO_SIGN" : ReflectUtil.getFieldValue(entMonthEnd, field));
                    } else if (signNode.type().equals(SignNode.Type.HUIQIAN)) {
                        variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entMonthEnd, field)) ? Arrays.asList("AUTO_SIGN") : Arrays.asList(ReflectUtil.getFieldValue(entMonthEnd, field).toString().split(",")));
                    }
                }
            }
        );
        ProcessInstance pi = runtimeService.startProcessInstanceByKey(procDefKey, businessKey,variables);
        entMonthEnd.setProcessId(pi.getProcessInstanceId());
        this.updateById(entMonthEnd);

        TQhAllRelation relation = allRelationService.getOne(new QueryWrapper<TQhAllRelation>().eq("serialno",entMonthEnd.getSerialno()));
        relation.setWorkStatus(entMonthEnd.getWorkStatus());
        relation.setProcessId(pi.getProcessInstanceId());
        Task task = actTaskService.createTaskQuery().processInstanceId(pi.getProcessInstanceId()).singleResult();
        relation.setNodename(task.getName());
        relation.setDeptNo(entMonthEnd.getApplyDeptNo());
        allRelationService.updateById(relation);
        //添加mq
        rabbitTemplate.convertAndSend("SignDirectExchange", "Sign.Direct.Routing", BeanUtil.beanToMap(relation));
        return Boolean.TRUE;
	}

    /**
     * 插入一条记录（选择字段，策略插入）
     *
     * @param entMonthEnd 实体对象
     */
    @Override
    public boolean save(EntMonthEnd entMonthEnd) {
        boolean rs = super.save(entMonthEnd);
        saveChild(entMonthEnd);
	    String tableName = EntMonthEnd.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
        TQhAllRelation relation = new TQhAllRelation();
        relation.setDtoName(tableName);
        relation.setMakerNo(entMonthEnd.getMakerNo());
        relation.setMakerName(entMonthEnd.getMakerName());
        relation.setSerialno(entMonthEnd.getSerialno());
        relation.setWorkflowid(wfConfigDto.getProcDefKey());
        relation.setWfName(wfConfigDto.getFormName());
        relation.setClassPackage(entMonthEnd.getClass().getName());
        relation.setWorkStatus(0);
        allRelationService.save(relation);
        return rs;
    }
	@Override
	public Boolean updateAndCheck(EntMonthEnd entMonthEnd, LeaveDto leaveDto) {
		try {
			this.updateById(entMonthEnd);
			taskService.checkTask(leaveDto);
		} catch (Exception e) {
			return false;
		}
		return true;
	}
    /**
     * 插入一条记录（选择字段，策略插入）
     *
     * @param entMonthEnd 实体对象
     */
    @Override
    public boolean updateById(EntMonthEnd entMonthEnd) {
        // 删除子表信息关联
        entMonthItemsService.remove(new QueryWrapper<EntMonthItems>().eq("pid", entMonthEnd.getId()));
        boolean rs = super.updateById(entMonthEnd);
        // 新增子表信息管理
        if(rs){
            // 新增子表信息管理
            saveChild(entMonthEnd);
        }
        return rs;
    }
    /**
     * 新增子表記錄
     *
     * @param entMonthEnd 表單对象
     */
    public void saveChild(EntMonthEnd entMonthEnd) {
        List<EntMonthItems> entMonthItemsLists = entMonthEnd.getEntMonthItemsLists();
        if (entMonthItemsLists != null) {
            for (EntMonthItems entMonthItems : entMonthItemsLists) {
                entMonthItems.setId(null);
                entMonthItems.setPid(entMonthEnd.getId());
                entMonthItemsService.save(entMonthItems);
            }
        }
    }

    /**
     * 删除（根据ID 批量删除）
     *
     * @param idList 主键ID列表
     */
    @Override
    public boolean removeByIds(Collection<? extends Serializable> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return false;
        }
        for (Serializable id : idList) {
            EntMonthEnd entMonthEnd = getById(id);
            // 删除子表信息关联
            entMonthItemsService.remove(new QueryWrapper<EntMonthItems>().eq("pid", entMonthEnd.getId()));
            }

        return SqlHelper.retBool(getBaseMapper().deleteBatchIds(idList));
    }
    /**
     * 根據ID獲取信息
     *
     * @param id
     * @return
     */
    @Override
    public EntMonthEnd getById(Serializable id) {
        EntMonthEnd entMonthEnd = super.getById(id);
        setChilds(entMonthEnd);
        return entMonthEnd;
    }

    /**
     * 獲取子表列表（${childTable.tableComment}）
     */
    public void setChilds(EntMonthEnd entMonthEnd){

    List<EntMonthItems> entMonthItemsLists = entMonthItemsService.list(new QueryWrapper<EntMonthItems>().eq("pid", entMonthEnd.getId()));
    entMonthEnd.setEntMonthItemsLists(entMonthItemsLists);
    }
}
