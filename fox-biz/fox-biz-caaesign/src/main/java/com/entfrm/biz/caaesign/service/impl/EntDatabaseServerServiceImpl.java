package com.entfrm.biz.caaesign.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import com.baomidou.mybatisplus.annotation.TableName;
import com.entfrm.biz.activiti.service.WfConfigService;
import com.entfrm.biz.activiti.entity.TQhAllRelation;
import com.entfrm.biz.activiti.service.TQhAllRelationService;
import org.springframework.stereotype.Service;
import com.entfrm.biz.caaesign.mapper.EntDatabaseServerMapper;
import com.entfrm.biz.caaesign.entity.EntDatabaseServer;
import com.entfrm.biz.caaesign.service.EntDatabaseServerService;
import com.entfrm.core.base.util.PinyinUtil;
import com.entfrm.core.data.util.SerialNumberTool;
import cn.hutool.core.date.DateUtil;
import lombok.AllArgsConstructor;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.runtime.ProcessInstance;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.transaction.annotation.Transactional;
import com.entfrm.core.base.enums.TaskStatusEnum;
import java.util.Map;
import com.entfrm.core.data.constant.CommonConstant;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.io.Serializable;
import com.entfrm.biz.caaesign.entity.EntDatabaseServerItems;
import com.entfrm.biz.caaesign.service.EntDatabaseServerItemsService;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import java.util.Collection;
import com.entfrm.biz.activiti.dto.WfConfigDto;
import java.lang.reflect.Field;
import com.google.common.collect.Maps;
import org.activiti.engine.task.Task;
import com.entfrm.core.security.util.SecurityUtil;
import com.entfrm.core.base.annotation.SignNode;
import java.util.Arrays;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.bean.BeanUtil;

/**
 * <AUTHOR>
 * @date 2021-08-26 16:12:03
 *
 * @description 數據庫服務申請單Service业务层
 */
@Service
@AllArgsConstructor
public class EntDatabaseServerServiceImpl extends ServiceImpl<EntDatabaseServerMapper, EntDatabaseServer> implements EntDatabaseServerService {
	private final RuntimeService runtimeService;
	private final org.activiti.engine.TaskService actTaskService;
	private final WfConfigService wfConfigService;
	private final TQhAllRelationService allRelationService;
    private final RabbitTemplate rabbitTemplate;

    private final EntDatabaseServerItemsService entDatabaseServerItemsService;

    	/**
	 * 启动流程
	 *
	 * @param entDatabaseServer
	 * @return
	 */
	@Override
	public Boolean startProcess(EntDatabaseServer entDatabaseServer) {
        entDatabaseServer.setWorkStatus(Integer.parseInt(TaskStatusEnum.CHECKING.getStatus()));
        String tableName = EntDatabaseServer.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
        String procDefKey = wfConfigDto.getProcDefKey();
        String businessKey = procDefKey + CommonConstant.BREAK_BUSINESS_KEY + entDatabaseServer.getId();
        Field fields[] = entDatabaseServer.getClass().getDeclaredFields();
        Map variables = Maps.newHashMap();
        //設置填單人
        variables.put("maker", SecurityUtil.getUser().getUsername());
        variables.put("serialNo", entDatabaseServer.getSerialno());
        variables.put("workflowName", wfConfigDto.getFormName());
        Arrays.stream(fields).forEach(field -> {
                    SignNode signNode = field.getAnnotation(SignNode.class);
                    /*if (ObjectUtil.isNotEmpty(signNode)) {
                        variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entDatabaseServer, field))?"AUTO_SIGN":ReflectUtil.getFieldValue(entDatabaseServer,field));
                    }*/
                    if (ObjectUtil.isNotEmpty(signNode)) {
                        if (signNode.type().equals(SignNode.Type.COMMON)) {
                            variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entDatabaseServer, field)) ? "AUTO_SIGN" : ReflectUtil.getFieldValue(entDatabaseServer, field));
                        } else if (signNode.type().equals(SignNode.Type.HUIQIAN)) {
                            variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entDatabaseServer, field)) ? Arrays.asList("AUTO_SIGN") : Arrays.asList(ReflectUtil.getFieldValue(entDatabaseServer, field).toString().split(",")));
                        }
                    }
                }
        );
        ProcessInstance pi = runtimeService.startProcessInstanceByKey(procDefKey, businessKey,variables);
        entDatabaseServer.setProcessId(pi.getProcessInstanceId());
        super.updateById(entDatabaseServer);

        TQhAllRelation relation = allRelationService.getOne(new QueryWrapper<TQhAllRelation>().eq("serialno",entDatabaseServer.getSerialno()));
        relation.setWorkStatus(entDatabaseServer.getWorkStatus());
        relation.setProcessId(pi.getProcessInstanceId());
        Task task = actTaskService.createTaskQuery().processInstanceId(pi.getProcessInstanceId()).singleResult();
        relation.setNodename(task.getName());
        relation.setDeptNo(entDatabaseServer.getMakerdeptno());
        allRelationService.updateById(relation);
        //添加mq
        rabbitTemplate.convertAndSend("SignDirectExchange", "Sign.Direct.Routing", BeanUtil.beanToMap(relation));
        return Boolean.TRUE;
	}

    /**
     * 插入一条记录（选择字段，策略插入）
     *
     * @param entDatabaseServer 实体对象
     */
    @Override
    public boolean save(EntDatabaseServer entDatabaseServer) {
        boolean rs = super.save(entDatabaseServer);
        saveChild(entDatabaseServer);
	    String tableName = EntDatabaseServer.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
        TQhAllRelation relation = new TQhAllRelation();
        relation.setDtoName(tableName);
        relation.setMakerNo(entDatabaseServer.getMakerNo());
        relation.setMakerName(entDatabaseServer.getMakerName());
        relation.setSerialno(entDatabaseServer.getSerialno());
        relation.setWorkflowid(wfConfigDto.getProcDefKey());
        relation.setWfName(wfConfigDto.getFormName());
        relation.setClassPackage(entDatabaseServer.getClass().getName());
        relation.setWorkStatus(0);
        allRelationService.save(relation);
        return rs;
    }

    /**
     * 插入一条记录（选择字段，策略插入）
     *
     * @param entDatabaseServer 实体对象
     */
    @Override
    public boolean updateById(EntDatabaseServer entDatabaseServer) {
        // 删除子表信息关联
        entDatabaseServerItemsService.remove(new QueryWrapper<EntDatabaseServerItems>().eq("pid", entDatabaseServer.getId()));
        boolean rs = super.updateById(entDatabaseServer);
        // 新增子表信息管理
        if(rs){
            // 新增子表信息管理
            saveChild(entDatabaseServer);
        }
        return rs;
    }
    /**
     * 新增子表記錄
     *
     * @param entDatabaseServer 表單对象
     */
    public void saveChild(EntDatabaseServer entDatabaseServer) {
        List<EntDatabaseServerItems> entDatabaseServerItemsLists = entDatabaseServer.getEntDatabaseServerItemsLists();
        if (entDatabaseServerItemsLists != null) {
            for (EntDatabaseServerItems entDatabaseServerItems : entDatabaseServerItemsLists) {
                entDatabaseServerItems.setId(null);
                entDatabaseServerItems.setPid(entDatabaseServer.getId());
                entDatabaseServerItemsService.save(entDatabaseServerItems);
            }
        }
    }

    /**
     * 删除（根据ID 批量删除）
     *
     * @param idList 主键ID列表
     */
    @Override
    public boolean removeByIds(Collection<? extends Serializable> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return false;
        }
        for (Serializable id : idList) {
            EntDatabaseServer entDatabaseServer = getById(id);
            // 删除子表信息关联
            entDatabaseServerItemsService.remove(new QueryWrapper<EntDatabaseServerItems>().eq("pid", entDatabaseServer.getId()));
            }

        return SqlHelper.retBool(getBaseMapper().deleteBatchIds(idList));
    }
    /**
     * 根據ID獲取信息
     *
     * @param id
     * @return
     */
    @Override
    public EntDatabaseServer getById(Serializable id) {
        EntDatabaseServer entDatabaseServer = super.getById(id);
        setChilds(entDatabaseServer);
        return entDatabaseServer;
    }

    /**
     * 獲取子表列表（${childTable.tableComment}）
     */
    public void setChilds(EntDatabaseServer entDatabaseServer){

    List<EntDatabaseServerItems> entDatabaseServerItemsLists = entDatabaseServerItemsService.list(new QueryWrapper<EntDatabaseServerItems>().eq("pid", entDatabaseServer.getId()));
    entDatabaseServer.setEntDatabaseServerItemsLists(entDatabaseServerItemsLists);
    }
}
