package com.entfrm.biz.caaesign.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import com.baomidou.mybatisplus.annotation.TableName;
import com.entfrm.biz.activiti.service.WfConfigService;
import com.entfrm.biz.activiti.entity.TQhAllRelation;
import com.entfrm.biz.activiti.service.TQhAllRelationService;
import com.entfrm.biz.activiti.service.TaskService;
import com.entfrm.biz.activiti.dto.LeaveDto;
import org.springframework.stereotype.Service;
import com.entfrm.biz.caaesign.mapper.EntVipApplyMapper;
import com.entfrm.biz.caaesign.entity.EntVipApply;
import com.entfrm.biz.caaesign.service.EntVipApplyService;
import com.entfrm.core.base.util.PinyinUtil;
import com.entfrm.core.data.util.SerialNumberTool;
import cn.hutool.core.date.DateUtil;
import lombok.AllArgsConstructor;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.runtime.ProcessInstance;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.transaction.annotation.Transactional;
import com.entfrm.core.base.enums.TaskStatusEnum;
import java.util.Map;
import com.entfrm.core.data.constant.CommonConstant;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.io.Serializable;
import com.entfrm.biz.caaesign.entity.EntVipApplyItems;
import com.entfrm.biz.caaesign.service.EntVipApplyItemsService;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import java.util.Collection;
import com.entfrm.biz.activiti.dto.WfConfigDto;
import java.lang.reflect.Field;
import com.google.common.collect.Maps;
import org.activiti.engine.task.Task;
import com.entfrm.core.security.util.SecurityUtil;
import com.entfrm.core.base.annotation.SignNode;
import java.util.Arrays;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.bean.BeanUtil;

/**
 * <AUTHOR>
 * @date 2024-06-04 10:12:01
 *
 * @description VIP就餐申請單Service业务层
 */
@Service
@AllArgsConstructor
public class EntVipApplyServiceImpl extends ServiceImpl<EntVipApplyMapper, EntVipApply> implements EntVipApplyService {
	private final RuntimeService runtimeService;
	private final org.activiti.engine.TaskService actTaskService;
	private final WfConfigService wfConfigService;
	private final TQhAllRelationService allRelationService;
    private final RabbitTemplate rabbitTemplate;
	private final TaskService taskService;

    private final EntVipApplyItemsService entVipApplyItemsService;

    	/**
	 * 启动流程
	 *
	 * @param entVipApply
	 * @return
	 */
	@Override
	public Boolean startProcess(EntVipApply entVipApply) {
        entVipApply.setWorkStatus(Integer.parseInt(TaskStatusEnum.CHECKING.getStatus()));
        String tableName = EntVipApply.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
        String procDefKey = wfConfigDto.getProcDefKey();
        String businessKey = procDefKey + CommonConstant.BREAK_BUSINESS_KEY + entVipApply.getId();
        Field fields[] = entVipApply.getClass().getDeclaredFields();
        Map variables = Maps.newHashMap();
        //設置填單人
        variables.put("maker", SecurityUtil.getUser().getUsername());
        variables.put("serialNo", entVipApply.getSerialno());
        variables.put("workflowName", wfConfigDto.getFormName());
        Arrays.stream(fields).forEach(field -> {
                SignNode signNode = field.getAnnotation(SignNode.class);
                if (ObjectUtil.isNotEmpty(signNode)) {
                    if (signNode.type().equals(SignNode.Type.COMMON)) {
                        variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entVipApply, field)) ? "AUTO_SIGN" : ReflectUtil.getFieldValue(entVipApply, field));
                    } else if (signNode.type().equals(SignNode.Type.HUIQIAN)) {
                        variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entVipApply, field)) ? Arrays.asList("AUTO_SIGN") : Arrays.asList(ReflectUtil.getFieldValue(entVipApply, field).toString().split(",")));
                    }
                }
            }
        );
        ProcessInstance pi = runtimeService.startProcessInstanceByKey(procDefKey, businessKey,variables);
        entVipApply.setProcessId(pi.getProcessInstanceId());
        this.updateById(entVipApply);

        TQhAllRelation relation = allRelationService.getOne(new QueryWrapper<TQhAllRelation>().eq("serialno",entVipApply.getSerialno()));
        relation.setWorkStatus(entVipApply.getWorkStatus());
        relation.setProcessId(pi.getProcessInstanceId());
        Task task = actTaskService.createTaskQuery().processInstanceId(pi.getProcessInstanceId()).singleResult();
        relation.setNodename(task.getName());
        relation.setDeptNo(entVipApply.getApplyDeptNo());
        allRelationService.updateById(relation);
        //添加mq
        rabbitTemplate.convertAndSend("SignDirectExchange", "Sign.Direct.Routing", BeanUtil.beanToMap(relation));
        return Boolean.TRUE;
	}

    /**
     * 插入一条记录（选择字段，策略插入）
     *
     * @param entVipApply 实体对象
     */
    @Override
    public boolean save(EntVipApply entVipApply) {
        boolean rs = super.save(entVipApply);
        saveChild(entVipApply);
	    String tableName = EntVipApply.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
        TQhAllRelation relation = new TQhAllRelation();
        relation.setDtoName(tableName);
        relation.setMakerNo(entVipApply.getMakerNo());
        relation.setMakerName(entVipApply.getMakerName());
        relation.setSerialno(entVipApply.getSerialno());
        relation.setWorkflowid(wfConfigDto.getProcDefKey());
        relation.setWfName(wfConfigDto.getFormName());
        relation.setClassPackage(entVipApply.getClass().getName());
        relation.setWorkStatus(0);
        allRelationService.save(relation);
        return rs;
    }
	@Override
	public Boolean updateAndCheck(EntVipApply entVipApply, LeaveDto leaveDto) {
		try {
			this.updateById(entVipApply);
			taskService.checkTask(leaveDto);
		} catch (Exception e) {
			return false;
		}
		return true;
	}
    /**
     * 插入一条记录（选择字段，策略插入）
     *
     * @param entVipApply 实体对象
     */
    @Override
    public boolean updateById(EntVipApply entVipApply) {
        // 删除子表信息关联
        entVipApplyItemsService.remove(new QueryWrapper<EntVipApplyItems>().eq("pid", entVipApply.getId()));
        boolean rs = super.updateById(entVipApply);
        // 新增子表信息管理
        if(rs){
            // 新增子表信息管理
            saveChild(entVipApply);
        }
        return rs;
    }
    /**
     * 新增子表記錄
     *
     * @param entVipApply 表單对象
     */
    public void saveChild(EntVipApply entVipApply) {
        List<EntVipApplyItems> entVipApplyItemsLists = entVipApply.getEntVipApplyItemsLists();
        if (entVipApplyItemsLists != null) {
            for (EntVipApplyItems entVipApplyItems : entVipApplyItemsLists) {
                entVipApplyItems.setId(null);
                entVipApplyItems.setPid(entVipApply.getId());
                entVipApplyItemsService.save(entVipApplyItems);
            }
        }
    }

    /**
     * 删除（根据ID 批量删除）
     *
     * @param idList 主键ID列表
     */
    @Override
    public boolean removeByIds(Collection<? extends Serializable> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return false;
        }
        for (Serializable id : idList) {
            EntVipApply entVipApply = getById(id);
            // 删除子表信息关联
            entVipApplyItemsService.remove(new QueryWrapper<EntVipApplyItems>().eq("pid", entVipApply.getId()));
            }

        return SqlHelper.retBool(getBaseMapper().deleteBatchIds(idList));
    }
    /**
     * 根據ID獲取信息
     *
     * @param id
     * @return
     */
    @Override
    public EntVipApply getById(Serializable id) {
        EntVipApply entVipApply = super.getById(id);
        setChilds(entVipApply);
        return entVipApply;
    }

    /**
     * 獲取子表列表（${childTable.tableComment}）
     */
    public void setChilds(EntVipApply entVipApply){

    List<EntVipApplyItems> entVipApplyItemsLists = entVipApplyItemsService.list(new QueryWrapper<EntVipApplyItems>().eq("pid", entVipApply.getId()));
    entVipApply.setEntVipApplyItemsLists(entVipApplyItemsLists);
    }
}
