package com.entfrm.biz.caaesign.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import com.baomidou.mybatisplus.annotation.TableName;
import com.entfrm.biz.activiti.service.WfConfigService;
import com.entfrm.biz.activiti.entity.TQhAllRelation;
import com.entfrm.biz.activiti.service.TQhAllRelationService;
import com.entfrm.biz.activiti.service.TaskService;
import com.entfrm.biz.activiti.dto.LeaveDto;
import org.springframework.stereotype.Service;
import com.entfrm.biz.caaesign.mapper.EntLailiTestMapper;
import com.entfrm.biz.caaesign.entity.EntLailiTest;
import com.entfrm.biz.caaesign.service.EntLailiTestService;
import com.entfrm.core.base.util.PinyinUtil;
import com.entfrm.core.data.util.SerialNumberTool;
import cn.hutool.core.date.DateUtil;
import lombok.AllArgsConstructor;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.runtime.ProcessInstance;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.transaction.annotation.Transactional;
import com.entfrm.core.base.enums.TaskStatusEnum;
import java.util.Map;
import com.entfrm.core.data.constant.CommonConstant;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.io.Serializable;
import com.entfrm.biz.caaesign.entity.EntLailiItems;
import com.entfrm.biz.caaesign.service.EntLailiItemsService;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import java.util.Collection;
import com.entfrm.biz.activiti.dto.WfConfigDto;
import java.lang.reflect.Field;
import com.google.common.collect.Maps;
import org.activiti.engine.task.Task;
import com.entfrm.core.security.util.SecurityUtil;
import com.entfrm.core.base.annotation.SignNode;
import java.util.Arrays;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.bean.BeanUtil;

/**
 * <AUTHOR>
 * @date 2022-12-21 15:28:09
 *
 * @description 來麗測試Service业务层
 */
@Service
@AllArgsConstructor
public class EntLailiTestServiceImpl extends ServiceImpl<EntLailiTestMapper, EntLailiTest> implements EntLailiTestService {
	private final RuntimeService runtimeService;
	private final org.activiti.engine.TaskService actTaskService;
	private final WfConfigService wfConfigService;
	private final TQhAllRelationService allRelationService;
    private final RabbitTemplate rabbitTemplate;
	private final TaskService taskService;

    private final EntLailiItemsService entLailiItemsService;

    	/**
	 * 启动流程
	 *
	 * @param entLailiTest
	 * @return
	 */
	@Override
	public Boolean startProcess(EntLailiTest entLailiTest) {
        entLailiTest.setWorkStatus(Integer.parseInt(TaskStatusEnum.CHECKING.getStatus()));
        String tableName = EntLailiTest.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
        String procDefKey = wfConfigDto.getProcDefKey();
        String businessKey = procDefKey + CommonConstant.BREAK_BUSINESS_KEY + entLailiTest.getId();
        Field fields[] = entLailiTest.getClass().getDeclaredFields();
        Map variables = Maps.newHashMap();
        //設置填單人
        variables.put("maker", SecurityUtil.getUser().getUsername());
        variables.put("serialNo", entLailiTest.getSerialno());
        variables.put("workflowName", wfConfigDto.getFormName());
        Arrays.stream(fields).forEach(field -> {
                SignNode signNode = field.getAnnotation(SignNode.class);
                if (ObjectUtil.isNotEmpty(signNode)) {
                    if (signNode.type().equals(SignNode.Type.COMMON)) {
                        variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entLailiTest, field)) ? "AUTO_SIGN" : ReflectUtil.getFieldValue(entLailiTest, field));
                    } else if (signNode.type().equals(SignNode.Type.HUIQIAN)) {
                        variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entLailiTest, field)) ? Arrays.asList("AUTO_SIGN") : Arrays.asList(ReflectUtil.getFieldValue(entLailiTest, field).toString().split(",")));
                    }
                }
            }
        );
        ProcessInstance pi = runtimeService.startProcessInstanceByKey(procDefKey, businessKey,variables);
        entLailiTest.setProcessId(pi.getProcessInstanceId());
        this.updateById(entLailiTest);

        TQhAllRelation relation = allRelationService.getOne(new QueryWrapper<TQhAllRelation>().eq("serialno",entLailiTest.getSerialno()));
        relation.setWorkStatus(entLailiTest.getWorkStatus());
        relation.setProcessId(pi.getProcessInstanceId());
        Task task = actTaskService.createTaskQuery().processInstanceId(pi.getProcessInstanceId()).singleResult();
        relation.setNodename(task.getName());
        relation.setDeptNo(entLailiTest.getMakerdeptno());
        allRelationService.updateById(relation);
        //添加mq
        rabbitTemplate.convertAndSend("SignDirectExchange", "Sign.Direct.Routing", BeanUtil.beanToMap(relation));
        return Boolean.TRUE;
	}

    /**
     * 插入一条记录（选择字段，策略插入）
     *
     * @param entLailiTest 实体对象
     */
    @Override
    public boolean save(EntLailiTest entLailiTest) {
        boolean rs = super.save(entLailiTest);
        saveChild(entLailiTest);
	    String tableName = EntLailiTest.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
        TQhAllRelation relation = new TQhAllRelation();
        relation.setDtoName(tableName);
        relation.setMakerNo(entLailiTest.getMakerNo());
        relation.setMakerName(entLailiTest.getMakerName());
        relation.setSerialno(entLailiTest.getSerialno());
        relation.setWorkflowid(wfConfigDto.getProcDefKey());
        relation.setWfName(wfConfigDto.getFormName());
        relation.setClassPackage(entLailiTest.getClass().getName());
        relation.setWorkStatus(0);
        allRelationService.save(relation);
        return rs;
    }
	@Override
	public Boolean updateAndCheck(EntLailiTest entLailiTest, LeaveDto leaveDto) {
		try {
			this.updateById(entLailiTest);
			taskService.checkTask(leaveDto);
		} catch (Exception e) {
			return false;
		}
		return true;
	}
    /**
     * 插入一条记录（选择字段，策略插入）
     *
     * @param entLailiTest 实体对象
     */
    @Override
    public boolean updateById(EntLailiTest entLailiTest) {
        // 删除子表信息关联
        entLailiItemsService.remove(new QueryWrapper<EntLailiItems>().eq("pid", entLailiTest.getId()));
        boolean rs = super.updateById(entLailiTest);
        // 新增子表信息管理
        if(rs){
            // 新增子表信息管理
            saveChild(entLailiTest);
        }
        return rs;
    }
    /**
     * 新增子表記錄
     *
     * @param entLailiTest 表單对象
     */
    public void saveChild(EntLailiTest entLailiTest) {
        List<EntLailiItems> entLailiItemsLists = entLailiTest.getEntLailiItemsLists();
        if (entLailiItemsLists != null) {
            for (EntLailiItems entLailiItems : entLailiItemsLists) {
                entLailiItems.setId(null);
                entLailiItems.setPid(entLailiTest.getId());
                entLailiItemsService.save(entLailiItems);
            }
        }
    }

    /**
     * 删除（根据ID 批量删除）
     *
     * @param idList 主键ID列表
     */
    @Override
    public boolean removeByIds(Collection<? extends Serializable> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return false;
        }
        for (Serializable id : idList) {
            EntLailiTest entLailiTest = getById(id);
            // 删除子表信息关联
            entLailiItemsService.remove(new QueryWrapper<EntLailiItems>().eq("pid", entLailiTest.getId()));
            }

        return SqlHelper.retBool(getBaseMapper().deleteBatchIds(idList));
    }
    /**
     * 根據ID獲取信息
     *
     * @param id
     * @return
     */
    @Override
    public EntLailiTest getById(Serializable id) {
        EntLailiTest entLailiTest = super.getById(id);
        setChilds(entLailiTest);
        return entLailiTest;
    }

    /**
     * 獲取子表列表（${childTable.tableComment}）
     */
    public void setChilds(EntLailiTest entLailiTest){

    List<EntLailiItems> entLailiItemsLists = entLailiItemsService.list(new QueryWrapper<EntLailiItems>().eq("pid", entLailiTest.getId()));
    entLailiTest.setEntLailiItemsLists(entLailiItemsLists);
    }
}
