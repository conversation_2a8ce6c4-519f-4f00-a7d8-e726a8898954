package com.entfrm.biz.caaesign.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import com.baomidou.mybatisplus.annotation.TableName;
import com.entfrm.biz.activiti.service.WfConfigService;
import com.entfrm.biz.activiti.entity.TQhAllRelation;
import com.entfrm.biz.activiti.service.TQhAllRelationService;
import org.springframework.stereotype.Service;
import com.entfrm.biz.caaesign.mapper.EntDispatchRepairMapper;
import com.entfrm.biz.caaesign.entity.EntDispatchRepair;
import com.entfrm.biz.caaesign.service.EntDispatchRepairService;
import com.entfrm.core.base.util.PinyinUtil;
import com.entfrm.core.data.util.SerialNumberTool;
import cn.hutool.core.date.DateUtil;
import lombok.AllArgsConstructor;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.runtime.ProcessInstance;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.transaction.annotation.Transactional;
import com.entfrm.core.base.enums.TaskStatusEnum;
import java.util.Map;
import com.entfrm.core.data.constant.CommonConstant;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.io.Serializable;
import com.entfrm.biz.caaesign.entity.EntDispatchRepairItems;
import com.entfrm.biz.caaesign.service.EntDispatchRepairItemsService;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import java.util.Collection;
import com.entfrm.biz.activiti.dto.WfConfigDto;
import java.lang.reflect.Field;
import com.google.common.collect.Maps;
import org.activiti.engine.task.Task;
import com.entfrm.core.security.util.SecurityUtil;
import com.entfrm.core.base.annotation.SignNode;
import java.util.Arrays;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.bean.BeanUtil;

/**
 * <AUTHOR>
 * @date 2021-10-12 14:08:24
 *
 * @description 總務零星修繕派工單Service业务层
 */
@Service
@AllArgsConstructor
public class EntDispatchRepairServiceImpl extends ServiceImpl<EntDispatchRepairMapper, EntDispatchRepair> implements EntDispatchRepairService {
	private final RuntimeService runtimeService;
	private final org.activiti.engine.TaskService actTaskService;
	private final WfConfigService wfConfigService;
	private final TQhAllRelationService allRelationService;
    private final RabbitTemplate rabbitTemplate;

    private final EntDispatchRepairItemsService entDispatchRepairItemsService;

    	/**
	 * 启动流程
	 *
	 * @param entDispatchRepair
	 * @return
	 */
	@Override
	public Boolean startProcess(EntDispatchRepair entDispatchRepair) {
        entDispatchRepair.setWorkStatus(Integer.parseInt(TaskStatusEnum.CHECKING.getStatus()));
        String tableName = EntDispatchRepair.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
        String procDefKey = wfConfigDto.getProcDefKey();
        String businessKey = procDefKey + CommonConstant.BREAK_BUSINESS_KEY + entDispatchRepair.getId();
        Field fields[] = entDispatchRepair.getClass().getDeclaredFields();
        Map variables = Maps.newHashMap();
        //設置填單人
        variables.put("maker", SecurityUtil.getUser().getUsername());
        variables.put("serialNo", entDispatchRepair.getSerialno());
        variables.put("workflowName", wfConfigDto.getFormName());
        Arrays.stream(fields).forEach(field -> {
                SignNode signNode = field.getAnnotation(SignNode.class);
                if (ObjectUtil.isNotEmpty(signNode)) {
                    if (signNode.type().equals(SignNode.Type.COMMON)) {
                        variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entDispatchRepair, field)) ? "AUTO_SIGN" : ReflectUtil.getFieldValue(entDispatchRepair, field));
                    } else if (signNode.type().equals(SignNode.Type.HUIQIAN)) {
                        variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entDispatchRepair, field)) ? Arrays.asList("AUTO_SIGN") : Arrays.asList(ReflectUtil.getFieldValue(entDispatchRepair, field).toString().split(",")));
                    }
                }
            }
        );
        ProcessInstance pi = runtimeService.startProcessInstanceByKey(procDefKey, businessKey,variables);
        entDispatchRepair.setProcessId(pi.getProcessInstanceId());
        this.updateById(entDispatchRepair);

        TQhAllRelation relation = allRelationService.getOne(new QueryWrapper<TQhAllRelation>().eq("serialno",entDispatchRepair.getSerialno()));
        relation.setWorkStatus(entDispatchRepair.getWorkStatus());
        relation.setProcessId(pi.getProcessInstanceId());
        Task task = actTaskService.createTaskQuery().processInstanceId(pi.getProcessInstanceId()).singleResult();
        relation.setNodename(task.getName());
        relation.setDeptNo(entDispatchRepair.getApplyDeptNo());
        allRelationService.updateById(relation);
        //添加mq
        rabbitTemplate.convertAndSend("SignDirectExchange", "Sign.Direct.Routing", BeanUtil.beanToMap(relation));
        return Boolean.TRUE;
	}

    /**
     * 插入一条记录（选择字段，策略插入）
     *
     * @param entDispatchRepair 实体对象
     */
    @Override
    public boolean save(EntDispatchRepair entDispatchRepair) {
        boolean rs = super.save(entDispatchRepair);
        saveChild(entDispatchRepair);
	    String tableName = EntDispatchRepair.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
        TQhAllRelation relation = new TQhAllRelation();
        relation.setDtoName(tableName);
        relation.setMakerNo(entDispatchRepair.getMakerNo());
        relation.setMakerName(entDispatchRepair.getMakerName());
        relation.setSerialno(entDispatchRepair.getSerialno());
        relation.setWorkflowid(wfConfigDto.getProcDefKey());
        relation.setWfName(wfConfigDto.getFormName());
        relation.setClassPackage(entDispatchRepair.getClass().getName());
        relation.setWorkStatus(0);
        allRelationService.save(relation);
        return rs;
    }

    /**
     * 插入一条记录（选择字段，策略插入）
     *
     * @param entDispatchRepair 实体对象
     */
    @Override
    public boolean updateById(EntDispatchRepair entDispatchRepair) {
        // 删除子表信息关联
        entDispatchRepairItemsService.remove(new QueryWrapper<EntDispatchRepairItems>().eq("pid", entDispatchRepair.getId()));
        boolean rs = super.updateById(entDispatchRepair);
        // 新增子表信息管理
        if(rs){
            // 新增子表信息管理
            saveChild(entDispatchRepair);
        }
        return rs;
    }
    /**
     * 新增子表記錄
     *
     * @param entDispatchRepair 表單对象
     */
    public void saveChild(EntDispatchRepair entDispatchRepair) {
        List<EntDispatchRepairItems> entDispatchRepairItemsLists = entDispatchRepair.getEntDispatchRepairItemsLists();
        if (entDispatchRepairItemsLists != null) {
            for (EntDispatchRepairItems entDispatchRepairItems : entDispatchRepairItemsLists) {
                entDispatchRepairItems.setId(null);
                entDispatchRepairItems.setPid(entDispatchRepair.getId());
                entDispatchRepairItemsService.save(entDispatchRepairItems);
            }
        }
    }

    /**
     * 删除（根据ID 批量删除）
     *
     * @param idList 主键ID列表
     */
    @Override
    public boolean removeByIds(Collection<? extends Serializable> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return false;
        }
        for (Serializable id : idList) {
            EntDispatchRepair entDispatchRepair = getById(id);
            // 删除子表信息关联
            entDispatchRepairItemsService.remove(new QueryWrapper<EntDispatchRepairItems>().eq("pid", entDispatchRepair.getId()));
            }

        return SqlHelper.retBool(getBaseMapper().deleteBatchIds(idList));
    }
    /**
     * 根據ID獲取信息
     *
     * @param id
     * @return
     */
    @Override
    public EntDispatchRepair getById(Serializable id) {
        EntDispatchRepair entDispatchRepair = super.getById(id);
        setChilds(entDispatchRepair);
        return entDispatchRepair;
    }

    /**
     * 獲取子表列表（${childTable.tableComment}）
     */
    public void setChilds(EntDispatchRepair entDispatchRepair){

    List<EntDispatchRepairItems> entDispatchRepairItemsLists = entDispatchRepairItemsService.list(new QueryWrapper<EntDispatchRepairItems>().eq("pid", entDispatchRepair.getId()));
    entDispatchRepair.setEntDispatchRepairItemsLists(entDispatchRepairItemsLists);
    }
}
