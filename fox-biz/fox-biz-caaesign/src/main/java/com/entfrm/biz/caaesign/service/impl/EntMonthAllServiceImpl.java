package com.entfrm.biz.caaesign.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.entfrm.biz.activiti.service.TQhAllRelationService;
import com.entfrm.biz.activiti.service.TaskService;
import com.entfrm.biz.activiti.service.WfConfigService;
import com.entfrm.biz.caaesign.entity.EntMachineAll;
import com.entfrm.biz.caaesign.entity.EntMonthAll;
import com.entfrm.biz.caaesign.mapper.EntMachineAllMapper;
import com.entfrm.biz.caaesign.mapper.EntMonthAllMapper;
import com.entfrm.biz.caaesign.service.EntMachineAllService;
import com.entfrm.biz.caaesign.service.EntMonthAllService;
import lombok.AllArgsConstructor;
import org.activiti.engine.RuntimeService;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2022-07-07 15:18:44
 *
 * @description 機台資安點檢暨抽檢匯總表（S3/S5）Service业务层
 */
@Service
@AllArgsConstructor
public class EntMonthAllServiceImpl extends ServiceImpl<EntMonthAllMapper, EntMonthAll> implements EntMonthAllService {
	private final RuntimeService runtimeService;
	private final org.activiti.engine.TaskService actTaskService;
	private final WfConfigService wfConfigService;
	private final TQhAllRelationService allRelationService;
	private final RabbitTemplate rabbitTemplate;
	private final TaskService taskService;
    }
