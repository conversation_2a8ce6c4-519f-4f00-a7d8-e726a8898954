package com.entfrm.biz.caaesign.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.entfrm.biz.activiti.dto.LeaveDto;
import com.entfrm.biz.activiti.dto.WfConfigDto;
import com.entfrm.biz.activiti.entity.TQhAllRelation;
import com.entfrm.biz.activiti.service.TQhAllRelationService;
import com.entfrm.biz.activiti.service.TaskService;
import com.entfrm.biz.activiti.service.WfConfigService;
import com.entfrm.biz.caaesign.entity.EntMoneyShare;
import com.entfrm.biz.caaesign.entity.EntMoneyShareAll;
import com.entfrm.biz.caaesign.mapper.EntMoneyShareAllMapper;
import com.entfrm.biz.caaesign.service.EntMoneyShareAllService;
import com.entfrm.biz.caaesign.service.EntMoneyShareItemsService;
import com.entfrm.core.base.annotation.SignNode;
import com.entfrm.core.base.enums.TaskStatusEnum;
import com.entfrm.core.data.constant.CommonConstant;
import com.entfrm.core.security.util.SecurityUtil;
import com.google.common.collect.Maps;
import org.activiti.engine.task.Task;
import lombok.AllArgsConstructor;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.runtime.ProcessInstance;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2022-06-01 10:10:11
 *
 * @description 费用分摊表Service业务层
 */
@Service
@AllArgsConstructor
public class EntMoneyShareAllServiceImpl extends ServiceImpl<EntMoneyShareAllMapper, EntMoneyShareAll> implements EntMoneyShareAllService {
	private final RuntimeService runtimeService;
	private final org.activiti.engine.TaskService actTaskService;
	private final WfConfigService wfConfigService;
	private final TQhAllRelationService allRelationService;
    private final RabbitTemplate rabbitTemplate;
	private final TaskService taskService;

    	/**
	 * 启动流程
	 *
	 * @param entMoneyShareAll
	 * @return
	 */
	@Override
	public Boolean startProcess(EntMoneyShareAll entMoneyShareAll) {
        entMoneyShareAll.setWorkStatus(Integer.parseInt(TaskStatusEnum.CHECKING.getStatus()));
        String tableName = EntMoneyShareAll.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
        String procDefKey = wfConfigDto.getProcDefKey();
        String businessKey = procDefKey + CommonConstant.BREAK_BUSINESS_KEY + entMoneyShareAll.getId();
        Field fields[] = entMoneyShareAll.getClass().getDeclaredFields();
        Map variables = Maps.newHashMap();
        //設置填單人
        variables.put("maker", SecurityUtil.getUser().getUsername());
        variables.put("serialNo", entMoneyShareAll.getSerialno());
        variables.put("workflowName", wfConfigDto.getFormName());
        Arrays.stream(fields).forEach(field -> {
                SignNode signNode = field.getAnnotation(SignNode.class);
                if (ObjectUtil.isNotEmpty(signNode)) {
                    if (signNode.type().equals(SignNode.Type.COMMON)) {
                        variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entMoneyShareAll, field)) ? "AUTO_SIGN" : ReflectUtil.getFieldValue(entMoneyShareAll, field));
                    } else if (signNode.type().equals(SignNode.Type.HUIQIAN)) {
                        variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entMoneyShareAll, field)) ? Arrays.asList("AUTO_SIGN") : Arrays.asList(ReflectUtil.getFieldValue(entMoneyShareAll, field).toString().split(",")));
                    }
                }
            }
        );
        ProcessInstance pi = runtimeService.startProcessInstanceByKey(procDefKey, businessKey,variables);
        entMoneyShareAll.setProcessId(pi.getProcessInstanceId());
        this.updateById(entMoneyShareAll);

        TQhAllRelation relation = allRelationService.getOne(new QueryWrapper<TQhAllRelation>().eq("serialno",entMoneyShareAll.getSerialno()));
        relation.setWorkStatus(entMoneyShareAll.getWorkStatus());
        relation.setProcessId(pi.getProcessInstanceId());
        Task task = actTaskService.createTaskQuery().processInstanceId(pi.getProcessInstanceId()).singleResult();
        relation.setNodename(task.getName());
        relation.setDeptNo(entMoneyShareAll.getApplyDeptNo());
        allRelationService.updateById(relation);
        //添加mq
        rabbitTemplate.convertAndSend("SignDirectExchange", "Sign.Direct.Routing", BeanUtil.beanToMap(relation));
        return Boolean.TRUE;
	}

    /**
     * 插入一条记录（选择字段，策略插入）
     *
     * @param entMoneyShareAll 实体对象
     */
    @Override
    public boolean save(EntMoneyShareAll entMoneyShareAll) {
        boolean rs = super.save(entMoneyShareAll);
	    String tableName = EntMoneyShareAll.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
        TQhAllRelation relation = new TQhAllRelation();
        relation.setDtoName(tableName);
        relation.setMakerNo(entMoneyShareAll.getMakerNo());
        relation.setMakerName(entMoneyShareAll.getMakerName());
        relation.setSerialno(entMoneyShareAll.getSerialno());
        relation.setWorkflowid(wfConfigDto.getProcDefKey());
        relation.setWfName(wfConfigDto.getFormName());
        relation.setClassPackage(entMoneyShareAll.getClass().getName());
        relation.setWorkStatus(0);
        allRelationService.save(relation);
        return rs;
    }
	@Override
	public Boolean updateAndCheck(EntMoneyShareAll entMoneyShareAll, LeaveDto leaveDto) {
		try {
			this.updateById(entMoneyShareAll);
			taskService.checkTask(leaveDto);
		} catch (Exception e) {
			return false;
		}
		return true;
	}

}
