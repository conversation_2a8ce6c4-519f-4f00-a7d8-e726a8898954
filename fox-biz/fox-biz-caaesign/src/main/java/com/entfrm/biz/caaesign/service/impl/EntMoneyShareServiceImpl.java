package com.entfrm.biz.caaesign.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import com.baomidou.mybatisplus.annotation.TableName;
import com.entfrm.biz.activiti.service.WfConfigService;
import com.entfrm.biz.activiti.entity.TQhAllRelation;
import com.entfrm.biz.activiti.service.TQhAllRelationService;
import com.entfrm.biz.activiti.service.TaskService;
import com.entfrm.biz.activiti.dto.LeaveDto;
import org.springframework.stereotype.Service;
import com.entfrm.biz.caaesign.mapper.EntMoneyShareMapper;
import com.entfrm.biz.caaesign.entity.EntMoneyShare;
import com.entfrm.biz.caaesign.service.EntMoneyShareService;
import com.entfrm.core.base.util.PinyinUtil;
import com.entfrm.core.data.util.SerialNumberTool;
import cn.hutool.core.date.DateUtil;
import lombok.AllArgsConstructor;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.runtime.ProcessInstance;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.transaction.annotation.Transactional;
import com.entfrm.core.base.enums.TaskStatusEnum;
import java.util.Map;
import com.entfrm.core.data.constant.CommonConstant;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.io.Serializable;
import com.entfrm.biz.caaesign.entity.EntMoneyShareItems;
import com.entfrm.biz.caaesign.service.EntMoneyShareItemsService;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import java.util.Collection;
import com.entfrm.biz.activiti.dto.WfConfigDto;
import java.lang.reflect.Field;
import com.google.common.collect.Maps;
import org.activiti.engine.task.Task;
import com.entfrm.core.security.util.SecurityUtil;
import com.entfrm.core.base.annotation.SignNode;
import java.util.Arrays;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.bean.BeanUtil;

/**
 * <AUTHOR>
 * @date 2022-06-06 14:36:02
 *
 * @description 费用分摊表Service业务层
 */
@Service
@AllArgsConstructor
public class EntMoneyShareServiceImpl extends ServiceImpl<EntMoneyShareMapper, EntMoneyShare> implements EntMoneyShareService {
	private final RuntimeService runtimeService;
	private final org.activiti.engine.TaskService actTaskService;
	private final WfConfigService wfConfigService;
	private final TQhAllRelationService allRelationService;
    private final RabbitTemplate rabbitTemplate;
	private final TaskService taskService;

    private final EntMoneyShareItemsService entMoneyShareItemsService;

    	/**
	 * 启动流程
	 *
	 * @param entMoneyShare
	 * @return
	 */
	@Override
	public Boolean startProcess(EntMoneyShare entMoneyShare) {
        entMoneyShare.setWorkStatus(Integer.parseInt(TaskStatusEnum.CHECKING.getStatus()));
        String tableName = EntMoneyShare.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
        String procDefKey = wfConfigDto.getProcDefKey();
        String businessKey = procDefKey + CommonConstant.BREAK_BUSINESS_KEY + entMoneyShare.getId();
        Field fields[] = entMoneyShare.getClass().getDeclaredFields();
        Map variables = Maps.newHashMap();
        //設置填單人
        variables.put("maker", SecurityUtil.getUser().getUsername());
        variables.put("serialNo", entMoneyShare.getSerialno());
        variables.put("workflowName", wfConfigDto.getFormName());
        Arrays.stream(fields).forEach(field -> {
                SignNode signNode = field.getAnnotation(SignNode.class);
                if (ObjectUtil.isNotEmpty(signNode)) {
                    if (signNode.type().equals(SignNode.Type.COMMON)) {
                        variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entMoneyShare, field)) ? "AUTO_SIGN" : ReflectUtil.getFieldValue(entMoneyShare, field));
                    } else if (signNode.type().equals(SignNode.Type.HUIQIAN)) {
                        variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entMoneyShare, field)) ? Arrays.asList("AUTO_SIGN") : Arrays.asList(ReflectUtil.getFieldValue(entMoneyShare, field).toString().split(",")));
                    }
                }
            }
        );
        ProcessInstance pi = runtimeService.startProcessInstanceByKey(procDefKey, businessKey,variables);
        entMoneyShare.setProcessId(pi.getProcessInstanceId());
        this.updateById(entMoneyShare);

        TQhAllRelation relation = allRelationService.getOne(new QueryWrapper<TQhAllRelation>().eq("serialno",entMoneyShare.getSerialno()));
        relation.setWorkStatus(entMoneyShare.getWorkStatus());
        relation.setProcessId(pi.getProcessInstanceId());
        Task task = actTaskService.createTaskQuery().processInstanceId(pi.getProcessInstanceId()).singleResult();
        relation.setNodename(task.getName());
        relation.setDeptNo(entMoneyShare.getApplyDeptNo());
        allRelationService.updateById(relation);
        //添加mq
        rabbitTemplate.convertAndSend("SignDirectExchange", "Sign.Direct.Routing", BeanUtil.beanToMap(relation));
        return Boolean.TRUE;
	}

    /**
     * 插入一条记录（选择字段，策略插入）
     *
     * @param entMoneyShare 实体对象
     */
    @Override
    public boolean save(EntMoneyShare entMoneyShare) {
        boolean rs = super.save(entMoneyShare);
        saveChild(entMoneyShare);
	    String tableName = EntMoneyShare.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
        TQhAllRelation relation = new TQhAllRelation();
        relation.setDtoName(tableName);
        relation.setMakerNo(entMoneyShare.getMakerNo());
        relation.setMakerName(entMoneyShare.getMakerName());
        relation.setSerialno(entMoneyShare.getSerialno());
        relation.setWorkflowid(wfConfigDto.getProcDefKey());
        relation.setWfName(wfConfigDto.getFormName());
        relation.setClassPackage(entMoneyShare.getClass().getName());
        relation.setWorkStatus(0);
        allRelationService.save(relation);
        return rs;
    }
	@Override
	public Boolean updateAndCheck(EntMoneyShare entMoneyShare, LeaveDto leaveDto) {
		try {
			this.updateById(entMoneyShare);
			taskService.checkTask(leaveDto);
		} catch (Exception e) {
			return false;
		}
		return true;
	}
    /**
     * 插入一条记录（选择字段，策略插入）
     *
     * @param entMoneyShare 实体对象
     */
    @Override
    public boolean updateById(EntMoneyShare entMoneyShare) {
        // 删除子表信息关联
        entMoneyShareItemsService.remove(new QueryWrapper<EntMoneyShareItems>().eq("pid", entMoneyShare.getId()));
        boolean rs = super.updateById(entMoneyShare);
        // 新增子表信息管理
        if(rs){
            // 新增子表信息管理
            saveChild(entMoneyShare);
        }
        return rs;
    }
    /**
     * 新增子表記錄
     *
     * @param entMoneyShare 表單对象
     */
    public void saveChild(EntMoneyShare entMoneyShare) {
        List<EntMoneyShareItems> entMoneyShareItemsLists = entMoneyShare.getEntMoneyShareItemsLists();
        if (entMoneyShareItemsLists != null) {
            for (EntMoneyShareItems entMoneyShareItems : entMoneyShareItemsLists) {
                entMoneyShareItems.setId(null);
                entMoneyShareItems.setPid(entMoneyShare.getId());
                entMoneyShareItemsService.save(entMoneyShareItems);
            }
        }
    }

    /**
     * 删除（根据ID 批量删除）
     *
     * @param idList 主键ID列表
     */
    @Override
    public boolean removeByIds(Collection<? extends Serializable> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return false;
        }
        for (Serializable id : idList) {
            EntMoneyShare entMoneyShare = getById(id);
            // 删除子表信息关联
            entMoneyShareItemsService.remove(new QueryWrapper<EntMoneyShareItems>().eq("pid", entMoneyShare.getId()));
            }

        return SqlHelper.retBool(getBaseMapper().deleteBatchIds(idList));
    }
    /**
     * 根據ID獲取信息
     *
     * @param id
     * @return
     */
    @Override
    public EntMoneyShare getById(Serializable id) {
        EntMoneyShare entMoneyShare = super.getById(id);
        setChilds(entMoneyShare);
        return entMoneyShare;
    }

    /**
     * 獲取子表列表（${childTable.tableComment}）
     */
    public void setChilds(EntMoneyShare entMoneyShare){

    List<EntMoneyShareItems> entMoneyShareItemsLists = entMoneyShareItemsService.list(new QueryWrapper<EntMoneyShareItems>().eq("pid", entMoneyShare.getId()));
    entMoneyShare.setEntMoneyShareItemsLists(entMoneyShareItemsLists);
    }
}
