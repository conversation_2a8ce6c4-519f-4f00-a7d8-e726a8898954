package com.entfrm.biz.caaesign.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import com.baomidou.mybatisplus.annotation.TableName;
import com.entfrm.biz.activiti.service.WfConfigService;
import com.entfrm.biz.activiti.entity.TQhAllRelation;
import com.entfrm.biz.activiti.service.TQhAllRelationService;
import org.springframework.stereotype.Service;
import com.entfrm.biz.caaesign.mapper.EntRemoteEmpowermentMapper;
import com.entfrm.biz.caaesign.entity.EntRemoteEmpowerment;
import com.entfrm.biz.caaesign.service.EntRemoteEmpowermentService;
import com.entfrm.core.base.util.PinyinUtil;
import com.entfrm.core.data.util.SerialNumberTool;
import cn.hutool.core.date.DateUtil;
import lombok.AllArgsConstructor;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.runtime.ProcessInstance;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.transaction.annotation.Transactional;
import com.entfrm.core.base.enums.TaskStatusEnum;
import java.util.Map;
import com.entfrm.core.data.constant.CommonConstant;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.io.Serializable;
import com.entfrm.biz.caaesign.entity.EntRemoteEmpowerItems;
import com.entfrm.biz.caaesign.service.EntRemoteEmpowerItemsService;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import java.util.Collection;
import com.entfrm.biz.activiti.dto.WfConfigDto;
import java.lang.reflect.Field;
import com.google.common.collect.Maps;
import org.activiti.engine.task.Task;
import com.entfrm.core.security.util.SecurityUtil;
import com.entfrm.core.base.annotation.SignNode;
import java.util.Arrays;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.bean.BeanUtil;

/**
 * <AUTHOR>
 * @date 2021-11-05 14:05:48
 *
 * @description 產線遠端賦權帳號申請單Service业务层
 */
@Service
@AllArgsConstructor
public class EntRemoteEmpowermentServiceImpl extends ServiceImpl<EntRemoteEmpowermentMapper, EntRemoteEmpowerment> implements EntRemoteEmpowermentService {
	private final RuntimeService runtimeService;
	private final org.activiti.engine.TaskService actTaskService;
	private final WfConfigService wfConfigService;
	private final TQhAllRelationService allRelationService;
    private final RabbitTemplate rabbitTemplate;

    private final EntRemoteEmpowerItemsService entRemoteEmpowerItemsService;

    	/**
	 * 启动流程
	 *
	 * @param entRemoteEmpowerment
	 * @return
	 */
	@Override
	public Boolean startProcess(EntRemoteEmpowerment entRemoteEmpowerment) {
        entRemoteEmpowerment.setWorkStatus(Integer.parseInt(TaskStatusEnum.CHECKING.getStatus()));
        String tableName = EntRemoteEmpowerment.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
        String procDefKey = wfConfigDto.getProcDefKey();
        String businessKey = procDefKey + CommonConstant.BREAK_BUSINESS_KEY + entRemoteEmpowerment.getId();
        Field fields[] = entRemoteEmpowerment.getClass().getDeclaredFields();
        Map variables = Maps.newHashMap();
        //設置填單人
        variables.put("maker", SecurityUtil.getUser().getUsername());
        variables.put("serialNo", entRemoteEmpowerment.getSerialno());
        variables.put("workflowName", wfConfigDto.getFormName());
        Arrays.stream(fields).forEach(field -> {
                SignNode signNode = field.getAnnotation(SignNode.class);
                if (ObjectUtil.isNotEmpty(signNode)) {
                    if (signNode.type().equals(SignNode.Type.COMMON)) {
                        variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entRemoteEmpowerment, field)) ? "AUTO_SIGN" : ReflectUtil.getFieldValue(entRemoteEmpowerment, field));
                    } else if (signNode.type().equals(SignNode.Type.HUIQIAN)) {
                        variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entRemoteEmpowerment, field)) ? Arrays.asList("AUTO_SIGN") : Arrays.asList(ReflectUtil.getFieldValue(entRemoteEmpowerment, field).toString().split(",")));
                    }
                }
            }
        );
        ProcessInstance pi = runtimeService.startProcessInstanceByKey(procDefKey, businessKey,variables);
        entRemoteEmpowerment.setProcessId(pi.getProcessInstanceId());
        this.updateById(entRemoteEmpowerment);

        TQhAllRelation relation = allRelationService.getOne(new QueryWrapper<TQhAllRelation>().eq("serialno",entRemoteEmpowerment.getSerialno()));
        relation.setWorkStatus(entRemoteEmpowerment.getWorkStatus());
        relation.setProcessId(pi.getProcessInstanceId());
        Task task = actTaskService.createTaskQuery().processInstanceId(pi.getProcessInstanceId()).singleResult();
        relation.setNodename(task.getName());
        relation.setDeptNo(entRemoteEmpowerment.getApplyDeptNo());
        allRelationService.updateById(relation);
        //添加mq
        rabbitTemplate.convertAndSend("SignDirectExchange", "Sign.Direct.Routing", BeanUtil.beanToMap(relation));
        return Boolean.TRUE;
	}

    /**
     * 插入一条记录（选择字段，策略插入）
     *
     * @param entRemoteEmpowerment 实体对象
     */
    @Override
    public boolean save(EntRemoteEmpowerment entRemoteEmpowerment) {
        boolean rs = super.save(entRemoteEmpowerment);
        saveChild(entRemoteEmpowerment);
	    String tableName = EntRemoteEmpowerment.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
        TQhAllRelation relation = new TQhAllRelation();
        relation.setDtoName(tableName);
        relation.setMakerNo(entRemoteEmpowerment.getMakerNo());
        relation.setMakerName(entRemoteEmpowerment.getMakerName());
        relation.setSerialno(entRemoteEmpowerment.getSerialno());
        relation.setWorkflowid(wfConfigDto.getProcDefKey());
        relation.setWfName(wfConfigDto.getFormName());
        relation.setClassPackage(entRemoteEmpowerment.getClass().getName());
        relation.setWorkStatus(0);
        allRelationService.save(relation);
        return rs;
    }

    /**
     * 插入一条记录（选择字段，策略插入）
     *
     * @param entRemoteEmpowerment 实体对象
     */
    @Override
    public boolean updateById(EntRemoteEmpowerment entRemoteEmpowerment) {
        // 删除子表信息关联
        entRemoteEmpowerItemsService.remove(new QueryWrapper<EntRemoteEmpowerItems>().eq("pid", entRemoteEmpowerment.getId()));
        boolean rs = super.updateById(entRemoteEmpowerment);
        // 新增子表信息管理
        if(rs){
            // 新增子表信息管理
            saveChild(entRemoteEmpowerment);
        }
        return rs;
    }
    /**
     * 新增子表記錄
     *
     * @param entRemoteEmpowerment 表單对象
     */
    public void saveChild(EntRemoteEmpowerment entRemoteEmpowerment) {
        List<EntRemoteEmpowerItems> entRemoteEmpowerItemsLists = entRemoteEmpowerment.getEntRemoteEmpowerItemsLists();
        if (entRemoteEmpowerItemsLists != null) {
            for (EntRemoteEmpowerItems entRemoteEmpowerItems : entRemoteEmpowerItemsLists) {
                entRemoteEmpowerItems.setId(null);
                entRemoteEmpowerItems.setPid(entRemoteEmpowerment.getId());
                entRemoteEmpowerItemsService.save(entRemoteEmpowerItems);
            }
        }
    }

    /**
     * 删除（根据ID 批量删除）
     *
     * @param idList 主键ID列表
     */
    @Override
    public boolean removeByIds(Collection<? extends Serializable> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return false;
        }
        for (Serializable id : idList) {
            EntRemoteEmpowerment entRemoteEmpowerment = getById(id);
            // 删除子表信息关联
            entRemoteEmpowerItemsService.remove(new QueryWrapper<EntRemoteEmpowerItems>().eq("pid", entRemoteEmpowerment.getId()));
            }

        return SqlHelper.retBool(getBaseMapper().deleteBatchIds(idList));
    }
    /**
     * 根據ID獲取信息
     *
     * @param id
     * @return
     */
    @Override
    public EntRemoteEmpowerment getById(Serializable id) {
        EntRemoteEmpowerment entRemoteEmpowerment = super.getById(id);
        setChilds(entRemoteEmpowerment);
        return entRemoteEmpowerment;
    }

    /**
     * 獲取子表列表（${childTable.tableComment}）
     */
    public void setChilds(EntRemoteEmpowerment entRemoteEmpowerment){

    List<EntRemoteEmpowerItems> entRemoteEmpowerItemsLists = entRemoteEmpowerItemsService.list(new QueryWrapper<EntRemoteEmpowerItems>().eq("pid", entRemoteEmpowerment.getId()));
    entRemoteEmpowerment.setEntRemoteEmpowerItemsLists(entRemoteEmpowerItemsLists);
    }
}
