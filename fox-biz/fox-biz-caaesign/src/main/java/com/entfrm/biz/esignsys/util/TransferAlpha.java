package com.entfrm.biz.esignsys.util;

import com.entfrm.biz.system.service.ConfigService;
import com.entfrm.core.base.util.SpringContextUtil;
import org.apache.commons.lang3.StringUtils;

import javax.imageio.ImageIO;
import javax.swing.*;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

public class TransferAlpha {

    static float RATE = 2;  //簽名圖片 寬/高
    static double WIDTHPADDINGRATE = 0; //默認寬度內填充占整體寬度比例
    static double HEIGHTPADDINGRATE = 0; //默認高度內填充占整體高度比例


    public static byte[] transferAlpha2Byte(InputStream is) {
        ByteArrayOutputStream byteArrayOutputStream = null;
        ConfigService configService = SpringContextUtil.getBean("configServiceImpl");
        String threshold = configService.getValueByKey("esign.alpha.threshold");
        int thresholdValue = 30;
        if(StringUtils.isNotBlank(threshold)){
            thresholdValue = Integer.parseInt(threshold);
            thresholdValue = thresholdValue<0?0:(thresholdValue>255?255:thresholdValue);
        }
        byte[] result = null;
        try {
            // 如果是MultipartFile类型，那么自身也有转换成流的方法：is = file.getInputStream();
            BufferedImage bi = ImageIO.read(is);
            Image image = (Image) bi;
            ImageIcon imageIcon = new ImageIcon(image);
            //在原圖片周圍加一層寬高為原來一半的白色邊框 ， 避免截取圖片時留白不足問題
            BufferedImage bufferedImage = new BufferedImage(imageIcon.getIconWidth()*2, imageIcon.getIconHeight()*2,
                    BufferedImage.TYPE_4BYTE_ABGR);  //畫布設置為原圖2倍大小
            Graphics2D g2D = (Graphics2D) bufferedImage.getGraphics();
            g2D.drawImage(imageIcon.getImage(), imageIcon.getIconWidth()/2, imageIcon.getIconHeight()/2, imageIcon.getImageObserver());
            int alpha = 0;
            int maxX = 0;   //最大橫坐標  默認最大橫坐標為 0
            int minX = bufferedImage.getWidth(); //最小橫坐標 默認最大
            int maxY = 0;    //最大縱坐標
            int minY = bufferedImage.getHeight(); //最小縱坐標
            int width = bufferedImage.getWidth(); //圖片寬
            int height = bufferedImage.getHeight();  //圖片高
            for (int j1 = bufferedImage.getMinY(); j1 < bufferedImage.getHeight(); j1++) {
                for (int j2 = bufferedImage.getMinX(); j2 < bufferedImage.getWidth(); j2++) {
                    int rgb = bufferedImage.getRGB(j2, j1);

                    int R = (rgb & 0xff0000) >> 16;
                    int G = (rgb & 0xff00) >> 8;
                    int B = (rgb & 0xff);
                    int factalpha = (rgb>>24) & 0xff;

                    if (((255 - R) < thresholdValue) && ((255 - G) < thresholdValue) && ((255 - B) < thresholdValue)||factalpha == 0) {
                        rgb = ((alpha + 1) << 24) | (rgb & 0x00ffffff);
                    }else{
                        if(j2<minX){
                            minX = j2;
                        }
                        if(j2>maxX){
                            maxX = j2;
                        }
                        if(j1<minY){
                            minY = j1;
                        }
                        if(j1>maxY){
                            maxY = j1;
                        }
                    }
                    bufferedImage.setRGB(j2, j1, rgb);
                }
            }
            java.util.List<Integer> recXY = countInterceptXY( minX,  maxX, minY,  maxY,  width, height); //獲取要截取圖片左上角坐標 及寬高
            BufferedImage pic = bufferedImage.getSubimage(recXY.get(0), recXY.get(1), recXY.get(2), recXY.get(3)); //截取對應圖片
            byteArrayOutputStream = new ByteArrayOutputStream();
            ImageIO.write(pic, "png", byteArrayOutputStream);//转换成byte数组
            result = byteArrayOutputStream.toByteArray();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch ( IOException e) {
                    // TODO Auto-generated catch block
                }
            }
            if (byteArrayOutputStream != null) {
                try {
                    byteArrayOutputStream.close();
                } catch (IOException e) {
                    // TODO Auto-generated catch block
                }
            }
        }
        return result;
    }

    /**
     * int[minx,miny,width,height]
     * ***/
    public static java.util.List<Integer> countInterceptXY(int minX, int maxX, int minY, int maxY, int width, int height){
        int factWidth = maxX - minX;  //實際簽名寬度
        int factHeight = maxY - minY;  //實際簽名高度
        int widthPadding = (int) (factWidth*WIDTHPADDINGRATE);  //實際寬度內填充
        int heightPadding = (int) (factHeight*HEIGHTPADDINGRATE); //實際高度內填充
        List<Integer> result = new ArrayList();
        if((double)factWidth/factHeight>=2){ //寬高比大於默認寬高比  按寬度計算
            int offset = (int) (((factWidth + widthPadding*2)/RATE - factHeight)/2);
            result.add(minX - widthPadding);
            result.add(minY - offset);
            result.add(factWidth + 2*widthPadding);
            result.add((int) ((factWidth + 2*widthPadding)/RATE));
        }else{  //按高度計算
            int offset = (int) (((factHeight + heightPadding*2)*RATE - factWidth)/2);
            result.add(minX - offset);
            result.add(minY - heightPadding);
            result.add((int) ((factHeight + 2*heightPadding)*RATE));
            result.add(factHeight + 2*heightPadding);
        }
        return result;
    }
}
