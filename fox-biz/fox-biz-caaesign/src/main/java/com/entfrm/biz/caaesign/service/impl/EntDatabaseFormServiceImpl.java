package com.entfrm.biz.caaesign.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import com.baomidou.mybatisplus.annotation.TableName;
import com.entfrm.biz.activiti.service.WfConfigService;
import com.entfrm.biz.activiti.entity.TQhAllRelation;
import com.entfrm.biz.activiti.service.TQhAllRelationService;
import org.springframework.stereotype.Service;
import com.entfrm.biz.caaesign.mapper.EntDatabaseFormMapper;
import com.entfrm.biz.caaesign.entity.EntDatabaseForm;
import com.entfrm.biz.caaesign.service.EntDatabaseFormService;
import com.entfrm.core.base.util.PinyinUtil;
import com.entfrm.core.data.util.SerialNumberTool;
import cn.hutool.core.date.DateUtil;
import lombok.AllArgsConstructor;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.runtime.ProcessInstance;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.transaction.annotation.Transactional;
import com.entfrm.core.base.enums.TaskStatusEnum;
import java.util.Map;
import com.entfrm.core.data.constant.CommonConstant;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.io.Serializable;
import com.entfrm.biz.caaesign.entity.EntDatabaseFormItems;
import com.entfrm.biz.caaesign.service.EntDatabaseFormItemsService;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import java.util.Collection;
import com.entfrm.biz.activiti.dto.WfConfigDto;
import java.lang.reflect.Field;
import com.google.common.collect.Maps;
import org.activiti.engine.task.Task;
import com.entfrm.core.security.util.SecurityUtil;
import com.entfrm.core.base.annotation.SignNode;
import java.util.Arrays;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.bean.BeanUtil;

/**
 * <AUTHOR>
 * @date 2021-11-19 10:19:33
 *
 * @description 數據庫服務申請單2Service业务层
 */
@Service
@AllArgsConstructor
public class EntDatabaseFormServiceImpl extends ServiceImpl<EntDatabaseFormMapper, EntDatabaseForm> implements EntDatabaseFormService {
	private final RuntimeService runtimeService;
	private final org.activiti.engine.TaskService actTaskService;
	private final WfConfigService wfConfigService;
	private final TQhAllRelationService allRelationService;
    private final RabbitTemplate rabbitTemplate;

    private final EntDatabaseFormItemsService entDatabaseFormItemsService;

    	/**
	 * 启动流程
	 *
	 * @param entDatabaseForm
	 * @return
	 */
	@Override
	public Boolean startProcess(EntDatabaseForm entDatabaseForm) {
        entDatabaseForm.setWorkStatus(Integer.parseInt(TaskStatusEnum.CHECKING.getStatus()));
        String tableName = EntDatabaseForm.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
        String procDefKey = wfConfigDto.getProcDefKey();
        String businessKey = procDefKey + CommonConstant.BREAK_BUSINESS_KEY + entDatabaseForm.getId();
        Field fields[] = entDatabaseForm.getClass().getDeclaredFields();
        Map variables = Maps.newHashMap();
        //設置填單人
        variables.put("maker", SecurityUtil.getUser().getUsername());
        variables.put("serialNo", entDatabaseForm.getSerialno());
        variables.put("workflowName", wfConfigDto.getFormName());
        Arrays.stream(fields).forEach(field -> {
                SignNode signNode = field.getAnnotation(SignNode.class);
                if (ObjectUtil.isNotEmpty(signNode)) {
                    if (signNode.type().equals(SignNode.Type.COMMON)) {
                        variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entDatabaseForm, field)) ? "AUTO_SIGN" : ReflectUtil.getFieldValue(entDatabaseForm, field));
                    } else if (signNode.type().equals(SignNode.Type.HUIQIAN)) {
                        variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entDatabaseForm, field)) ? Arrays.asList("AUTO_SIGN") : Arrays.asList(ReflectUtil.getFieldValue(entDatabaseForm, field).toString().split(",")));
                    }
                }
            }
        );
        ProcessInstance pi = runtimeService.startProcessInstanceByKey(procDefKey, businessKey,variables);
        entDatabaseForm.setProcessId(pi.getProcessInstanceId());
        this.updateById(entDatabaseForm);

        TQhAllRelation relation = allRelationService.getOne(new QueryWrapper<TQhAllRelation>().eq("serialno",entDatabaseForm.getSerialno()));
        relation.setWorkStatus(entDatabaseForm.getWorkStatus());
        relation.setProcessId(pi.getProcessInstanceId());
        Task task = actTaskService.createTaskQuery().processInstanceId(pi.getProcessInstanceId()).singleResult();
        relation.setNodename(task.getName());
        relation.setDeptNo(entDatabaseForm.getApplyDeptNo());
        allRelationService.updateById(relation);
        //添加mq
        rabbitTemplate.convertAndSend("SignDirectExchange", "Sign.Direct.Routing", BeanUtil.beanToMap(relation));
        return Boolean.TRUE;
	}

    /**
     * 插入一条记录（选择字段，策略插入）
     *
     * @param entDatabaseForm 实体对象
     */
    @Override
    public boolean save(EntDatabaseForm entDatabaseForm) {
        boolean rs = super.save(entDatabaseForm);
        saveChild(entDatabaseForm);
	    String tableName = EntDatabaseForm.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
        TQhAllRelation relation = new TQhAllRelation();
        relation.setDtoName(tableName);
        relation.setMakerNo(entDatabaseForm.getMakerNo());
        relation.setMakerName(entDatabaseForm.getMakerName());
        relation.setSerialno(entDatabaseForm.getSerialno());
        relation.setWorkflowid(wfConfigDto.getProcDefKey());
        relation.setWfName(wfConfigDto.getFormName());
        relation.setClassPackage(entDatabaseForm.getClass().getName());
        relation.setWorkStatus(0);
        allRelationService.save(relation);
        return rs;
    }

    /**
     * 插入一条记录（选择字段，策略插入）
     *
     * @param entDatabaseForm 实体对象
     */
    @Override
    public boolean updateById(EntDatabaseForm entDatabaseForm) {
        // 删除子表信息关联
        entDatabaseFormItemsService.remove(new QueryWrapper<EntDatabaseFormItems>().eq("pid", entDatabaseForm.getId()));
        boolean rs = super.updateById(entDatabaseForm);
        // 新增子表信息管理
        if(rs){
            // 新增子表信息管理
            saveChild(entDatabaseForm);
        }
        return rs;
    }
    /**
     * 新增子表記錄
     *
     * @param entDatabaseForm 表單对象
     */
    public void saveChild(EntDatabaseForm entDatabaseForm) {
        List<EntDatabaseFormItems> entDatabaseFormItemsLists = entDatabaseForm.getEntDatabaseFormItemsLists();
        if (entDatabaseFormItemsLists != null) {
            for (EntDatabaseFormItems entDatabaseFormItems : entDatabaseFormItemsLists) {
                entDatabaseFormItems.setId(null);
                entDatabaseFormItems.setPid(entDatabaseForm.getId());
                entDatabaseFormItemsService.save(entDatabaseFormItems);
            }
        }
    }

    /**
     * 删除（根据ID 批量删除）
     *
     * @param idList 主键ID列表
     */
    @Override
    public boolean removeByIds(Collection<? extends Serializable> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return false;
        }
        for (Serializable id : idList) {
            EntDatabaseForm entDatabaseForm = getById(id);
            // 删除子表信息关联
            entDatabaseFormItemsService.remove(new QueryWrapper<EntDatabaseFormItems>().eq("pid", entDatabaseForm.getId()));
            }

        return SqlHelper.retBool(getBaseMapper().deleteBatchIds(idList));
    }
    /**
     * 根據ID獲取信息
     *
     * @param id
     * @return
     */
    @Override
    public EntDatabaseForm getById(Serializable id) {
        EntDatabaseForm entDatabaseForm = super.getById(id);
        setChilds(entDatabaseForm);
        return entDatabaseForm;
    }

    /**
     * 獲取子表列表（${childTable.tableComment}）
     */
    public void setChilds(EntDatabaseForm entDatabaseForm){

    List<EntDatabaseFormItems> entDatabaseFormItemsLists = entDatabaseFormItemsService.list(new QueryWrapper<EntDatabaseFormItems>().eq("pid", entDatabaseForm.getId()));
    entDatabaseForm.setEntDatabaseFormItemsLists(entDatabaseFormItemsLists);
    }
}
