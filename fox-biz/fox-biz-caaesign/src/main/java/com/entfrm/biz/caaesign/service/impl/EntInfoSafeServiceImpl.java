package com.entfrm.biz.caaesign.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import com.baomidou.mybatisplus.annotation.TableName;
import com.entfrm.biz.activiti.service.WfConfigService;
import com.entfrm.biz.activiti.entity.TQhAllRelation;
import com.entfrm.biz.activiti.service.TQhAllRelationService;
import com.entfrm.biz.activiti.service.TaskService;
import com.entfrm.biz.activiti.dto.LeaveDto;
import org.springframework.stereotype.Service;
import com.entfrm.biz.caaesign.mapper.EntInfoSafeMapper;
import com.entfrm.biz.caaesign.entity.EntInfoSafe;
import com.entfrm.biz.caaesign.service.EntInfoSafeService;
import com.entfrm.core.base.util.PinyinUtil;
import com.entfrm.core.data.util.SerialNumberTool;
import cn.hutool.core.date.DateUtil;
import lombok.AllArgsConstructor;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.runtime.ProcessInstance;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.transaction.annotation.Transactional;
import com.entfrm.core.base.enums.TaskStatusEnum;
import java.util.Map;
import com.entfrm.core.data.constant.CommonConstant;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.io.Serializable;
import com.entfrm.biz.caaesign.entity.EntInfoSafeItems;
import com.entfrm.biz.caaesign.service.EntInfoSafeItemsService;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import java.util.Collection;
import com.entfrm.biz.activiti.dto.WfConfigDto;
import java.lang.reflect.Field;
import com.google.common.collect.Maps;
import org.activiti.engine.task.Task;
import com.entfrm.core.security.util.SecurityUtil;
import com.entfrm.core.base.annotation.SignNode;
import java.util.Arrays;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.bean.BeanUtil;

/**
 * <AUTHOR>
 * @date 2022-06-23 13:04:01
 *
 * @description 應用系統資安評估申請表Service业务层
 */
@Service
@AllArgsConstructor
public class EntInfoSafeServiceImpl extends ServiceImpl<EntInfoSafeMapper, EntInfoSafe> implements EntInfoSafeService {
	private final RuntimeService runtimeService;
	private final org.activiti.engine.TaskService actTaskService;
	private final WfConfigService wfConfigService;
	private final TQhAllRelationService allRelationService;
    private final RabbitTemplate rabbitTemplate;
	private final TaskService taskService;

    private final EntInfoSafeItemsService entInfoSafeItemsService;

    	/**
	 * 启动流程
	 *
	 * @param entInfoSafe
	 * @return
	 */
	@Override
	public Boolean startProcess(EntInfoSafe entInfoSafe) {
        entInfoSafe.setWorkStatus(Integer.parseInt(TaskStatusEnum.CHECKING.getStatus()));
        String tableName = EntInfoSafe.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
        String procDefKey = wfConfigDto.getProcDefKey();
        String businessKey = procDefKey + CommonConstant.BREAK_BUSINESS_KEY + entInfoSafe.getId();
        Field fields[] = entInfoSafe.getClass().getDeclaredFields();
        Map variables = Maps.newHashMap();
        //設置填單人
        variables.put("maker", SecurityUtil.getUser().getUsername());
        variables.put("serialNo", entInfoSafe.getSerialno());
        variables.put("workflowName", wfConfigDto.getFormName());
        Arrays.stream(fields).forEach(field -> {
                SignNode signNode = field.getAnnotation(SignNode.class);
                if (ObjectUtil.isNotEmpty(signNode)) {
                    if (signNode.type().equals(SignNode.Type.COMMON)) {
                        variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entInfoSafe, field)) ? "AUTO_SIGN" : ReflectUtil.getFieldValue(entInfoSafe, field));
                    } else if (signNode.type().equals(SignNode.Type.HUIQIAN)) {
                        variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entInfoSafe, field)) ? Arrays.asList("AUTO_SIGN") : Arrays.asList(ReflectUtil.getFieldValue(entInfoSafe, field).toString().split(",")));
                    }
                }
            }
        );
        ProcessInstance pi = runtimeService.startProcessInstanceByKey(procDefKey, businessKey,variables);
        entInfoSafe.setProcessId(pi.getProcessInstanceId());
        this.updateById(entInfoSafe);

        TQhAllRelation relation = allRelationService.getOne(new QueryWrapper<TQhAllRelation>().eq("serialno",entInfoSafe.getSerialno()));
        relation.setWorkStatus(entInfoSafe.getWorkStatus());
        relation.setProcessId(pi.getProcessInstanceId());
        Task task = actTaskService.createTaskQuery().processInstanceId(pi.getProcessInstanceId()).singleResult();
        relation.setNodename(task.getName());
        relation.setDeptNo(entInfoSafe.getApplyDeptNo());
        allRelationService.updateById(relation);
        //添加mq
        rabbitTemplate.convertAndSend("SignDirectExchange", "Sign.Direct.Routing", BeanUtil.beanToMap(relation));
        return Boolean.TRUE;
	}

    /**
     * 插入一条记录（选择字段，策略插入）
     *
     * @param entInfoSafe 实体对象
     */
    @Override
    public boolean save(EntInfoSafe entInfoSafe) {
        boolean rs = super.save(entInfoSafe);
        saveChild(entInfoSafe);
	    String tableName = EntInfoSafe.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
        TQhAllRelation relation = new TQhAllRelation();
        relation.setDtoName(tableName);
        relation.setMakerNo(entInfoSafe.getMakerNo());
        relation.setMakerName(entInfoSafe.getMakerName());
        relation.setSerialno(entInfoSafe.getSerialno());
        relation.setWorkflowid(wfConfigDto.getProcDefKey());
        relation.setWfName(wfConfigDto.getFormName());
        relation.setClassPackage(entInfoSafe.getClass().getName());
        relation.setWorkStatus(0);
        allRelationService.save(relation);
        return rs;
    }
	@Override
	public Boolean updateAndCheck(EntInfoSafe entInfoSafe, LeaveDto leaveDto) {
		try {
			this.updateById(entInfoSafe);
			taskService.checkTask(leaveDto);
		} catch (Exception e) {
			return false;
		}
		return true;
	}
    /**
     * 插入一条记录（选择字段，策略插入）
     *
     * @param entInfoSafe 实体对象
     */
    @Override
    public boolean updateById(EntInfoSafe entInfoSafe) {
        // 删除子表信息关联
        entInfoSafeItemsService.remove(new QueryWrapper<EntInfoSafeItems>().eq("pid", entInfoSafe.getId()));
        boolean rs = super.updateById(entInfoSafe);
        // 新增子表信息管理
        if(rs){
            // 新增子表信息管理
            saveChild(entInfoSafe);
        }
        return rs;
    }
    /**
     * 新增子表記錄
     *
     * @param entInfoSafe 表單对象
     */
    public void saveChild(EntInfoSafe entInfoSafe) {
        List<EntInfoSafeItems> entInfoSafeItemsLists = entInfoSafe.getEntInfoSafeItemsLists();
        if (entInfoSafeItemsLists != null) {
            for (EntInfoSafeItems entInfoSafeItems : entInfoSafeItemsLists) {
                entInfoSafeItems.setId(null);
                entInfoSafeItems.setPid(entInfoSafe.getId());
                entInfoSafeItemsService.save(entInfoSafeItems);
            }
        }
    }

    /**
     * 删除（根据ID 批量删除）
     *
     * @param idList 主键ID列表
     */
    @Override
    public boolean removeByIds(Collection<? extends Serializable> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return false;
        }
        for (Serializable id : idList) {
            EntInfoSafe entInfoSafe = getById(id);
            // 删除子表信息关联
            entInfoSafeItemsService.remove(new QueryWrapper<EntInfoSafeItems>().eq("pid", entInfoSafe.getId()));
            }

        return SqlHelper.retBool(getBaseMapper().deleteBatchIds(idList));
    }
    /**
     * 根據ID獲取信息
     *
     * @param id
     * @return
     */
    @Override
    public EntInfoSafe getById(Serializable id) {
        EntInfoSafe entInfoSafe = super.getById(id);
        setChilds(entInfoSafe);
        return entInfoSafe;
    }

    /**
     * 獲取子表列表（${childTable.tableComment}）
     */
    public void setChilds(EntInfoSafe entInfoSafe){

    List<EntInfoSafeItems> entInfoSafeItemsLists = entInfoSafeItemsService.list(new QueryWrapper<EntInfoSafeItems>().eq("pid", entInfoSafe.getId()));
    entInfoSafe.setEntInfoSafeItemsLists(entInfoSafeItemsLists);
    }
}
