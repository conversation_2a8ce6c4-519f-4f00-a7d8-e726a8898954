package com.entfrm.biz.caaesign.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import com.baomidou.mybatisplus.annotation.TableName;
import com.entfrm.biz.activiti.service.WfConfigService;
import com.entfrm.biz.activiti.entity.TQhAllRelation;
import com.entfrm.biz.activiti.service.TQhAllRelationService;
import org.springframework.stereotype.Service;
import com.entfrm.biz.caaesign.mapper.TestStudentMapper;
import com.entfrm.biz.caaesign.entity.TestStudent;
import com.entfrm.biz.caaesign.service.TestStudentService;
import com.entfrm.core.base.util.PinyinUtil;
import com.entfrm.core.data.util.SerialNumberTool;
import cn.hutool.core.date.DateUtil;
import lombok.AllArgsConstructor;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.runtime.ProcessInstance;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.transaction.annotation.Transactional;
import com.entfrm.core.base.enums.TaskStatusEnum;
import java.util.Map;
import com.entfrm.core.data.constant.CommonConstant;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.io.Serializable;
import com.entfrm.biz.caaesign.entity.AskLeave;
import com.entfrm.biz.caaesign.service.AskLeaveService;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import java.util.Collection;
import com.entfrm.biz.activiti.dto.WfConfigDto;
import java.lang.reflect.Field;
import com.google.common.collect.Maps;
import org.activiti.engine.task.Task;
import com.entfrm.core.security.util.SecurityUtil;
import com.entfrm.core.base.annotation.SignNode;
import java.util.Arrays;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.bean.BeanUtil;

/**
 * <AUTHOR>
 * @date 2021-10-11 10:35:08
 *
 * @description 測試_學生信息Service业务层
 */
@Service
@AllArgsConstructor
public class TestStudentServiceImpl extends ServiceImpl<TestStudentMapper, TestStudent> implements TestStudentService {
	private final RuntimeService runtimeService;
	private final org.activiti.engine.TaskService actTaskService;
	private final WfConfigService wfConfigService;
	private final TQhAllRelationService allRelationService;
    private final RabbitTemplate rabbitTemplate;

    private final AskLeaveService askLeaveService;

    	/**
	 * 启动流程
	 *
	 * @param testStudent
	 * @return
	 */
	@Override
	public Boolean startProcess(TestStudent testStudent) {
        testStudent.setWorkStatus(Integer.parseInt(TaskStatusEnum.CHECKING.getStatus()));
        String tableName = TestStudent.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
        String procDefKey = wfConfigDto.getProcDefKey();
        String businessKey = procDefKey + CommonConstant.BREAK_BUSINESS_KEY + testStudent.getId();
        Field fields[] = testStudent.getClass().getDeclaredFields();
        Map variables = Maps.newHashMap();
        //設置填單人
        variables.put("maker", SecurityUtil.getUser().getUsername());
        variables.put("serialNo", testStudent.getSerialno());
        variables.put("workflowName", wfConfigDto.getFormName());
        Arrays.stream(fields).forEach(field -> {
                    SignNode signNode = field.getAnnotation(SignNode.class);
                    if (ObjectUtil.isNotEmpty(signNode)) {
                        variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(testStudent, field))?"AUTO_SIGN":ReflectUtil.getFieldValue(testStudent,field));
                    } else if (signNode.type().equals(SignNode.Type.HUIQIAN)) {
                        variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(testStudent, field)) ? Arrays.asList("AUTO_SIGN") : Arrays.asList(ReflectUtil.getFieldValue(testStudent, field).toString().split(",")));
                    }
                }

        );
        ProcessInstance pi = runtimeService.startProcessInstanceByKey(procDefKey, businessKey,variables);
        testStudent.setProcessId(pi.getProcessInstanceId());
        this.updateById(testStudent);

        TQhAllRelation relation = allRelationService.getOne(new QueryWrapper<TQhAllRelation>().eq("serialno",testStudent.getSerialno()));
        relation.setWorkStatus(testStudent.getWorkStatus());
        relation.setProcessId(pi.getProcessInstanceId());
        Task task = actTaskService.createTaskQuery().processInstanceId(pi.getProcessInstanceId()).singleResult();
        relation.setNodename(task.getName());
        relation.setDeptNo(testStudent.getMakerdeptno());
        allRelationService.updateById(relation);
        //添加mq
        rabbitTemplate.convertAndSend("SignDirectExchange", "Sign.Direct.Routing", BeanUtil.beanToMap(relation));
        return Boolean.TRUE;
	}

    /**
     * 插入一条记录（选择字段，策略插入）
     *
     * @param testStudent 实体对象
     */
    @Override
    public boolean save(TestStudent testStudent) {
        boolean rs = super.save(testStudent);
        saveChild(testStudent);
	    String tableName = TestStudent.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
        TQhAllRelation relation = new TQhAllRelation();
        relation.setDtoName(tableName);
        relation.setMakerNo(testStudent.getMakerNo());
        relation.setMakerName(testStudent.getMakerName());
        relation.setSerialno(testStudent.getSerialno());
        relation.setWorkflowid(wfConfigDto.getProcDefKey());
        relation.setWfName(wfConfigDto.getFormName());
        relation.setClassPackage(testStudent.getClass().getName());
        relation.setWorkStatus(0);
        allRelationService.save(relation);
        return rs;
    }

    /**
     * 插入一条记录（选择字段，策略插入）
     *
     * @param testStudent 实体对象
     */
    @Override
    public boolean updateById(TestStudent testStudent) {
        // 删除子表信息关联
        askLeaveService.remove(new QueryWrapper<AskLeave>().eq("pid", testStudent.getId()));
        boolean rs = super.updateById(testStudent);
        // 新增子表信息管理
        if(rs){
            // 新增子表信息管理
            saveChild(testStudent);
        }
        return rs;
    }
    /**
     * 新增子表記錄
     *
     * @param testStudent 表單对象
     */
    public void saveChild(TestStudent testStudent) {
        List<AskLeave> askLeaveLists = testStudent.getAskLeaveLists();
        if (askLeaveLists != null) {
            for (AskLeave askLeave : askLeaveLists) {
                askLeave.setId(null);
                askLeave.setPid(testStudent.getId());
                askLeaveService.save(askLeave);
            }
        }
    }

    /**
     * 删除（根据ID 批量删除）
     *
     * @param idList 主键ID列表
     */
    @Override
    public boolean removeByIds(Collection<? extends Serializable> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return false;
        }
        for (Serializable id : idList) {
            TestStudent testStudent = getById(id);
            // 删除子表信息关联
            askLeaveService.remove(new QueryWrapper<AskLeave>().eq("pid", testStudent.getId()));
            }

        return SqlHelper.retBool(getBaseMapper().deleteBatchIds(idList));
    }
    /**
     * 根據ID獲取信息
     *
     * @param id
     * @return
     */
    @Override
    public TestStudent getById(Serializable id) {
        TestStudent testStudent = super.getById(id);
        setChilds(testStudent);
        return testStudent;
    }

    /**
     * 獲取子表列表（${childTable.tableComment}）
     */
    public void setChilds(TestStudent testStudent){

    List<AskLeave> askLeaveLists = askLeaveService.list(new QueryWrapper<AskLeave>().eq("pid", testStudent.getId()));
    testStudent.setAskLeaveLists(askLeaveLists);
    }
}
