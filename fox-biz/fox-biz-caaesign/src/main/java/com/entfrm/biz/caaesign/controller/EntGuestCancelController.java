package com.entfrm.biz.caaesign.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.entfrm.biz.activiti.dto.LeaveDto;
import com.entfrm.biz.caaesign.entity.EntGuestCancel;
import com.entfrm.biz.caaesign.service.EntGuestCancelService;
import com.entfrm.core.base.annotation.SignNode;
import com.entfrm.core.base.util.PinyinUtil;
import com.entfrm.core.data.util.SerialNumberTool;
import com.entfrm.biz.feign.FeignService;
import com.entfrm.biz.feign.dto.User;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.annotation.TableName;
import com.entfrm.biz.activiti.dto.LeaveDto;
import com.entfrm.biz.activiti.service.WfConfigService;
import com.entfrm.biz.design.service.DesignBuildFormService;
import com.entfrm.biz.system.service.ITQhUserformhsService;
import com.entfrm.biz.design.entity.DesignBuildForm;
import com.entfrm.core.data.annotation.DataFilter;
import com.entfrm.core.log.annotation.OperLog;
import com.entfrm.core.security.util.SecurityUtil;
import com.google.common.collect.Maps;
import org.activiti.engine.RuntimeService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.entfrm.core.base.api.R;
import com.entfrm.core.base.util.ExcelUtil;
import lombok.AllArgsConstructor;
import org.activiti.engine.task.Task;
import org.activiti.engine.TaskService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Field;

import org.springframework.web.multipart.MultipartFile;
import lombok.SneakyThrows;
import com.entfrm.biz.system.service.TableSignConfigService;
import com.entfrm.biz.system.entity.TableSignConfig;

import java.util.*;

import com.entfrm.biz.system.entity.DictData;
import com.entfrm.biz.system.service.DictDataService;
import com.entfrm.biz.caaesign.entity.EntGuestItems1;
import com.entfrm.biz.caaesign.service.EntGuestItems1Service;
import com.entfrm.biz.caaesign.entity.EntGuestItems2;
import com.entfrm.biz.caaesign.service.EntGuestItems2Service;

/**
 * <AUTHOR>
 * @date 2024-10-17 08:03:26
 * @description iPEBG移轉設備銷賬聯絡單Controller
 */
@Api("iPEBG移轉設備銷賬聯絡單管理")
@RestController
@AllArgsConstructor
@RequestMapping("/caaesign/entGuestCancel")
public class EntGuestCancelController {

    private final EntGuestCancelService entGuestCancelService;
    private final WfConfigService wfConfigService;
    private final TaskService taskService;
    private final com.entfrm.biz.activiti.service.TaskService taskBunessService;
    private final RuntimeService runtimeService;
    private final ITQhUserformhsService tQhUserformhsService;
    private final TableSignConfigService signConfigService;
    private final DictDataService dictDataService;
    private final EntGuestItems1Service entGuestItems1Service;
    private final EntGuestItems2Service entGuestItems2Service;

    @Resource
    private FeignService feignService;

    private QueryWrapper<EntGuestCancel> getQueryWrapper(EntGuestCancel entGuestCancel) {
        return new QueryWrapper<EntGuestCancel>()
                .eq
                        (!StrUtil.isEmptyIfStr(entGuestCancel.getWorkStatus()), "work_status", String.valueOf(entGuestCancel.getWorkStatus()))
                .eq
                        (StrUtil.isNotBlank(entGuestCancel.getSerialno()), "serialno", entGuestCancel.
                                getSerialno())
                .eq
                        (StrUtil.isNotBlank(entGuestCancel.getMakerNo()), "maker_no", entGuestCancel.
                                getMakerNo())
                .eq
                        (StrUtil.isNotBlank(entGuestCancel.getMakerfactoryid()), "makerfactoryid", entGuestCancel.
                                getMakerfactoryid())
                .orderByDesc("create_time");
    }

    @ApiOperation("iPEBG移轉設備銷賬聯絡單列表")
//        @PreAuthorize("@ps.hasPerm('entGuestCancel_view')")
    @GetMapping("/list")
    @DataFilter
    public R list(Page page, EntGuestCancel entGuestCancel) {
        IPage<EntGuestCancel> entGuestCancelPage = entGuestCancelService.page(page, getQueryWrapper(entGuestCancel));
        entGuestCancelPage.getRecords().forEach(bEntity -> {
            bEntity.setIsOwn(SecurityUtil.getUser().getUsername().equals(bEntity.getMakerNo()));
            try {
                if (bEntity.getProcessId() != null) {
                    Task task = taskService.createTaskQuery().processInstanceId(bEntity.getProcessId()).singleResult();
                    if (task != null) {
                        Set<String> assigens = taskBunessService.getTaskCandidate(task.getId());
                        List<String> assigenss = new ArrayList<>();
                        for (String assigen : assigens) {
                            User user = feignService.getOne(assigen);
                            if (user != null) assigenss.add(assigen + "/" + user.getNickName());
                        }
                        if (assigens.size() > 1) {
                            bEntity.setSignPerson(ArrayUtil.join(assigenss.toArray(), ","));
                        } else if (assigens.size() == 1) {
                            bEntity.setSignPerson(assigenss.get(0));
                        }
                        bEntity.setSignNode(task.getName());
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        return R.ok(entGuestCancelPage.getRecords(), entGuestCancelPage.getTotal());
    }


    @ApiOperation("iPEBG移轉設備銷賬聯絡單移動端列表")
//        @PreAuthorize("@ps.hasPerm('entGuestCancel_view')")
    @GetMapping("/listApp")
    @DataFilter
    public R listApp(Page page, EntGuestCancel entGuestCancel) {
        IPage<EntGuestCancel> entGuestCancelPage = entGuestCancelService.page(page, getQueryWrapper(entGuestCancel).eq
                (StrUtil.isNotBlank(SecurityUtil.getUser().getUsername()), "create_by", SecurityUtil.getUser().getUsername()));
        entGuestCancelPage.getRecords().forEach(bEntity -> {
            try {
                if (bEntity.getProcessId() != null) {
                    Task task = taskService.createTaskQuery().processInstanceId(bEntity.getProcessId()).singleResult();
                    if (task != null) {
                        Set<String> assigens = taskBunessService.getTaskCandidate(task.getId());
                        List<String> assigenss = new ArrayList<>();
                        for (String assigen : assigens) {
                            User user = feignService.getOne(assigen);
                            if (user != null) assigenss.add(assigen + "/" + user.getNickName());
                        }
                        if (assigens.size() > 1) {
                            bEntity.setSignPerson(ArrayUtil.join(assigenss.toArray(), ","));
                        } else if (assigens.size() == 1) {
                            bEntity.setSignPerson(assigenss.get(0));
                        }
                        bEntity.setSignNode(task.getName());
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        return R.ok(entGuestCancelPage.getRecords(), entGuestCancelPage.getTotal());
    }

    @ApiOperation("iPEBG移轉設備銷賬聯絡單查询")
    @GetMapping("/{id}")
    public R getById(@PathVariable("id") String id) {
        EntGuestCancel entGuestCancel = entGuestCancelService.getById(id);
        if (StrUtil.isNotBlank(entGuestCancel.getProcessId())) {
            Task task = taskService.createTaskQuery().processInstanceId(entGuestCancel.getProcessId()).singleResult();
            if (null != task) {
                entGuestCancel.setCurrentname(task.getName());
                Field fields[] = entGuestCancel.getClass().getDeclaredFields();
                Arrays.stream(fields).forEach(field -> {
                    SignNode signNode = field.getAnnotation(SignNode.class);
                    if (task.getTaskDefinitionKey().equals(field.getName())) {
                        entGuestCancel.setCurrentorder(signNode.order() + "");
                    }
                });
            }
//		    aHhlz.setCurrentorder(AnnotationUtil.getAnnotationValueMap(AHhlz.class, SignNode.class)));
        }
        return R.ok(entGuestCancel);
    }

    @OperLog("iPEBG移轉設備銷賬聯絡單新增")
    @ApiOperation("iPEBG移轉設備銷賬聯絡單新增")
//    @PreAuthorize("@ps.hasPerm('entGuestCancel_add')")
    @PostMapping("/save")
    public R save(@Validated @RequestBody EntGuestCancel entGuestCancel) {
        entGuestCancel.setCreateDate(new Date());  //電子簽核必須字段
        entGuestCancel.setWorkStatus(0);
        entGuestCancel.setSerialno(PinyinUtil.getFirstSpell("iPEBG移轉設備銷賬聯絡單") + DateUtil.format(new Date(), "yyyyMMddHHmmss") + SerialNumberTool.getInstance().generaterNextNumber(4));
        entGuestCancel.setMakerNo(SecurityUtil.getUser().getUsername());
        entGuestCancel.setMakerName(SecurityUtil.getUser().getNickName());
        entGuestCancel.setMakerdeptno(tQhUserformhsService.findByEmpnoForEntfrm(SecurityUtil.getUser().getUsername()).getDeptno());
        entGuestCancelService.save(entGuestCancel);
        return R.ok();
    }

    @OperLog("iPEBG移轉設備銷賬聯絡單修改")
    @ApiOperation("iPEBG移轉設備銷賬聯絡單修改")
//    @PreAuthorize("@ps.hasPerm('entGuestCancel_edit')")
    @PutMapping("/update")
    public R update(@Validated @RequestBody EntGuestCancel entGuestCancel) {
        entGuestCancelService.updateById(entGuestCancel);
        return R.ok();
    }

    @PutMapping("/updateAttachids")
    public R updateAttachids(@Validated @RequestBody EntGuestCancel entGuestCancel) {
        entGuestCancelService.updateAttachids(entGuestCancel);
        return R.ok();
    }

    @OperLog("iPEBG移轉設備銷賬聯絡單删除")
    @ApiOperation("iPEBG移轉設備銷賬聯絡單删除")
//    @PreAuthorize("@ps.hasPerm('entGuestCancel_del')")
    @DeleteMapping("/remove/{id}")
    public R remove(@PathVariable("id") String[] id) {
        return R.ok(entGuestCancelService.removeByIds(Arrays.asList(id)));
    }


    //    @PreAuthorize("@ps.hasPerm('entGuestCancel_export')")
    @GetMapping("/export")
    public R export(EntGuestCancel entGuestCancel) {
        List<EntGuestCancel> list = entGuestCancelService.list(getQueryWrapper(entGuestCancel));
        try {
            list.forEach(bEntity -> {
                        if (bEntity.getProcessId() != null) {
                            Task task = taskService.createTaskQuery().processInstanceId(bEntity.getProcessId()).singleResult();
                            if (task != null) {
                                Set<String> assigens = taskBunessService.getTaskCandidate(task.getId());
                                List<String> assigenss = new ArrayList<>();
                                for (String assigen : assigens) {
                                    User user = feignService.getOne(assigen);
                                    if (user != null) assigenss.add(assigen + "/" + user.getNickName());
                                }
                                if (assigens.size() > 1) {
                                    bEntity.setSignPerson(ArrayUtil.join(assigenss.toArray(), ","));
                                } else if (assigens.size() == 1) {
                                    bEntity.setSignPerson(assigenss.get(0));
                                }
                                bEntity.setSignNode(task.getName());
                            }
                        }
                    }
            );
        } catch (Exception e) {
            e.printStackTrace();
        }
        ExcelUtil<EntGuestCancel> util = new ExcelUtil<EntGuestCancel>(EntGuestCancel.class);

        return util.exportExcel(list, "iPEBG移轉設備銷賬聯絡單数据");
    }

    @OperLog("啟動iPEBG移轉設備銷賬聯絡單流程")
//	@PreAuthorize("@ps.hasPerm('entGuestCancel_submit')")
    @GetMapping("/startProcess/{id}")
    public R startProcess(@PathVariable("id") String id) {
        /*List<DesignBuildForm> list = designBuildFormService.list(new QueryWrapper<DesignBuildForm>().eq("db_id", dataSourceId)
                .eq("table_id", tableId));
        if(list.size() == 0){
            return R.error("biz.design.buildForm.unDesign", new String[]{});
        }*/
        return R.ok(entGuestCancelService.startProcess(entGuestCancelService.getById(id)));
    }

    @ApiOperation("添加iPEBG移轉設備銷賬聯絡單,并發起簽核流程")
    @PostMapping("/saveAndStartProcess")
    public R saveAndStartProcess(@Validated @RequestBody EntGuestCancel entGuestCancel) {
        entGuestCancel.setWorkStatus(0);
        entGuestCancel.
                setSerialno(PinyinUtil.getFirstSpell("iPEBG移轉設備銷賬聯絡單") + DateUtil.format(new Date(), "yyyyMMddHHmmss") + SerialNumberTool.getInstance().generaterNextNumber(4));
        entGuestCancel.setMakerNo(SecurityUtil.getUser().getUsername());
        entGuestCancel.setMakerName(SecurityUtil.getUser().getNickName());
        entGuestCancel.
                setMakerdeptno(tQhUserformhsService.findByEmpnoForEntfrm(SecurityUtil.getUser().getUsername()).getDeptno());
        entGuestCancel.setCreateDate(new Date());  //電子簽核必須字段
        entGuestCancelService.save(entGuestCancel);
        return R.ok(entGuestCancelService.startProcess(entGuestCancel));
    }

    @OperLog("iPEBG移轉設備銷賬聯絡單修改,并發起簽核流程")
    @ApiOperation("iPEBG移轉設備銷賬聯絡單修改,并發起簽核流程")
    @PutMapping("/updateAndStartProcess")
    public R updateAndStartProcess(@Validated @RequestBody EntGuestCancel entGuestCancel) {
        entGuestCancelService.updateById(entGuestCancel);
        return R.ok(entGuestCancelService.startProcess(entGuestCancel));
    }

    @OperLog("iPEBG移轉設備銷賬聯絡單修改,并發起簽核流程")
    @ApiOperation("iPEBG移轉設備銷賬聯絡單修改,并發起簽核流程")
    @PutMapping("/updateAndResubmitProcess")
    public R updateAndResubmitProcess(@Validated @RequestBody EntGuestCancel entGuestCancel) {
        HttpServletRequest request = ((ServletRequestAttributes) Objects
                .requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
        String ip = ServletUtil.getClientIP(request);
        entGuestCancelService.updateById(entGuestCancel);
        Field fields[] = entGuestCancel.getClass().getDeclaredFields();
        Map variables = Maps.newHashMap();
        Arrays.stream(fields).forEach(field -> {
                    SignNode signNode = field.getAnnotation(SignNode.class);
                    if (ObjectUtil.isNotEmpty(signNode)) {
                        if (signNode.type().equals(SignNode.Type.COMMON)) {
                            variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entGuestCancel, field)) ? "AUTO_SIGN" : ReflectUtil.getFieldValue(entGuestCancel, field));
                        } else if (signNode.type().equals(SignNode.Type.HUIQIAN)) {
                            variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entGuestCancel, field)) ? Arrays.asList("AUTO_SIGN") : Arrays.asList(ReflectUtil.getFieldValue(entGuestCancel, field).toString().split(",")));
                        }
                    }
                }
        );
        runtimeService.setVariables(entGuestCancel.getProcessId(), variables);
        LeaveDto leaveDto = new LeaveDto();
        leaveDto.setProcessId(entGuestCancel.getProcessId());
        leaveDto.setPass("5");
        leaveDto.setOperIp(ip);
        leaveDto.setComment("重新提交");
        taskBunessService.checkTask(leaveDto);
        return R.ok();
    }

    List<Field> fieldsList;
    List userList;

    @GetMapping("/getSignPath/{processId}")
    public R getSignPath(@PathVariable("processId") String processId) {
        EntGuestCancel entGuestCancel = entGuestCancelService.getOne(new QueryWrapper<EntGuestCancel>().eq("process_id", processId));
        Field fields[] = EntGuestCancel.class.getDeclaredFields();
        userList = new ArrayList();
        fieldsList = new ArrayList();
        Arrays.stream(fields).forEach(field -> {
                    SignNode signNode = field.getAnnotation(SignNode.class);
                    if (ObjectUtil.isNotEmpty(signNode)) {
                        fieldsList.add(field);
                    }
                }
        );
        fieldsList.sort(Comparator.comparingInt(
                m -> ((SignNode) m.getAnnotation(SignNode.class)).order()
        ));
        Task task = taskService.createTaskQuery().processInstanceId(processId).singleResult();
        fieldsList.stream().forEach(field -> {
            StringBuffer signPath = new StringBuffer();
            TableSignConfig config = signConfigService.getSignConfig("EntGuestCancel", field.getName());
            if (null != task && field.getName().equals(task.getTaskDefinitionKey())) {
                if (config != null) {
                    userList.add(signPath.append("<font color=\"red\" size=\"4\"><strong>").append(config.getColValue()).append("(").append(ReflectUtil.getFieldValue(entGuestCancel, field.getName())).append("/").append(ReflectUtil.getFieldValue(entGuestCancel, com.entfrm.core.base.util.StrUtil.replaceLast(field.getName(), "no", "name"))).append(")").append("</strong></font>"));
                } else {
                    userList.add(signPath.append("<font color=\"red\" size=\"4\"><strong>").append(((SignNode) field.getAnnotation(SignNode.class)).nodeName()).append("(").append(ReflectUtil.getFieldValue(entGuestCancel, field.getName())).append("/").append(ReflectUtil.getFieldValue(entGuestCancel, com.entfrm.core.base.util.StrUtil.replaceLast(field.getName(), "no", "name"))).append(")").append("</strong></font>"));
                }
            } else {
                if (config != null) {
                    userList.add(signPath.append(config.getColValue()).append("(").append(ReflectUtil.getFieldValue(entGuestCancel, field.getName())).append("/").append(ReflectUtil.getFieldValue(entGuestCancel, com.entfrm.core.base.util.StrUtil.replaceLast(field.getName(), "no", "name"))).append(")"));
                } else {
                    userList.add(signPath.append(((SignNode) field.getAnnotation(SignNode.class)).nodeName()).append("(").append(ReflectUtil.getFieldValue(entGuestCancel, field.getName())).append("/").append(ReflectUtil.getFieldValue(entGuestCancel, com.entfrm.core.base.util.StrUtil.replaceLast(field.getName(), "no", "name"))).append(")"));
                }
            }
        });
//		System.out.println(JSONUtil.toJsonStr(ArrayUtil.join(userList.toArray(), "-->")).replace("null",""));
        return R.ok(JSONUtil.toJsonStr(ArrayUtil.join(userList.toArray(), "-->")).replace("null", ""));
    }


    @GetMapping("/getSignPathApp/{processId}")
    public R getSignPathApp(@PathVariable("processId") String processId) {
        EntGuestCancel entGuestCancel = entGuestCancelService.getOne(new QueryWrapper<EntGuestCancel>().eq("process_id", processId));
        Field fields[] = EntGuestCancel.class.getDeclaredFields();
        userList = new ArrayList();
        fieldsList = new ArrayList();
        Arrays.stream(fields).forEach(field -> {
                    SignNode signNode = field.getAnnotation(SignNode.class);
                    if (ObjectUtil.isNotEmpty(signNode)) {
                        fieldsList.add(field);
                    }
                }
        );
        fieldsList.sort(Comparator.comparingInt(
                m -> ((SignNode) m.getAnnotation(SignNode.class)).order()
        ));
        Task task = taskService.createTaskQuery().processInstanceId(processId).singleResult();
        Map<String, Object> result = new HashMap<String, Object>();
        int activeCount = -1;

        for (int i = 0; i < fieldsList.size(); i++) {
            StringBuffer signPath = new StringBuffer();
            TableSignConfig config = signConfigService.getSignConfig("EntGuestCancel", fieldsList.get(i).getName());
            if (ReflectUtil.getFieldValue(entGuestCancel, fieldsList.get(i).getName()) != null && !"".equals(ReflectUtil.getFieldValue(entGuestCancel, fieldsList.get(i).getName()))) {
                if (config != null) {
                    userList.add(signPath.append(config.getColValue()).append("(").append(ReflectUtil.getFieldValue(entGuestCancel, fieldsList.get(i).getName())).append("/").append(ReflectUtil.getFieldValue(entGuestCancel, com.entfrm.core.base.util.StrUtil.replaceLast(fieldsList.get(i).getName(), "no", "name"))).append(")"));
                } else {
                    userList.add(signPath.append(((SignNode) fieldsList.get(i).getAnnotation(SignNode.class)).nodeName()).append("(").append(ReflectUtil.getFieldValue(entGuestCancel, fieldsList.get(i).getName())).append("/").append(ReflectUtil.getFieldValue(entGuestCancel, com.entfrm.core.base.util.StrUtil.replaceLast(fieldsList.get(i).getName(), "no", "name"))).append(")"));
                }
                if (null != task && fieldsList.get(i).getName().equals(task.getTaskDefinitionKey())) {
                    activeCount = i;
                }
            }
        }
        result.put("info", userList);
        result.put("activeCount", activeCount);
        return R.ok(result);
    }

    /**
     * 方法描述: 審核需要更新主表調用方法
     *
     * @Author: S6114648
     * @CreateDate: 2021/10/7  9:34
     * @Return
     **/

    @PostMapping("/updateAndCheck")
    public R updateAndCheck(@RequestBody EntGuestCancel entGuestCancel) {
        HttpServletRequest request = ((ServletRequestAttributes) Objects
                .requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
        String ip = ServletUtil.getClientIP(request);
        LeaveDto leaveDto = entGuestCancel.getLeaveDto();
        leaveDto.setOperIp(ip);
        if (entGuestCancelService.updateAndCheck(entGuestCancel, leaveDto)) {
            return R.ok();
        } else {
            return R.error();
        }
    }

    @OperLog("通過類名動態獲取審核節點名稱")
    @GetMapping("/getSignConfigList")
    public R getSignConfigList() {
        return R.ok(signConfigService.getSignConfigList("EntGuestCancel"));
    }


    @OperLog("iPEBG移轉設備銷賬聯絡單詳情数据导入")
    @PostMapping("/importEntGuestItems1")
    public R importEntGuestItems1(
            MultipartFile file, boolean updateSupport) {
        ExcelUtil<EntGuestItems1> util = new ExcelUtil<EntGuestItems1>(EntGuestItems1.
                class);
        Map<String, List<Map<String, Object>>> dicts = new HashMap<String, List<Map<String, Object>>>();
        List<EntGuestItems1> EntGuestItems1List = null;
        try {
            EntGuestItems1List = util.importExcelTemplet(file.getInputStream(), dicts);
            int failNum = 0;
            int i = 1;
            StringBuilder failureMsg = new StringBuilder();
            Map<String, DictData> outLegalMap = dictDataService.getDictDataMap("guest_out_legal");
            Map<String, DictData> outTransferLegalMap = dictDataService.getDictDataMap("guest_out_transfer");
            Map<String, DictData> inLegalMap = dictDataService.getDictDataMap("guest_in_legal");
            Map<String, DictData> inTransferLegalMap = dictDataService.getDictDataMap("guest_in_transfer");
            //驗證從表下拉和單選
            for (EntGuestItems1 EntGuestItems1 : EntGuestItems1List) {
                if (ObjectUtil.isEmpty(EntGuestItems1.getOutLegal()) || ObjectUtil.isEmpty(outLegalMap.get(EntGuestItems1.getOutLegal()))) {
                    failureMsg.append("<br/>第" + i + "條" + EntGuestItems1.getOutLegal() + " <出售方法人> 數據異常，請檢查");
                    failNum++;
                }
                if (ObjectUtil.isEmpty(EntGuestItems1.getOutTransferLegal()) || ObjectUtil.isEmpty(outTransferLegalMap.get(EntGuestItems1.getOutTransferLegal()))) {
                    failureMsg.append("<br/>第" + i + "條" + EntGuestItems1.getOutTransferLegal() + " <出售方轉單法人> 數據異常，請檢查");
                    failNum++;
                }
                if (ObjectUtil.isEmpty(EntGuestItems1.getInLegal()) || ObjectUtil.isEmpty(inLegalMap.get(EntGuestItems1.getInLegal()))) {
                    failureMsg.append("<br/>第" + i + "條" + EntGuestItems1.getInLegal() + " <購入方法人> 數據異常，請檢查");
                    failNum++;
                }
                if (ObjectUtil.isEmpty(EntGuestItems1.getInTransferLegal()) || ObjectUtil.isEmpty(inTransferLegalMap.get(EntGuestItems1.getInTransferLegal()))) {
                    failureMsg.append("<br/>第" + i + "條" + EntGuestItems1.getInTransferLegal() + " <購入方轉單法人> 數據異常，請檢查");
                    failNum++;
                }
                i++;
            }
            if (failNum > 0) {
                return R.error("導入信息異常，請檢查：" + failureMsg.toString());
            }
        } catch (Exception e) {
            return R.error("導入信息異常，請檢查！");
        }
        return R.ok(EntGuestItems1List);
    }

    /**
     * 方法描述: 導出字表模版
     *
     * @Author: S6114648
     * @CreateDate: 2021/10/7  9:34
     * @Return
     **/

    @GetMapping("/exportEntGuestItems1Templet")
    public R exportEntGuestItems1Templet() {
        ExcelUtil<EntGuestItems1> util = new ExcelUtil<EntGuestItems1>(EntGuestItems1.class);
        return util.exportExcelTemplet(null, "iPEBG移轉設備銷賬聯絡單詳情模版", new HashMap<String, List>());
    }

    @OperLog("iPEBG移轉設備銷賬聯絡單詳情数据导入")
    @PostMapping("/importEntGuestItems2")
    public R importEntGuestItems2(
            MultipartFile file, boolean updateSupport) {
        ExcelUtil<EntGuestItems2> util = new ExcelUtil<EntGuestItems2>(EntGuestItems2.
                class);
        Map<String, List<Map<String, Object>>> dicts = new HashMap<String, List<Map<String, Object>>>();
        List<EntGuestItems2> EntGuestItems2List = null;
        try {
            EntGuestItems2List = util.importExcelTemplet(file.getInputStream(), dicts);
            int failNum = 0;
            int i = 1;
            StringBuilder failureMsg = new StringBuilder();
            Map<String, DictData> equipmentCurrencyMap = dictDataService.getDictDataMap("guest_eqp_currency");
            //驗證從表下拉和單選
            for (EntGuestItems2 EntGuestItems2 : EntGuestItems2List) {
                if (ObjectUtil.isEmpty(EntGuestItems2.getEquipmentCurrency()) || ObjectUtil.isEmpty(equipmentCurrencyMap.get(EntGuestItems2.getEquipmentCurrency()))) {
                    failureMsg.append("<br/>第" + i + "條" + EntGuestItems2.getEquipmentCurrency() + " <幣別> 數據異常，請檢查");
                    failNum++;
                }
                i++;
            }
            if (failNum > 0) {
                return R.error("導入信息異常，請檢查：" + failureMsg.toString());
            }
        } catch (Exception e) {
            return R.error("導入信息異常，請檢查！");
        }
        return R.ok(EntGuestItems2List);
    }

    /**
     * 方法描述: 導出字表模版
     *
     * @Author: S6114648
     * @CreateDate: 2021/10/7  9:34
     * @Return
     **/

    @GetMapping("/exportEntGuestItems2Templet")
    public R exportEntGuestItems2Templet() {
        ExcelUtil<EntGuestItems2> util = new ExcelUtil<EntGuestItems2>(EntGuestItems2.class);
        return util.exportExcelTemplet(null, "iPEBG移轉設備銷賬聯絡單詳情模版", new HashMap<String, List>());
    }

}
