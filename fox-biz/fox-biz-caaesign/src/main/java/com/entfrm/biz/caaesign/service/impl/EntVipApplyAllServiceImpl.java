package com.entfrm.biz.caaesign.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.entfrm.biz.activiti.dto.LeaveDto;
import com.entfrm.biz.activiti.dto.WfConfigDto;
import com.entfrm.biz.activiti.entity.TQhAllRelation;
import com.entfrm.biz.activiti.service.TQhAllRelationService;
import com.entfrm.biz.activiti.service.TaskService;
import com.entfrm.biz.activiti.service.WfConfigService;
import com.entfrm.biz.caaesign.entity.EntVipApply;
import com.entfrm.biz.caaesign.entity.EntVipApplyAll;
import com.entfrm.biz.caaesign.entity.EntVipApplyItems;
import com.entfrm.biz.caaesign.mapper.EntVipApplyAllMapper;
import com.entfrm.biz.caaesign.mapper.EntVipApplyMapper;
import com.entfrm.biz.caaesign.service.EntVipApplyAllService;
import com.entfrm.biz.caaesign.service.EntVipApplyItemsService;
import com.entfrm.biz.caaesign.service.EntVipApplyService;
import com.entfrm.core.base.annotation.SignNode;
import com.entfrm.core.base.enums.TaskStatusEnum;
import com.entfrm.core.data.constant.CommonConstant;
import com.entfrm.core.security.util.SecurityUtil;
import com.google.common.collect.Maps;
import org.activiti.engine.task.Task;
import lombok.AllArgsConstructor;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.runtime.ProcessInstance;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-06-04 10:12:01
 *
 * @description VIP就餐申請單Service业务层
 */
@Service
@AllArgsConstructor
public class EntVipApplyAllServiceImpl extends ServiceImpl<EntVipApplyAllMapper, EntVipApplyAll> implements EntVipApplyAllService {
}
