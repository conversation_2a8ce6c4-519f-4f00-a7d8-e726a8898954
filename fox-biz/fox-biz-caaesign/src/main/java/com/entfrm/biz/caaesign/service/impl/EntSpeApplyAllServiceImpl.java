package com.entfrm.biz.caaesign.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.entfrm.biz.activiti.dto.LeaveDto;
import com.entfrm.biz.activiti.dto.WfConfigDto;
import com.entfrm.biz.activiti.entity.TQhAllRelation;
import com.entfrm.biz.activiti.service.TQhAllRelationService;
import com.entfrm.biz.activiti.service.TaskService;
import com.entfrm.biz.activiti.service.WfConfigService;
import com.entfrm.biz.caaesign.entity.EntSpeApply;
import com.entfrm.biz.caaesign.entity.EntSpeApplyAll;
import com.entfrm.biz.caaesign.entity.EntSpeApplyItems;
import com.entfrm.biz.caaesign.mapper.EntSpeApplyAllMapper;
import com.entfrm.biz.caaesign.mapper.EntSpeApplyMapper;
import com.entfrm.biz.caaesign.service.EntSpeApplyAllService;
import com.entfrm.biz.caaesign.service.EntSpeApplyItemsService;
import com.entfrm.biz.caaesign.service.EntSpeApplyService;
import com.entfrm.core.base.annotation.SignNode;
import com.entfrm.core.base.enums.TaskStatusEnum;
import com.entfrm.core.data.constant.CommonConstant;
import com.entfrm.core.security.util.SecurityUtil;
import com.google.common.collect.Maps;
import org.activiti.engine.task.Task;
import lombok.AllArgsConstructor;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.runtime.ProcessInstance;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-06-04 10:12:31
 *
 * @description 特殊就餐申請單Service业务层
 */
@Service
@AllArgsConstructor
public class EntSpeApplyAllServiceImpl extends ServiceImpl<EntSpeApplyAllMapper, EntSpeApplyAll> implements EntSpeApplyAllService {
}
