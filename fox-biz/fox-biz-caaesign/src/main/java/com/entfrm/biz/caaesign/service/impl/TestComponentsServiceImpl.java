package com.entfrm.biz.caaesign.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import com.baomidou.mybatisplus.annotation.TableName;
import com.entfrm.biz.activiti.service.WfConfigService;
import com.entfrm.biz.activiti.entity.TQhAllRelation;
import com.entfrm.biz.activiti.service.TQhAllRelationService;
import com.entfrm.biz.activiti.service.TaskService;
import com.entfrm.biz.activiti.dto.LeaveDto;
import org.springframework.stereotype.Service;
import com.entfrm.biz.caaesign.mapper.TestComponentsMapper;
import com.entfrm.biz.caaesign.entity.TestComponents;
import com.entfrm.biz.caaesign.service.TestComponentsService;
import com.entfrm.core.base.util.PinyinUtil;
import com.entfrm.core.data.util.SerialNumberTool;
import cn.hutool.core.date.DateUtil;
import lombok.AllArgsConstructor;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.runtime.ProcessInstance;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.transaction.annotation.Transactional;
import com.entfrm.core.base.enums.TaskStatusEnum;
import java.util.Map;
import com.entfrm.core.data.constant.CommonConstant;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.io.Serializable;
import com.entfrm.biz.caaesign.entity.TestComponentsChild;
import com.entfrm.biz.caaesign.service.TestComponentsChildService;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import java.util.Collection;
import com.entfrm.biz.activiti.dto.WfConfigDto;
import java.lang.reflect.Field;
import com.google.common.collect.Maps;
import org.activiti.engine.task.Task;
import com.entfrm.core.security.util.SecurityUtil;
import com.entfrm.core.base.annotation.SignNode;
import java.util.Arrays;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.bean.BeanUtil;

/**
 * <AUTHOR>
 * @date 2022-02-18 17:04:25
 *
 * @description 組件Service业务层
 */
@Service
@AllArgsConstructor
public class TestComponentsServiceImpl extends ServiceImpl<TestComponentsMapper, TestComponents> implements TestComponentsService {
	private final RuntimeService runtimeService;
	private final org.activiti.engine.TaskService actTaskService;
	private final WfConfigService wfConfigService;
	private final TQhAllRelationService allRelationService;
    private final RabbitTemplate rabbitTemplate;
	private final TaskService taskService;

    private final TestComponentsChildService testComponentsChildService;

    	/**
	 * 启动流程
	 *
	 * @param testComponents
	 * @return
	 */
	@Override
	public Boolean startProcess(TestComponents testComponents) {
        testComponents.setWorkStatus(Integer.parseInt(TaskStatusEnum.CHECKING.getStatus()));
        String tableName = TestComponents.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
        String procDefKey = wfConfigDto.getProcDefKey();
        String businessKey = procDefKey + CommonConstant.BREAK_BUSINESS_KEY + testComponents.getId();
        Field fields[] = testComponents.getClass().getDeclaredFields();
        Map variables = Maps.newHashMap();
        //設置填單人
        variables.put("maker", SecurityUtil.getUser().getUsername());
        variables.put("serialNo", testComponents.getSerialno());
        variables.put("workflowName", wfConfigDto.getFormName());
        Arrays.stream(fields).forEach(field -> {
                SignNode signNode = field.getAnnotation(SignNode.class);
                if (ObjectUtil.isNotEmpty(signNode)) {
                    if (signNode.type().equals(SignNode.Type.COMMON)) {
                        variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(testComponents, field)) ? "AUTO_SIGN" : ReflectUtil.getFieldValue(testComponents, field));
                    } else if (signNode.type().equals(SignNode.Type.HUIQIAN)) {
                        variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(testComponents, field)) ? Arrays.asList("AUTO_SIGN") : Arrays.asList(ReflectUtil.getFieldValue(testComponents, field).toString().split(",")));
                    }
                }
            }
        );
        ProcessInstance pi = runtimeService.startProcessInstanceByKey(procDefKey, businessKey,variables);
        testComponents.setProcessId(pi.getProcessInstanceId());
        this.updateById(testComponents);

        TQhAllRelation relation = allRelationService.getOne(new QueryWrapper<TQhAllRelation>().eq("serialno",testComponents.getSerialno()));
        relation.setWorkStatus(testComponents.getWorkStatus());
        relation.setProcessId(pi.getProcessInstanceId());
        Task task = actTaskService.createTaskQuery().processInstanceId(pi.getProcessInstanceId()).singleResult();
        relation.setNodename(task.getName());
        relation.setDeptNo(testComponents.getMakerdeptno());
        allRelationService.updateById(relation);
        //添加mq
        rabbitTemplate.convertAndSend("SignDirectExchange", "Sign.Direct.Routing", BeanUtil.beanToMap(relation));
        return Boolean.TRUE;
	}

    /**
     * 插入一条记录（选择字段，策略插入）
     *
     * @param testComponents 实体对象
     */
    @Override
    public boolean save(TestComponents testComponents) {
        boolean rs = super.save(testComponents);
        saveChild(testComponents);
	    String tableName = TestComponents.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
        TQhAllRelation relation = new TQhAllRelation();
        relation.setDtoName(tableName);
        relation.setMakerNo(testComponents.getMakerNo());
        relation.setMakerName(testComponents.getMakerName());
        relation.setSerialno(testComponents.getSerialno());
        relation.setWorkflowid(wfConfigDto.getProcDefKey());
        relation.setWfName(wfConfigDto.getFormName());
        relation.setClassPackage(testComponents.getClass().getName());
        relation.setWorkStatus(0);
        allRelationService.save(relation);
        return rs;
    }
	@Override
	public Boolean updateAndCheck(TestComponents testComponents, LeaveDto leaveDto) {
		try {
			this.updateById(testComponents);
			taskService.checkTask(leaveDto);
		} catch (Exception e) {
			return false;
		}
		return true;
	}
    /**
     * 插入一条记录（选择字段，策略插入）
     *
     * @param testComponents 实体对象
     */
    @Override
    public boolean updateById(TestComponents testComponents) {
        // 删除子表信息关联
        testComponentsChildService.remove(new QueryWrapper<TestComponentsChild>().eq("pid", testComponents.getId()));
        boolean rs = super.updateById(testComponents);
        // 新增子表信息管理
        if(rs){
            // 新增子表信息管理
            saveChild(testComponents);
        }
        return rs;
    }
    /**
     * 新增子表記錄
     *
     * @param testComponents 表單对象
     */
    public void saveChild(TestComponents testComponents) {
        List<TestComponentsChild> testComponentsChildLists = testComponents.getTestComponentsChildLists();
        if (testComponentsChildLists != null) {
            for (TestComponentsChild testComponentsChild : testComponentsChildLists) {
                testComponentsChild.setId(null);
                testComponentsChild.setPid(testComponents.getId());
                testComponentsChildService.save(testComponentsChild);
            }
        }
    }

    /**
     * 删除（根据ID 批量删除）
     *
     * @param idList 主键ID列表
     */
    @Override
    public boolean removeByIds(Collection<? extends Serializable> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return false;
        }
        for (Serializable id : idList) {
            TestComponents testComponents = getById(id);
            // 删除子表信息关联
            testComponentsChildService.remove(new QueryWrapper<TestComponentsChild>().eq("pid", testComponents.getId()));
            }

        return SqlHelper.retBool(getBaseMapper().deleteBatchIds(idList));
    }
    /**
     * 根據ID獲取信息
     *
     * @param id
     * @return
     */
    @Override
    public TestComponents getById(Serializable id) {
        TestComponents testComponents = super.getById(id);
        setChilds(testComponents);
        return testComponents;
    }

    /**
     * 獲取子表列表（${childTable.tableComment}）
     */
    public void setChilds(TestComponents testComponents){

    List<TestComponentsChild> testComponentsChildLists = testComponentsChildService.list(new QueryWrapper<TestComponentsChild>().eq("pid", testComponents.getId()));
    testComponents.setTestComponentsChildLists(testComponentsChildLists);
    }
}
