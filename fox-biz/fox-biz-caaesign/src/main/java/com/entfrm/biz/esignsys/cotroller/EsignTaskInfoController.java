package com.entfrm.biz.esignsys.cotroller;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.http.HttpUtil;
import com.entfrm.biz.esignsys.dto.TaskPageSelectDto;
import com.entfrm.biz.esignsys.entity.EsignAppMyTaskTypeListEntity;
import com.entfrm.biz.esignsys.service.EsignAppMyTaskTypeListService;
import com.entfrm.biz.system.service.ConfigService;
import com.entfrm.core.base.api.R;
import com.entfrm.core.base.util.IPUtil;
import com.entfrm.core.security.util.SecurityUtil;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

@RestController
@AllArgsConstructor
@RequestMapping("/caaesign/esignTaskInfo")
public class EsignTaskInfoController {
    private final ConfigService configService;
    private final EsignAppMyTaskTypeListService esignAppMyTaskTypeListService;


    /*待辦任務列表顯示
     * 目前做審核
     * 只查看狀態為2的  wu分頁*/
    @RequestMapping(value = "listMyTask")
    public R listMyTask(TaskPageSelectDto taskPageSelectDto) {
        String urlString = configService.getValueByKey("esign.root.url") + "/esignForMobile/listMyTask";
        //   String urlString =  "http://*************:8080/esignForMobile/listMyTask";
        Map<String, Object> paramMap = MapUtil.newHashMap();
        paramMap.put("empno", SecurityUtil.getUser().getUsername());
        paramMap.put("page", taskPageSelectDto.getPage());
        paramMap.put("size", taskPageSelectDto.getSize());
        paramMap.put("workstatus", 2);
        paramMap.put("startDate", taskPageSelectDto.getStartTime());
        paramMap.put("endDate", taskPageSelectDto.getEndTime());
        String result1 = HttpUtil.post(urlString, paramMap);
        return R.ok(result1);
    }

    /*待辦任務列表顯示
     * 目前做審核
     * 只查看狀態為2的  wu分頁*/
    @RequestMapping(value = "listMyTaskSpeed")
    public R listMyTaskSpeed(TaskPageSelectDto taskPageSelectDto) {
          String urlString = configService.getValueByKey("esign.root.url") + "/esignForMobile/listMyTaskSpeed";
        //  String urlString =  "http://*************:8099/esignForMobile/listMyTaskSpeed";
        Map<String, Object> paramMap = MapUtil.newHashMap();
        paramMap.put("empno", SecurityUtil.getUser().getUsername());
        paramMap.put("page", taskPageSelectDto.getPage());
        paramMap.put("size", taskPageSelectDto.getSize());
        paramMap.put("workstatus", 2);
        paramMap.put("startDate", taskPageSelectDto.getStartTime());
        paramMap.put("endDate", taskPageSelectDto.getEndTime());
        String result1 = HttpUtil.post(urlString, paramMap);
        return R.ok(result1);
    }
    /* 一下待辦申請單*/
    @RequestMapping(value = "getNextMyTask")
    public R getNextMyTask(TaskPageSelectDto taskPageSelectDto) {
         String urlString = configService.getValueByKey("esign.root.url") + "/esignForMobile/getNextMyTask";
        // String urlString = "http://*************:8099/esignForMobile/getNextMyTask";

        Map<String, Object> paramMap = MapUtil.newHashMap();
        paramMap.put("empno", SecurityUtil.getUser().getUsername());
        paramMap.put("serialno", taskPageSelectDto.getSerialno());
        String result1 = HttpUtil.post(urlString, paramMap);
        return R.ok(result1);
    }


    /*已辦任務列表顯示
     * 目前做審核
     * 只查看狀態為2的  wu分頁*/
    @RequestMapping(value = "listMyDownTask")
    public R listMyDownTask(TaskPageSelectDto taskPageSelectDto) {
        String urlString = configService.getValueByKey("esign.root.url") + "/esignForMobile/listMyDownTask";
        //  String urlString = "http://*************:8080/esignForMobile/listMyDownTask";
        Map<String, Object> paramMap = MapUtil.newHashMap();
        paramMap.put("empno", SecurityUtil.getUser().getUsername());
        paramMap.put("page", taskPageSelectDto.getPage());
        paramMap.put("rows", taskPageSelectDto.getSize());
        paramMap.put("workstatus", taskPageSelectDto.getStatus());
        paramMap.put("startDate", taskPageSelectDto.getStartTime());
        paramMap.put("endDate", taskPageSelectDto.getEndTime());
        paramMap.put("workFlowId", taskPageSelectDto.getWorkFlowId());
        String result1 = HttpUtil.post(urlString, paramMap);
        return R.ok(result1);
    }


    /*已辦任務列表顯示
     * 目前做審核
     * 只查看狀態為2的  wu分頁*/
    @RequestMapping(value = "appMyTaskTypeList")
    public R appMyTaskTypeList() {
        List<EsignAppMyTaskTypeListEntity> list = esignAppMyTaskTypeListService.list();
        return R.ok(list);
    }
    /*待辦任務數量
     * 目前做審核
     * 只查看狀態為2的  wu分頁*/
    @RequestMapping(value = "listMyTaskCount")
    public R listMyTaskCount() {
        String urlString = configService.getValueByKey("esign.root.url") + "/esignForMobile/listMyTaskCount";
        Map<String, Object> paramMap = MapUtil.newHashMap();
        paramMap.put("empno", SecurityUtil.getUser().getUsername());
        String result1 = HttpUtil.post(urlString, paramMap);
        return R.ok(result1);
    }

    /* 已辦任務數量
     * 目前做審核
     * 只查看狀態為2的  wu分頁*/
    @RequestMapping(value = "listMyDownTaskCount")
    public R listMyDownTaskCount() {
        String urlString = configService.getValueByKey("esign.root.url") + "/esignForMobile/listMyDownTaskCount";
        Map<String, Object> paramMap = MapUtil.newHashMap();
        paramMap.put("empno", SecurityUtil.getUser().getUsername());
        String result1 = HttpUtil.post(urlString, paramMap);
        return R.ok(result1);
    }

    /**
     * 獲取當前審核節點信息
     * @param serialno
     * @return
     */
    @RequestMapping(value = "getNodeInfo/{serialno}")
    public R getNodeInfo(@PathVariable("serialno") String serialno) {
        String urlString = configService.getValueByKey("esign.root.url") + "/wfcontrollerForMobile/getNodeInfo";
        String para = "/" + serialno;
        String result1 = HttpUtil.get(urlString + para, CharsetUtil.CHARSET_UTF_8);
        return R.ok(result1);
    }



    /*待辦任務列表顯示   wu分頁*/
    @RequestMapping(value = "listRetractMyTask")
    public R listRetractMyTask(@RequestParam(required = false) String applyName) {
        String urlString = configService.getValueByKey("esign.root.url") + "/esignForMobile/listRetractMyTask";
        //  String urlString = "http://*************:8080/esignForMobile/listRetractMyTask";
        Map<String, Object> paramMap = MapUtil.newHashMap();
        paramMap.put("empno", SecurityUtil.getUser().getUsername());
        paramMap.put("workstatus", 2);
        paramMap.put("applyName", applyName);
        String result1 = HttpUtil.post(urlString, paramMap);
        return R.ok(result1);
    }

    @RequestMapping(value = "listMyTaskByType")
    public R listMyTaskByType(TaskPageSelectDto taskPageSelectDto,@RequestParam String applyType,
                              @RequestParam(required = false) String applyName,
                              @RequestParam(required = false) String level,
                              @RequestParam(required = false) String ynStop) {
         String urlString = configService.getValueByKey("esign.root.url") + "/esignForMobile/listMyTaskByType";
        // String urlString = "http://*************:8099/esignForMobile/listMyTaskByType";
        Map<String, Object> paramMap = MapUtil.newHashMap();
        paramMap.put("empno", SecurityUtil.getUser().getUsername());
        paramMap.put("workstatus", 2);
        paramMap.put("applyType", applyType);
        paramMap.put("applyName", applyName);
        paramMap.put("level", level);
        paramMap.put("ynStop", ynStop);
        paramMap.put("page", taskPageSelectDto.getPage());
        paramMap.put("rows", taskPageSelectDto.getSize());
        String result1 = HttpUtil.post(urlString, paramMap);
        return R.ok(result1);
    }
    @RequestMapping(value = "listMyTaskByTypeCount")
    public R listMyTaskByTypeCount(@RequestParam String applyType,
                              @RequestParam(required = false) String applyName,
                              @RequestParam(required = false) String level) {
        String urlString = configService.getValueByKey("esign.root.url") + "/esignForMobile/listMyTaskByTypeCount";
        //String urlString = "http://*************:8099/esignForMobile/listMyTaskByTypeCount";
        Map<String, Object> paramMap = MapUtil.newHashMap();
        paramMap.put("empno", SecurityUtil.getUser().getUsername());
        paramMap.put("workstatus", 2);
        paramMap.put("applyType", applyType);
        paramMap.put("applyName", applyName);
        paramMap.put("level", level);
        String result1 = HttpUtil.post(urlString, paramMap);
        return R.ok(result1);
    }
    /* 承辦人任務列表顯示
     *  S6073061
     *
     * */
    @RequestMapping(value = "listMyMakeTask")
    public R listMyMakeTask(TaskPageSelectDto taskPageSelectDto) {
        String urlString = configService.getValueByKey("esign.root.url") + "/esignForMobile/listMyMakeTask";
        //String urlString = "http://*************:8084/esignForMobile/listMyMakeTask";
        Map<String, Object> paramMap = MapUtil.newHashMap();
        paramMap.put("empno", SecurityUtil.getUser().getUsername());
        paramMap.put("page", taskPageSelectDto.getPage());
        paramMap.put("rows", taskPageSelectDto.getSize());
        paramMap.put("workstatus", taskPageSelectDto.getStatus());
        paramMap.put("startDate", taskPageSelectDto.getStartTime());
        paramMap.put("endDate", taskPageSelectDto.getEndTime());
        String result1 = HttpUtil.post(urlString, paramMap);
        return R.ok(result1);
    }
    /* 承辦人任務列表顯示
     *  S6073061
     *
     * */
    @RequestMapping(value = "listMyMakeTaskCount")
    public R listMyMakeTaskCount() {
        String urlString = configService.getValueByKey("esign.root.url") + "/esignForMobile/listMyMakeTaskCount";
        //String urlString = "http://*************:8084/esignForMobile/listMyMakeTaskCount";
        Map<String, Object> paramMap = MapUtil.newHashMap();
        paramMap.put("empno", SecurityUtil.getUser().getUsername());
        String result1 = HttpUtil.post(urlString, paramMap);
        return R.ok(result1);
    }
    /* 審核任務 status 0 通過  1 駁回  備註中文轉碼問題   手機ip獲取問題
     * @param request
     * @param serialnos
     * @param attachidsremark
     * @param status
     * @return
             */
    @RequestMapping(value = "completeTaskBatch")
    public R completeTaskBatch(HttpServletRequest request,
                               @RequestParam String serialnos,
                               @RequestParam(required = false) String attachidsremark,
                               @RequestParam String status) {
        String URLString = configService.getValueByKey("esign.root.url");
        String urlString = URLString + "/wffileesignprocessForMobie/completeTaskBachFormobile";
        //String urlString = "http://*************:8086/wffileesignprocessForMobie/completeTaskBachFormobile";
        Map<String, Object> paramMap = MapUtil.newHashMap();

        String userNo = SecurityUtil.getUser().getUsername();
        paramMap.put("serialnos", serialnos);
        paramMap.put("attachidsremark", attachidsremark);
        paramMap.put("status", status);
        paramMap.put("empno", userNo);
        paramMap.put("ip", IPUtil.getIpAddress(request));
        String  result1 = HttpUtil.post(urlString, paramMap);
        if(R.ESIGN_SUCCESS.equals(result1)) {
            return R.ok("");
        }else{
            return R.error("");
        }
    }


    /* 暫不處理
     * @param request
     * @param formNo  表單編號
     * @return
     */
    @RequestMapping(value = "notProcessingNow")
    public R notProcessingNow(HttpServletRequest request,
                               @RequestParam String formNo) {
        String URLString = configService.getValueByKey("esign.root.url");
      String urlString = URLString + "/wffileesignprocessForMobie/notProcessingNow";
        //  String urlString = "http://*************:8099/wffileesignprocessForMobie/notProcessingNow";
        Map<String, Object> paramMap = MapUtil.newHashMap();

        String userNo = SecurityUtil.getUser().getUsername();
        paramMap.put("formNo", formNo);
        paramMap.put("empno", userNo);
        String  result1 = HttpUtil.post(urlString, paramMap);
        if(R.ESIGN_SUCCESS.equals(result1)) {
            return R.ok("");
        }else{
            return R.error("");
        }
    }
}
