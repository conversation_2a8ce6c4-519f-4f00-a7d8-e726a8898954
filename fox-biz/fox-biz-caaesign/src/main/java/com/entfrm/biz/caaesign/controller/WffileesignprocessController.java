package com.entfrm.biz.caaesign.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.entfrm.biz.esignsys.dto.EsignFTPDto;
import com.entfrm.biz.esignsys.entity.EsignDict;
import com.entfrm.biz.esignsys.entity.EsignFtpInfoEntity;
import com.entfrm.biz.esignsys.entity.EsignTPubFileobjectEntity;
import com.entfrm.biz.esignsys.entity.EsignTQhAllRelationEntity;
import com.entfrm.biz.esignsys.service.*;
import com.entfrm.biz.esignsys.util.*;
import com.entfrm.biz.feign.FeignDmzService;
import com.entfrm.biz.system.service.ConfigService;
import com.entfrm.biz.system.util.JsonUtils;
import com.entfrm.core.base.api.R;
import com.entfrm.core.base.util.IPUtil;
import com.entfrm.core.security.util.SecurityUtil;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

@Api("文檔簽核管理")
@RestController
@AllArgsConstructor
@RequestMapping("/caaesign/wffileesignprocess")
public class WffileesignprocessController {


    private final ConfigService configService;

    private final EsignDictService dictService;
    private final EsignFileFtpService fileFtpService;
    private final EsignFileOosService fileOosService;

    private final EsignFileobjectService fileobjectService;

    private final EsignTallAhRelationService relationService;

    private final FeignDmzService feignDmzService;

    public static final String NEWESIGN_DOWNLOAD_URL = "/apiForDmz/newEsign/download";
    public static final String NEWESIGN_DOWNLOAD_OOS_URL = "/apiForDmz/newEsign/downloadWithOos";

    public static final String SIGNAUTUREIMG_ROOT_PATH = "/userSignatureImg/pc";


    @RequestMapping("/completeTask")
    public R completeTask(HttpServletRequest request,
                          @RequestParam String serialno,
                          @RequestParam(required = false) String attachidsremark,
                          @RequestParam String status,
                          @RequestParam(value = "files",required=false) MultipartFile[] files,
                          @RequestParam(required = false) String signedIds,
                          @RequestParam(required = false) String remarkShow) throws Exception {
        String userNo = SecurityUtil.getUser().getUsername();
       String URLString = configService.getValueByKey("esign.root.url");
        // String URLString = "*************:8099/";
//        System.out.println("completeTask---------------------"+ userDto.toString());
        String attachids2 = "";
        if(files!=null&&files.length>0){

            String directory = "";
            String urlString = URLString + "/wffileesignprocessForMobie/getFtpsInfoByEmpno";
            String para = "empNo=" + relationService.getOne(new QueryWrapper<EsignTQhAllRelationEntity>().eq("serialno",serialno)).getCreateBy();
            List<EsignFtpInfoEntity> ftpInfoEntities = JsonUtils.parseArray(HttpUtil.get(urlString + "?" + para, CharsetUtil.CHARSET_UTF_8), EsignFtpInfoEntity.class);
            for (EsignFtpInfoEntity info : ftpInfoEntities) {
                if ("2".equals(info.getType())) {
                    directory = info.getOosFilePath();
                    break;
                }
            }
            String rootPath = dictService.getById(22873).getValue();
            StringBuilder attachids2Builder = new StringBuilder();
            for (MultipartFile file : files) {
                if (!file.isEmpty()) {
//                    final String fileType = PdfUtil.getFileType(file);
//                    if(FileTypeContant.FILE_TYPE_PDF.equals(fileType)) {
//                      EsignTPubFileobjectEntity tPubFileobjectEntity = fileFtpService.updateFile(file, request, ftpDto, userNo);
//                     TPubFileobjectEntity tPubFileobjectEntity = fileFtpService.updateFilePdfMegreImageEncrypt(file,pdfMergeImage(file), request, ftpDto, userDto.getUserNo());
                    EsignTPubFileobjectEntity tPubFileobjectEntity = fileOosService.updateFileWithOos(file,request,rootPath,directory,userNo);
                    attachids2Builder.append(tPubFileobjectEntity.getId() + ",");
//                    }else{
//                        return R.error("請上傳正確的文件格式");
//                    }
                }
            }
            attachids2 = attachids2Builder.length() > 0 ? attachids2Builder.toString().substring(0, attachids2Builder.length() - 1) : "";
        }
        String urlString = URLString + "/wffileesignprocessForMobie/completeTaskForMobile";
        Map<String, Object> paramMap = MapUtil.newHashMap();
        paramMap.put("serialno", serialno);
        paramMap.put("attachidsremark", attachidsremark);
        paramMap.put("status", status);
        paramMap.put("attachids2", attachids2);
        paramMap.put("empno", userNo);
        paramMap.put("ip", IPUtil.getIpAddress(request));
        paramMap.put("signedIds", signedIds);
        paramMap.put("remarkShow", remarkShow);
        String result1 = HttpUtil.post(urlString, paramMap);

        if(R.ESIGN_SUCCESS.equals(result1)) {
            return R.ok("操作成功");
        }else{
            return R.error(result1);
        }
    }


    /* 獲取詳情
     * 目前做審核
     * 只查看狀態為2的  wu分頁*/
    @RequestMapping(value = "queryDeatailBySerialno")
    public R queryDeatailBySerialno(@RequestParam(required = true) String serialno) {
        String URLString = configService.getValueByKey("esign.root.url");
        String urlString = URLString + "/wffileesignprocessForMobie/queryDeatailBySerialno";
        String userNo = SecurityUtil.getUser().getUsername();
        String para = "serialno=" + serialno+"&empNo="+userNo;
        String result1 = HttpUtil.get(urlString + "?" + para, CharsetUtil.CHARSET_UTF_8);
        return R.ok(result1);
    }




    @RequestMapping("/uploadSignature")
    public R uploadSignature(HttpServletRequest request,
                                  @RequestParam(value = "files") MultipartFile[] files) throws Exception {
        String empNo = SecurityUtil.getUser().getUsername();
        if(empNo==null||"".equals(empNo)){
            return R.error("无签名人信息");
        }
        EsignTPubFileobjectEntity entity = new EsignTPubFileobjectEntity();
        List<EsignTPubFileobjectEntity> entitys = fileobjectService.list(new QueryWrapper<EsignTPubFileobjectEntity>()
                .eq(StrUtil.isNotBlank(empNo), "NAME", empNo));
        if(entitys!=null&&entitys.size()>0){
            entity = entitys.get(0);
        }
        MultipartFile file = null;
        if(files!=null&&files.length>0){
            file = files[0];
        }
        String fileName = file.getOriginalFilename();
        //扩展名
        String fileExtensionName = fileName.substring(fileName.lastIndexOf('.') + 1);
        if(!"png".equals(fileExtensionName)&&!"jpg".equals(fileExtensionName)){
            return R.error("签名格式错误");
        }
        //使用UUID防止文件名重复，覆盖别人的文件
        String uploadFileName = UUID.randomUUID().toString() + "." + fileExtensionName;

        try {
//            byte[] img = TransferAlpha.transferAlpha2Byte(file.getInputStream());
//            inputStreamMap.put(uploadFileName,new ByteArrayInputStream(img));
            //到此为止，文件已经上传服务器成功
            String rootPath = dictService.getById(22873).getValue();
            /**
             *生成路徑
             */
            String path = SIGNAUTUREIMG_ROOT_PATH + "/" + DateUtil.format(new Date(), "yyyy/MM/dd");
            //把文件上传到FTP服务器,与FTP文件服务器对接
//            feignDmzService.newEsignUploadSignature(ftpIp,ftpPort,ftpUser,ftpPass,rootPath,URL,uploadFileName,file);
            //把文件上传到OOS服务器,与FTP文件服务器对接
            feignDmzService.newEsignUploadSignatureWithOos(file,uploadFileName,rootPath + path);
            //已将文件上传FTP
            Date date = new Date();
            //上传完之后，删除upload下面的文件
            int fileSize = new Long(file.getSize()).intValue();
            entity.setName(empNo);
            entity.setUploader(empNo);
            entity.setUrl(path + "/" + uploadFileName);
            entity.setIp(IPUtil.getIpAddress(request));
            entity.setType(fileExtensionName);
            entity.setSizez(fileSize);
            entity.setCreatedate(DateUtils.getNowTime("yyyy-MM-dd HH:mm:ss"));
            entity.setCreateBy(empNo);
            entity.setCreateDate(date);
            entity.setUpdateBy(empNo);
            entity.setUpdateDate(date);
            if(ObjectUtils.isNotNull(entity.getId())){
                fileobjectService.updateById(entity);
            }else{
                fileobjectService.save(entity);
            }
        } catch (Exception e) {
            return R.error("上传签名失败");
        }
        return R.ok("上传成功");

    }

    @RequestMapping("showSignatureImg")
    public void showSignatureImg(HttpServletResponse response) throws Exception {
        String empNo = SecurityUtil.getUser().getUsername();
        EsignTPubFileobjectEntity entity = new EsignTPubFileobjectEntity();
        List<EsignTPubFileobjectEntity> entitys = fileobjectService.list(new QueryWrapper<EsignTPubFileobjectEntity>()
                .eq(StrUtil.isNotBlank(empNo), "NAME", empNo));
        if (entitys != null && entitys.size() > 0) {
            entity = entitys.get(0);
        }
        String rootPath = dictService.getById(22873).getValue();
        String ssoEndpoint = configService.getValueByKey("apisix.base.url");
        Map<String, Object> param = new HashMap<>();
        param.put("filePath", entity.getUrl());
        param.put("directory", rootPath);
        final byte[] bytes = HttpRequest
                .get(ssoEndpoint + NEWESIGN_DOWNLOAD_OOS_URL)
                .form(param)
                .execute()
                .bodyBytes();
        FTPUtil.downSignatureByByte(response,bytes);
    }


    @RequestMapping("switchListen")
    public R switchListen(HttpServletResponse response, @RequestParam String pageStatus, @RequestParam String formNo) {
        String URLString = configService.getValueByKey("esign.root.url");
        //   String URLString = "http://10.76.214.167:8080";
        String urlString = URLString + "/wfcontrollerForMobile/switchListen";
        String empNo = SecurityUtil.getUser().getUsername();
        Map<String, Object> paramMap = MapUtil.newHashMap();
        paramMap.put("pageStatus", pageStatus);
        paramMap.put("formNo", formNo);
        paramMap.put("empNo", empNo);
        String result1 = HttpUtil.post(urlString, paramMap);
        if(R.ESIGN_SUCCESS.equals(result1)) {
            return R.ok("");
        }else{
            return R.error("");
        }
    }


    @RequestMapping("showSignatureImgBak")
    public void showSignatureImgBak(HttpServletResponse response) throws Exception {
        String empNo = SecurityUtil.getUser().getUsername();
        EsignTPubFileobjectEntity entity = new EsignTPubFileobjectEntity();
        List<EsignTPubFileobjectEntity> entitys = fileobjectService.list(new QueryWrapper<EsignTPubFileobjectEntity>()
                .eq(StrUtil.isNotBlank(empNo), "NAME", empNo));
        if (entitys != null && entitys.size() > 0) {
            entity = entitys.get(0);
        }
        String ftpIp = null;
        String ftpUser = null;
        String ftpPass = null;
        String rootPath = null;
        Integer ftpPort = null;
        List<EsignDict> dictList = dictService.getDictByType("ftps_upload_para");
        for (EsignDict dict : dictList) {
            if ("ftps_upload_para_01".equals(dict.getCodeUniq())) {
                ftpUser = dict.getValue();
            }
            if ("ftps_upload_para_02".equals(dict.getCodeUniq())) {
                ftpPass = dict.getValue();
            }
            if ("ftps_upload_para_03".equals(dict.getCodeUniq())) {
                ftpIp = dict.getValue();
            }
            if ("ftps_upload_para_04".equals(dict.getCodeUniq())) {
                ftpPort = Integer.parseInt(dict.getValue());
            }
        }
        rootPath = dictService.getById(101002).getValue();
        new FTPUtil(ftpIp, ftpPort, ftpUser, ftpPass, rootPath).downSignature(response,entity.getUrl(),entity.getType());
    }
}
