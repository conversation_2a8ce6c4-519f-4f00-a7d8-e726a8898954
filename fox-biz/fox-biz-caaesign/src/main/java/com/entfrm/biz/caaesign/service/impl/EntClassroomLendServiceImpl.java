package com.entfrm.biz.caaesign.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
    import com.baomidou.mybatisplus.annotation.TableName;
import com.entfrm.biz.activiti.dto.WfConfigDto;
import com.entfrm.biz.activiti.entity.TQhAllRelation;
import com.entfrm.biz.activiti.service.TQhAllRelationService;
import com.entfrm.biz.activiti.service.WfConfigService;
import com.entfrm.core.base.annotation.SignNode;
import com.entfrm.core.security.util.SecurityUtil;
import com.google.common.collect.Maps;
import org.activiti.engine.task.Task;
import org.springframework.stereotype.Service;
import com.entfrm.biz.caaesign.mapper.EntClassroomLendMapper;
import com.entfrm.biz.caaesign.entity.EntClassroomLend;
import com.entfrm.biz.caaesign.service.EntClassroomLendService;
import com.entfrm.core.base.util.PinyinUtil;
import com.entfrm.core.data.util.SerialNumberTool;
import cn.hutool.core.date.DateUtil;
import lombok.AllArgsConstructor;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.runtime.ProcessInstance;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.transaction.annotation.Transactional;
import com.entfrm.core.base.enums.TaskStatusEnum;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Map;
import com.entfrm.core.data.constant.CommonConstant;

/**
 * <AUTHOR>
 * @date 2021-10-28 11:04:26
 *
 * @description 教育訓練室&視訊會議室借用申請Service业务层
 */
@Service
@AllArgsConstructor
public class EntClassroomLendServiceImpl extends ServiceImpl<EntClassroomLendMapper, EntClassroomLend> implements EntClassroomLendService {
	private final RuntimeService runtimeService;
	private final org.activiti.engine.TaskService actTaskService;
	private final WfConfigService wfConfigService;
	private final TQhAllRelationService allRelationService;
	private final RabbitTemplate rabbitTemplate;
    	/**
	 * 启动流程
	 *
	 * @param entClassroomLend
	 * @return
	 */
	@Override
	public Boolean startProcess(EntClassroomLend entClassroomLend) {
        entClassroomLend.setWorkStatus(Integer.parseInt(TaskStatusEnum.CHECKING.getStatus()));
		String tableName = EntClassroomLend.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
		String procDefKey = wfConfigDto.getProcDefKey();
		String businessKey = procDefKey + CommonConstant.BREAK_BUSINESS_KEY + entClassroomLend.getId();
		Field fields[] = entClassroomLend.getClass().getDeclaredFields();
        Map variables = Maps.newHashMap();
        //設置填單人
        variables.put("maker", SecurityUtil.getUser().getUsername());
        variables.put("serialNo", entClassroomLend.getSerialno());
        variables.put("workflowName", wfConfigDto.getFormName());
        Arrays.stream(fields).forEach(field -> {
                    SignNode signNode = field.getAnnotation(SignNode.class);
                    if (ObjectUtil.isNotEmpty(signNode)) {
                        if (signNode.type().equals(SignNode.Type.COMMON)) {
                            variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entClassroomLend, field)) ? "AUTO_SIGN" : ReflectUtil.getFieldValue(entClassroomLend, field));
                        } else if (signNode.type().equals(SignNode.Type.HUIQIAN)) {
                            variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entClassroomLend, field)) ? Arrays.asList("AUTO_SIGN") : Arrays.asList(ReflectUtil.getFieldValue(entClassroomLend, field).toString().split(",")));
                        }
                    }
                }
        );
		ProcessInstance pi = runtimeService.startProcessInstanceByKey(procDefKey, businessKey,variables);
        entClassroomLend.setProcessId(pi.getProcessInstanceId());
		super.updateById(entClassroomLend);

		TQhAllRelation relation = allRelationService.getOne(new QueryWrapper<TQhAllRelation>().eq("serialno",entClassroomLend.getSerialno()));
		relation.setWorkStatus(entClassroomLend.getWorkStatus());
		relation.setProcessId(pi.getProcessInstanceId());
        Task task = actTaskService.createTaskQuery().processInstanceId(pi.getProcessInstanceId()).singleResult();
        relation.setNodename(task.getName());
        relation.setDeptNo(entClassroomLend.getApplyDeptNo());
		allRelationService.updateById(relation);
		//添加mq
		rabbitTemplate.convertAndSend("SignDirectExchange", "Sign.Direct.Routing", BeanUtil.beanToMap(relation));
		return Boolean.TRUE;
	}

	/**
     * 插入一条记录（选择字段，策略插入）
     *
     */
	@Override
	public boolean save(EntClassroomLend entClassroomLend) {
		boolean rs = super.save(entClassroomLend);
		String tableName = EntClassroomLend.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
		TQhAllRelation relation = new TQhAllRelation();
		relation.setDtoName(tableName);
		relation.setMakerNo(entClassroomLend.getMakerNo());
		relation.setMakerName(entClassroomLend.getMakerName());
		relation.setSerialno(entClassroomLend.getSerialno());
		relation.setWorkflowid(wfConfigDto.getProcDefKey());
		relation.setWfName(wfConfigDto.getFormName());
		relation.setClassPackage(entClassroomLend.getClass().getName());
		relation.setWorkStatus(0);
		allRelationService.save(relation);
		return rs;
	}
}
