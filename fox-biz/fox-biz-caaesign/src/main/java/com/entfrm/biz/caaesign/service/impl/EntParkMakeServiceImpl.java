package com.entfrm.biz.caaesign.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import com.baomidou.mybatisplus.annotation.TableName;
import com.entfrm.biz.activiti.service.WfConfigService;
import com.entfrm.biz.activiti.entity.TQhAllRelation;
import com.entfrm.biz.activiti.service.TQhAllRelationService;
import com.entfrm.biz.activiti.service.TaskService;
import com.entfrm.biz.activiti.dto.LeaveDto;
import org.springframework.stereotype.Service;
import com.entfrm.biz.caaesign.mapper.EntParkMakeMapper;
import com.entfrm.biz.caaesign.entity.EntParkMake;
import com.entfrm.biz.caaesign.service.EntParkMakeService;
import com.entfrm.core.base.util.PinyinUtil;
import com.entfrm.core.data.util.SerialNumberTool;
import cn.hutool.core.date.DateUtil;
import lombok.AllArgsConstructor;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.runtime.ProcessInstance;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.transaction.annotation.Transactional;
import com.entfrm.core.base.enums.TaskStatusEnum;
import java.util.Map;
import com.entfrm.core.data.constant.CommonConstant;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.io.Serializable;
import com.entfrm.biz.caaesign.entity.EntParkMakeItems1;
import com.entfrm.biz.caaesign.service.EntParkMakeItems1Service;
import com.entfrm.biz.caaesign.entity.EntParkMakeItems2;
import com.entfrm.biz.caaesign.service.EntParkMakeItems2Service;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import java.util.Collection;
import com.entfrm.biz.activiti.dto.WfConfigDto;
import java.lang.reflect.Field;
import com.google.common.collect.Maps;
import org.activiti.engine.task.Task;
import com.entfrm.core.security.util.SecurityUtil;
import com.entfrm.core.base.annotation.SignNode;
import java.util.Arrays;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.bean.BeanUtil;

/**
 * <AUTHOR>
 * @date 2022-05-20 14:29:59
 *
 * @description 生產技術中心零件製作申請單Service业务层
 */
@Service
@AllArgsConstructor
public class EntParkMakeServiceImpl extends ServiceImpl<EntParkMakeMapper, EntParkMake> implements EntParkMakeService {
	private final RuntimeService runtimeService;
	private final org.activiti.engine.TaskService actTaskService;
	private final WfConfigService wfConfigService;
	private final TQhAllRelationService allRelationService;
    private final RabbitTemplate rabbitTemplate;
	private final TaskService taskService;

    private final EntParkMakeItems1Service entParkMakeItems1Service;
    private final EntParkMakeItems2Service entParkMakeItems2Service;

    	/**
	 * 启动流程
	 *
	 * @param entParkMake
	 * @return
	 */
	@Override
	public Boolean startProcess(EntParkMake entParkMake) {
        entParkMake.setWorkStatus(Integer.parseInt(TaskStatusEnum.CHECKING.getStatus()));
        String tableName = EntParkMake.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
        String procDefKey = wfConfigDto.getProcDefKey();
        String businessKey = procDefKey + CommonConstant.BREAK_BUSINESS_KEY + entParkMake.getId();
        Field fields[] = entParkMake.getClass().getDeclaredFields();
        Map variables = Maps.newHashMap();
        //設置填單人
        variables.put("maker", SecurityUtil.getUser().getUsername());
        variables.put("serialNo", entParkMake.getSerialno());
        variables.put("workflowName", wfConfigDto.getFormName());
        Arrays.stream(fields).forEach(field -> {
                SignNode signNode = field.getAnnotation(SignNode.class);
                if (ObjectUtil.isNotEmpty(signNode)) {
                    if (signNode.type().equals(SignNode.Type.COMMON)) {
                        variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entParkMake, field)) ? "AUTO_SIGN" : ReflectUtil.getFieldValue(entParkMake, field));
                    } else if (signNode.type().equals(SignNode.Type.HUIQIAN)) {
                        variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entParkMake, field)) ? Arrays.asList("AUTO_SIGN") : Arrays.asList(ReflectUtil.getFieldValue(entParkMake, field).toString().split(",")));
                    }
                }
            }
        );
        ProcessInstance pi = runtimeService.startProcessInstanceByKey(procDefKey, businessKey,variables);
        entParkMake.setProcessId(pi.getProcessInstanceId());
        this.updateById(entParkMake);

        TQhAllRelation relation = allRelationService.getOne(new QueryWrapper<TQhAllRelation>().eq("serialno",entParkMake.getSerialno()));
        relation.setWorkStatus(entParkMake.getWorkStatus());
        relation.setProcessId(pi.getProcessInstanceId());
        Task task = actTaskService.createTaskQuery().processInstanceId(pi.getProcessInstanceId()).singleResult();
        relation.setNodename(task.getName());
        relation.setDeptNo(entParkMake.getApplyDeptNo());
        allRelationService.updateById(relation);
        //添加mq
        rabbitTemplate.convertAndSend("SignDirectExchange", "Sign.Direct.Routing", BeanUtil.beanToMap(relation));
        return Boolean.TRUE;
	}

    /**
     * 插入一条记录（选择字段，策略插入）
     *
     * @param entParkMake 实体对象
     */
    @Override
    public boolean save(EntParkMake entParkMake) {
        boolean rs = super.save(entParkMake);
        saveChild(entParkMake);
	    String tableName = EntParkMake.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
        TQhAllRelation relation = new TQhAllRelation();
        relation.setDtoName(tableName);
        relation.setMakerNo(entParkMake.getMakerNo());
        relation.setMakerName(entParkMake.getMakerName());
        relation.setSerialno(entParkMake.getSerialno());
        relation.setWorkflowid(wfConfigDto.getProcDefKey());
        relation.setWfName(wfConfigDto.getFormName());
        relation.setClassPackage(entParkMake.getClass().getName());
        relation.setWorkStatus(0);
        allRelationService.save(relation);
        return rs;
    }
	@Override
	public Boolean updateAndCheck(EntParkMake entParkMake, LeaveDto leaveDto) {
		try {
			this.updateById(entParkMake);
			taskService.checkTask(leaveDto);
		} catch (Exception e) {
			return false;
		}
		return true;
	}
    /**
     * 插入一条记录（选择字段，策略插入）
     *
     * @param entParkMake 实体对象
     */
    @Override
    public boolean updateById(EntParkMake entParkMake) {
        // 删除子表信息关联
        entParkMakeItems1Service.remove(new QueryWrapper<EntParkMakeItems1>().eq("pid", entParkMake.getId()));
        // 删除子表信息关联
        entParkMakeItems2Service.remove(new QueryWrapper<EntParkMakeItems2>().eq("pid", entParkMake.getId()));
        boolean rs = super.updateById(entParkMake);
        // 新增子表信息管理
        if(rs){
            // 新增子表信息管理
            saveChild(entParkMake);
        }
        return rs;
    }
    /**
     * 新增子表記錄
     *
     * @param entParkMake 表單对象
     */
    public void saveChild(EntParkMake entParkMake) {
        List<EntParkMakeItems1> entParkMakeItems1Lists = entParkMake.getEntParkMakeItems1Lists();
        if (entParkMakeItems1Lists != null) {
            for (EntParkMakeItems1 entParkMakeItems1 : entParkMakeItems1Lists) {
                entParkMakeItems1.setId(null);
                entParkMakeItems1.setPid(entParkMake.getId());
                entParkMakeItems1Service.save(entParkMakeItems1);
            }
        }
        List<EntParkMakeItems2> entParkMakeItems2Lists = entParkMake.getEntParkMakeItems2Lists();
        if (entParkMakeItems2Lists != null) {
            for (EntParkMakeItems2 entParkMakeItems2 : entParkMakeItems2Lists) {
                entParkMakeItems2.setId(null);
                entParkMakeItems2.setPid(entParkMake.getId());
                entParkMakeItems2Service.save(entParkMakeItems2);
            }
        }
    }

    /**
     * 删除（根据ID 批量删除）
     *
     * @param idList 主键ID列表
     */
    @Override
    public boolean removeByIds(Collection<? extends Serializable> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return false;
        }
        for (Serializable id : idList) {
            EntParkMake entParkMake = getById(id);
            // 删除子表信息关联
            entParkMakeItems1Service.remove(new QueryWrapper<EntParkMakeItems1>().eq("pid", entParkMake.getId()));
            // 删除子表信息关联
            entParkMakeItems2Service.remove(new QueryWrapper<EntParkMakeItems2>().eq("pid", entParkMake.getId()));
            }

        return SqlHelper.retBool(getBaseMapper().deleteBatchIds(idList));
    }
    /**
     * 根據ID獲取信息
     *
     * @param id
     * @return
     */
    @Override
    public EntParkMake getById(Serializable id) {
        EntParkMake entParkMake = super.getById(id);
        setChilds(entParkMake);
        return entParkMake;
    }

    /**
     * 獲取子表列表（${childTable.tableComment}）
     */
    public void setChilds(EntParkMake entParkMake){

    List<EntParkMakeItems1> entParkMakeItems1Lists = entParkMakeItems1Service.list(new QueryWrapper<EntParkMakeItems1>().eq("pid", entParkMake.getId()));
    entParkMake.setEntParkMakeItems1Lists(entParkMakeItems1Lists);
    List<EntParkMakeItems2> entParkMakeItems2Lists = entParkMakeItems2Service.list(new QueryWrapper<EntParkMakeItems2>().eq("pid", entParkMake.getId()));
    entParkMake.setEntParkMakeItems2Lists(entParkMakeItems2Lists);
    }
}
