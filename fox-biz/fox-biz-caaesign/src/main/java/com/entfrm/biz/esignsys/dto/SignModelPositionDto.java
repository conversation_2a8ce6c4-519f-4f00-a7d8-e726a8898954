package com.entfrm.biz.esignsys.dto;


/**
 * 簽名位置
 *
 * <AUTHOR>
 * @date 2021-12-02
 */
public class SignModelPositionDto {
    private static final long serialVersionUID = 1L;

    private String id;
    //申請單編號
    private String applyId;
    //簽核節點
    private String signPoint;
    //審核人工號
    private String signNo;
    //審核人姓名
    private String signName;
    //總次序
    private String showOrder ;
    //X坐標
    private double positionX;
    //Y坐標
    private double positionY;
    //簽核節點次序
    private int signPointOrder;
    //PDF頁數
    private int pageNumber;
    //模板ID
    private String modelId;
    //Ftp url
    private String ImgUrl;
    //簽核時間
    private String signTime;
    //備註
    private String  decrib;
    private Integer flag=0;

    //節點狀態 駁回/通過
    private String ispass;

    //簽名顯示方式（1-默認 垂直顯示  2-簽名居左時間與備註居右 3-簽名居左時間居右備註居下）
    private String imgShowType;

    //簽名圖片實際寬度
    private double imgWidth;

    // 批註是否顯示到簽核件(Y-顯示 N-不顯示)
    private String remarkShow;


    //Ftp url
    private byte[] ImgBytes;


    private String whetherToSign;

    private String proxyChargeno;

    private String proxyChargename;

    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    public String getSignPoint() {
        return signPoint;
    }

    public void setSignPoint(String signPoint) {
        this.signPoint = signPoint;
    }

    public String getSignNo() {
        return signNo;
    }

    public void setSignNo(String signNo) {
        this.signNo = signNo;
    }


    public String getSignName() {
        return signName;
    }

    public void setSignName(String signName) {
        this.signName = signName;
    }


    public String getShowOrder() {
        return showOrder;
    }

    public void setShowOrder(String showOrder) {
        this.showOrder = showOrder;
    }

    public double getPositionX() {
        return positionX;
    }

    public void setPositionX(double positionX) {
        this.positionX = positionX;
    }

    public double getPositionY() {
        return positionY;
    }

    public void setPositionY(double positionY) {
        this.positionY = positionY;
    }

    public int getSignPointOrder() {
        return signPointOrder;
    }

    public void setSignPointOrder(int signPointOrder) {
        this.signPointOrder = signPointOrder;
    }

    public int getPageNumber() {
        return pageNumber;
    }

    public void setPageNumber(int pageNumber) {
        this.pageNumber = pageNumber;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getModelId() {
        return modelId;
    }

    public void setModelId(String modelId) {
        this.modelId = modelId;
    }

    public String getImgUrl() {
        return ImgUrl;
    }

    public void setImgUrl(String imgUrl) {
        ImgUrl = imgUrl;
    }

    public String getSignTime() {
        return signTime;
    }

    public void setSignTime(String signTime) {
        this.signTime = signTime;
    }

    public String getDecrib() {
        return decrib;
    }

    public void setDecrib(String decrib) {
        this.decrib = decrib;
    }

    public Integer getFlag() {
        return flag;
    }

    public void setFlag(Integer flag) {
        this.flag = flag;
    }

    public String getIspass() {
        return ispass;
    }

    public void setIspass(String ispass) {
        this.ispass = ispass;
    }

    public String getImgShowType() {
        return imgShowType;
    }

    public void setImgShowType(String imgShowType) {
        this.imgShowType = imgShowType;
    }

    public double getImgWidth() {
        return imgWidth;
    }

    public void setImgWidth(double imgWidth) {
        this.imgWidth = imgWidth;
    }

    public String getRemarkShow() {
        return remarkShow;
    }

    public void setRemarkShow(String remarkShow) {
        this.remarkShow = remarkShow;
    }

    public byte[] getImgBytes() {
        return ImgBytes;
    }

    public void setImgBytes(byte[] imgBytes) {
        ImgBytes = imgBytes;
    }

    public String getWhetherToSign() {
        return whetherToSign;
    }

    public void setWhetherToSign(String whetherToSign) {
        this.whetherToSign = whetherToSign;
    }

    public String getProxyChargeno() {
        return proxyChargeno;
    }

    public void setProxyChargeno(String proxyChargeno) {
        this.proxyChargeno = proxyChargeno;
    }

    public String getProxyChargename() {
        return proxyChargename;
    }

    public void setProxyChargename(String proxyChargename) {
        this.proxyChargename = proxyChargename;
    }
}

