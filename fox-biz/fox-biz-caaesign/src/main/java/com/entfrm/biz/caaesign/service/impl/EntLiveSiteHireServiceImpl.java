package com.entfrm.biz.caaesign.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.entfrm.biz.activiti.entity.TQhAllRelation;
import com.entfrm.biz.activiti.service.TQhAllRelationService;
import com.entfrm.biz.activiti.service.WfConfigService;
import com.entfrm.biz.caaesign.entity.EntLiveSiteHire;
import com.entfrm.biz.caaesign.mapper.EntLiveSiteHireMapper;
import com.entfrm.biz.caaesign.service.EntLiveSiteHireService;
import com.entfrm.core.base.annotation.SignNode;
import com.entfrm.core.base.enums.TaskStatusEnum;
import com.entfrm.core.data.constant.CommonConstant;
import com.entfrm.core.security.util.SecurityUtil;
import com.google.common.collect.Maps;
import org.activiti.engine.task.Task;
import lombok.AllArgsConstructor;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.runtime.ProcessInstance;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021-06-03 16:19:38
 *
 * @description 生活服務區場地佔用申請單Service业务层
 */
@Service
@AllArgsConstructor
public class EntLiveSiteHireServiceImpl extends ServiceImpl<EntLiveSiteHireMapper, EntLiveSiteHire> implements EntLiveSiteHireService {
	private final RuntimeService runtimeService;
	private final org.activiti.engine.TaskService actTaskService;
	private final WfConfigService wfConfigService;
	private final TQhAllRelationService allRelationService;
	private final RabbitTemplate rabbitTemplate;
    	/**
	 * 启动流程
	 *
	 * @param entLiveSiteHire
	 * @return
	 */
	@Override
	public Boolean startProcess(EntLiveSiteHire entLiveSiteHire) {
        entLiveSiteHire.setWorkStatus(Integer.parseInt(TaskStatusEnum.CHECKING.getStatus()));
		String tableName = EntLiveSiteHire.class.getAnnotation(TableName.class).value();
		Map config = wfConfigService.getTableKey(tableName);
		String procDefKey = config.get("procdefkey")+"";
		String businessKey = procDefKey + CommonConstant.BREAK_BUSINESS_KEY + entLiveSiteHire.getId();
		//設置簽核人
		Field fields[] = entLiveSiteHire.getClass().getDeclaredFields();
		Map variables = Maps.newHashMap();
		//設置填單人
		variables.put("maker", SecurityUtil.getUser().getUsername());
		variables.put("serialNo", entLiveSiteHire.getSerialno());
		variables.put("workflowName", config.get("formname"));
		Arrays.stream(fields).forEach(field -> {
					SignNode signNode = field.getAnnotation(SignNode.class);
					if (ObjectUtil.isNotEmpty(signNode)) {
						variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entLiveSiteHire, field))?"AUTO_SIGN":ReflectUtil.getFieldValue(entLiveSiteHire,field));
					}
				}
		);
		ProcessInstance pi = runtimeService.startProcessInstanceByKey(procDefKey, businessKey,variables);
        entLiveSiteHire.setProcessId(pi.getProcessInstanceId());
		this.updateById(entLiveSiteHire);
		TQhAllRelation relation = allRelationService.getOne(new QueryWrapper<TQhAllRelation>().eq("serialno",entLiveSiteHire.getSerialno()));
		relation.setWorkStatus(entLiveSiteHire.getWorkStatus());
		relation.setProcessId(pi.getProcessInstanceId());
        Task task = actTaskService.createTaskQuery().processInstanceId(pi.getProcessInstanceId()).singleResult();
        relation.setNodename(task.getName());
        relation.setDeptNo(entLiveSiteHire.getMakerdeptno());
		allRelationService.updateById(relation);
		//添加mq
		rabbitTemplate.convertAndSend("SignDirectExchange", "Sign.Direct.Routing", BeanUtil.beanToMap(relation));
		return Boolean.TRUE;
	}

	/**
     * 插入一条记录（选择字段，策略插入）
     *
     */
	@Override
	public boolean save(EntLiveSiteHire entLiveSiteHire) {
		boolean rs = super.save(entLiveSiteHire);
		String tableName = EntLiveSiteHire.class.getAnnotation(TableName.class).value();
		Map config = wfConfigService.getTableKey(tableName);
		TQhAllRelation relation = new TQhAllRelation();
		relation.setDtoName(tableName);
		relation.setMakerNo(entLiveSiteHire.getMakerNo());
		relation.setMakerName(entLiveSiteHire.getMakerName());
		relation.setSerialno(entLiveSiteHire.getSerialno());
		relation.setWorkflowid(config.get("procdefkey")+"");
		relation.setWfName(config.get("formname") + "");
		relation.setWorkStatus(0);
		allRelationService.save(relation);
		return rs;
	}
}
