package com.entfrm.biz.caaesign.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import com.baomidou.mybatisplus.annotation.TableName;
import com.entfrm.biz.activiti.service.WfConfigService;
import com.entfrm.biz.activiti.entity.TQhAllRelation;
import com.entfrm.biz.activiti.service.TQhAllRelationService;
import org.springframework.stereotype.Service;
import com.entfrm.biz.caaesign.mapper.AaaCantingMapper;
import com.entfrm.biz.caaesign.entity.AaaCanting;
import com.entfrm.biz.caaesign.service.AaaCantingService;
import com.entfrm.core.base.util.PinyinUtil;
import com.entfrm.core.data.util.SerialNumberTool;
import cn.hutool.core.date.DateUtil;
import lombok.AllArgsConstructor;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.runtime.ProcessInstance;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.transaction.annotation.Transactional;
import com.entfrm.core.base.enums.TaskStatusEnum;
import java.util.Map;
import com.entfrm.core.data.constant.CommonConstant;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.io.Serializable;
import com.entfrm.biz.caaesign.entity.AaaCantingtb;
import com.entfrm.biz.caaesign.service.AaaCantingtbService;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import java.util.Collection;
import com.entfrm.biz.activiti.dto.WfConfigDto;
import java.lang.reflect.Field;
import com.google.common.collect.Maps;
import org.activiti.engine.task.Task;
import com.entfrm.core.security.util.SecurityUtil;
import com.entfrm.core.base.annotation.SignNode;
import java.util.Arrays;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.bean.BeanUtil;

/**
 * <AUTHOR>
 * @date 2021-10-11 09:39:53
 *
 * @description 餐廳就餐費用結算Service业务层
 */
@Service
@AllArgsConstructor
public class AaaCantingServiceImpl extends ServiceImpl<AaaCantingMapper, AaaCanting> implements AaaCantingService {
	private final RuntimeService runtimeService;
	private final org.activiti.engine.TaskService actTaskService;
	private final WfConfigService wfConfigService;
	private final TQhAllRelationService allRelationService;
    private final RabbitTemplate rabbitTemplate;

    private final AaaCantingtbService aaaCantingtbService;

    	/**
	 * 启动流程
	 *
	 * @param aaaCanting
	 * @return
	 */
	@Override
	public Boolean startProcess(AaaCanting aaaCanting) {
        aaaCanting.setWorkStatus(Integer.parseInt(TaskStatusEnum.CHECKING.getStatus()));
        String tableName = AaaCanting.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
        String procDefKey = wfConfigDto.getProcDefKey();
        String businessKey = procDefKey + CommonConstant.BREAK_BUSINESS_KEY + aaaCanting.getId();
        Field fields[] = aaaCanting.getClass().getDeclaredFields();
        Map variables = Maps.newHashMap();
        //設置填單人
        variables.put("maker", SecurityUtil.getUser().getUsername());
        variables.put("serialNo", aaaCanting.getSerialno());
        variables.put("workflowName", wfConfigDto.getFormName());
        Arrays.stream(fields).forEach(field -> {
                SignNode signNode = field.getAnnotation(SignNode.class);
                if (ObjectUtil.isNotEmpty(signNode)) {
                    if (signNode.type().equals(SignNode.Type.COMMON)) {
                        variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(aaaCanting, field)) ? "AUTO_SIGN" : ReflectUtil.getFieldValue(aaaCanting, field));
                    } else if (signNode.type().equals(SignNode.Type.HUIQIAN)) {
                        variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(aaaCanting, field)) ? Arrays.asList("AUTO_SIGN") : Arrays.asList(ReflectUtil.getFieldValue(aaaCanting, field).toString().split(",")));
                    }
                }
            }
        );
        ProcessInstance pi = runtimeService.startProcessInstanceByKey(procDefKey, businessKey,variables);
        aaaCanting.setProcessId(pi.getProcessInstanceId());
        this.updateById(aaaCanting);

        TQhAllRelation relation = allRelationService.getOne(new QueryWrapper<TQhAllRelation>().eq("serialno",aaaCanting.getSerialno()));
        relation.setWorkStatus(aaaCanting.getWorkStatus());
        relation.setProcessId(pi.getProcessInstanceId());
        Task task = actTaskService.createTaskQuery().processInstanceId(pi.getProcessInstanceId()).singleResult();
        relation.setNodename(task.getName());
        relation.setDeptNo(aaaCanting.getMakerdeptno());
        allRelationService.updateById(relation);
        //添加mq
        rabbitTemplate.convertAndSend("SignDirectExchange", "Sign.Direct.Routing", BeanUtil.beanToMap(relation));
        return Boolean.TRUE;
	}

    /**
     * 插入一条记录（选择字段，策略插入）
     *
     * @param aaaCanting 实体对象
     */
    @Override
    public boolean save(AaaCanting aaaCanting) {
        boolean rs = super.save(aaaCanting);
        saveChild(aaaCanting);
	    String tableName = AaaCanting.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
        TQhAllRelation relation = new TQhAllRelation();
        relation.setDtoName(tableName);
        relation.setMakerNo(aaaCanting.getMakerNo());
        relation.setMakerName(aaaCanting.getMakerName());
        relation.setSerialno(aaaCanting.getSerialno());
        relation.setWorkflowid(wfConfigDto.getProcDefKey());
        relation.setWfName(wfConfigDto.getFormName());
        relation.setClassPackage(aaaCanting.getClass().getName());
        relation.setWorkStatus(0);
        allRelationService.save(relation);
        return rs;
    }

    /**
     * 插入一条记录（选择字段，策略插入）
     *
     * @param aaaCanting 实体对象
     */
    @Override
    public boolean updateById(AaaCanting aaaCanting) {
        // 删除子表信息关联
        aaaCantingtbService.remove(new QueryWrapper<AaaCantingtb>().eq("pid", aaaCanting.getId()));
        boolean rs = super.updateById(aaaCanting);
        // 新增子表信息管理
        if(rs){
            // 新增子表信息管理
            saveChild(aaaCanting);
        }
        return rs;
    }
    /**
     * 新增子表記錄
     *
     * @param aaaCanting 表單对象
     */
    public void saveChild(AaaCanting aaaCanting) {
        List<AaaCantingtb> aaaCantingtbLists = aaaCanting.getAaaCantingtbLists();
        if (aaaCantingtbLists != null) {
            for (AaaCantingtb aaaCantingtb : aaaCantingtbLists) {
                aaaCantingtb.setId(null);
                aaaCantingtb.setPid(aaaCanting.getId());
                aaaCantingtbService.save(aaaCantingtb);
            }
        }
    }

    /**
     * 删除（根据ID 批量删除）
     *
     * @param idList 主键ID列表
     */
    @Override
    public boolean removeByIds(Collection<? extends Serializable> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return false;
        }
        for (Serializable id : idList) {
            AaaCanting aaaCanting = getById(id);
            // 删除子表信息关联
            aaaCantingtbService.remove(new QueryWrapper<AaaCantingtb>().eq("pid", aaaCanting.getId()));
            }

        return SqlHelper.retBool(getBaseMapper().deleteBatchIds(idList));
    }
    /**
     * 根據ID獲取信息
     *
     * @param id
     * @return
     */
    @Override
    public AaaCanting getById(Serializable id) {
        AaaCanting aaaCanting = super.getById(id);
        setChilds(aaaCanting);
        return aaaCanting;
    }

    /**
     * 獲取子表列表（${childTable.tableComment}）
     */
    public void setChilds(AaaCanting aaaCanting){

    List<AaaCantingtb> aaaCantingtbLists = aaaCantingtbService.list(new QueryWrapper<AaaCantingtb>().eq("pid", aaaCanting.getId()));
    aaaCanting.setAaaCantingtbLists(aaaCantingtbLists);
    }
}
