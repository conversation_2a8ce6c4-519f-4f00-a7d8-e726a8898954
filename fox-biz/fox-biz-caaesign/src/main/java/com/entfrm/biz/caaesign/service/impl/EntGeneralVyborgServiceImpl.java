package com.entfrm.biz.caaesign.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import com.baomidou.mybatisplus.annotation.TableName;
import com.entfrm.biz.activiti.service.WfConfigService;
import com.entfrm.biz.activiti.entity.TQhAllRelation;
import com.entfrm.biz.activiti.service.TQhAllRelationService;
import org.springframework.stereotype.Service;
import com.entfrm.biz.caaesign.mapper.EntGeneralVyborgMapper;
import com.entfrm.biz.caaesign.entity.EntGeneralVyborg;
import com.entfrm.biz.caaesign.service.EntGeneralVyborgService;
import com.entfrm.core.base.util.PinyinUtil;
import com.entfrm.core.data.util.SerialNumberTool;
import cn.hutool.core.date.DateUtil;
import lombok.AllArgsConstructor;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.runtime.ProcessInstance;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.transaction.annotation.Transactional;
import com.entfrm.core.base.enums.TaskStatusEnum;
import java.util.Map;
import com.entfrm.core.data.constant.CommonConstant;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.io.Serializable;
import com.entfrm.biz.caaesign.entity.EntGeneralVyborgItems;
import com.entfrm.biz.caaesign.service.EntGeneralVyborgItemsService;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import java.util.Collection;
import com.entfrm.biz.activiti.dto.WfConfigDto;
import java.lang.reflect.Field;
import com.google.common.collect.Maps;
import org.activiti.engine.task.Task;
import com.entfrm.core.security.util.SecurityUtil;
import com.entfrm.core.base.annotation.SignNode;
import java.util.Arrays;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.bean.BeanUtil;

/**
 * <AUTHOR>
 * @date 2022-01-07 08:45:35
 *
 * @description 總務維保維修申請單Service业务层
 */
@Service
@AllArgsConstructor
public class EntGeneralVyborgServiceImpl extends ServiceImpl<EntGeneralVyborgMapper, EntGeneralVyborg> implements EntGeneralVyborgService {
	private final RuntimeService runtimeService;
	private final org.activiti.engine.TaskService actTaskService;
	private final WfConfigService wfConfigService;
	private final TQhAllRelationService allRelationService;
    private final RabbitTemplate rabbitTemplate;

    private final EntGeneralVyborgItemsService entGeneralVyborgItemsService;

    	/**
	 * 启动流程
	 *
	 * @param entGeneralVyborg
	 * @return
	 */
	@Override
	public Boolean startProcess(EntGeneralVyborg entGeneralVyborg) {
        entGeneralVyborg.setWorkStatus(Integer.parseInt(TaskStatusEnum.CHECKING.getStatus()));
        String tableName = EntGeneralVyborg.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
        String procDefKey = wfConfigDto.getProcDefKey();
        String businessKey = procDefKey + CommonConstant.BREAK_BUSINESS_KEY + entGeneralVyborg.getId();
        Field fields[] = entGeneralVyborg.getClass().getDeclaredFields();
        Map variables = Maps.newHashMap();
        //設置填單人
        variables.put("maker", SecurityUtil.getUser().getUsername());
        variables.put("serialNo", entGeneralVyborg.getSerialno());
        variables.put("workflowName", wfConfigDto.getFormName());
        Arrays.stream(fields).forEach(field -> {
                SignNode signNode = field.getAnnotation(SignNode.class);
                if (ObjectUtil.isNotEmpty(signNode)) {
                    if (signNode.type().equals(SignNode.Type.COMMON)) {
                        variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entGeneralVyborg, field)) ? "AUTO_SIGN" : ReflectUtil.getFieldValue(entGeneralVyborg, field));
                    } else if (signNode.type().equals(SignNode.Type.HUIQIAN)) {
                        variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entGeneralVyborg, field)) ? Arrays.asList("AUTO_SIGN") : Arrays.asList(ReflectUtil.getFieldValue(entGeneralVyborg, field).toString().split(",")));
                    }
                }
            }
        );
        ProcessInstance pi = runtimeService.startProcessInstanceByKey(procDefKey, businessKey,variables);
        entGeneralVyborg.setProcessId(pi.getProcessInstanceId());
        this.updateById(entGeneralVyborg);

        TQhAllRelation relation = allRelationService.getOne(new QueryWrapper<TQhAllRelation>().eq("serialno",entGeneralVyborg.getSerialno()));
        relation.setWorkStatus(entGeneralVyborg.getWorkStatus());
        relation.setProcessId(pi.getProcessInstanceId());
        Task task = actTaskService.createTaskQuery().processInstanceId(pi.getProcessInstanceId()).singleResult();
        relation.setNodename(task.getName());
        relation.setDeptNo(entGeneralVyborg.getApplyDeptNo());
        allRelationService.updateById(relation);
        //添加mq
        rabbitTemplate.convertAndSend("SignDirectExchange", "Sign.Direct.Routing", BeanUtil.beanToMap(relation));
        return Boolean.TRUE;
	}

    /**
     * 插入一条记录（选择字段，策略插入）
     *
     * @param entGeneralVyborg 实体对象
     */
    @Override
    public boolean save(EntGeneralVyborg entGeneralVyborg) {
        boolean rs = super.save(entGeneralVyborg);
        saveChild(entGeneralVyborg);
	    String tableName = EntGeneralVyborg.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
        TQhAllRelation relation = new TQhAllRelation();
        relation.setDtoName(tableName);
        relation.setMakerNo(entGeneralVyborg.getMakerNo());
        relation.setMakerName(entGeneralVyborg.getMakerName());
        relation.setSerialno(entGeneralVyborg.getSerialno());
        relation.setWorkflowid(wfConfigDto.getProcDefKey());
        relation.setWfName(wfConfigDto.getFormName());
        relation.setClassPackage(entGeneralVyborg.getClass().getName());
        relation.setWorkStatus(0);
        allRelationService.save(relation);
        return rs;
    }

    /**
     * 插入一条记录（选择字段，策略插入）
     *
     * @param entGeneralVyborg 实体对象
     */
    @Override
    public boolean updateById(EntGeneralVyborg entGeneralVyborg) {
        // 删除子表信息关联
        entGeneralVyborgItemsService.remove(new QueryWrapper<EntGeneralVyborgItems>().eq("pid", entGeneralVyborg.getId()));
        boolean rs = super.updateById(entGeneralVyborg);
        // 新增子表信息管理
        if(rs){
            // 新增子表信息管理
            saveChild(entGeneralVyborg);
        }
        return rs;
    }
    /**
     * 新增子表記錄
     *
     * @param entGeneralVyborg 表單对象
     */
    public void saveChild(EntGeneralVyborg entGeneralVyborg) {
        List<EntGeneralVyborgItems> entGeneralVyborgItemsLists = entGeneralVyborg.getEntGeneralVyborgItemsLists();
        if (entGeneralVyborgItemsLists != null) {
            for (EntGeneralVyborgItems entGeneralVyborgItems : entGeneralVyborgItemsLists) {
                entGeneralVyborgItems.setId(null);
                entGeneralVyborgItems.setPid(entGeneralVyborg.getId());
                entGeneralVyborgItemsService.save(entGeneralVyborgItems);
            }
        }
    }

    /**
     * 删除（根据ID 批量删除）
     *
     * @param idList 主键ID列表
     */
    @Override
    public boolean removeByIds(Collection<? extends Serializable> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return false;
        }
        for (Serializable id : idList) {
            EntGeneralVyborg entGeneralVyborg = getById(id);
            // 删除子表信息关联
            entGeneralVyborgItemsService.remove(new QueryWrapper<EntGeneralVyborgItems>().eq("pid", entGeneralVyborg.getId()));
            }

        return SqlHelper.retBool(getBaseMapper().deleteBatchIds(idList));
    }
    /**
     * 根據ID獲取信息
     *
     * @param id
     * @return
     */
    @Override
    public EntGeneralVyborg getById(Serializable id) {
        EntGeneralVyborg entGeneralVyborg = super.getById(id);
        setChilds(entGeneralVyborg);
        return entGeneralVyborg;
    }

    /**
     * 獲取子表列表（${childTable.tableComment}）
     */
    public void setChilds(EntGeneralVyborg entGeneralVyborg){

    List<EntGeneralVyborgItems> entGeneralVyborgItemsLists = entGeneralVyborgItemsService.list(new QueryWrapper<EntGeneralVyborgItems>().eq("pid", entGeneralVyborg.getId()));
    entGeneralVyborg.setEntGeneralVyborgItemsLists(entGeneralVyborgItemsLists);
    }
}
