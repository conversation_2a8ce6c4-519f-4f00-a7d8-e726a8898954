package com.entfrm.biz.caaesign.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
    import com.baomidou.mybatisplus.annotation.TableName;
import com.entfrm.biz.activiti.dto.WfConfigDto;
import com.entfrm.biz.activiti.entity.TQhAllRelation;
import com.entfrm.biz.activiti.service.TQhAllRelationService;
import com.entfrm.biz.activiti.service.WfConfigService;
import com.entfrm.core.base.annotation.SignNode;
import com.entfrm.core.security.util.SecurityUtil;
import com.google.common.collect.Maps;
import org.activiti.engine.task.Task;
import org.springframework.stereotype.Service;
import com.entfrm.biz.caaesign.mapper.HrmTestMapper;
import com.entfrm.biz.caaesign.entity.HrmTest;
import com.entfrm.biz.caaesign.service.HrmTestService;
import com.entfrm.core.base.util.PinyinUtil;
import com.entfrm.core.data.util.SerialNumberTool;
import cn.hutool.core.date.DateUtil;
import lombok.AllArgsConstructor;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.runtime.ProcessInstance;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.transaction.annotation.Transactional;
import com.entfrm.core.base.enums.TaskStatusEnum;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Map;
import com.entfrm.core.data.constant.CommonConstant;

/**
 * <AUTHOR>
 * @date 2021-08-23 08:55:03
 *
 * @description 新功能表單測試流程Service业务层
 */
@Service
@AllArgsConstructor
public class HrmTestServiceImpl extends ServiceImpl<HrmTestMapper, HrmTest> implements HrmTestService {
	private final RuntimeService runtimeService;
	private final org.activiti.engine.TaskService actTaskService;
	private final WfConfigService wfConfigService;
	private final TQhAllRelationService allRelationService;
	private final RabbitTemplate rabbitTemplate;
    	/**
	 * 启动流程
	 *
	 * @param hrmTest
	 * @return
	 */
	@Override
	public Boolean startProcess(HrmTest hrmTest) {
        hrmTest.setWorkStatus(Integer.parseInt(TaskStatusEnum.CHECKING.getStatus()));
		String tableName = HrmTest.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
		String procDefKey = wfConfigDto.getProcDefKey();
		String businessKey = procDefKey + CommonConstant.BREAK_BUSINESS_KEY + hrmTest.getId();
		Field fields[] = hrmTest.getClass().getDeclaredFields();
        Map variables = Maps.newHashMap();
        //設置填單人
        variables.put("maker", SecurityUtil.getUser().getUsername());
        variables.put("serialNo", hrmTest.getSerialno());
        variables.put("workflowName", wfConfigDto.getFormName());
        Arrays.stream(fields).forEach(field -> {
                    SignNode signNode = field.getAnnotation(SignNode.class);
                    if (ObjectUtil.isNotEmpty(signNode)) {
                        variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(hrmTest, field))?"AUTO_SIGN":ReflectUtil.getFieldValue(hrmTest,field));
                    }
                }
        );
		ProcessInstance pi = runtimeService.startProcessInstanceByKey(procDefKey, businessKey,variables);
        hrmTest.setProcessId(pi.getProcessInstanceId());
		this.updateById(hrmTest);

		TQhAllRelation relation = allRelationService.getOne(new QueryWrapper<TQhAllRelation>().eq("serialno",hrmTest.getSerialno()));
		relation.setWorkStatus(hrmTest.getWorkStatus());
		relation.setProcessId(pi.getProcessInstanceId());
        Task task = actTaskService.createTaskQuery().processInstanceId(pi.getProcessInstanceId()).singleResult();
        relation.setNodename(task.getName());
        relation.setDeptNo(hrmTest.getMakerdeptno());
		allRelationService.updateById(relation);
		//添加mq
		rabbitTemplate.convertAndSend("SignDirectExchange", "Sign.Direct.Routing", BeanUtil.beanToMap(relation));
		return Boolean.TRUE;
	}

	/**
     * 插入一条记录（选择字段，策略插入）
     *
     */
	@Override
	public boolean save(HrmTest hrmTest) {
		boolean rs = super.save(hrmTest);
		String tableName = HrmTest.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
		TQhAllRelation relation = new TQhAllRelation();
		relation.setDtoName(tableName);
		relation.setMakerNo(hrmTest.getMakerNo());
		relation.setMakerName(hrmTest.getMakerName());
		relation.setSerialno(hrmTest.getSerialno());
		relation.setWorkflowid(wfConfigDto.getProcDefKey());
		relation.setWfName(wfConfigDto.getFormName());
		relation.setClassPackage(hrmTest.getClass().getName());
		relation.setWorkStatus(0);
		allRelationService.save(relation);
		return rs;
	}
}
