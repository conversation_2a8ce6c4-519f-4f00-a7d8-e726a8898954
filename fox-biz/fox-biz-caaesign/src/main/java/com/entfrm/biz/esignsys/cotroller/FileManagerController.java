package com.entfrm.biz.esignsys.cotroller;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.entfrm.biz.esignsys.dto.EsignFTPDto;
import com.entfrm.biz.esignsys.dto.ResponseByteData;
import com.entfrm.biz.esignsys.dto.SignModelPositionDto;
import com.entfrm.biz.esignsys.entity.EsignDict;
import com.entfrm.biz.esignsys.entity.EsignFtpInfoEntity;
import com.entfrm.biz.esignsys.entity.EsignTPubFileobjectEntity;
import com.entfrm.biz.esignsys.entity.WfNodeinfoEntity;
import com.entfrm.biz.esignsys.service.EsignDictService;
import com.entfrm.biz.esignsys.service.EsignFileobjectService;
import com.entfrm.biz.esignsys.service.SignModelPositionService;
import com.entfrm.biz.esignsys.util.AESFileUtils;
import com.entfrm.biz.esignsys.util.FTPUtil;
import com.entfrm.biz.system.entity.AppApiOperLog;
import com.entfrm.biz.system.service.AppApiOperLogService;
import com.entfrm.biz.system.service.ConfigService;
import com.entfrm.biz.system.util.JsonUtils;
import com.entfrm.core.log.annotation.OperLog;
import com.entfrm.core.security.util.SecurityUtil;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.io.InputStream;
import java.util.*;

/**
 * @Auther F1858847 王磊
 * @Date 2021/1/29 9:41
 */
@Slf4j
@Controller
@RequestMapping("/caaesign/fileManager")
public class FileManagerController {
    private static Logger logger = LoggerFactory.getLogger(FTPUtil.class);
    @Autowired
    private EsignDictService dictService;

    @Autowired
    private EsignFileobjectService fileobjectService;

    @Autowired
    private SignModelPositionService signModelPositionService;
    @Autowired
    private ConfigService configService;
    @Autowired
    private AppApiOperLogService appApiOperLogService;
    public static final String NEWESIGN_DOWNLOAD_URL = "/apiForDmz/newEsign/download";
    public static final String NEWESIGN_DOWNLOAD_OOS_URL = "/apiForDmz/newEsign/downloadWithOos";
    public static final String NEWESIGN_DOWNLOAD_MAP_URL = "/apiForDmz/newEsign/downloadMap";

    @OperLog("移動端附件下載")
    @RequestMapping("/downloadFile/{id}")
    public void downloadFile(@PathVariable(value = "id") String id, HttpServletRequest request,
                             HttpServletResponse response) throws IOException {

//        Long startTimeBussiness = System.currentTimeMillis();
        AppApiOperLog appApiOperLogBusiness = new AppApiOperLog(
                SecurityUtil.getUser().getUsername(),
                id,
                "/caaesign/fileManager/downloadFile/"+ id,
                new Date(),
                "downloadFile business",
                "dmz"
        );
        EsignTPubFileobjectEntity entity = fileobjectService.getById(id);
        String fileType = request.getParameter("type"); //下載的附件類型 0-下載附件 1-簽核時附件
        String filePath = entity.getUrl();
        String rootUrlString = configService.getValueByKey("esign.root.url");


        List<SignModelPositionDto> signModelPositionList = signModelPositionService.findListByModelID(id);

        appApiOperLogBusiness.setRequestEnd(new Date());
        appApiOperLogService.save(appApiOperLogBusiness);
//        Long executeTime = System.currentTimeMillis() - startTimeBussiness;
//        System.out.println("業務邏輯------" + executeTime + "------");
        if (signModelPositionList != null && signModelPositionList.size() > 0) {   //存在簽核位置
            String serialno = signModelPositionList.get(0).getApplyId();
            List<SignModelPositionDto> allPosition = signModelPositionService.findAllBySerialno(serialno); //查詢所有需要合成的簽核位置信息
            String urlString = rootUrlString + "/wffileesignprocessForMobie/getCuurentNode?serialno="+serialno;
            WfNodeinfoEntity currentNode = JsonUtils.parseObject(HttpUtil.get(urlString , CharsetUtil.CHARSET_UTF_8),WfNodeinfoEntity.class);
            String applyStatus = "0";
            if(ObjectUtil.isNotNull(currentNode)){
                applyStatus = currentNode.getDynfield02(); //單據狀態
            }
            for (SignModelPositionDto signModelPositionDto : allPosition) {
                if (ObjectUtil.isNotNull(currentNode)&&signModelPositionDto.getSignPoint().equals(currentNode.getColname())&&signModelPositionDto.getSignNo().equals(currentNode.getDynfield01())) {
                    signModelPositionDto.setFlag(1);
                }
            }

            List<SignModelPositionDto> signHistory = null; //所有以簽核節點 包含駁回節點
            if("4".equals(applyStatus)){ //單據為駁回時
               List reutnAllInfo = signModelPositionService.findReturnInfo(serialno);
               if(reutnAllInfo!=null&&reutnAllInfo.size()>1){  //駁回條數大於等於2條時 查詢倒數第二條駁回記錄之後的所有簽核記錄
                   signHistory = signModelPositionService.findReturnSignHistoryBySerialno1(serialno);
               }else{  //駁回次數 為一條時查詢所有已簽核記錄
                   signHistory = signModelPositionService.findReturnSignHistoryBySerialno2(serialno);
               }
            }else{
                signHistory = signModelPositionService.findSignHistoryBySerialno(serialno);
            }

            for (SignModelPositionDto positionDto : signHistory) {
                for (SignModelPositionDto modelPositionDto : allPosition) {
                    if (modelPositionDto.getId().equals(positionDto.getId())) {
                        modelPositionDto.setSignTime(positionDto.getSignTime());
                        modelPositionDto.setWhetherToSign(positionDto.getWhetherToSign());
                        modelPositionDto.setProxyChargeno(positionDto.getProxyChargeno());
                        modelPositionDto.setProxyChargename(positionDto.getProxyChargename());
                        String decrib = positionDto.getDecrib();
                        if("Y".equals(positionDto.getWhetherToSign())){
                            decrib = decrib.replace("("+positionDto.getProxyChargeno()+"/"+positionDto.getProxyChargename()+"代簽)","");
                            modelPositionDto.setSignNo(positionDto.getProxyChargeno());
                            modelPositionDto.setSignName(positionDto.getProxyChargename());
                            modelPositionDto.setImgUrl(positionDto.getImgUrl());
                        }
                        modelPositionDto.setDecrib(decrib);
                        if("4".equals(applyStatus)){ //表單為駁回狀態
                            if("駁回".equals(positionDto.getIspass())){
                                modelPositionDto.setFlag(3);
                            }else{
                                modelPositionDto.setFlag(2);
                            }
                        }else{  //審核中或審核完成
                            if("駁回".equals(positionDto.getIspass())){
                                modelPositionDto.setDecrib("");
                                modelPositionDto.setSignTime("");
                            }else{
                                modelPositionDto.setFlag(2);
                            }
                        }
                    }
                }
            }

            if(allPosition!=null&&allPosition.size()>0){ //存在需要合成的簽名 項pdf中添加要合成的圖片與文本
//                    ftpUtil.download(request, response, filePath, entity.getName(),allPosition,fileType,applyStatus);

                AppApiOperLog appApiOperLogApisix1 = new AppApiOperLog(
                        SecurityUtil.getUser().getUsername(),
                        id,
                        "/caaesign/fileManager/downloadFile/"+ id,
                        new Date(),
                        "apisix downloadFile1",
                        "dmz"
                );
//        Long startTimeBussiness1 = System.currentTimeMillis();
                byte[] pdfBytes = getFileWithApisix(filePath);
//        Long executeTime1 = System.currentTimeMillis() - startTimeBussiness1;
//        System.out.println("startTimeBussiness1------" + executeTime1 + "------");
                for (SignModelPositionDto signModelPositionDto : allPosition) {
                    if(0!=signModelPositionDto.getFlag()
                            && 1!=signModelPositionDto.getFlag()
                            && 3!=signModelPositionDto.getFlag() ) {
                        if(StringUtils.isNotBlank(signModelPositionDto.getImgUrl())) {
                            signModelPositionDto.setImgBytes(getFileWithApisix(signModelPositionDto.getImgUrl()));
                        }
                    }
                }
                appApiOperLogApisix1.setRequestEnd(new Date());
                appApiOperLogService.save(appApiOperLogApisix1);
//                Long startTimeBussiness2 = System.currentTimeMillis();

                AppApiOperLog appApiOperLogDmz1 = new AppApiOperLog(
                        SecurityUtil.getUser().getUsername(),
                        id,
                        "/caaesign/fileManager/downloadFile/"+ id,
                        new Date(),
                        "dmz downloadFile1",
                        "dmz"
                );
                FTPUtil.downloadByBytes(request, response, pdfBytes, entity.getName(),allPosition,fileType,applyStatus);
                appApiOperLogDmz1.setRequestEnd(new Date());
                appApiOperLogService.save(appApiOperLogDmz1);
//                Long executeTime2 = System.currentTimeMillis() - startTimeBussiness2;
//                System.out.println("startTimeBussiness2------" + executeTime2 + "------");
            }else{  //不存在需要合成的簽名 直接下載
//                ftpUtil.download(request, response, filePath, entity.getName());
                AppApiOperLog appApiOperLogApisix2 = new AppApiOperLog(
                        SecurityUtil.getUser().getUsername(),
                        id,
                        "/caaesign/fileManager/downloadFile/"+ id,
                        new Date(),
                        "apisix downloadFile2",
                        "dmz"
                );
                byte[] bytes = getFileWithApisix(filePath);
                appApiOperLogApisix2.setRequestEnd(new Date());
                appApiOperLogService.save(appApiOperLogApisix2);


                AppApiOperLog appApiOperLogDmz2 = new AppApiOperLog(
                        SecurityUtil.getUser().getUsername(),
                        id,
                        "/caaesign/fileManager/downloadFile/"+ id,
                        new Date(),
                        "dmz downloadFile2",
                        "dmz"
                );
                FTPUtil.downloadByBytes(request, response,bytes, entity.getName());
                appApiOperLogDmz2.setRequestEnd(new Date());
                appApiOperLogService.save(appApiOperLogDmz2);
            }
        }else{ //兼容舊手簽檔
//            ftpUtil.download(request, response, filePath, entity.getName());
//            Long executeTime1 = System.currentTimeMillis() - startTimeBussiness;
//            System.out.println("總時間------" + executeTime1 + "------");
            AppApiOperLog appApiOperLogApisix3 = new AppApiOperLog(
                    SecurityUtil.getUser().getUsername(),
                    id,
                    "/caaesign/fileManager/downloadFile/"+ id,
                    new Date(),
                    "apisix downloadFile3",
                    "dmz"
            );
            byte[] bytes = getFileWithApisix(filePath);
            appApiOperLogApisix3.setRequestEnd(new Date());
            appApiOperLogService.save(appApiOperLogApisix3);
            AppApiOperLog appApiOperLogDmz3 = new AppApiOperLog(
                    SecurityUtil.getUser().getUsername(),
                    id,
                    "/caaesign/fileManager/downloadFile/"+ id,
                    new Date(),
                    "dmz downloadFile3",
                    "dmz"
            );
            FTPUtil.downloadByBytes(request, response,bytes, entity.getName());
            appApiOperLogDmz3.setRequestEnd(new Date());
            appApiOperLogService.save(appApiOperLogDmz3);
        }
//        Long executeTime2 = System.currentTimeMillis() - startTimeBussiness;
//        System.out.println("總時間------" + executeTime2 + "------");
    }


    @OperLog("移動端簽核檔首頁下載")
    @RequestMapping("/downloadPdfFirstPage/{id}")
    public void downloadPdfFirstPage(@PathVariable(value = "id") String id, HttpServletRequest request,
                             HttpServletResponse response) throws Exception {
        AppApiOperLog appApiOperLogBusiness = new AppApiOperLog(
                SecurityUtil.getUser().getUsername(),
                id,
                "/caaesign/fileManager/downloadPdfFirstPage/"+ id,
                new Date(),
                "downloadFile business",
                "dmz"
        );
        EsignTPubFileobjectEntity entity = fileobjectService.getById(id);

        String filePath = entity.getUrl();

        byte[] bytes = getFileWithApisix(filePath.replace(".pdf",".firstPagePdf"));
        if(bytes==null||bytes.length < 100){  //無壓縮檔下載原檔
            bytes = getFileWithApisix(filePath);
        }
      // FTPUtil.downloadByBytes(request, response,bytes, entity.getName());
      //  pdf解密
      ByteArrayInputStream bis = new ByteArrayInputStream(bytes);
      byte[] cs = AESFileUtils.decryptFileReturnFile(FTPUtil.getKeyBase64(),bis);
      FTPUtil.downloadByBytes(request, response,cs, entity.getName());

        appApiOperLogBusiness.setRequestEnd(new Date());
        appApiOperLogService.save(appApiOperLogBusiness);
    }


    public byte[] getFileWithApisix(String filePath){
        String rootPath = dictService.getById(22873).getValue();
        String ssoEndpoint = configService.getValueByKey("apisix.base.url");
//        String ssoEndpoint = "http://10.76.145.232:8888/esign";
        Map<String, Object> param = new HashMap<>();
        param.put("filePath", filePath);
        param.put("directory", rootPath);
        final byte[] bytes = HttpRequest
                .get(ssoEndpoint + NEWESIGN_DOWNLOAD_OOS_URL)
                .form(param)
                .execute()
                .bodyBytes();

        return bytes;
    }
     public EsignFTPDto searchFtp(){
         EsignFTPDto ftpDto = new EsignFTPDto();
         List<EsignDict> dictList = dictService.getDictByType("ftps_upload_para");
         for (EsignDict dict : dictList) {
             if ("ftps_upload_para_01".equals(dict.getCodeUniq())) {
                 ftpDto.setFtpUser(dict.getValue());
             }
             if ("ftps_upload_para_02".equals(dict.getCodeUniq())) {
                 ftpDto.setFtpPass(dict.getValue());
             }
             if ("ftps_upload_para_03".equals(dict.getCodeUniq())) {
                 ftpDto.setFtpIp(dict.getValue());
             }
             if ("ftps_upload_para_04".equals(dict.getCodeUniq())) {
                 ftpDto.setPort(Integer.parseInt(dict.getValue()));
             }
         }
         return ftpDto;
     }
}
