package com.entfrm.biz.esignsys.cotroller;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.entfrm.biz.esignsys.dto.MobileLoginDto;
import com.entfrm.biz.esignsys.entity.*;
import com.entfrm.biz.esignsys.service.*;
import com.entfrm.biz.system.service.ConfigService;
import com.entfrm.biz.system.util.JsonUtils;
import com.entfrm.core.base.api.R;
import com.entfrm.core.security.entity.EntfrmUser;
import com.entfrm.core.security.util.SecurityUtil;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Auther F1858847 王磊
 * @Date 2021/1/30 11:43
 */
@RestController
@AllArgsConstructor
@RequestMapping("/sysinfo")
public class SysinfoController {

    private final EsignTQhUserformhsService tQhUserformhsService;
    private final EsignMobileAppUpdateHistoryService mobileAppUpdateHistoryService;
    private final EsignFileobjectService fileobjectService;
    private final EsignUserService esignUserService;
    private final ConfigService configService;
    private final EsignSysLeaveMessageService esignSysLeaveMessageService;
    private final EsignFileOosService fileOosService;
    private final EsignDictService dictService;
    public static final String LEAVEMESSAGE_ROOT_PATH = "userLeaveMessage/pc";

    /*跳轉到上線申請單圖標*/
    @RequestMapping("getUserInfo")
    @ResponseBody
    public Object getUserInfo(HttpServletRequest request) {
        String username = SecurityUtil.getUser().getUsername();
        List<EsignTQhUserformhsEntity> tQhUserformhsEntitys = tQhUserformhsService.list(new QueryWrapper<EsignTQhUserformhsEntity>()
                .eq("EMPNO", username));

        if (tQhUserformhsEntitys.size() > 0) {
            EsignTQhUserformhsEntity tQhUserformhsEntity = tQhUserformhsEntitys.get(0);

            MobileLoginDto returnInfo = new MobileLoginDto().toMobileLogin(tQhUserformhsEntity);
            List<EsignTPubFileobjectEntity> entitys = fileobjectService.list(new QueryWrapper<EsignTPubFileobjectEntity>()
                    .eq(StrUtil.isNotBlank(username), "NAME", username));
            if (entitys != null && entitys.size() > 0) {
                returnInfo.setSignFilePath("/caaesign/wffileesignprocess/showSignatureImg");
            }
            //獲取個人信息
            List<EsignUser> sysusers = esignUserService.list(new QueryWrapper<EsignUser>()
                    .eq(StrUtil.isNotBlank(username), "LOGIN_NAME", username));
            if (sysusers != null && sysusers.size() > 0) {
                returnInfo.setEmail(sysusers.get(0).getEmail());
                returnInfo.setPhone(sysusers.get(0).getPhone());
                returnInfo.setSysid(sysusers.get(0).getId());
                returnInfo.setEmailset(sysusers.get(0).getEmailset());
                returnInfo.setTimingsend(sysusers.get(0).getTimingsend());
                returnInfo.setTimingperiod(sysusers.get(0).getTimingperiod());
                returnInfo.setEmailsetapp(sysusers.get(0).getEmailsetapp());
                returnInfo.setTimingsendapp(sysusers.get(0).getTimingsendapp());
                returnInfo.setTimingperiodapp(sysusers.get(0).getTimingperiodapp());
                returnInfo.setNotification(sysusers.get(0).getNotification());
            }

            return R.ok(returnInfo);
        }

        return R.error("獲取人員信息失敗");
    }

    @RequestMapping("getUserInfoByEmpno")
    @ResponseBody
    public Object getUserInfo(HttpServletRequest request, @RequestParam String empNo) {
        List<EsignTQhUserformhsEntity> tQhUserformhsEntitys = tQhUserformhsService.list(new QueryWrapper<EsignTQhUserformhsEntity>()
                .eq("EMPNO", empNo));

        if (tQhUserformhsEntitys.size() > 0) {
            EsignTQhUserformhsEntity tQhUserformhsEntity = tQhUserformhsEntitys.get(0);
            return R.ok(tQhUserformhsEntity);
        }

        return R.error("獲取人員信息失敗");
    }


    @RequestMapping("versionUpdate")
    @ResponseBody
    public Object versionUpdate(EsignMobileAppUpdateHistoryEntity esignMobileAppUpdateHistoryEntity) {
        List<EsignMobileAppUpdateHistoryEntity> mobileAppUpdateHistoryEntitys = mobileAppUpdateHistoryService.list(new QueryWrapper<EsignMobileAppUpdateHistoryEntity>()
                .eq(StrUtil.isNotBlank(esignMobileAppUpdateHistoryEntity.getAppSource()), "app_source", esignMobileAppUpdateHistoryEntity.getAppSource())
                .eq(StrUtil.isNotBlank(esignMobileAppUpdateHistoryEntity.getChannel()), "channel", esignMobileAppUpdateHistoryEntity.getChannel()));
        if (mobileAppUpdateHistoryEntitys.size() > 0) {
            return R.ok(mobileAppUpdateHistoryEntitys.get(0), "獲取版本信息成功");
        }
        return R.error("獲取版本信息失敗");

    }

    @RequestMapping("/updateSysInfo")
    public R updateSysInfo(MobileLoginDto mobileLoginDto) {
        String urlString = configService.getValueByKey("esign.root.url") + "/entrfrm/updateSysInfo";
        //String urlString = "http://10.76.213.247:8084/entrfrm/updateSysInfo";
        Map<String, Object> paramMap = MapUtil.newHashMap();
        paramMap.put("empno", SecurityUtil.getUser().getUsername());
        paramMap.put("email", mobileLoginDto.getEmail());
        paramMap.put("phone", mobileLoginDto.getPhone());
        paramMap.put("emailset", mobileLoginDto.getEmailset());
        paramMap.put("timingsend", mobileLoginDto.getTimingsend());
        paramMap.put("timingperiod", mobileLoginDto.getTimingperiod());
        paramMap.put("emailsetapp", mobileLoginDto.getEmailsetapp());
        paramMap.put("timingsendapp", mobileLoginDto.getTimingsendapp());
        paramMap.put("timingperiodapp", mobileLoginDto.getTimingperiodapp());
        String result1 = HttpUtil.post(urlString, paramMap);
        return R.ok();
    }


    @RequestMapping("/commitLeaveMessage")
    public R commitLeaveMessage(EsignSysLeaveMessageEntity esignSysLeaveMessageEntity,HttpServletRequest request) throws Exception {
        String attachids = "";
        String userNo = SecurityUtil.getUser().getUsername();
        if (esignSysLeaveMessageEntity.getFiles() != null && esignSysLeaveMessageEntity.getFiles().length > 0) {

            String rootPath = dictService.getById(22873).getValue();
            StringBuilder attachids2Builder = new StringBuilder();
            for (MultipartFile file : esignSysLeaveMessageEntity.getFiles()) {
                if (!file.isEmpty()) {
                    EsignTPubFileobjectEntity tPubFileobjectEntity = fileOosService.updateLeaveMessageFileWithOos(file, request, rootPath, LEAVEMESSAGE_ROOT_PATH, userNo);
                    attachids2Builder.append(tPubFileobjectEntity.getId() + ",");
                }
            }
            attachids = attachids2Builder.length() > 0 ? attachids2Builder.toString().substring(0, attachids2Builder.length() - 1) : "";
            esignSysLeaveMessageEntity.setAttachids(attachids);
        }
        esignSysLeaveMessageEntity.setCreateBy(userNo);
        esignSysLeaveMessageEntity.setCreateDate(new Date());
        esignSysLeaveMessageEntity.setChannel("便易簽APP");
        esignSysLeaveMessageService.save(esignSysLeaveMessageEntity);
        return R.ok();
    }

    @RequestMapping("updatePassword")
    @ResponseBody
    public Object updatePassword(@RequestParam String oldPassword, @RequestParam String newPassword) throws Exception {

        EntfrmUser user = SecurityUtil.getUser();
        //電子簽核平台登錄本地賬號
        boolean login = esignUserService.updatePassword(user.getUsername(), oldPassword,newPassword);
        if (login) {
            return R.ok();
        }
        return R.ok(null,"原密碼錯誤，請確認。");
    }
}
