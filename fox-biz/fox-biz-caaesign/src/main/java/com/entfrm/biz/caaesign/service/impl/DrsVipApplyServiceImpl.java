package com.entfrm.biz.caaesign.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
    import com.baomidou.mybatisplus.annotation.TableName;
import com.entfrm.biz.activiti.dto.WfConfigDto;
import com.entfrm.biz.activiti.entity.TQhAllRelation;
import com.entfrm.biz.activiti.service.TQhAllRelationService;
import com.entfrm.biz.activiti.service.WfConfigService;
import com.entfrm.biz.activiti.service.TaskService;
import com.entfrm.biz.activiti.dto.LeaveDto;
import com.entfrm.core.base.annotation.SignNode;
import com.entfrm.core.security.util.SecurityUtil;
import com.google.common.collect.Maps;
import org.activiti.engine.task.Task;
import org.springframework.stereotype.Service;
import com.entfrm.biz.caaesign.mapper.DrsVipApplyMapper;
import com.entfrm.biz.caaesign.entity.DrsVipApply;
import com.entfrm.biz.caaesign.service.DrsVipApplyService;
import com.entfrm.core.base.util.PinyinUtil;
import com.entfrm.core.data.util.SerialNumberTool;
import cn.hutool.core.date.DateUtil;
import lombok.AllArgsConstructor;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.runtime.ProcessInstance;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.transaction.annotation.Transactional;
import com.entfrm.core.base.enums.TaskStatusEnum;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Map;
import com.entfrm.core.data.constant.CommonConstant;

/**
 * <AUTHOR>
 * @date 2023-09-22 13:43:54
 *
 * @description VIP就餐申請Service业务层
 */
@Service
@AllArgsConstructor
public class DrsVipApplyServiceImpl extends ServiceImpl<DrsVipApplyMapper, DrsVipApply> implements DrsVipApplyService {
	private final RuntimeService runtimeService;
	private final org.activiti.engine.TaskService actTaskService;
	private final WfConfigService wfConfigService;
	private final TQhAllRelationService allRelationService;
	private final RabbitTemplate rabbitTemplate;
	private final TaskService taskService;
    	/**
	 * 启动流程
	 *
	 * @param drsVipApply
	 * @return
	 */
	@Override
	public Boolean startProcess(DrsVipApply drsVipApply) {
        drsVipApply.setWorkStatus(Integer.parseInt(TaskStatusEnum.CHECKING.getStatus()));
		String tableName = DrsVipApply.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
		String procDefKey = wfConfigDto.getProcDefKey();
		String businessKey = procDefKey + CommonConstant.BREAK_BUSINESS_KEY + drsVipApply.getId();
		Field fields[] = drsVipApply.getClass().getDeclaredFields();
        Map variables = Maps.newHashMap();
        //設置填單人
        variables.put("maker", SecurityUtil.getUser().getUsername());
        variables.put("serialNo", drsVipApply.getSerialno());
        variables.put("workflowName", wfConfigDto.getFormName());
        Arrays.stream(fields).forEach(field -> {
                    SignNode signNode = field.getAnnotation(SignNode.class);
                    if (ObjectUtil.isNotEmpty(signNode)) {
                        if (signNode.type().equals(SignNode.Type.COMMON)) {
                            variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(drsVipApply, field)) ? "AUTO_SIGN" : ReflectUtil.getFieldValue(drsVipApply, field));
                        } else if (signNode.type().equals(SignNode.Type.HUIQIAN)) {
                            variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(drsVipApply, field)) ? Arrays.asList("AUTO_SIGN") : Arrays.asList(ReflectUtil.getFieldValue(drsVipApply, field).toString().split(",")));
                        }
                    }
                }
        );
		ProcessInstance pi = runtimeService.startProcessInstanceByKey(procDefKey, businessKey,variables);
        drsVipApply.setProcessId(pi.getProcessInstanceId());
		super.updateById(drsVipApply);

		TQhAllRelation relation = allRelationService.getOne(new QueryWrapper<TQhAllRelation>().eq("serialno",drsVipApply.getSerialno()));
		relation.setWorkStatus(drsVipApply.getWorkStatus());
		relation.setProcessId(pi.getProcessInstanceId());
        Task task = actTaskService.createTaskQuery().processInstanceId(pi.getProcessInstanceId()).singleResult();
        relation.setNodename(task.getName());
        relation.setDeptNo(drsVipApply.getMakerdeptno());
		allRelationService.updateById(relation);
		//添加mq
		rabbitTemplate.convertAndSend("SignDirectExchange", "Sign.Direct.Routing", BeanUtil.beanToMap(relation));
		return Boolean.TRUE;
	}

	/**
     * 插入一条记录（选择字段，策略插入）
     *
     */
	@Override
	public boolean save(DrsVipApply drsVipApply) {
		boolean rs = super.save(drsVipApply);
		String tableName = DrsVipApply.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
		TQhAllRelation relation = new TQhAllRelation();
		relation.setDtoName(tableName);
		relation.setMakerNo(drsVipApply.getMakerNo());
		relation.setMakerName(drsVipApply.getMakerName());
		relation.setSerialno(drsVipApply.getSerialno());
		relation.setWorkflowid(wfConfigDto.getProcDefKey());
		relation.setWfName(wfConfigDto.getFormName());
		relation.setClassPackage(drsVipApply.getClass().getName());
		relation.setWorkStatus(0);
		allRelationService.save(relation);
		return rs;
	}
	@Override
	public Boolean updateAndCheck(DrsVipApply drsVipApply, LeaveDto leaveDto) {
		try {
			this.updateById(drsVipApply);
			taskService.checkTask(leaveDto);
		} catch (Exception e) {
			return false;
		}
		return true;
	}
}
