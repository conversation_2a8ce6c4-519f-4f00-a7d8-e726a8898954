package com.entfrm.biz.caaesign.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.entfrm.core.base.annotation.Excel;
import com.entfrm.core.base.annotation.Excels;
import com.entfrm.core.base.annotation.SignNode;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.entfrm.core.data.entity.BaseBizSignEntity;
import com.baomidou.mybatisplus.annotation.TableField;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * <AUTHOR>
 * @date 2021-09-08 10:22:55
 * @description 產線聯網設備入網申請單对象 EntEquipmentNetwork
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ent_equipment_network")
public class EntEquipmentNetwork extends BaseBizSignEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    @TableId
    private String id;

    /**
     * 填單人所在部門
     */
    private String makerdeptno;

    /**
     * 所在廠區
     */
    private String makerfactoryid;

    /**
     * 承辦人工號
     */
    private String applyEmpNo;

    /**
     * 承辦人姓名
     */
    private String applyEmpName;

    /**
     * 單位代碼
     */
    private String applyDeptNo;

    /**
     * 費用代碼
     */
    private String applyCostNo;

    /**
     * 聯繫方式
     */
    private String applyTel;

    /**
     * 單位名稱
     */
    @Excel(name = "單位名稱",type = Excel.Type.ALL,order = 4)
    private String applyDeptName;

    /**
     * 聯繫郵箱
     */
    private String applyMail;

    /**
     * 需求說明
     */
    private String applyExplain;

    /**
     * 課級主管工號
     */
    /*@SignNode(type = SignNode.Type.COMMON, order = 1, nodeName = "課級主管", canBatch = 0)*/
    private String kchargeno;

    /**
     * 課級主管姓名
     */
    private String kchargename;

    /**
     * 部級主管工號
     */
    @SignNode(type = SignNode.Type.HUIQIAN,order = 2,nodeName = "部門主管",canBatch = 1)
    private String bchargeno;

    /**
     * 部級主管姓名
     */
    private String bchargename;

    /**
     * IT生技確認工號
     */
    /*@SignNode(type = SignNode.Type.COMMON, order = 3, nodeName = "IT生技確認", canBatch = 0)*/
    private String itsjqrchargeno;

    /**
     * IT生技確認姓名
     */
    private String itsjqrchargename;

    /**
     * IT生技複審工號
     */
   /* @SignNode(type = SignNode.Type.COMMON, order = 4, nodeName = "IT生技複審", canBatch = 0)*/
    private String itsjfschargeno;

    /**
     * IT生技複審姓名
     */
    private String itsjfschargename;

    /**
     * 資訊運維確認工號
     */
    @SignNode(type = SignNode.Type.HUIQIAN, order = 1, nodeName = "資訊運維課級主管", canBatch = 0)
    private String zxywqrchargeno;

    /**
     * 資訊運維確認姓名
     */
    private String zxywqrchargename;

    /**
     * 資訊運維審核工號
     */
    @SignNode(type = SignNode.Type.HUIQIAN, order = 3, nodeName = "資訊運維廠區主管", canBatch = 1)
    private String zxywshchargeno;

    /**
     * 資訊運維審核姓名
     */
    private String zxywshchargename;

    /**
     * 設備入網工號
     */
    @SignNode(type = SignNode.Type.HUIQIAN, order = 4, nodeName = "設備入網作業", canBatch = 0)
    private String sbrwchargeno;

    /**
     * 設備入網姓名
     */
    private String sbrwchargename;
    /** 創建日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createDate;

    /** 更新日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date updateDate;

    @TableField(exist = false)
    private List<EntEquipmentItems> entEquipmentItemsLists = new ArrayList<>();

    @TableField(exist = false)
    private String currentname;
    @TableField(exist = false)
    private String currentorder;
}
