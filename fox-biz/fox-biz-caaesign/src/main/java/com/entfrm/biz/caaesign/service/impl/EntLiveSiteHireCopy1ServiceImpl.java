package com.entfrm.biz.caaesign.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
    import com.baomidou.mybatisplus.annotation.TableName;
import com.entfrm.biz.activiti.entity.TQhAllRelation;
import com.entfrm.biz.activiti.service.TQhAllRelationService;
import com.entfrm.biz.activiti.service.WfConfigService;
import org.springframework.stereotype.Service;
import com.entfrm.biz.caaesign.mapper.EntLiveSiteHireCopy1Mapper;
import com.entfrm.biz.caaesign.entity.EntLiveSiteHireCopy1;
import com.entfrm.biz.caaesign.service.EntLiveSiteHireCopy1Service;
import com.entfrm.core.base.util.PinyinUtil;
import com.entfrm.core.data.util.SerialNumberTool;
import cn.hutool.core.date.DateUtil;
import lombok.AllArgsConstructor;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.runtime.ProcessInstance;
import org.springframework.transaction.annotation.Transactional;
import com.entfrm.core.base.enums.TaskStatusEnum;
import java.util.Map;
import org.activiti.engine.task.Task;
import com.entfrm.core.data.constant.CommonConstant;

/**
 * <AUTHOR>
 * @date 2021-07-08 08:04:18
 *
 * @description 生活服務區場地佔用申請單測試1Service业务层
 */
@Service
@AllArgsConstructor
public class EntLiveSiteHireCopy1ServiceImpl extends ServiceImpl<EntLiveSiteHireCopy1Mapper, EntLiveSiteHireCopy1> implements EntLiveSiteHireCopy1Service {
	private final RuntimeService runtimeService;
	private final org.activiti.engine.TaskService actTaskService;
	private final WfConfigService wfConfigService;
	private final TQhAllRelationService allRelationService;
    	/**
	 * 启动流程
	 *
	 * @param entLiveSiteHireCopy1
	 * @return
	 */
	@Override
	public Boolean startProcess(EntLiveSiteHireCopy1 entLiveSiteHireCopy1) {
        entLiveSiteHireCopy1.setWorkStatus(Integer.parseInt(TaskStatusEnum.CHECKING.getStatus()));
		String tableName = EntLiveSiteHireCopy1.class.getAnnotation(TableName.class).value();
		Map config = wfConfigService.getTableKey(tableName);
		String procDefKey = config.get("procDefKey")+"";
		String businessKey = procDefKey + CommonConstant.BREAK_BUSINESS_KEY + entLiveSiteHireCopy1.getId();
		ProcessInstance pi = runtimeService.startProcessInstanceByKey(procDefKey, businessKey);
        entLiveSiteHireCopy1.setProcessId(pi.getProcessInstanceId());
		this.updateById(entLiveSiteHireCopy1);

		TQhAllRelation relation = allRelationService.getOne(new QueryWrapper<TQhAllRelation>().eq("serialno",entLiveSiteHireCopy1.getSerialno()));
		relation.setWorkStatus(entLiveSiteHireCopy1.getWorkStatus());
		relation.setProcessId(pi.getProcessInstanceId());
        Task task = actTaskService.createTaskQuery().processInstanceId(pi.getProcessInstanceId()).singleResult();
        relation.setNodename(task.getName());
        relation.setDeptNo(entLiveSiteHireCopy1.getApplyDeptNo());
		allRelationService.updateById(relation);
		return Boolean.TRUE;
	}

	/**
     * 插入一条记录（选择字段，策略插入）
     *
     */
	@Override
	public boolean save(EntLiveSiteHireCopy1 entLiveSiteHireCopy1) {
		boolean rs = super.save(entLiveSiteHireCopy1);
		String tableName = EntLiveSiteHireCopy1.class.getAnnotation(TableName.class).value();
		Map config = wfConfigService.getTableKey(tableName);
		TQhAllRelation relation = new TQhAllRelation();
		relation.setDtoName(tableName);
		relation.setMakerNo(entLiveSiteHireCopy1.getMakerNo());
		relation.setMakerName(entLiveSiteHireCopy1.getMakerName());
		relation.setSerialno(entLiveSiteHireCopy1.getSerialno());
		relation.setWorkflowid(config.get("procDefKey")+"");
		relation.setWfName(config.get("formName") + "");
		relation.setWorkStatus(0);
		allRelationService.save(relation);
		return rs;
	}
}
