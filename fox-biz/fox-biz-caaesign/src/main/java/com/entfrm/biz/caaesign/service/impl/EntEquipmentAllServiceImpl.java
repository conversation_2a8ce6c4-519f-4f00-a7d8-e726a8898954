package com.entfrm.biz.caaesign.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.entfrm.biz.activiti.service.TQhAllRelationService;
import com.entfrm.biz.activiti.service.WfConfigService;
import com.entfrm.biz.caaesign.entity.EntEquipmentAll;
import com.entfrm.biz.caaesign.mapper.EntEquipmentAllMapper;
import com.entfrm.biz.caaesign.service.EntEquipmentAllService;
import lombok.AllArgsConstructor;
import org.activiti.engine.RuntimeService;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2021-09-08 13:49:26
 *
 * @description 產線聯網設備入網明細表Service业务层
 */
@Service
@AllArgsConstructor
public class EntEquipmentAllServiceImpl extends ServiceImpl<EntEquipmentAllMapper, EntEquipmentAll> implements EntEquipmentAllService {
	private final RuntimeService runtimeService;
	private final org.activiti.engine.TaskService actTaskService;
	private final WfConfigService wfConfigService;
	private final TQhAllRelationService allRelationService;
	private final RabbitTemplate rabbitTemplate;
    }
