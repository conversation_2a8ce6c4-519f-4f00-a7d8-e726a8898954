package com.entfrm.biz.caaesign.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
    import com.baomidou.mybatisplus.annotation.TableName;
import com.entfrm.biz.activiti.dto.WfConfigDto;
import com.entfrm.biz.activiti.entity.TQhAllRelation;
import com.entfrm.biz.activiti.service.TQhAllRelationService;
import com.entfrm.biz.activiti.service.WfConfigService;
import com.entfrm.biz.activiti.service.TaskService;
import com.entfrm.biz.activiti.dto.LeaveDto;
import com.entfrm.core.base.annotation.SignNode;
import com.entfrm.core.security.util.SecurityUtil;
import com.google.common.collect.Maps;
import org.activiti.engine.task.Task;
import org.springframework.stereotype.Service;
import com.entfrm.biz.caaesign.mapper.EntGuestItems1Mapper;
import com.entfrm.biz.caaesign.entity.EntGuestItems1;
import com.entfrm.biz.caaesign.service.EntGuestItems1Service;
import com.entfrm.core.base.util.PinyinUtil;
import com.entfrm.core.data.util.SerialNumberTool;
import cn.hutool.core.date.DateUtil;
import lombok.AllArgsConstructor;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.runtime.ProcessInstance;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.transaction.annotation.Transactional;
import com.entfrm.core.base.enums.TaskStatusEnum;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Map;
import com.entfrm.core.data.constant.CommonConstant;

/**
 * <AUTHOR>
 * @date 2024-10-17 08:03:26
 *
 * @description iPEBG移轉設備銷賬聯絡單從1Service业务层
 */
@Service
@AllArgsConstructor
public class EntGuestItems1ServiceImpl extends ServiceImpl<EntGuestItems1Mapper, EntGuestItems1> implements EntGuestItems1Service {
	private final RuntimeService runtimeService;
	private final org.activiti.engine.TaskService actTaskService;
	private final WfConfigService wfConfigService;
	private final TQhAllRelationService allRelationService;
	private final RabbitTemplate rabbitTemplate;
	private final TaskService taskService;
    }
