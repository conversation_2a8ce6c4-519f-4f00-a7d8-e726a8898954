package com.entfrm.biz.esignsys.util;

import cn.hutool.core.util.StrUtil;
import com.entfrm.biz.esignsys.dto.SignModelPositionDto;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;

public class EditPdf {

    private static float signDivWidth = 100;  //瀏覽器頁面 簽名寬度 與前台一致
    private static float viewWidth = 800;  //瀏覽器 簽核檔模板寬度 與前台一致
    private static float rate = 2;  //簽名圖片 長/寬


    //將簽名圖片添加到
    public static byte[] pdfAddImg(HttpServletRequest request, HttpServletResponse response, byte[] pdf, List<SignModelPositionDto> signModelPositionList, String fileName,String fileType,String applyStatus) throws DocumentException, IOException {
        PdfReader reader = new PdfReader(pdf);  //讀取簽核檔模板
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        PdfStamper stamper = new PdfStamper(reader, bos);
        byte[] resutl = null;
        try{
            BaseFont font = BaseFont.createFont("static/fonts/mingliu.ttc,1", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
            int pdfPageNum = reader.getNumberOfPages();  //簽核文檔模板頁數
            if(pdfPageNum>0){  //簽核檔模板不為空
                for(int i=0;i<pdfPageNum;i++){  //讀取簽核檔每一頁
                    PdfContentByte page = stamper.getOverContent(i+1);
                    Rectangle inlinePageSize = reader.getPageSizeWithRotation(i+1);
                    //PdfImportedPage inlinePageSize = page.getPdfWriter().getImportedPage(reader,(i+1));
                    for(SignModelPositionDto item:signModelPositionList){  //將每一頁 簽核名圖片加載到 對應pdf頁上
                        if(item.getPageNumber() == (i+1)){
                            Double x = item.getPositionX();
                            Double y = item.getPositionY();

                            //瀏覽器 頁面坐標 左上角為0,0 向右向下 增大     簽名起點為左上角
                            //pdf頁面坐標 左下角為0,0 向右向上  增大    簽名起點為左下角
                            float pdfWidth = inlinePageSize.getWidth();
                            float pdfHeight = inlinePageSize.getHeight();
                            String signType = "0"; //默认竖版
                            if(pdfWidth>pdfHeight){  //横版
                                signType = "1";
                            }
                            if((inlinePageSize.getRotation()/90)%2==1){
                                pdfHeight = inlinePageSize.getWidth();
                                pdfWidth = inlinePageSize.getHeight();
                                signType = "0".equals(signType)?"1":"0";
                            }
                            double imgZoomWidth = item.getImgWidth(); //縮放後寬度
                            String imgShowType = item.getImgShowType();  //簽名顯示位置類型  1-默認 垂直顯示  2-簽名居左時間與備註居右 3-簽名居左時間居右備註居下
                            float wholeHeightRate = 1;

                            if(imgShowType!=null&&imgZoomWidth>0){
                                if("1".equals(imgShowType)){
                                    signDivWidth = (float) imgZoomWidth;
                                }
                                if("2".equals(imgShowType)){
                                    signDivWidth = (float) (imgZoomWidth/2);
                                    wholeHeightRate = (float) 0.25;
                                }
                                if("3".equals(imgShowType)){
                                    signDivWidth = (float) (imgZoomWidth/2);
                                    wholeHeightRate = (float) 0.375;
                                }
                            }
                            float signatureWidth = pdfWidth*signDivWidth/viewWidth; //簽名圖片寬度
                            float signatureHeight = signatureWidth/rate; //簽名圖片高度

                            float factWidth = (float) (pdfWidth*imgZoomWidth/viewWidth); //整體寬度
                            float factHeight = factWidth*wholeHeightRate; //整體高度

                            float signaturePositionX = (float) (pdfWidth*x/viewWidth); //簽名圖片坐標X
                            float signaturePositionY = (float) (pdfHeight - (pdfWidth*y/viewWidth) );  //簽名圖片坐標Y

                            if (0==item.getFlag()) { //未簽核
                                if(!"4".equals(applyStatus)&&!"1".equals(fileType)){ //駁回狀態未簽核節點不顯示
                                    setSignDiv(font, factWidth, factHeight, signaturePositionX, signaturePositionY, item, page,imgShowType);
                                }
                            } else if (1==item.getFlag()) { //當前節點
                                if("0".equals(fileType)) {
                                    setSignDiv(font, factWidth, factHeight, signaturePositionX, signaturePositionY, item, page, imgShowType);
                                }
                            }else if(3==item.getFlag()){ //駁回節點
                                //applyStatus  0-駁回 1-其他
                                buildWordFromSource1(font, signatureWidth, signatureHeight, signaturePositionX, signaturePositionY, item.getSignName(), page,signType);
                            } else{  //已簽核節點
                                if (item.getImgUrl() == null || "".equals(item.getImgUrl())) { //未上傳簽名圖片則 顯示文字
                                    buildWordFromSource(font, signatureWidth, signatureHeight, signaturePositionX, signaturePositionY, item.getSignName(), page, 25, "");
                                } else { //已上傳簽名 顯示簽名圖片
                                    Image image = buildImageFromSource(signatureWidth, signatureHeight, signaturePositionX, signaturePositionY, item);  //獲取圖片 並確定圖片顯示位置
                                    page.addImage(image);
                                }
                            }
                            if("Y".equals(item.getWhetherToSign())){ //审核为代签 添加代字
                                Image image = addAgencyMark(signatureWidth, signatureHeight, signaturePositionX, signaturePositionY);
                                page.addImage(image);
                            }
                            if(imgShowType==null||"".equals(imgShowType)){
                                imgShowType = "1";
                            }
                            //簽核時間
                            buildWordFromSource(font, signatureWidth, signatureHeight, signaturePositionX, signaturePositionY, item.getSignTime(), page,11,imgShowType);
                            String remarkShow = item.getRemarkShow();
                            //批註
                            if(item.getDecrib()!=null&&!"".equals(item.getDecrib())&&!"N".equals(remarkShow)&&!"Z".equals(remarkShow)){
                                buildWordFromSource(font, signatureWidth, signatureHeight, signaturePositionX, signaturePositionY, item.getDecrib(), page,8,imgShowType+"b");
                            }
                        }
                    }

                }
            }
            stamper.close();
            resutl = bos.toByteArray();
        }catch(Exception e){
            e.printStackTrace();
        }finally {
            if(bos != null){
                bos.flush();
                bos.close();
            }
        }
        return  resutl;
    }


    //創建圖片並確定圖片大小及位置
    private static Image buildImageFromSource(float signatureWidth, float signatureHeight, float signaturePositionX, float signaturePositionY, SignModelPositionDto item) throws DocumentException {
        try {
            Image image = Image.getInstance(item.getImgBytes());
            image.setAlignment(Image.LEFT);
            if (1==item.getFlag()) {
                image.setBorder(Image.BOX);
                image.setBorderWidth(3f);
                image.setBorderColor(new BaseColor(252, 252, 4));
            } else if (0==item.getFlag()) {
                image.setBorder(Image.BOX);
                image.setBorderWidth(1f);
                image.setBorderColor(BaseColor.GREEN);
            }else{
                image.setBorder(Image.NO_BORDER);
            }
            image.setAbsolutePosition(signaturePositionX, signaturePositionY-signatureHeight); //簽名位置
            image.scaleAbsolute(signatureWidth, signatureHeight);  //簽名圖片大小
            return image;
        } catch (BadElementException | IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    //顯示文字  簽名文字/審核時間/備註
    private static void buildWordFromSource(BaseFont fontStyle,float signatureWidth, float signatureHeight, float signaturePositionX, float signaturePositionY, String time, PdfContentByte cb , int fontSize,String imgShowType) throws DocumentException {
        if (StrUtil.isNotEmpty(time)) {
            fontSize = (int) (signatureWidth * fontSize / 75);
            ColumnText columnText = new ColumnText(cb);
            //x,y,z,w  x-左下橫坐標 y-左下縱坐標  z-右上橫坐標 w-右上縱坐標
            float x = signaturePositionX;
            float y = signaturePositionY - signatureHeight ;
            float z = signaturePositionX + signatureWidth;
            float w = signaturePositionY ;
            // 坐標加減字體大小的倍數為對審核時間和備註的微調
            if (StrUtil.isNotEmpty(imgShowType)) {
                if("1".equals(imgShowType)){ //簽核時間
                    w = y + fontSize/2;
                    y = y - signatureHeight/2 + fontSize/2;
                }
                if("2".equals(imgShowType)){ //簽核時間
                    x = x + signatureWidth;
                    y = y + signatureHeight/2;
                    z = z + signatureWidth;
                }
                if("3".equals(imgShowType)){ //簽核時間
                    x = x + signatureWidth;
                    y = y + fontSize/2;
                    z = z + signatureWidth;
                    w = w - signatureHeight/2 + fontSize/2;
                }
                if("1b".equals(imgShowType)){ //備註
                    w = y - signatureHeight/2 + fontSize ;
                    y = y - signatureHeight + fontSize;
                }
                if("2b".equals(imgShowType)){ //備註
                    x = x + signatureWidth;
                    z = z + signatureWidth;
                    w = w - signatureHeight/2;
                }
                if("3b".equals(imgShowType)){ //備註
                    w = y  + fontSize/2;
                    y = y - signatureHeight/2 + fontSize/2;
                    z = z + signatureWidth;
                }
            }else{
                y = y + fontSize/3;
                w = w + fontSize/3;
            }
            if(fontSize>30){
                fontSize = fontSize -1;
            }
            columnText.setSimpleColumn(x, y, z, w);
            Chunk elements = new Chunk(time);
            Font font = new Font(fontStyle, fontSize, Font.NORMAL, BaseColor.BLACK);
            elements.setFont(font);
            columnText.addElement(elements);
            columnText.go();
        }
    }

    //備註
    private static void buildWordFromSource1(BaseFont fontStyle,float signatureWidth, float signatureHeight, float signaturePositionX, float signaturePositionY, String time, PdfContentByte cb , String signType) throws DocumentException {
        //姓名與駁回二字在同一行
        if (StrUtil.isNotEmpty(time)) {
            ColumnText columnText = new ColumnText(cb);
            columnText.setSimpleColumn(signaturePositionX, signaturePositionY-signatureHeight, signaturePositionX +signatureWidth, signaturePositionY);

            Paragraph elements = new Paragraph(time);
            if ("1".equals(signType)) {  //駁回
                Font font = new Font(fontStyle, (int) (signatureWidth*18/(75)), Font.NORMAL, BaseColor.BLACK);
                elements.setFont(font);
                Chunk elements3 = new Chunk(" ");
                Chunk elements2 = new Chunk("駁回");

                Font font2 = new Font(fontStyle,  (int) (signatureWidth*14/(75)), Font.BOLD, BaseColor.RED);
                elements2.setFont(font2);
                elements.add(elements3);
                elements.add(elements2);
            }else{
                Font font = new Font(fontStyle,  (int) (signatureWidth*17/(75)), Font.NORMAL, BaseColor.BLACK);
                elements.setFont(font);
                Chunk elements3 = new Chunk(" ");
                Chunk elements2 = new Chunk("駁回");

                Font font2 = new Font(fontStyle,  (int) (signatureWidth*10/(75)), Font.BOLD, BaseColor.RED);
                elements2.setFont(font2);
                elements.add(elements3);
                elements.add(elements2);
            }
            columnText.addElement(elements);
            columnText.go();
        }
    }

    //正在簽核 與未簽核塊
    private static void setSignDiv(BaseFont fontStyle,float signatureWidth, float signatureHeight, float signaturePositionX, float signaturePositionY, SignModelPositionDto item, PdfContentByte cb,String imgShowType) {
        PdfPTable tableDetail = new PdfPTable(1);// 2列的表格以及单元格的宽度。
        tableDetail.setTotalWidth(signatureWidth);
        PdfPCell cell = new PdfPCell();
        cell.setBorder(Rectangle.NO_BORDER);
        cell.setCellEvent(new PdfUtil.CustomCellRight(item.getFlag()));
        cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
        cell.setFixedHeight(signatureHeight);
        cell.setPaddingTop(-10);
        int cols = 1;
        if("2".equals(imgShowType)||"3".equals(imgShowType)){
            cols = 2;
        }
        Paragraph elements = new Paragraph();
        BaseColor GRAY1 = new BaseColor(140, 140, 140);
        int fontSize = (int) (signatureWidth*12/(75*cols));
        Font font = new Font(fontStyle, fontSize, Font.NORMAL, GRAY1);
        Chunk chunk = new Chunk(item.getSignName(),font);
        chunk.setLineHeight((float) (fontSize*1.5));
        elements.add(chunk);
        elements.setAlignment(Element.ALIGN_CENTER);
        int fontSize1 = (int) (signatureWidth*10/(75*cols));

        Font font1 = new Font(fontStyle, fontSize1, Font.NORMAL, GRAY1);
        Chunk chunk1 = new Chunk("正在簽核",font1);
        chunk1.setLineHeight((float) (fontSize1*1.5));
        cell.setBackgroundColor(BaseColor.YELLOW);
        if (0 == item.getFlag()) {
            chunk1 = new Chunk("未簽核",font1);
            cell.setBackgroundColor(BaseColor.WHITE);
        }
        if(cols==2){
            elements.setAlignment(Element.ALIGN_CENTER);
            Phrase director2 = new Phrase();
            director2.add("     ");
            director2.add(chunk1);
            elements.add(director2);
            cell.addElement(elements);
        }else{
            elements.add(Chunk.NEWLINE);
            elements.add(chunk1);
            elements.setAlignment(Element.ALIGN_CENTER);
            cell.addElement(elements);
        }
        tableDetail.addCell(cell);
        tableDetail.writeSelectedRows(0, -1, signaturePositionX, signaturePositionY, cb);
    }

    private static Image addAgencyMark(float signatureWidth, float signatureHeight, float signaturePositionX, float signaturePositionY) throws IOException, BadElementException {
        Image image = Image.getInstance(EditPdf.class.getClassLoader().getResource("./agent_mark.png"));
        image.setAlignment(Image.LEFT);
        image.setBorder(Image.NO_BORDER);
        image.setAbsolutePosition(signaturePositionX + signatureWidth - (float)(signatureWidth*2.5/10), signaturePositionY  - (float)(signatureWidth*2.5/10)); //簽名位置
        image.scaleAbsolute( (float)(signatureWidth*2.5/10),  (float)(signatureWidth*2.5/10));  //簽名圖片大小
        return image;
    }
}
