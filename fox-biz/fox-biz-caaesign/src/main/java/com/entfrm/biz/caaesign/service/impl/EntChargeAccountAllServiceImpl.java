package com.entfrm.biz.caaesign.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.entfrm.biz.activiti.service.TQhAllRelationService;
import com.entfrm.biz.activiti.service.TaskService;
import com.entfrm.biz.activiti.service.WfConfigService;
import com.entfrm.biz.caaesign.entity.EntChargeAccountAll;
import com.entfrm.biz.caaesign.entity.EntChargeItems;
import com.entfrm.biz.caaesign.mapper.EntChargeAccountAllMapper;
import com.entfrm.biz.caaesign.mapper.EntChargeItemsMapper;
import com.entfrm.biz.caaesign.service.EntChargeAccountAllService;
import com.entfrm.biz.caaesign.service.EntChargeItemsService;
import lombok.AllArgsConstructor;
import org.activiti.engine.RuntimeService;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2022-12-28 13:42:41
 *
 * @description 經管類系統賬號申請表從表Service业务层
 */
@Service
@AllArgsConstructor
public class EntChargeAccountAllServiceImpl extends ServiceImpl<EntChargeAccountAllMapper, EntChargeAccountAll> implements EntChargeAccountAllService {
	private final RuntimeService runtimeService;
	private final org.activiti.engine.TaskService actTaskService;
	private final WfConfigService wfConfigService;
	private final TQhAllRelationService allRelationService;
	private final RabbitTemplate rabbitTemplate;
	private final TaskService taskService;
    }
