package com.entfrm.biz.esignsys.cotroller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.entfrm.biz.esignsys.dto.EsignTQhUserformhsDto;
import com.entfrm.biz.esignsys.dto.WebLoginDto;
import com.entfrm.biz.esignsys.entity.EsignTPubFileobjectEntity;
import com.entfrm.biz.esignsys.entity.EsignTQhUserformhsEntity;
import com.entfrm.biz.esignsys.entity.EsignUser;
import com.entfrm.biz.esignsys.service.EsignDictService;
import com.entfrm.biz.esignsys.service.EsignFileobjectService;
import com.entfrm.biz.esignsys.service.EsignTQhUserformhsService;
import com.entfrm.biz.esignsys.service.EsignUserService;
import com.entfrm.biz.esignsys.util.DateUtils;
import com.entfrm.biz.esignsys.util.FTPUtil;
import com.entfrm.biz.feign.FeignDmzService;
import com.entfrm.biz.feign.FeignService;
import com.entfrm.biz.system.service.ConfigService;
import com.entfrm.biz.system.util.JsonUtils;
import com.entfrm.core.base.api.R;
import com.entfrm.core.base.util.AESUtil;
import com.entfrm.core.base.util.IPUtil;
import com.entfrm.core.security.entity.EntfrmUser;
import com.entfrm.core.security.util.SecurityUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2020/3/14
 * @description token 管理
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/esignSystem")
public class SystemController {

    @Autowired
    private EsignUserService esignUserService;
    @Autowired
    private ConfigService configService;
    @Autowired
    private EsignDictService dictService;
    @Autowired
    private EsignFileobjectService fileobjectService;
    @Autowired
    private FeignDmzService feignDmzService;

    public static final String NEWESIGN_DOWNLOAD_OOS_URL = "/apiForDmz/newEsign/downloadWithOos";

    public static final String SIGNAUTUREIMG_ROOT_PATH = "userAvatarImg/pc";

    @RequestMapping(value ="updatePassword", method = RequestMethod.POST)
    @ResponseBody
    public Object updatePassword(@RequestParam String oldPassword, @RequestParam String newPassword){

        EntfrmUser user = SecurityUtil.getUser();
        //電子簽核平台登錄本地賬號
        boolean login = esignUserService.updatePassword(user.getUsername(), oldPassword,newPassword);
        if (login) {
            return R.ok();
        }
        return R.error();
    }

    @RequestMapping(value ="updateNotification", method = RequestMethod.POST)
    @ResponseBody
    public Object updateNotification(@RequestParam String notification) {

        EntfrmUser user = SecurityUtil.getUser();
        //電子簽核平台登錄本地賬號
        boolean login = esignUserService.updateNotification(user.getUsername(), notification);
        if (login) {
            return R.ok();
        }
        return R.error();
    }


    /**
     * 獲取頭像文件的名字
     * @param empNo
     * @return
     */
    private String getAvatarStr(String empNo){
        return empNo + "-avatar";
    }

    @RequestMapping("/uploadAvatar")
    public R uploadAvatar(HttpServletRequest request,
                             @RequestParam(value = "files") MultipartFile[] files) throws Exception {
        String empNo = SecurityUtil.getUser().getUsername();
        String avatarStr = getAvatarStr(empNo);
        if(empNo==null||"".equals(empNo)){
            return R.error("无頭像信息");
        }
        EsignTPubFileobjectEntity entity = new EsignTPubFileobjectEntity();
        List<EsignTPubFileobjectEntity> entitys = fileobjectService.list(new QueryWrapper<EsignTPubFileobjectEntity>()
                .eq("NAME", avatarStr));
        if(entitys!=null&&entitys.size()>0){
            entity = entitys.get(0);
        }
        MultipartFile file = null;
        if(files!=null&&files.length>0){
            file = files[0];
        }
        String fileName = file.getOriginalFilename();
        //扩展名
        String fileExtensionName = fileName.substring(fileName.lastIndexOf('.') + 1);
        if(!"png".equals(fileExtensionName)&&!"jpg".equals(fileExtensionName)){
            return R.error("頭像格式错误");
        }
        //使用UUID防止文件名重复，覆盖别人的文件
        String uploadFileName = UUID.randomUUID().toString();

        try {
//            byte[] img = TransferAlpha.transferAlpha2Byte(file.getInputStream());
//            inputStreamMap.put(uploadFileName,new ByteArrayInputStream(img));
            //到此为止，文件已经上传服务器成功
            /**
             *生成路徑
             */
            String path = SIGNAUTUREIMG_ROOT_PATH + "/" + DateUtil.format(new Date(), "yyyy/MM/dd");
            //把文件上传到FTP服务器,与FTP文件服务器对接
//            feignDmzService.newEsignUploadSignature(ftpIp,ftpPort,ftpUser,ftpPass,rootPath,URL,uploadFileName,file);
            //把文件上传到OOS服务器,与FTP文件服务器对接
            feignDmzService.uploadWithOos(file,uploadFileName,path);
            //已将文件上传FTP
            Date date = new Date();
            //上传完之后，删除upload下面的文件
            int fileSize = new Long(file.getSize()).intValue();
            entity.setName(avatarStr);
            entity.setUploader(empNo);
            entity.setUrl("/" + path + "/" + uploadFileName+ "." + fileExtensionName);
            entity.setIp(IPUtil.getIpAddress(request));
            entity.setType(fileExtensionName);
            entity.setSizez(fileSize);
            entity.setCreatedate(DateUtils.getNowTime("yyyy-MM-dd HH:mm:ss"));
            entity.setCreateBy(empNo);
            entity.setCreateDate(date);
            entity.setUpdateBy(empNo);
            entity.setUpdateDate(date);
            if(ObjectUtils.isNotNull(entity.getId())){
                fileobjectService.updateById(entity);
            }else{
                fileobjectService.save(entity);
            }
        } catch (Exception e) {
            return R.error("上传頭像失败");
        }
        return R.ok(null,"上传成功");

    }

    @RequestMapping("showAvatarImg")
    public void showAvatarImg(HttpServletResponse response) throws Exception {
        String empNo = SecurityUtil.getUser().getUsername();
        String avatarStr = getAvatarStr(empNo);
        EsignTPubFileobjectEntity entity = null;
        List<EsignTPubFileobjectEntity> entitys = fileobjectService.list(new QueryWrapper<EsignTPubFileobjectEntity>()
                .eq("NAME", avatarStr));
        if (entitys != null && entitys.size() > 0) {
            entity = entitys.get(0);
        }else{
            entitys = fileobjectService.list(new QueryWrapper<EsignTPubFileobjectEntity>()
                    .eq("NAME", "avatar_default"));
            if (entitys != null && entitys.size() > 0) {
                entity = entitys.get(0);
            }
        }
        if(ObjectUtil.isNotNull(entity)) {
            String rootPath = dictService.getById(22873).getValue();
            String ssoEndpoint = configService.getValueByKey("apisix.base.url");
            Map<String, Object> param = new HashMap<>();
            param.put("filePath", entity.getUrl());
            param.put("directory", rootPath);
            final byte[] bytes = HttpRequest
                    .get(ssoEndpoint + NEWESIGN_DOWNLOAD_OOS_URL)
                    .form(param)
                    .execute()
                    .bodyBytes();
            FTPUtil.downSignatureByByte(response, bytes);
        }
    }

    @RequestMapping("showSignatureImgWithEmpNo")
    public void showSignatureImgWithEmpNo(HttpServletResponse response,String empNo) throws Exception {
        EsignTPubFileobjectEntity entity = new EsignTPubFileobjectEntity();
        List<EsignTPubFileobjectEntity> entitys = fileobjectService.list(new QueryWrapper<EsignTPubFileobjectEntity>()
                .eq(StrUtil.isNotBlank(empNo), "NAME", empNo));
        if (entitys != null && entitys.size() > 0) {
            entity = entitys.get(0);
        }
        String rootPath = dictService.getById(22873).getValue();
        String ssoEndpoint = configService.getValueByKey("apisix.base.url");
        Map<String, Object> param = new HashMap<>();
        param.put("filePath", entity.getUrl());
        param.put("directory", rootPath);
        final byte[] bytes = HttpRequest
                .get(ssoEndpoint + NEWESIGN_DOWNLOAD_OOS_URL)
                .form(param)
                .execute()
                .bodyBytes();
        FTPUtil.downSignatureByByte(response,bytes);
    }
}
