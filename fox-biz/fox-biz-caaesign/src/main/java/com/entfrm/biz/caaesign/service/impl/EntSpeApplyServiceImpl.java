package com.entfrm.biz.caaesign.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import com.baomidou.mybatisplus.annotation.TableName;
import com.entfrm.biz.activiti.service.WfConfigService;
import com.entfrm.biz.activiti.entity.TQhAllRelation;
import com.entfrm.biz.activiti.service.TQhAllRelationService;
import com.entfrm.biz.activiti.service.TaskService;
import com.entfrm.biz.activiti.dto.LeaveDto;
import org.springframework.stereotype.Service;
import com.entfrm.biz.caaesign.mapper.EntSpeApplyMapper;
import com.entfrm.biz.caaesign.entity.EntSpeApply;
import com.entfrm.biz.caaesign.service.EntSpeApplyService;
import com.entfrm.core.base.util.PinyinUtil;
import com.entfrm.core.data.util.SerialNumberTool;
import cn.hutool.core.date.DateUtil;
import lombok.AllArgsConstructor;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.runtime.ProcessInstance;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.transaction.annotation.Transactional;
import com.entfrm.core.base.enums.TaskStatusEnum;
import java.util.Map;
import com.entfrm.core.data.constant.CommonConstant;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.io.Serializable;
import com.entfrm.biz.caaesign.entity.EntSpeApplyItems;
import com.entfrm.biz.caaesign.service.EntSpeApplyItemsService;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import java.util.Collection;
import com.entfrm.biz.activiti.dto.WfConfigDto;
import java.lang.reflect.Field;
import com.google.common.collect.Maps;
import org.activiti.engine.task.Task;
import com.entfrm.core.security.util.SecurityUtil;
import com.entfrm.core.base.annotation.SignNode;
import java.util.Arrays;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.bean.BeanUtil;

/**
 * <AUTHOR>
 * @date 2024-06-04 10:12:31
 *
 * @description 特殊就餐申請單Service业务层
 */
@Service
@AllArgsConstructor
public class EntSpeApplyServiceImpl extends ServiceImpl<EntSpeApplyMapper, EntSpeApply> implements EntSpeApplyService {
	private final RuntimeService runtimeService;
	private final org.activiti.engine.TaskService actTaskService;
	private final WfConfigService wfConfigService;
	private final TQhAllRelationService allRelationService;
    private final RabbitTemplate rabbitTemplate;
	private final TaskService taskService;

    private final EntSpeApplyItemsService entSpeApplyItemsService;

    	/**
	 * 启动流程
	 *
	 * @param entSpeApply
	 * @return
	 */
	@Override
	public Boolean startProcess(EntSpeApply entSpeApply) {
        entSpeApply.setWorkStatus(Integer.parseInt(TaskStatusEnum.CHECKING.getStatus()));
        String tableName = EntSpeApply.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
        String procDefKey = wfConfigDto.getProcDefKey();
        String businessKey = procDefKey + CommonConstant.BREAK_BUSINESS_KEY + entSpeApply.getId();
        Field fields[] = entSpeApply.getClass().getDeclaredFields();
        Map variables = Maps.newHashMap();
        //設置填單人
        variables.put("maker", SecurityUtil.getUser().getUsername());
        variables.put("serialNo", entSpeApply.getSerialno());
        variables.put("workflowName", wfConfigDto.getFormName());
        Arrays.stream(fields).forEach(field -> {
                SignNode signNode = field.getAnnotation(SignNode.class);
                if (ObjectUtil.isNotEmpty(signNode)) {
                    if (signNode.type().equals(SignNode.Type.COMMON)) {
                        variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entSpeApply, field)) ? "AUTO_SIGN" : ReflectUtil.getFieldValue(entSpeApply, field));
                    } else if (signNode.type().equals(SignNode.Type.HUIQIAN)) {
                        variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entSpeApply, field)) ? Arrays.asList("AUTO_SIGN") : Arrays.asList(ReflectUtil.getFieldValue(entSpeApply, field).toString().split(",")));
                    }
                }
            }
        );
        ProcessInstance pi = runtimeService.startProcessInstanceByKey(procDefKey, businessKey,variables);
        entSpeApply.setProcessId(pi.getProcessInstanceId());
        this.updateById(entSpeApply);

        TQhAllRelation relation = allRelationService.getOne(new QueryWrapper<TQhAllRelation>().eq("serialno",entSpeApply.getSerialno()));
        relation.setWorkStatus(entSpeApply.getWorkStatus());
        relation.setProcessId(pi.getProcessInstanceId());
        Task task = actTaskService.createTaskQuery().processInstanceId(pi.getProcessInstanceId()).singleResult();
        relation.setNodename(task.getName());
        relation.setDeptNo(entSpeApply.getApplyDeptNo());
        allRelationService.updateById(relation);
        //添加mq
        rabbitTemplate.convertAndSend("SignDirectExchange", "Sign.Direct.Routing", BeanUtil.beanToMap(relation));
        return Boolean.TRUE;
	}

    /**
     * 插入一条记录（选择字段，策略插入）
     *
     * @param entSpeApply 实体对象
     */
    @Override
    public boolean save(EntSpeApply entSpeApply) {
        boolean rs = super.save(entSpeApply);
        saveChild(entSpeApply);
	    String tableName = EntSpeApply.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
        TQhAllRelation relation = new TQhAllRelation();
        relation.setDtoName(tableName);
        relation.setMakerNo(entSpeApply.getMakerNo());
        relation.setMakerName(entSpeApply.getMakerName());
        relation.setSerialno(entSpeApply.getSerialno());
        relation.setWorkflowid(wfConfigDto.getProcDefKey());
        relation.setWfName(wfConfigDto.getFormName());
        relation.setClassPackage(entSpeApply.getClass().getName());
        relation.setWorkStatus(0);
        allRelationService.save(relation);
        return rs;
    }
	@Override
	public Boolean updateAndCheck(EntSpeApply entSpeApply, LeaveDto leaveDto) {
		try {
			this.updateById(entSpeApply);
			taskService.checkTask(leaveDto);
		} catch (Exception e) {
			return false;
		}
		return true;
	}
    /**
     * 插入一条记录（选择字段，策略插入）
     *
     * @param entSpeApply 实体对象
     */
    @Override
    public boolean updateById(EntSpeApply entSpeApply) {
        // 删除子表信息关联
        entSpeApplyItemsService.remove(new QueryWrapper<EntSpeApplyItems>().eq("pid", entSpeApply.getId()));
        boolean rs = super.updateById(entSpeApply);
        // 新增子表信息管理
        if(rs){
            // 新增子表信息管理
            saveChild(entSpeApply);
        }
        return rs;
    }
    /**
     * 新增子表記錄
     *
     * @param entSpeApply 表單对象
     */
    public void saveChild(EntSpeApply entSpeApply) {
        List<EntSpeApplyItems> entSpeApplyItemsLists = entSpeApply.getEntSpeApplyItemsLists();
        if (entSpeApplyItemsLists != null) {
            for (EntSpeApplyItems entSpeApplyItems : entSpeApplyItemsLists) {
                entSpeApplyItems.setId(null);
                entSpeApplyItems.setPid(entSpeApply.getId());
                entSpeApplyItemsService.save(entSpeApplyItems);
            }
        }
    }

    /**
     * 删除（根据ID 批量删除）
     *
     * @param idList 主键ID列表
     */
    @Override
    public boolean removeByIds(Collection<? extends Serializable> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return false;
        }
        for (Serializable id : idList) {
            EntSpeApply entSpeApply = getById(id);
            // 删除子表信息关联
            entSpeApplyItemsService.remove(new QueryWrapper<EntSpeApplyItems>().eq("pid", entSpeApply.getId()));
            }

        return SqlHelper.retBool(getBaseMapper().deleteBatchIds(idList));
    }
    /**
     * 根據ID獲取信息
     *
     * @param id
     * @return
     */
    @Override
    public EntSpeApply getById(Serializable id) {
        EntSpeApply entSpeApply = super.getById(id);
        setChilds(entSpeApply);
        return entSpeApply;
    }

    /**
     * 獲取子表列表（${childTable.tableComment}）
     */
    public void setChilds(EntSpeApply entSpeApply){

    List<EntSpeApplyItems> entSpeApplyItemsLists = entSpeApplyItemsService.list(new QueryWrapper<EntSpeApplyItems>().eq("pid", entSpeApply.getId()));
    entSpeApply.setEntSpeApplyItemsLists(entSpeApplyItemsLists);
    }
}
