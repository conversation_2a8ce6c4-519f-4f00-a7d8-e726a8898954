package com.entfrm.biz.caaesign.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import com.baomidou.mybatisplus.annotation.TableName;
import com.entfrm.biz.activiti.service.WfConfigService;
import com.entfrm.biz.activiti.entity.TQhAllRelation;
import com.entfrm.biz.activiti.service.TQhAllRelationService;
import com.entfrm.biz.activiti.service.TaskService;
import com.entfrm.biz.activiti.dto.LeaveDto;
import org.springframework.stereotype.Service;
import com.entfrm.biz.caaesign.mapper.EntDmsAccessMapper;
import com.entfrm.biz.caaesign.entity.EntDmsAccess;
import com.entfrm.biz.caaesign.service.EntDmsAccessService;
import com.entfrm.core.base.util.PinyinUtil;
import com.entfrm.core.data.util.SerialNumberTool;
import cn.hutool.core.date.DateUtil;
import lombok.AllArgsConstructor;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.runtime.ProcessInstance;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.transaction.annotation.Transactional;
import com.entfrm.core.base.enums.TaskStatusEnum;
import java.util.Map;
import com.entfrm.core.data.constant.CommonConstant;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.io.Serializable;
import com.entfrm.biz.caaesign.entity.EntDmsAccessItems;
import com.entfrm.biz.caaesign.service.EntDmsAccessItemsService;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import java.util.Collection;
import com.entfrm.biz.activiti.dto.WfConfigDto;
import java.lang.reflect.Field;
import com.google.common.collect.Maps;
import org.activiti.engine.task.Task;
import com.entfrm.core.security.util.SecurityUtil;
import com.entfrm.core.base.annotation.SignNode;
import java.util.Arrays;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.bean.BeanUtil;

/**
 * <AUTHOR>
 * @date 2024-07-16 13:33:10
 *
 * @description 宿舍區門禁權限管制申請表Service业务层
 */
@Service
@AllArgsConstructor
public class EntDmsAccessServiceImpl extends ServiceImpl<EntDmsAccessMapper, EntDmsAccess> implements EntDmsAccessService {
	private final RuntimeService runtimeService;
	private final org.activiti.engine.TaskService actTaskService;
	private final WfConfigService wfConfigService;
	private final TQhAllRelationService allRelationService;
    private final RabbitTemplate rabbitTemplate;
	private final TaskService taskService;

    private final EntDmsAccessItemsService entDmsAccessItemsService;

    	/**
	 * 启动流程
	 *
	 * @param entDmsAccess
	 * @return
	 */
	@Override
	public Boolean startProcess(EntDmsAccess entDmsAccess) {
        entDmsAccess.setWorkStatus(Integer.parseInt(TaskStatusEnum.CHECKING.getStatus()));
        String tableName = EntDmsAccess.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
        String procDefKey = wfConfigDto.getProcDefKey();
        String businessKey = procDefKey + CommonConstant.BREAK_BUSINESS_KEY + entDmsAccess.getId();
        Field fields[] = entDmsAccess.getClass().getDeclaredFields();
        Map variables = Maps.newHashMap();
        //設置填單人
        variables.put("maker", SecurityUtil.getUser().getUsername());
        variables.put("serialNo", entDmsAccess.getSerialno());
        variables.put("workflowName", wfConfigDto.getFormName());
        Arrays.stream(fields).forEach(field -> {
                SignNode signNode = field.getAnnotation(SignNode.class);
                if (ObjectUtil.isNotEmpty(signNode)) {
                    if (signNode.type().equals(SignNode.Type.COMMON)) {
                        variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entDmsAccess, field)) ? "AUTO_SIGN" : ReflectUtil.getFieldValue(entDmsAccess, field));
                    } else if (signNode.type().equals(SignNode.Type.HUIQIAN)) {
                        variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entDmsAccess, field)) ? Arrays.asList("AUTO_SIGN") : Arrays.asList(ReflectUtil.getFieldValue(entDmsAccess, field).toString().split(",")));
                    }
                }
            }
        );
        ProcessInstance pi = runtimeService.startProcessInstanceByKey(procDefKey, businessKey,variables);
        entDmsAccess.setProcessId(pi.getProcessInstanceId());
        this.updateById(entDmsAccess);

        TQhAllRelation relation = allRelationService.getOne(new QueryWrapper<TQhAllRelation>().eq("serialno",entDmsAccess.getSerialno()));
        relation.setWorkStatus(entDmsAccess.getWorkStatus());
        relation.setProcessId(pi.getProcessInstanceId());
        Task task = actTaskService.createTaskQuery().processInstanceId(pi.getProcessInstanceId()).singleResult();
        relation.setNodename(task.getName());
        relation.setDeptNo(entDmsAccess.getApplyDeptNo());
        allRelationService.updateById(relation);
        //添加mq
        rabbitTemplate.convertAndSend("SignDirectExchange", "Sign.Direct.Routing", BeanUtil.beanToMap(relation));
        return Boolean.TRUE;
	}

    /**
     * 插入一条记录（选择字段，策略插入）
     *
     * @param entDmsAccess 实体对象
     */
    @Override
    public boolean save(EntDmsAccess entDmsAccess) {
        boolean rs = super.save(entDmsAccess);
        saveChild(entDmsAccess);
	    String tableName = EntDmsAccess.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
        TQhAllRelation relation = new TQhAllRelation();
        relation.setDtoName(tableName);
        relation.setMakerNo(entDmsAccess.getMakerNo());
        relation.setMakerName(entDmsAccess.getMakerName());
        relation.setSerialno(entDmsAccess.getSerialno());
        relation.setWorkflowid(wfConfigDto.getProcDefKey());
        relation.setWfName(wfConfigDto.getFormName());
        relation.setClassPackage(entDmsAccess.getClass().getName());
        relation.setWorkStatus(0);
        allRelationService.save(relation);
        return rs;
    }
	@Override
	public Boolean updateAndCheck(EntDmsAccess entDmsAccess, LeaveDto leaveDto) {
		try {
			this.updateById(entDmsAccess);
			taskService.checkTask(leaveDto);
		} catch (Exception e) {
			return false;
		}
		return true;
	}
    /**
     * 插入一条记录（选择字段，策略插入）
     *
     * @param entDmsAccess 实体对象
     */
    @Override
    public boolean updateById(EntDmsAccess entDmsAccess) {
        // 删除子表信息关联
        entDmsAccessItemsService.remove(new QueryWrapper<EntDmsAccessItems>().eq("pid", entDmsAccess.getId()));
        boolean rs = super.updateById(entDmsAccess);
        // 新增子表信息管理
        if(rs){
            // 新增子表信息管理
            saveChild(entDmsAccess);
        }
        return rs;
    }
    /**
     * 新增子表記錄
     *
     * @param entDmsAccess 表單对象
     */
    public void saveChild(EntDmsAccess entDmsAccess) {
        List<EntDmsAccessItems> entDmsAccessItemsLists = entDmsAccess.getEntDmsAccessItemsLists();
        if (entDmsAccessItemsLists != null) {
            for (EntDmsAccessItems entDmsAccessItems : entDmsAccessItemsLists) {
                entDmsAccessItems.setId(null);
                entDmsAccessItems.setPid(entDmsAccess.getId());
                entDmsAccessItemsService.save(entDmsAccessItems);
            }
        }
    }

    /**
     * 删除（根据ID 批量删除）
     *
     * @param idList 主键ID列表
     */
    @Override
    public boolean removeByIds(Collection<? extends Serializable> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return false;
        }
        for (Serializable id : idList) {
            EntDmsAccess entDmsAccess = getById(id);
            // 删除子表信息关联
            entDmsAccessItemsService.remove(new QueryWrapper<EntDmsAccessItems>().eq("pid", entDmsAccess.getId()));
            }

        return SqlHelper.retBool(getBaseMapper().deleteBatchIds(idList));
    }
    /**
     * 根據ID獲取信息
     *
     * @param id
     * @return
     */
    @Override
    public EntDmsAccess getById(Serializable id) {
        EntDmsAccess entDmsAccess = super.getById(id);
        setChilds(entDmsAccess);
        return entDmsAccess;
    }

    /**
     * 獲取子表列表（${childTable.tableComment}）
     */
    public void setChilds(EntDmsAccess entDmsAccess){

    List<EntDmsAccessItems> entDmsAccessItemsLists = entDmsAccessItemsService.list(new QueryWrapper<EntDmsAccessItems>().eq("pid", entDmsAccess.getId()));
    entDmsAccess.setEntDmsAccessItemsLists(entDmsAccessItemsLists);
    }
}
