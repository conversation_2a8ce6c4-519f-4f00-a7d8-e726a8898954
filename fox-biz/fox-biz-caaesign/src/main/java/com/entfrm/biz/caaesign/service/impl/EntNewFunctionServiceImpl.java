package com.entfrm.biz.caaesign.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import com.baomidou.mybatisplus.annotation.TableName;
import com.entfrm.biz.activiti.service.WfConfigService;
import com.entfrm.biz.activiti.entity.TQhAllRelation;
import com.entfrm.biz.activiti.service.TQhAllRelationService;
import com.entfrm.biz.activiti.service.TaskService;
import com.entfrm.biz.activiti.dto.LeaveDto;
import org.springframework.stereotype.Service;
import com.entfrm.biz.caaesign.mapper.EntNewFunctionMapper;
import com.entfrm.biz.caaesign.entity.EntNewFunction;
import com.entfrm.biz.caaesign.service.EntNewFunctionService;
import com.entfrm.core.base.util.PinyinUtil;
import com.entfrm.core.data.util.SerialNumberTool;
import cn.hutool.core.date.DateUtil;
import lombok.AllArgsConstructor;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.runtime.ProcessInstance;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.transaction.annotation.Transactional;
import com.entfrm.core.base.enums.TaskStatusEnum;
import java.util.Map;
import com.entfrm.core.data.constant.CommonConstant;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.io.Serializable;
import com.entfrm.biz.caaesign.entity.EntNewFunctionItems1;
import com.entfrm.biz.caaesign.service.EntNewFunctionItems1Service;
import com.entfrm.biz.caaesign.entity.EntNewFunctionItems2;
import com.entfrm.biz.caaesign.service.EntNewFunctionItems2Service;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import java.util.Collection;
import com.entfrm.biz.activiti.dto.WfConfigDto;
import java.lang.reflect.Field;
import com.google.common.collect.Maps;
import org.activiti.engine.task.Task;
import com.entfrm.core.security.util.SecurityUtil;
import com.entfrm.core.base.annotation.SignNode;
import java.util.Arrays;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.bean.BeanUtil;

/**
 * <AUTHOR>
 * @date 2022-02-25 11:27:06
 *
 * @description 新功能服务需求申请单Service业务层
 */
@Service
@AllArgsConstructor
public class EntNewFunctionServiceImpl extends ServiceImpl<EntNewFunctionMapper, EntNewFunction> implements EntNewFunctionService {
	private final RuntimeService runtimeService;
	private final org.activiti.engine.TaskService actTaskService;
	private final WfConfigService wfConfigService;
	private final TQhAllRelationService allRelationService;
    private final RabbitTemplate rabbitTemplate;
	private final TaskService taskService;

    private final EntNewFunctionItems1Service entNewFunctionItems1Service;
    private final EntNewFunctionItems2Service entNewFunctionItems2Service;

    	/**
	 * 启动流程
	 *
	 * @param entNewFunction
	 * @return
	 */
	@Override
	public Boolean startProcess(EntNewFunction entNewFunction) {
        entNewFunction.setWorkStatus(Integer.parseInt(TaskStatusEnum.CHECKING.getStatus()));
        String tableName = EntNewFunction.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
        String procDefKey = wfConfigDto.getProcDefKey();
        String businessKey = procDefKey + CommonConstant.BREAK_BUSINESS_KEY + entNewFunction.getId();
        Field fields[] = entNewFunction.getClass().getDeclaredFields();
        Map variables = Maps.newHashMap();
        //設置填單人
        variables.put("maker", SecurityUtil.getUser().getUsername());
        variables.put("serialNo", entNewFunction.getSerialno());
        variables.put("workflowName", wfConfigDto.getFormName());
        Arrays.stream(fields).forEach(field -> {
                SignNode signNode = field.getAnnotation(SignNode.class);
                if (ObjectUtil.isNotEmpty(signNode)) {
                    if (signNode.type().equals(SignNode.Type.COMMON)) {
                        variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entNewFunction, field)) ? "AUTO_SIGN" : ReflectUtil.getFieldValue(entNewFunction, field));
                    } else if (signNode.type().equals(SignNode.Type.HUIQIAN)) {
                        variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entNewFunction, field)) ? Arrays.asList("AUTO_SIGN") : Arrays.asList(ReflectUtil.getFieldValue(entNewFunction, field).toString().split(",")));
                    }
                }
            }
        );
        ProcessInstance pi = runtimeService.startProcessInstanceByKey(procDefKey, businessKey,variables);
        entNewFunction.setProcessId(pi.getProcessInstanceId());
        this.updateById(entNewFunction);

        TQhAllRelation relation = allRelationService.getOne(new QueryWrapper<TQhAllRelation>().eq("serialno",entNewFunction.getSerialno()));
        relation.setWorkStatus(entNewFunction.getWorkStatus());
        relation.setProcessId(pi.getProcessInstanceId());
        Task task = actTaskService.createTaskQuery().processInstanceId(pi.getProcessInstanceId()).singleResult();
        relation.setNodename(task.getName());
        relation.setDeptNo(entNewFunction.getMakerdeptno());
        allRelationService.updateById(relation);
        //添加mq
        rabbitTemplate.convertAndSend("SignDirectExchange", "Sign.Direct.Routing", BeanUtil.beanToMap(relation));
        return Boolean.TRUE;
	}

    /**
     * 插入一条记录（选择字段，策略插入）
     *
     * @param entNewFunction 实体对象
     */
    @Override
    public boolean save(EntNewFunction entNewFunction) {
        boolean rs = super.save(entNewFunction);
        saveChild(entNewFunction);
	    String tableName = EntNewFunction.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
        TQhAllRelation relation = new TQhAllRelation();
        relation.setDtoName(tableName);
        relation.setMakerNo(entNewFunction.getMakerNo());
        relation.setMakerName(entNewFunction.getMakerName());
        relation.setSerialno(entNewFunction.getSerialno());
        relation.setWorkflowid(wfConfigDto.getProcDefKey());
        relation.setWfName(wfConfigDto.getFormName());
        relation.setClassPackage(entNewFunction.getClass().getName());
        relation.setWorkStatus(0);
        allRelationService.save(relation);
        return rs;
    }
	@Override
	public Boolean updateAndCheck(EntNewFunction entNewFunction, LeaveDto leaveDto) {
		try {
			this.updateById(entNewFunction);
			taskService.checkTask(leaveDto);
		} catch (Exception e) {
			return false;
		}
		return true;
	}
    /**
     * 插入一条记录（选择字段，策略插入）
     *
     * @param entNewFunction 实体对象
     */
    @Override
    public boolean updateById(EntNewFunction entNewFunction) {
        // 删除子表信息关联
        entNewFunctionItems1Service.remove(new QueryWrapper<EntNewFunctionItems1>().eq("pid", entNewFunction.getId()));
        // 删除子表信息关联
        entNewFunctionItems2Service.remove(new QueryWrapper<EntNewFunctionItems2>().eq("pid", entNewFunction.getId()));
        boolean rs = super.updateById(entNewFunction);
        // 新增子表信息管理
        if(rs){
            // 新增子表信息管理
            saveChild(entNewFunction);
        }
        return rs;
    }
    /**
     * 新增子表記錄
     *
     * @param entNewFunction 表單对象
     */
    public void saveChild(EntNewFunction entNewFunction) {
        List<EntNewFunctionItems1> entNewFunctionItems1Lists = entNewFunction.getEntNewFunctionItems1Lists();
        if (entNewFunctionItems1Lists != null) {
            for (EntNewFunctionItems1 entNewFunctionItems1 : entNewFunctionItems1Lists) {
                entNewFunctionItems1.setId(null);
                entNewFunctionItems1.setPid(entNewFunction.getId());
                entNewFunctionItems1Service.save(entNewFunctionItems1);
            }
        }
        List<EntNewFunctionItems2> entNewFunctionItems2Lists = entNewFunction.getEntNewFunctionItems2Lists();
        if (entNewFunctionItems2Lists != null) {
            for (EntNewFunctionItems2 entNewFunctionItems2 : entNewFunctionItems2Lists) {
                entNewFunctionItems2.setId(null);
                entNewFunctionItems2.setPid(entNewFunction.getId());
                entNewFunctionItems2Service.save(entNewFunctionItems2);
            }
        }
    }

    /**
     * 删除（根据ID 批量删除）
     *
     * @param idList 主键ID列表
     */
    @Override
    public boolean removeByIds(Collection<? extends Serializable> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return false;
        }
        for (Serializable id : idList) {
            EntNewFunction entNewFunction = getById(id);
            // 删除子表信息关联
            entNewFunctionItems1Service.remove(new QueryWrapper<EntNewFunctionItems1>().eq("pid", entNewFunction.getId()));
            // 删除子表信息关联
            entNewFunctionItems2Service.remove(new QueryWrapper<EntNewFunctionItems2>().eq("pid", entNewFunction.getId()));
            }

        return SqlHelper.retBool(getBaseMapper().deleteBatchIds(idList));
    }
    /**
     * 根據ID獲取信息
     *
     * @param id
     * @return
     */
    @Override
    public EntNewFunction getById(Serializable id) {
        EntNewFunction entNewFunction = super.getById(id);
        setChilds(entNewFunction);
        return entNewFunction;
    }

    /**
     * 獲取子表列表（${childTable.tableComment}）
     */
    public void setChilds(EntNewFunction entNewFunction){

    List<EntNewFunctionItems1> entNewFunctionItems1Lists = entNewFunctionItems1Service.list(new QueryWrapper<EntNewFunctionItems1>().eq("pid", entNewFunction.getId()));
    entNewFunction.setEntNewFunctionItems1Lists(entNewFunctionItems1Lists);
    List<EntNewFunctionItems2> entNewFunctionItems2Lists = entNewFunctionItems2Service.list(new QueryWrapper<EntNewFunctionItems2>().eq("pid", entNewFunction.getId()));
    entNewFunction.setEntNewFunctionItems2Lists(entNewFunctionItems2Lists);
    }
}
