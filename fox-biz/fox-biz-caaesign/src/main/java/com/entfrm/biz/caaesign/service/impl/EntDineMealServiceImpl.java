package com.entfrm.biz.caaesign.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import com.baomidou.mybatisplus.annotation.TableName;
import com.entfrm.biz.activiti.service.WfConfigService;
import com.entfrm.biz.activiti.entity.TQhAllRelation;
import com.entfrm.biz.activiti.service.TQhAllRelationService;
import com.entfrm.biz.activiti.service.TaskService;
import com.entfrm.biz.activiti.dto.LeaveDto;
import org.springframework.stereotype.Service;
import com.entfrm.biz.caaesign.mapper.EntDineMealMapper;
import com.entfrm.biz.caaesign.entity.EntDineMeal;
import com.entfrm.biz.caaesign.service.EntDineMealService;
import com.entfrm.core.base.util.PinyinUtil;
import com.entfrm.core.data.util.SerialNumberTool;
import cn.hutool.core.date.DateUtil;
import lombok.AllArgsConstructor;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.runtime.ProcessInstance;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.transaction.annotation.Transactional;
import com.entfrm.core.base.enums.TaskStatusEnum;
import java.util.Map;
import com.entfrm.core.data.constant.CommonConstant;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.io.Serializable;
import com.entfrm.biz.caaesign.entity.EntDineMealItems;
import com.entfrm.biz.caaesign.service.EntDineMealItemsService;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import java.util.Collection;
import com.entfrm.biz.activiti.dto.WfConfigDto;
import java.lang.reflect.Field;
import com.google.common.collect.Maps;
import org.activiti.engine.task.Task;
import com.entfrm.core.security.util.SecurityUtil;
import com.entfrm.core.base.annotation.SignNode;
import java.util.Arrays;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.bean.BeanUtil;

/**
 * <AUTHOR>
 * @date 2024-09-10 12:00:22
 *
 * @description iPEBG-iPEG濟源廠區就餐申請表Service业务层
 */
@Service
@AllArgsConstructor
public class EntDineMealServiceImpl extends ServiceImpl<EntDineMealMapper, EntDineMeal> implements EntDineMealService {
	private final RuntimeService runtimeService;
	private final org.activiti.engine.TaskService actTaskService;
	private final WfConfigService wfConfigService;
	private final TQhAllRelationService allRelationService;
    private final RabbitTemplate rabbitTemplate;
	private final TaskService taskService;

    private final EntDineMealItemsService entDineMealItemsService;

    	/**
	 * 启动流程
	 *
	 * @param entDineMeal
	 * @return
	 */
	@Override
	public Boolean startProcess(EntDineMeal entDineMeal) {
        entDineMeal.setWorkStatus(Integer.parseInt(TaskStatusEnum.CHECKING.getStatus()));
        String tableName = EntDineMeal.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
        String procDefKey = wfConfigDto.getProcDefKey();
        String businessKey = procDefKey + CommonConstant.BREAK_BUSINESS_KEY + entDineMeal.getId();
        Field fields[] = entDineMeal.getClass().getDeclaredFields();
        Map variables = Maps.newHashMap();
        //設置填單人
        variables.put("maker", SecurityUtil.getUser().getUsername());
        variables.put("serialNo", entDineMeal.getSerialno());
        variables.put("workflowName", wfConfigDto.getFormName());
        Arrays.stream(fields).forEach(field -> {
                SignNode signNode = field.getAnnotation(SignNode.class);
                if (ObjectUtil.isNotEmpty(signNode)) {
                    if (signNode.type().equals(SignNode.Type.COMMON)) {
                        variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entDineMeal, field)) ? "AUTO_SIGN" : ReflectUtil.getFieldValue(entDineMeal, field));
                    } else if (signNode.type().equals(SignNode.Type.HUIQIAN)) {
                        variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entDineMeal, field)) ? Arrays.asList("AUTO_SIGN") : Arrays.asList(ReflectUtil.getFieldValue(entDineMeal, field).toString().split(",")));
                    }
                }
            }
        );
        ProcessInstance pi = runtimeService.startProcessInstanceByKey(procDefKey, businessKey,variables);
        entDineMeal.setProcessId(pi.getProcessInstanceId());
        this.updateById(entDineMeal);

        TQhAllRelation relation = allRelationService.getOne(new QueryWrapper<TQhAllRelation>().eq("serialno",entDineMeal.getSerialno()));
        relation.setWorkStatus(entDineMeal.getWorkStatus());
        relation.setProcessId(pi.getProcessInstanceId());
        Task task = actTaskService.createTaskQuery().processInstanceId(pi.getProcessInstanceId()).singleResult();
        relation.setNodename(task.getName());
        relation.setDeptNo(entDineMeal.getApplyDeptNo());
        allRelationService.updateById(relation);
        //添加mq
        rabbitTemplate.convertAndSend("SignDirectExchange", "Sign.Direct.Routing", BeanUtil.beanToMap(relation));
        return Boolean.TRUE;
	}

    /**
     * 插入一条记录（选择字段，策略插入）
     *
     * @param entDineMeal 实体对象
     */
    @Override
    public boolean save(EntDineMeal entDineMeal) {
        boolean rs = super.save(entDineMeal);
        saveChild(entDineMeal);
	    String tableName = EntDineMeal.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
        TQhAllRelation relation = new TQhAllRelation();
        relation.setDtoName(tableName);
        relation.setMakerNo(entDineMeal.getMakerNo());
        relation.setMakerName(entDineMeal.getMakerName());
        relation.setSerialno(entDineMeal.getSerialno());
        relation.setWorkflowid(wfConfigDto.getProcDefKey());
        relation.setWfName(wfConfigDto.getFormName());
        relation.setClassPackage(entDineMeal.getClass().getName());
        relation.setWorkStatus(0);
        allRelationService.save(relation);
        return rs;
    }
	@Override
	public Boolean updateAndCheck(EntDineMeal entDineMeal, LeaveDto leaveDto) {
		try {
			this.updateById(entDineMeal);
			taskService.checkTask(leaveDto);
		} catch (Exception e) {
			return false;
		}
		return true;
	}
    /**
     * 插入一条记录（选择字段，策略插入）
     *
     * @param entDineMeal 实体对象
     */
    @Override
    public boolean updateById(EntDineMeal entDineMeal) {
        // 删除子表信息关联
        entDineMealItemsService.remove(new QueryWrapper<EntDineMealItems>().eq("pid", entDineMeal.getId()));
        boolean rs = super.updateById(entDineMeal);
        // 新增子表信息管理
        if(rs){
            // 新增子表信息管理
            saveChild(entDineMeal);
        }
        return rs;
    }
    /**
     * 新增子表記錄
     *
     * @param entDineMeal 表單对象
     */
    public void saveChild(EntDineMeal entDineMeal) {
        List<EntDineMealItems> entDineMealItemsLists = entDineMeal.getEntDineMealItemsLists();
        if (entDineMealItemsLists != null) {
            for (EntDineMealItems entDineMealItems : entDineMealItemsLists) {
                entDineMealItems.setId(null);
                entDineMealItems.setPid(entDineMeal.getId());
                entDineMealItemsService.save(entDineMealItems);
            }
        }
    }

    /**
     * 删除（根据ID 批量删除）
     *
     * @param idList 主键ID列表
     */
    @Override
    public boolean removeByIds(Collection<? extends Serializable> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return false;
        }
        for (Serializable id : idList) {
            EntDineMeal entDineMeal = getById(id);
            // 删除子表信息关联
            entDineMealItemsService.remove(new QueryWrapper<EntDineMealItems>().eq("pid", entDineMeal.getId()));
            }

        return SqlHelper.retBool(getBaseMapper().deleteBatchIds(idList));
    }
    /**
     * 根據ID獲取信息
     *
     * @param id
     * @return
     */
    @Override
    public EntDineMeal getById(Serializable id) {
        EntDineMeal entDineMeal = super.getById(id);
        setChilds(entDineMeal);
        return entDineMeal;
    }

    /**
     * 獲取子表列表（${childTable.tableComment}）
     */
    public void setChilds(EntDineMeal entDineMeal){

    List<EntDineMealItems> entDineMealItemsLists = entDineMealItemsService.list(new QueryWrapper<EntDineMealItems>().eq("pid", entDineMeal.getId()));
    entDineMeal.setEntDineMealItemsLists(entDineMealItemsLists);
    }
}
