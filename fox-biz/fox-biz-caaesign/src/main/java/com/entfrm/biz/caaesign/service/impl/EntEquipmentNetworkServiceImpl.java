package com.entfrm.biz.caaesign.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import com.baomidou.mybatisplus.annotation.TableName;
import com.entfrm.biz.activiti.service.WfConfigService;
import com.entfrm.biz.activiti.entity.TQhAllRelation;
import com.entfrm.biz.activiti.service.TQhAllRelationService;
import org.springframework.stereotype.Service;
import com.entfrm.biz.caaesign.mapper.EntEquipmentNetworkMapper;
import com.entfrm.biz.caaesign.entity.EntEquipmentNetwork;
import com.entfrm.biz.caaesign.service.EntEquipmentNetworkService;
import com.entfrm.core.base.util.PinyinUtil;
import com.entfrm.core.data.util.SerialNumberTool;
import cn.hutool.core.date.DateUtil;
import lombok.AllArgsConstructor;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.runtime.ProcessInstance;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.transaction.annotation.Transactional;
import com.entfrm.core.base.enums.TaskStatusEnum;
import java.util.Map;
import com.entfrm.core.data.constant.CommonConstant;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.io.Serializable;
import com.entfrm.biz.caaesign.entity.EntEquipmentItems;
import com.entfrm.biz.caaesign.service.EntEquipmentItemsService;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import java.util.Collection;
import com.entfrm.biz.activiti.dto.WfConfigDto;
import java.lang.reflect.Field;
import com.google.common.collect.Maps;
import org.activiti.engine.task.Task;
import com.entfrm.core.security.util.SecurityUtil;
import com.entfrm.core.base.annotation.SignNode;
import java.util.Arrays;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.bean.BeanUtil;

/**
 * <AUTHOR>
 * @date 2021-09-08 10:22:55
 *
 * @description 產線聯網設備入網申請單Service业务层
 */
@Service
@AllArgsConstructor
public class EntEquipmentNetworkServiceImpl extends ServiceImpl<EntEquipmentNetworkMapper, EntEquipmentNetwork> implements EntEquipmentNetworkService {
	private final RuntimeService runtimeService;
	private final org.activiti.engine.TaskService actTaskService;
	private final WfConfigService wfConfigService;
	private final TQhAllRelationService allRelationService;
    private final RabbitTemplate rabbitTemplate;

    private final EntEquipmentItemsService entEquipmentItemsService;

    	/**
	 * 启动流程
	 *
	 * @param entEquipmentNetwork
	 * @return
	 */
	@Override
	public Boolean startProcess(EntEquipmentNetwork entEquipmentNetwork) {
        entEquipmentNetwork.setWorkStatus(Integer.parseInt(TaskStatusEnum.CHECKING.getStatus()));
        String tableName = EntEquipmentNetwork.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
        String procDefKey = wfConfigDto.getProcDefKey();
        String businessKey = procDefKey + CommonConstant.BREAK_BUSINESS_KEY + entEquipmentNetwork.getId();
        Field fields[] = entEquipmentNetwork.getClass().getDeclaredFields();
        Map variables = Maps.newHashMap();
        //設置填單人
        variables.put("maker", SecurityUtil.getUser().getUsername());
        variables.put("serialNo", entEquipmentNetwork.getSerialno());
        variables.put("workflowName", wfConfigDto.getFormName());
        Arrays.stream(fields).forEach(field -> {
                    SignNode signNode = field.getAnnotation(SignNode.class);
                    /*if (ObjectUtil.isNotEmpty(signNode)) {
                        variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entEquipmentNetwork, field))?"AUTO_SIGN":ReflectUtil.getFieldValue(entEquipmentNetwork,field));
                    }*/
                    if (ObjectUtil.isNotEmpty(signNode)) {
                        if (signNode.type().equals(SignNode.Type.COMMON)) {
                            variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entEquipmentNetwork, field)) ? "AUTO_SIGN" : ReflectUtil.getFieldValue(entEquipmentNetwork, field));
                        } else if (signNode.type().equals(SignNode.Type.HUIQIAN)) {
                            variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entEquipmentNetwork, field)) ? Arrays.asList("AUTO_SIGN") : Arrays.asList(ReflectUtil.getFieldValue(entEquipmentNetwork, field).toString().split(",")));
                        }
                    }
                }
        );
        ProcessInstance pi = runtimeService.startProcessInstanceByKey(procDefKey, businessKey,variables);
        entEquipmentNetwork.setProcessId(pi.getProcessInstanceId());
        this.updateById(entEquipmentNetwork);

        TQhAllRelation relation = allRelationService.getOne(new QueryWrapper<TQhAllRelation>().eq("serialno",entEquipmentNetwork.getSerialno()));
        relation.setWorkStatus(entEquipmentNetwork.getWorkStatus());
        relation.setProcessId(pi.getProcessInstanceId());
        Task task = actTaskService.createTaskQuery().processInstanceId(pi.getProcessInstanceId()).singleResult();
        relation.setNodename(task.getName());
        relation.setDeptNo(entEquipmentNetwork.getApplyDeptNo());
        allRelationService.updateById(relation);
        //添加mq
        rabbitTemplate.convertAndSend("SignDirectExchange", "Sign.Direct.Routing", BeanUtil.beanToMap(relation));
        return Boolean.TRUE;
	}

    /**
     * 插入一条记录（选择字段，策略插入）
     *
     * @param entEquipmentNetwork 实体对象
     */
    @Override
    public boolean save(EntEquipmentNetwork entEquipmentNetwork) {
        boolean rs = super.save(entEquipmentNetwork);
        saveChild(entEquipmentNetwork);
	    String tableName = EntEquipmentNetwork.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
        TQhAllRelation relation = new TQhAllRelation();
        relation.setDtoName(tableName);
        relation.setMakerNo(entEquipmentNetwork.getMakerNo());
        relation.setMakerName(entEquipmentNetwork.getMakerName());
        relation.setSerialno(entEquipmentNetwork.getSerialno());
        relation.setWorkflowid(wfConfigDto.getProcDefKey());
        relation.setWfName(wfConfigDto.getFormName());
        relation.setClassPackage(entEquipmentNetwork.getClass().getName());
        relation.setWorkStatus(0);
        allRelationService.save(relation);
        return rs;
    }

    /**
     * 插入一条记录（选择字段，策略插入）
     *
     * @param entEquipmentNetwork 实体对象
     */
    @Override
    public boolean updateById(EntEquipmentNetwork entEquipmentNetwork) {
        // 删除子表信息关联
        entEquipmentItemsService.remove(new QueryWrapper<EntEquipmentItems>().eq("pid", entEquipmentNetwork.getId()));
        boolean rs = super.updateById(entEquipmentNetwork);
        // 新增子表信息管理
        if(rs){
            // 新增子表信息管理
            saveChild(entEquipmentNetwork);
        }
        return rs;
    }
    /**
     * 新增子表記錄
     *
     * @param entEquipmentNetwork 表單对象
     */
    public void saveChild(EntEquipmentNetwork entEquipmentNetwork) {
        List<EntEquipmentItems> entEquipmentItemsLists = entEquipmentNetwork.getEntEquipmentItemsLists();
        if (entEquipmentItemsLists != null) {
            for (EntEquipmentItems entEquipmentItems : entEquipmentItemsLists) {
                entEquipmentItems.setId(null);
                entEquipmentItems.setPid(entEquipmentNetwork.getId());
                entEquipmentItemsService.save(entEquipmentItems);
            }
        }
    }

    /**
     * 删除（根据ID 批量删除）
     *
     * @param idList 主键ID列表
     */
    @Override
    public boolean removeByIds(Collection<? extends Serializable> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return false;
        }
        for (Serializable id : idList) {
            EntEquipmentNetwork entEquipmentNetwork = getById(id);
            // 删除子表信息关联
            entEquipmentItemsService.remove(new QueryWrapper<EntEquipmentItems>().eq("pid", entEquipmentNetwork.getId()));
            }

        return SqlHelper.retBool(getBaseMapper().deleteBatchIds(idList));
    }
    /**
     * 根據ID獲取信息
     *
     * @param id
     * @return
     */
    @Override
    public EntEquipmentNetwork getById(Serializable id) {
        EntEquipmentNetwork entEquipmentNetwork = super.getById(id);
        setChilds(entEquipmentNetwork);
        return entEquipmentNetwork;
    }

    /**
     * 獲取子表列表（${childTable.tableComment}）
     */
    public void setChilds(EntEquipmentNetwork entEquipmentNetwork){

    List<EntEquipmentItems> entEquipmentItemsLists = entEquipmentItemsService.list(new QueryWrapper<EntEquipmentItems>().eq("pid", entEquipmentNetwork.getId()));
    entEquipmentNetwork.setEntEquipmentItemsLists(entEquipmentItemsLists);
    }
}
