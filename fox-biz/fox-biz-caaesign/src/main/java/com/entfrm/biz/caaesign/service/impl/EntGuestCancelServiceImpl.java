package com.entfrm.biz.caaesign.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import com.baomidou.mybatisplus.annotation.TableName;
import com.entfrm.biz.activiti.service.WfConfigService;
import com.entfrm.biz.activiti.entity.TQhAllRelation;
import com.entfrm.biz.activiti.service.TQhAllRelationService;
import com.entfrm.biz.activiti.service.TaskService;
import com.entfrm.biz.activiti.dto.LeaveDto;
import org.springframework.stereotype.Service;
import com.entfrm.biz.caaesign.mapper.EntGuestCancelMapper;
import com.entfrm.biz.caaesign.entity.EntGuestCancel;
import com.entfrm.biz.caaesign.service.EntGuestCancelService;
import com.entfrm.core.base.util.PinyinUtil;
import com.entfrm.core.data.util.SerialNumberTool;
import cn.hutool.core.date.DateUtil;
import lombok.AllArgsConstructor;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.runtime.ProcessInstance;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.transaction.annotation.Transactional;
import com.entfrm.core.base.enums.TaskStatusEnum;
import java.util.Map;
import com.entfrm.core.data.constant.CommonConstant;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.io.Serializable;
import com.entfrm.biz.caaesign.entity.EntGuestItems1;
import com.entfrm.biz.caaesign.service.EntGuestItems1Service;
import com.entfrm.biz.caaesign.entity.EntGuestItems2;
import com.entfrm.biz.caaesign.service.EntGuestItems2Service;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import java.util.Collection;
import com.entfrm.biz.activiti.dto.WfConfigDto;
import java.lang.reflect.Field;
import com.google.common.collect.Maps;
import org.activiti.engine.task.Task;
import com.entfrm.core.security.util.SecurityUtil;
import com.entfrm.core.base.annotation.SignNode;
import java.util.Arrays;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.bean.BeanUtil;

/**
 * <AUTHOR>
 * @date 2024-10-17 08:03:26
 *
 * @description iPEBG移轉設備銷賬聯絡單Service业务层
 */
@Service
@AllArgsConstructor
public class EntGuestCancelServiceImpl extends ServiceImpl<EntGuestCancelMapper, EntGuestCancel> implements EntGuestCancelService {
	private final RuntimeService runtimeService;
	private final org.activiti.engine.TaskService actTaskService;
	private final WfConfigService wfConfigService;
	private final TQhAllRelationService allRelationService;
    private final RabbitTemplate rabbitTemplate;
	private final TaskService taskService;

    private final EntGuestItems1Service entGuestItems1Service;
    private final EntGuestItems2Service entGuestItems2Service;

    	/**
	 * 启动流程
	 *
	 * @param entGuestCancel
	 * @return
	 */
	@Override
	public Boolean startProcess(EntGuestCancel entGuestCancel) {
        entGuestCancel.setWorkStatus(Integer.parseInt(TaskStatusEnum.CHECKING.getStatus()));
        String tableName = EntGuestCancel.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
        String procDefKey = wfConfigDto.getProcDefKey();
        String businessKey = procDefKey + CommonConstant.BREAK_BUSINESS_KEY + entGuestCancel.getId();
        Field fields[] = entGuestCancel.getClass().getDeclaredFields();
        Map variables = Maps.newHashMap();
        //設置填單人
        variables.put("maker", SecurityUtil.getUser().getUsername());
        variables.put("serialNo", entGuestCancel.getSerialno());
        variables.put("workflowName", wfConfigDto.getFormName());
        Arrays.stream(fields).forEach(field -> {
                SignNode signNode = field.getAnnotation(SignNode.class);
                if (ObjectUtil.isNotEmpty(signNode)) {
                    if (signNode.type().equals(SignNode.Type.COMMON)) {
                        variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entGuestCancel, field)) ? "AUTO_SIGN" : ReflectUtil.getFieldValue(entGuestCancel, field));
                    } else if (signNode.type().equals(SignNode.Type.HUIQIAN)) {
                        variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entGuestCancel, field)) ? Arrays.asList("AUTO_SIGN") : Arrays.asList(ReflectUtil.getFieldValue(entGuestCancel, field).toString().split(",")));
                    }
                }
            }
        );
        ProcessInstance pi = runtimeService.startProcessInstanceByKey(procDefKey, businessKey,variables);
        entGuestCancel.setProcessId(pi.getProcessInstanceId());
        this.updateById(entGuestCancel);

        TQhAllRelation relation = allRelationService.getOne(new QueryWrapper<TQhAllRelation>().eq("serialno",entGuestCancel.getSerialno()));
        relation.setWorkStatus(entGuestCancel.getWorkStatus());
        relation.setProcessId(pi.getProcessInstanceId());
        Task task = actTaskService.createTaskQuery().processInstanceId(pi.getProcessInstanceId()).singleResult();
        relation.setNodename(task.getName());
        relation.setDeptNo(entGuestCancel.getMakerdeptno());
        allRelationService.updateById(relation);
        //添加mq
        rabbitTemplate.convertAndSend("SignDirectExchange", "Sign.Direct.Routing", BeanUtil.beanToMap(relation));
        return Boolean.TRUE;
	}

    /**
     * 插入一条记录（选择字段，策略插入）
     *
     * @param entGuestCancel 实体对象
     */
    @Override
    public boolean save(EntGuestCancel entGuestCancel) {
        boolean rs = super.save(entGuestCancel);
        saveChild(entGuestCancel);
	    String tableName = EntGuestCancel.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
        TQhAllRelation relation = new TQhAllRelation();
        relation.setDtoName(tableName);
        relation.setMakerNo(entGuestCancel.getMakerNo());
        relation.setMakerName(entGuestCancel.getMakerName());
        relation.setSerialno(entGuestCancel.getSerialno());
        relation.setWorkflowid(wfConfigDto.getProcDefKey());
        relation.setWfName(wfConfigDto.getFormName());
        relation.setClassPackage(entGuestCancel.getClass().getName());
        relation.setWorkStatus(0);
        allRelationService.save(relation);
        return rs;
    }
	@Override
	public Boolean updateAndCheck(EntGuestCancel entGuestCancel, LeaveDto leaveDto) {
		try {
			this.updateById(entGuestCancel);
			taskService.checkTask(leaveDto);
		} catch (Exception e) {
			return false;
		}
		return true;
	}
    /**
     * 插入一条记录（选择字段，策略插入）
     *
     * @param entGuestCancel 实体对象
     */
    @Override
    public boolean updateById(EntGuestCancel entGuestCancel) {
        // 删除子表信息关联
        entGuestItems1Service.remove(new QueryWrapper<EntGuestItems1>().eq("pid", entGuestCancel.getId()));
        // 删除子表信息关联
        entGuestItems2Service.remove(new QueryWrapper<EntGuestItems2>().eq("pid", entGuestCancel.getId()));
        boolean rs = super.updateById(entGuestCancel);
        // 新增子表信息管理
        if(rs){
            // 新增子表信息管理
            saveChild(entGuestCancel);
        }
        return rs;
    }

    public boolean updateAttachids(EntGuestCancel entGuestCancel) {
        return super.updateById(entGuestCancel);
    }
    /**
     * 新增子表記錄
     *
     * @param entGuestCancel 表單对象
     */
    public void saveChild(EntGuestCancel entGuestCancel) {
        List<EntGuestItems1> entGuestItems1Lists = entGuestCancel.getEntGuestItems1Lists();
        if (entGuestItems1Lists != null) {
            for (EntGuestItems1 entGuestItems1 : entGuestItems1Lists) {
                entGuestItems1.setId(null);
                entGuestItems1.setPid(entGuestCancel.getId());
                entGuestItems1Service.save(entGuestItems1);
            }
        }
        List<EntGuestItems2> entGuestItems2Lists = entGuestCancel.getEntGuestItems2Lists();
        if (entGuestItems2Lists != null) {
            for (EntGuestItems2 entGuestItems2 : entGuestItems2Lists) {
                entGuestItems2.setId(null);
                entGuestItems2.setPid(entGuestCancel.getId());
                entGuestItems2Service.save(entGuestItems2);
            }
        }
    }

    /**
     * 删除（根据ID 批量删除）
     *
     * @param idList 主键ID列表
     */
    @Override
    public boolean removeByIds(Collection<? extends Serializable> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return false;
        }
        for (Serializable id : idList) {
            EntGuestCancel entGuestCancel = getById(id);
            // 删除子表信息关联
            entGuestItems1Service.remove(new QueryWrapper<EntGuestItems1>().eq("pid", entGuestCancel.getId()));
            // 删除子表信息关联
            entGuestItems2Service.remove(new QueryWrapper<EntGuestItems2>().eq("pid", entGuestCancel.getId()));
            }

        return SqlHelper.retBool(getBaseMapper().deleteBatchIds(idList));
    }
    /**
     * 根據ID獲取信息
     *
     * @param id
     * @return
     */
    @Override
    public EntGuestCancel getById(Serializable id) {
        EntGuestCancel entGuestCancel = super.getById(id);
        setChilds(entGuestCancel);
        return entGuestCancel;
    }

    /**
     * 獲取子表列表（${childTable.tableComment}）
     */
    public void setChilds(EntGuestCancel entGuestCancel){

    List<EntGuestItems1> entGuestItems1Lists = entGuestItems1Service.list(new QueryWrapper<EntGuestItems1>().eq("pid", entGuestCancel.getId()));
    entGuestCancel.setEntGuestItems1Lists(entGuestItems1Lists);
    List<EntGuestItems2> entGuestItems2Lists = entGuestItems2Service.list(new QueryWrapper<EntGuestItems2>().eq("pid", entGuestCancel.getId()));
    entGuestCancel.setEntGuestItems2Lists(entGuestItems2Lists);
    }
}
