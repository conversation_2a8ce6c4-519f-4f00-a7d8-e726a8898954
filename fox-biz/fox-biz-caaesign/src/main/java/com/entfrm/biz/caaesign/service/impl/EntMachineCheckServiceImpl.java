package com.entfrm.biz.caaesign.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.entfrm.biz.activiti.dto.LeaveDto;
import com.entfrm.biz.activiti.dto.WfConfigDto;
import com.entfrm.biz.activiti.entity.TQhAllRelation;
import com.entfrm.biz.activiti.service.TQhAllRelationService;
import com.entfrm.biz.activiti.service.TaskService;
import com.entfrm.biz.activiti.service.WfConfigService;
import com.entfrm.biz.caaesign.entity.EntMachineCheck;
import com.entfrm.biz.caaesign.entity.EntMachineItems;
import com.entfrm.biz.caaesign.mapper.EntMachineCheckMapper;
import com.entfrm.biz.caaesign.service.EntMachineCheckService;
import com.entfrm.biz.caaesign.service.EntMachineItemsService;
import com.entfrm.biz.feign.ch.ChOpenapiFeignService;
import com.entfrm.biz.feign.ch.dto.EntMachineCheckDto;
import com.entfrm.biz.feign.ch.dto.EntMachineItemsDto;
import com.entfrm.core.base.annotation.SignNode;
import com.entfrm.core.base.enums.TaskStatusEnum;
import com.entfrm.core.data.constant.CommonConstant;
import com.entfrm.core.security.util.SecurityUtil;
import com.google.common.collect.Maps;
import org.activiti.engine.task.Task;
import lombok.AllArgsConstructor;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.runtime.ProcessInstance;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2022-12-09 16:50:32
 *
 * @description 機台資安點檢暨抽檢匯總表Service业务层
 */
@Service
@AllArgsConstructor
public class EntMachineCheckServiceImpl extends ServiceImpl<EntMachineCheckMapper, EntMachineCheck> implements EntMachineCheckService {

	private final RuntimeService runtimeService;
	private final org.activiti.engine.TaskService actTaskService;
	private final WfConfigService wfConfigService;
	private final TQhAllRelationService allRelationService;
    private final RabbitTemplate rabbitTemplate;
	private final TaskService taskService;

    private final EntMachineItemsService entMachineItemsService;
    private final ChOpenapiFeignService chOpenapiFeignService;

    	/**
	 * 启动流程
	 *
	 * @param entMachineCheck
	 * @return
	 */
	@Override
	public Boolean startProcess(EntMachineCheck entMachineCheck) {
        entMachineCheck.setWorkStatus(Integer.parseInt(TaskStatusEnum.CHECKING.getStatus()));
        String tableName = EntMachineCheck.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
        String procDefKey = wfConfigDto.getProcDefKey();
        String businessKey = procDefKey + CommonConstant.BREAK_BUSINESS_KEY + entMachineCheck.getId();
        Field fields[] = entMachineCheck.getClass().getDeclaredFields();
        Map variables = Maps.newHashMap();
        //設置填單人
        variables.put("maker", SecurityUtil.getUser().getUsername());
        variables.put("serialNo", entMachineCheck.getSerialno());
        variables.put("workflowName", wfConfigDto.getFormName());
        Arrays.stream(fields).forEach(field -> {
                SignNode signNode = field.getAnnotation(SignNode.class);
                if (ObjectUtil.isNotEmpty(signNode)) {
                    if (signNode.type().equals(SignNode.Type.COMMON)) {
                        variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entMachineCheck, field)) ? "AUTO_SIGN" : ReflectUtil.getFieldValue(entMachineCheck, field));
                    } else if (signNode.type().equals(SignNode.Type.HUIQIAN)) {
                        variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entMachineCheck, field)) ? Arrays.asList("AUTO_SIGN") : Arrays.asList(ReflectUtil.getFieldValue(entMachineCheck, field).toString().split(",")));
                    }
                }
            }
        );
        ProcessInstance pi = runtimeService.startProcessInstanceByKey(procDefKey, businessKey,variables);
        entMachineCheck.setProcessId(pi.getProcessInstanceId());
        this.updateById(entMachineCheck);

        TQhAllRelation relation = allRelationService.getOne(new QueryWrapper<TQhAllRelation>().eq("serialno",entMachineCheck.getSerialno()));
        relation.setWorkStatus(entMachineCheck.getWorkStatus());
        relation.setProcessId(pi.getProcessInstanceId());
        Task task = actTaskService.createTaskQuery().processInstanceId(pi.getProcessInstanceId()).singleResult();
        relation.setNodename(task.getName());
        relation.setDeptNo(entMachineCheck.getApplyDeptNo());
        allRelationService.updateById(relation);
        //添加mq
        rabbitTemplate.convertAndSend("SignDirectExchange", "Sign.Direct.Routing", BeanUtil.beanToMap(relation));
        return Boolean.TRUE;
	}

    /**
     * 插入一条记录（选择字段，策略插入）
     *
     * @param entMachineCheck 实体对象
     */
    @Override
    public boolean save(EntMachineCheck entMachineCheck) {
        boolean rs = super.save(entMachineCheck);
        saveChild(entMachineCheck);
	    String tableName = EntMachineCheck.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
        TQhAllRelation relation = new TQhAllRelation();
        relation.setDtoName(tableName);
        relation.setMakerNo(entMachineCheck.getMakerNo());
        relation.setMakerName(entMachineCheck.getMakerName());
        relation.setSerialno(entMachineCheck.getSerialno());
        relation.setWorkflowid(wfConfigDto.getProcDefKey());
        relation.setWfName(wfConfigDto.getFormName());
        relation.setClassPackage(entMachineCheck.getClass().getName());
        relation.setWorkStatus(0);
        allRelationService.save(relation);
        return rs;
    }
	@Override
	public Boolean updateAndCheck(EntMachineCheck entMachineCheck, LeaveDto leaveDto) {
		try {
			this.updateById(entMachineCheck);
			taskService.checkTask(leaveDto);
		} catch (Exception e) {
			return false;
		}
		return true;
	}


    /**
     * 插入一条记录（选择字段，策略插入）
     *
     * @param entMachineCheck 实体对象
     */
    @Override
    public boolean updateById(EntMachineCheck entMachineCheck) {
        // 删除子表信息关联
        entMachineItemsService.remove(new QueryWrapper<EntMachineItems>().eq("pid", entMachineCheck.getId()));
        boolean rs = super.updateById(entMachineCheck);
        // 新增子表信息管理
        if(rs){
            // 新增子表信息管理
            saveChild(entMachineCheck);
        }
        return rs;
    }
    /**
     * 新增子表記錄
     *
     * @param entMachineCheck 表單对象
     */
    public void saveChild(EntMachineCheck entMachineCheck) {
        List<EntMachineItems> entMachineItemsLists = entMachineCheck.getEntMachineItemsLists();
        if (entMachineItemsLists != null) {
            for (EntMachineItems entMachineItems : entMachineItemsLists) {
                entMachineItems.setId(null);
                entMachineItems.setPid(entMachineCheck.getId());
                entMachineItemsService.save(entMachineItems);
            }
        }
    }

    /**
     * 删除（根据ID 批量删除）
     *
     * @param idList 主键ID列表
     */
    @Override
    public boolean removeByIds(Collection<? extends Serializable> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return false;
        }
        for (Serializable id : idList) {
            EntMachineCheck entMachineCheck = getById(id);
            // 删除子表信息关联
            entMachineItemsService.remove(new QueryWrapper<EntMachineItems>().eq("pid", entMachineCheck.getId()));
            }

        return SqlHelper.retBool(getBaseMapper().deleteBatchIds(idList));
    }
    /**
     * 根據ID獲取信息
     *
     * @param id
     * @return
     */
    @Override
    public EntMachineCheck getById(Serializable id) {
        EntMachineCheck entMachineCheck = super.getById(id);
        setChilds(entMachineCheck);
        return entMachineCheck;
    }

    /**
     * 獲取子表列表（${childTable.tableComment}）
     */
    public void setChilds(EntMachineCheck entMachineCheck){

    List<EntMachineItems> entMachineItemsLists = entMachineItemsService.list(new QueryWrapper<EntMachineItems>().eq("pid", entMachineCheck.getId()));
    entMachineCheck.setEntMachineItemsLists(entMachineItemsLists);
    }


    @Override
    public EntMachineCheck getBySerialNo(String serialNo) {
        EntMachineCheck entMachineCheck = getOne(new QueryWrapper<EntMachineCheck>().eq("serialno", serialNo));
        setChilds(entMachineCheck);
        return entMachineCheck;
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public Boolean sendChService(String serialNo) {
        try {
            EntMachineCheck entMachineCheck = getBySerialNo(serialNo);
            //拋轉主表數據
            EntMachineCheckDto entMachineCheckDto = new EntMachineCheckDto();
            BeanUtils.copyProperties(entMachineCheck,entMachineCheckDto);
            entMachineCheckDto.setApplyEmpno(entMachineCheck.getApplyEmpNo());
            entMachineCheckDto.setApplyEmpname(entMachineCheck.getApplyEmpName());
            entMachineCheckDto.setApplyDeptno(entMachineCheck.getApplyDeptNo());
            entMachineCheckDto.setApplyDeptnam(entMachineCheck.getApplyDeptNam());
            entMachineCheckDto.setApplyCostno(entMachineCheck.getApplyCostNo());
            entMachineCheckDto.setMakerFactoryid(entMachineCheck.getMakerfactoryid());
            entMachineCheckDto.setCompletTime(new Date());
            chOpenapiFeignService.saveZhubiaoImport(entMachineCheckDto);
            //拋轉從表數據
            for (EntMachineItems entMachineItemsList : entMachineCheck.getEntMachineItemsLists()) {
                EntMachineItemsDto entMachineItemsDto = new EntMachineItemsDto();
                BeanUtils.copyProperties(entMachineItemsList,entMachineItemsDto);
                entMachineItemsDto.setCheckVirusdate(entMachineItemsList.getCheckVirusDate());
                entMachineItemsDto.setYnRecycleadministrator(entMachineItemsList.getYnRecycleAdministrator());
                entMachineItemsDto.setFactory(entMachineCheck.getMakerfactoryid());
                chOpenapiFeignService.saveEsignImport(entMachineItemsDto);
            }
            return false;
        } catch (BeansException e) {
            log.error("拋轉數據失敗"+e.getMessage() ,e);
        }
        return true;
    }
}
