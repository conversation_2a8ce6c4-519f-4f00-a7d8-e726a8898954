package com.entfrm.biz.caaesign.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
    import com.baomidou.mybatisplus.annotation.TableName;
import com.entfrm.biz.activiti.dto.WfConfigDto;
import com.entfrm.biz.activiti.entity.TQhAllRelation;
import com.entfrm.biz.activiti.service.TQhAllRelationService;
import com.entfrm.biz.activiti.service.WfConfigService;
import com.entfrm.core.base.annotation.SignNode;
import com.entfrm.core.security.util.SecurityUtil;
import com.google.common.collect.Maps;
import org.activiti.engine.task.Task;
import org.springframework.stereotype.Service;
import com.entfrm.biz.caaesign.mapper.EntGeneralRepairMapper;
import com.entfrm.biz.caaesign.entity.EntGeneralRepair;
import com.entfrm.biz.caaesign.service.EntGeneralRepairService;
import com.entfrm.core.base.util.PinyinUtil;
import com.entfrm.core.data.util.SerialNumberTool;
import cn.hutool.core.date.DateUtil;
import lombok.AllArgsConstructor;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.runtime.ProcessInstance;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.transaction.annotation.Transactional;
import com.entfrm.core.base.enums.TaskStatusEnum;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Map;
import com.entfrm.core.data.constant.CommonConstant;

/**
 * <AUTHOR>
 * @date 2021-08-27 14:50:48
 *
 * @description 總務零星修繕派工單Service业务层
 */
@Service
@AllArgsConstructor
public class EntGeneralRepairServiceImpl extends ServiceImpl<EntGeneralRepairMapper, EntGeneralRepair> implements EntGeneralRepairService {
	private final RuntimeService runtimeService;
	private final org.activiti.engine.TaskService actTaskService;
	private final WfConfigService wfConfigService;
	private final TQhAllRelationService allRelationService;
	private final RabbitTemplate rabbitTemplate;
    	/**
	 * 启动流程
	 *
	 * @param entGeneralRepair
	 * @return
	 */
	@Override
	public Boolean startProcess(EntGeneralRepair entGeneralRepair) {
        entGeneralRepair.setWorkStatus(Integer.parseInt(TaskStatusEnum.CHECKING.getStatus()));
		String tableName = EntGeneralRepair.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
		String procDefKey = wfConfigDto.getProcDefKey();
		String businessKey = procDefKey + CommonConstant.BREAK_BUSINESS_KEY + entGeneralRepair.getId();
		Field fields[] = entGeneralRepair.getClass().getDeclaredFields();
        Map variables = Maps.newHashMap();
        //設置填單人
        variables.put("maker", SecurityUtil.getUser().getUsername());
        variables.put("serialNo", entGeneralRepair.getSerialno());
        variables.put("workflowName", wfConfigDto.getFormName());
        Arrays.stream(fields).forEach(field -> {
                    SignNode signNode = field.getAnnotation(SignNode.class);
                    if (ObjectUtil.isNotEmpty(signNode)) {
                        variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entGeneralRepair, field))?"AUTO_SIGN":ReflectUtil.getFieldValue(entGeneralRepair,field));
                    }
                }
        );
		ProcessInstance pi = runtimeService.startProcessInstanceByKey(procDefKey, businessKey,variables);
        entGeneralRepair.setProcessId(pi.getProcessInstanceId());
		this.updateById(entGeneralRepair);

		TQhAllRelation relation = allRelationService.getOne(new QueryWrapper<TQhAllRelation>().eq("serialno",entGeneralRepair.getSerialno()));
		relation.setWorkStatus(entGeneralRepair.getWorkStatus());
		relation.setProcessId(pi.getProcessInstanceId());
        Task task = actTaskService.createTaskQuery().processInstanceId(pi.getProcessInstanceId()).singleResult();
        relation.setNodename(task.getName());
        relation.setDeptNo(entGeneralRepair.getMakerdeptno());
		allRelationService.updateById(relation);
		//添加mq
		rabbitTemplate.convertAndSend("SignDirectExchange", "Sign.Direct.Routing", BeanUtil.beanToMap(relation));
		return Boolean.TRUE;
	}

	/**
     * 插入一条记录（选择字段，策略插入）
     *
     */
	@Override
	public boolean save(EntGeneralRepair entGeneralRepair) {
		boolean rs = super.save(entGeneralRepair);
		String tableName = EntGeneralRepair.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
		TQhAllRelation relation = new TQhAllRelation();
		relation.setDtoName(tableName);
		relation.setMakerNo(entGeneralRepair.getMakerNo());
		relation.setMakerName(entGeneralRepair.getMakerName());
		relation.setSerialno(entGeneralRepair.getSerialno());
		relation.setWorkflowid(wfConfigDto.getProcDefKey());
		relation.setWfName(wfConfigDto.getFormName());
		relation.setClassPackage(entGeneralRepair.getClass().getName());
		relation.setWorkStatus(0);
		allRelationService.save(relation);
		return rs;
	}
}
