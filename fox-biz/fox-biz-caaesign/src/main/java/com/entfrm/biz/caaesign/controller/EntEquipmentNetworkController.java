package com.entfrm.biz.caaesign.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.entfrm.biz.activiti.dto.LeaveDto;
import com.entfrm.biz.activiti.dto.WfConfigDto;
import com.entfrm.biz.activiti.entity.WfConfig;
import com.entfrm.biz.activiti.service.WfConfigService;
import com.entfrm.biz.caaesign.base.BaseController;
import com.entfrm.biz.caaesign.entity.EntEquipmentAll;
import com.entfrm.biz.caaesign.entity.EntEquipmentItems;
import com.entfrm.biz.caaesign.entity.EntEquipmentNetwork;
import com.entfrm.biz.caaesign.entity.EntSpeApply;
import com.entfrm.biz.caaesign.service.EntEquipmentAllService;
import com.entfrm.biz.caaesign.service.EntEquipmentItemsService;
import com.entfrm.biz.caaesign.service.EntEquipmentNetworkService;
import com.entfrm.biz.feign.FeignService;
import com.entfrm.biz.feign.dto.User;
import com.entfrm.biz.msg.service.InfoContentService;
import com.entfrm.biz.system.entity.DictData;
import com.entfrm.biz.system.entity.TableSignConfig;
import com.entfrm.biz.system.service.ConfigService;
import com.entfrm.biz.system.service.DictDataService;
import com.entfrm.biz.system.service.ITQhUserformhsService;
import com.entfrm.biz.system.service.TableSignConfigService;
import com.entfrm.core.base.annotation.SignNode;
import com.entfrm.core.base.api.R;
import com.entfrm.core.base.util.ExcelUtil;
import com.entfrm.core.base.util.PinyinUtil;
import com.entfrm.core.data.annotation.DataFilter;
import com.entfrm.core.data.util.SerialNumberTool;
import com.entfrm.core.log.annotation.OperLog;
import com.entfrm.core.security.util.SecurityUtil;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.TaskService;
import org.activiti.engine.task.Task;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Field;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2021-09-08 10:22:55
 * @description 產線聯網設備入網申請單Controller
 */
@Api("產線聯網設備入網申請單管理")
@RestController
@AllArgsConstructor
@RequestMapping("/caaesign/entEquipmentNetwork")
public class EntEquipmentNetworkController  extends BaseController {

    private final EntEquipmentNetworkService entEquipmentNetworkService;
    private final WfConfigService wfConfigService;
    private final TaskService taskService;
    private final com.entfrm.biz.activiti.service.TaskService taskBunessService;
    private final RuntimeService runtimeService;
    private final ITQhUserformhsService tQhUserformhsService;
    private final InfoContentService infoContentService;
    private final ConfigService configService;
    private final EntEquipmentItemsService entEquipmentItemsService;
    private final EntEquipmentAllService entEquipmentAllService;
    private final DictDataService dictDataService;
    @Resource
    private FeignService feignService;
    private final TableSignConfigService signConfigService;

    private QueryWrapper<EntEquipmentNetwork> getQueryWrapper(EntEquipmentNetwork entEquipmentNetwork) {
        return new QueryWrapper<EntEquipmentNetwork>()
                .between(StrUtil.isNotBlank(entEquipmentNetwork.getBeginTime()) && StrUtil.isNotBlank(entEquipmentNetwork.getEndTime()), "create_time", entEquipmentNetwork.getBeginDate(), entEquipmentNetwork.getEndDate())
                .between(StrUtil.isNotBlank(entEquipmentNetwork.getBeginSignTime()) && StrUtil.isNotBlank(entEquipmentNetwork.getEndSignTime()), "complet_time", entEquipmentNetwork.getBeginSignDate(), entEquipmentNetwork.getEndSignDate())
                .eq(!StrUtil.isEmptyIfStr(entEquipmentNetwork.getWorkStatus()), "work_status", String.valueOf(entEquipmentNetwork.getWorkStatus()))
                .eq(StrUtil.isNotBlank(entEquipmentNetwork.getSerialno()), "serialno", entEquipmentNetwork.getSerialno())
                .like(StrUtil.isNotBlank(entEquipmentNetwork.getMakerfactoryid()), "makerfactoryid", entEquipmentNetwork.getMakerfactoryid())
                .eq(StrUtil.isNotBlank(entEquipmentNetwork.getApplyEmpNo()), "apply_emp_no", entEquipmentNetwork.getApplyEmpNo())
                .eq(StrUtil.isNotBlank(entEquipmentNetwork.getApplyDeptNo()), "apply_dept_no", entEquipmentNetwork.getApplyDeptNo())
                .orderByDesc("create_time");
    }
    private QueryWrapper<EntEquipmentAll> getQueryWrapper1(EntEquipmentAll entEquipmentAll) {
        return new QueryWrapper<EntEquipmentAll>()
                .between(StrUtil.isNotBlank(entEquipmentAll.getBeginTime()) && StrUtil.isNotBlank(entEquipmentAll.getEndTime()), "create_time", entEquipmentAll.getBeginDate(), entEquipmentAll.getEndDate())
                .between(StrUtil.isNotBlank(entEquipmentAll.getBeginSignTime()) && StrUtil.isNotBlank(entEquipmentAll.getEndSignTime()), "complet_time", entEquipmentAll.getBeginSignDate(), entEquipmentAll.getEndSignDate())
                .eq(!StrUtil.isEmptyIfStr(entEquipmentAll.getWorkStatus()), "work_status", String.valueOf(entEquipmentAll.getWorkStatus()))
                .eq(StrUtil.isNotBlank(entEquipmentAll.getSerialno()), "serialno", entEquipmentAll.getSerialno())
                .like(StrUtil.isNotBlank(entEquipmentAll.getMakerfactoryid()), "makerfactoryid", entEquipmentAll.getMakerfactoryid())
                .eq(StrUtil.isNotBlank(entEquipmentAll.getApplyEmpNo()), "apply_emp_no", entEquipmentAll.getApplyEmpNo())
                .eq(StrUtil.isNotBlank(entEquipmentAll.getApplyDeptNo()), "apply_dept_no", entEquipmentAll.getApplyDeptNo())
                .orderByDesc("create_time");
    }
    @ApiOperation("產線聯網設備入網申請單列表")
//        @PreAuthorize("@ps.hasPerm('entEquipmentNetwork_view')")
    @GetMapping("/list")
    @DataFilter
    public R list(Page page, EntEquipmentNetwork entEquipmentNetwork) {
        QueryWrapper queryWrapper = this.getQueryWrapper(entEquipmentNetwork);
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("userNo", SecurityUtil.getUser().getUsername());
        paramMap.put("menuId" , entEquipmentNetwork.getMenuId());
        String userDeptInfo = HttpUtil.get(configService.getValueByKey("esign.datafilter.address"), paramMap);
        if (StrUtil.isNotEmpty(userDeptInfo)) {
            if (!userDeptInfo.equals("-1")) {
                protectBach(Arrays.asList(userDeptInfo.split(",")),s->queryWrapper.or(true).in("makerdeptno", s));
            }
        } else {
            queryWrapper.eq("maker_no", SecurityUtil.getUser().getUsername());
        }
        IPage<EntEquipmentNetwork> entEquipmentNetworkPage = entEquipmentNetworkService.page(page, queryWrapper);
        entEquipmentNetworkPage.getRecords().forEach(bEntity -> {
            bEntity.setIsOwn(SecurityUtil.getUser().getUsername().equals(bEntity.getMakerNo()));
            bEntity.setMakerfactoryid((bEntity.getMakerfactoryid().substring(1,bEntity.getMakerfactoryid().length()-1).split(",")[0]).replaceAll("\"",""));
            try {
                if (bEntity.getProcessId() != null) {
                    Task task = taskService.createTaskQuery().processInstanceId(bEntity.getProcessId()).singleResult();
                    if (task != null) {
                        Set<String> assigens = taskBunessService.getTaskCandidate(task.getId());
                        List<String> assigenss = new ArrayList<>();
                        for (String assigen : assigens) {
                            User user = feignService.getOne(assigen);
                            if (user != null) assigenss.add(assigen + "/" + user.getNickName());
                        }
                        if (assigens.size() > 1) {
                            bEntity.setSignPerson(ArrayUtil.join(assigenss.toArray(), ","));
                        } else if (assigens.size() == 1) {
                            bEntity.setSignPerson(assigenss.get(0));
                        }
                        bEntity.setSignNode(task.getName());
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        return R.ok(entEquipmentNetworkPage.getRecords(), entEquipmentNetworkPage.getTotal());
    }

    @ApiOperation("產線聯網設備入網申請單查询")
    @GetMapping("/{id}")
    public R getById(@PathVariable("id") String id) {
        /*EntEquipmentNetwork entEquipmentNetwork=entEquipmentNetworkService.getById(id);
        if(entEquipmentNetwork.getWorkStatus()==2){
            String name=taskService.createTaskQuery().processInstanceId(entEquipmentNetwork.getProcessId()).singleResult().getName();
            entEquipmentNetwork.setCurrentname(name);
            if(name.equals("課級主管")){
                entEquipmentNetwork.setCurrentorder("1");
            }else if(name.equals("部級主管")){
                entEquipmentNetwork.setCurrentorder("2");
            }else if(name.equals("IT生技確認")){
                entEquipmentNetwork.setCurrentorder("3");
            }else if(name.equals("IT生技複審")){
                entEquipmentNetwork.setCurrentorder("4");
            }else if(name.equals("資訊運維確認")){
                entEquipmentNetwork.setCurrentorder("5");
            }else if(name.equals("資訊運維審核")){
                entEquipmentNetwork.setCurrentorder("6");
            }else if(name.equals("設備入網")){
                entEquipmentNetwork.setCurrentorder("7");
            }
        }*/
        EntEquipmentNetwork entEquipmentNetwork=entEquipmentNetworkService.getById(id);
        if (StrUtil.isNotBlank(entEquipmentNetwork.getProcessId())) {
            Task task = taskService.createTaskQuery().processInstanceId(entEquipmentNetwork.getProcessId()).singleResult();
            if (null != task) {
                entEquipmentNetwork.setCurrentname(task.getName());
                Field fields[] = entEquipmentNetwork.getClass().getDeclaredFields();
                Arrays.stream(fields).forEach(field -> {
                    SignNode signNode = field.getAnnotation(SignNode.class);
                    if (task.getTaskDefinitionKey().equals(field.getName())) {
                        entEquipmentNetwork.setCurrentorder(signNode.order() + "");
                    }
                });
            }
//		    aHhlz.setCurrentorder(AnnotationUtil.getAnnotationValueMap(AHhlz.class, SignNode.class)));
        }
        return R.ok(entEquipmentNetwork);
    }

    @OperLog("產線聯網設備入網申請單新增")
    @ApiOperation("產線聯網設備入網申請單新增")
//    @PreAuthorize("@ps.hasPerm('entEquipmentNetwork_add')")
    @PostMapping("/save")
    public R save(@Validated @RequestBody EntEquipmentNetwork entEquipmentNetwork) {
        entEquipmentNetwork.setWorkStatus(0);
        entEquipmentNetwork.setCreateDate(new Date());
        String tableName = EntEquipmentNetwork.class.getAnnotation(TableName.class).value();
        Map config = wfConfigService.getTableKey(tableName);
        entEquipmentNetwork.setSerialno(PinyinUtil.getFirstSpell("產線聯網設備入網申請單") + DateUtil.format(new Date(), "yyyyMMddHHmmss") + SerialNumberTool.getInstance().generaterNextNumber(4));
        entEquipmentNetwork.setMakerNo(SecurityUtil.getUser().getUsername());
        entEquipmentNetwork.setMakerName(SecurityUtil.getUser().getNickName());
        entEquipmentNetwork.setMakerdeptno(tQhUserformhsService.findByEmpnoForEntfrm(SecurityUtil.getUser().getUsername()).getDeptno());
        entEquipmentNetworkService.save(entEquipmentNetwork);
        return R.ok();
    }

    @OperLog("產線聯網設備入網申請單修改")
    @ApiOperation("產線聯網設備入網申請單修改")
//    @PreAuthorize("@ps.hasPerm('entEquipmentNetwork_edit')")
    @PutMapping("/update")
    public R update(@Validated @RequestBody EntEquipmentNetwork entEquipmentNetwork) {
        entEquipmentNetworkService.updateById(entEquipmentNetwork);
        return R.ok();
    }


    @OperLog("產線聯網設備入網申請單删除")
    @ApiOperation("產線聯網設備入網申請單删除")
//    @PreAuthorize("@ps.hasPerm('entEquipmentNetwork_del')")
    @DeleteMapping("/remove/{id}")
    public R remove(@PathVariable("id") String[] id) {
        return R.ok(entEquipmentNetworkService.removeByIds(Arrays.asList(id)));
    }


    //    @PreAuthorize("@ps.hasPerm('entEquipmentNetwork_export')")
    @GetMapping("/export")
    public R export(EntEquipmentAll entEquipmentAll) {
        QueryWrapper queryWrapper = this.getQueryWrapper1(entEquipmentAll);
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("userNo", SecurityUtil.getUser().getUsername());
        paramMap.put("menuId" , entEquipmentAll.getMenuId());
        String userDeptInfo = HttpUtil.get(configService.getValueByKey("esign.datafilter.address"), paramMap);
        if (StrUtil.isNotEmpty(userDeptInfo)) {
            if (!userDeptInfo.equals("-1")) {
                protectBach(Arrays.asList(userDeptInfo.split(",")),s->queryWrapper.or(true).in("makerdeptno", s));
            }
        } else {
            queryWrapper.eq("maker_no", SecurityUtil.getUser().getUsername());
        }
        Map<String,List> dicts = new HashMap<String,List>();
        dicts.put("work_status",beanToMap(infoContentService.getType("work_status")));
        dicts.put("makerfactoryid",beanToMap(infoContentService.getType("caaesign_factory")));
        List<EntEquipmentAll> list = entEquipmentAllService.list(queryWrapper);
        List<DictData> projectSubjectList = dictDataService.list(new QueryWrapper<DictData>().eq("dict_type", "caaesign_factory"));
        list.forEach(bEntity -> {
            bEntity.setIsOwn(SecurityUtil.getUser().getUsername().equals(bEntity.getMakerNo()));
            bEntity.setMakerfactoryid((bEntity.getMakerfactoryid().substring(1,bEntity.getMakerfactoryid().length()-1).split(",")[0]).replaceAll("\"",""));
            projectSubjectList.forEach(dict->{
                if (bEntity.getMakerfactoryid().contains(dict.getValue())) {
                    bEntity.setMakerfactoryid(bEntity.getMakerfactoryid().replace(dict.getValue(),dict.getLabel()));
                }
            });
            try {
                if (bEntity.getProcessId() != null) {
                    Task task = taskService.createTaskQuery().processInstanceId(bEntity.getProcessId()).singleResult();
                    if (task != null) {
                        Set<String> assigens = taskBunessService.getTaskCandidate(task.getId());
                        List<String> assigenss = new ArrayList<>();
                        for (String assigen : assigens) {
                            User user = feignService.getOne(assigen);
                            if (user != null) assigenss.add(assigen + "/" + user.getNickName());
                        }
                        if (assigens.size() > 1) {
                            bEntity.setSignPerson(ArrayUtil.join(assigenss.toArray(), ","));
                        } else if (assigens.size() == 1) {
                            bEntity.setSignPerson(assigenss.get(0));
                        }
                        bEntity.setSignNode(task.getName());
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        ExcelUtil<EntEquipmentAll> util = new ExcelUtil<EntEquipmentAll>(EntEquipmentAll.class);
        return util.exportExcelNew(list, "產線聯網設備入網申請單数据",dicts);
    }
    public List<Map<String,Object>> beanToMap(List<Map<String,Object>> lists){
        List<Map<String,Object>> result = new ArrayList<Map<String,Object>>();
        for(Map<String,Object> bean : lists){
            Map<String,Object> map = new HashMap<String,Object>();
            map.put("id",bean.get("value"));
            map.put("value",bean.get("label"));
            result.add(map);
        }
        return result;
    }
    @OperLog("啟動產線聯網設備入網申請單流程")
//	@PreAuthorize("@ps.hasPerm('entEquipmentNetwork_submit')")
    @GetMapping("/startProcess/{id}")
    public R startProcess(@PathVariable("id") String id) {
        /*List<DesignBuildForm> list = designBuildFormService.list(new QueryWrapper<DesignBuildForm>().eq("db_id", dataSourceId)
                .eq("table_id", tableId));
        if(list.size() == 0){
            return R.error("biz.design.buildForm.unDesign", new String[]{});
        }*/
        return R.ok(entEquipmentNetworkService.startProcess(entEquipmentNetworkService.getById(id)));
    }

    @ApiOperation("添加產線聯網設備入網申請單,并發起簽核流程")
    @PostMapping("/saveAndStartProcess")
    public R saveAndStartProcess(@Validated @RequestBody EntEquipmentNetwork entEquipmentNetwork) {
        entEquipmentNetwork.setWorkStatus(0);
        String tableName = EntEquipmentNetwork.class.getAnnotation(TableName.class).value();
        Map config = wfConfigService.getTableKey(tableName);
        entEquipmentNetwork.setSerialno(PinyinUtil.getFirstSpell(config.get("formname") + "") + com.entfrm.core.base.util.DateUtil.format(new Date(), "yyyyMMddHHmmss") + SerialNumberTool.getInstance().generaterNextNumber(4));
        entEquipmentNetwork.setMakerNo(SecurityUtil.getUser().getUsername());
        entEquipmentNetwork.setMakerName(SecurityUtil.getUser().getNickName());
        entEquipmentNetwork.setMakerdeptno(tQhUserformhsService.findByEmpnoForEntfrm(SecurityUtil.getUser().getUsername()).getDeptno());
        entEquipmentNetwork.setCreateDate(new Date());  //電子簽核必須字段
        entEquipmentNetworkService.save(entEquipmentNetwork);
        return R.ok(entEquipmentNetworkService.startProcess(entEquipmentNetwork));
    }

    @OperLog("產線聯網設備入網申請單修改,并發起簽核流程")
    @ApiOperation("產線聯網設備入網申請單修改,并發起簽核流程")
    @PutMapping("/updateAndStartProcess")
    public R updateAndStartProcess(@Validated @RequestBody EntEquipmentNetwork entEquipmentNetwork) {
        entEquipmentNetworkService.updateById(entEquipmentNetwork);
        return R.ok(entEquipmentNetworkService.startProcess(entEquipmentNetwork));
    }

    @OperLog("產線聯網設備入網申請單修改,并發起簽核流程")
    @ApiOperation("產線聯網設備入網申請單修改,并發起簽核流程")
    @PutMapping("/updateAndResubmitProcess")
    public R updateAndResubmitProcess(@Validated @RequestBody EntEquipmentNetwork entEquipmentNetwork) {
        HttpServletRequest request = ((ServletRequestAttributes) Objects
                .requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
        String ip = ServletUtil.getClientIP(request);
        entEquipmentNetworkService.updateById(entEquipmentNetwork);
        Field fields[] = entEquipmentNetwork.getClass().getDeclaredFields();
        Map variables = Maps.newHashMap();
        Arrays.stream(fields).forEach(field -> {
                    /*SignNode signNode = field.getAnnotation(SignNode.class);
                    if (ObjectUtil.isNotEmpty(signNode)) {
                        variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entEquipmentNetwork, field)) ? "AUTO_SIGN" : ReflectUtil.getFieldValue(entEquipmentNetwork, field));
                    }*/
                    SignNode signNode = field.getAnnotation(SignNode.class);
                    if (ObjectUtil.isNotEmpty(signNode)) {
                        if (signNode.type().equals(SignNode.Type.COMMON)) {
                            variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entEquipmentNetwork, field)) ? "AUTO_SIGN" : ReflectUtil.getFieldValue(entEquipmentNetwork, field));
                        } else if (signNode.type().equals(SignNode.Type.HUIQIAN)) {
                            variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entEquipmentNetwork, field)) ? Arrays.asList("AUTO_SIGN") : Arrays.asList(ReflectUtil.getFieldValue(entEquipmentNetwork, field).toString().split(",")));
                        }
                    }
                }
        );
        runtimeService.setVariables(entEquipmentNetwork.getProcessId(), variables);
        LeaveDto leaveDto = new LeaveDto();
        leaveDto.setProcessId(entEquipmentNetwork.getProcessId());
        leaveDto.setPass("5");
        leaveDto.setOperIp(ip);
        leaveDto.setComment("重新提交");
        taskBunessService.checkTask(leaveDto);
        return R.ok();
    }

    List<Field> fieldsList;
    List userList;

    @GetMapping("/getSignPath/{processId}")
    public R getSignPath(@PathVariable("processId") String processId) {
        EntEquipmentNetwork entEquipmentNetwork = entEquipmentNetworkService.getOne(new QueryWrapper<EntEquipmentNetwork>().eq("process_id", processId));
        Field fields[] = EntEquipmentNetwork.class.getDeclaredFields();
        userList = new ArrayList();
        fieldsList = new ArrayList();
        Arrays.stream(fields).forEach(field -> {
                    SignNode signNode = field.getAnnotation(SignNode.class);
                    if (ObjectUtil.isNotEmpty(signNode)) {
                        fieldsList.add(field);
                    }
                }
        );
        fieldsList.sort(Comparator.comparingInt(
                m -> ((SignNode) m.getAnnotation(SignNode.class)).order()
        ));
        Task task = taskService.createTaskQuery().processInstanceId(processId).singleResult();
        /*String tableName = EntEquipmentNetwork.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
        WfConfig config = wfConfigService.getOne(new QueryWrapper<WfConfig>().eq("workflow_key", wfConfigDto.getProcDefKey()));*/
        fieldsList.stream().forEach(field -> {
            StringBuffer signPath = new StringBuffer();
            TableSignConfig config = signConfigService.getSignConfig("EntEquipmentNetwork", field.getName());
            if (null != task && field.getName().equals(task.getTaskDefinitionKey())) {
                if (config != null) {
                    userList.add(signPath.append("<font color=\"red\" size=\"4\"><strong>").append(config.getColValue()).append("(").append(ReflectUtil.getFieldValue(entEquipmentNetwork, field.getName())).append("/").append(ReflectUtil.getFieldValue(entEquipmentNetwork, com.entfrm.core.base.util.StrUtil.replaceLast(field.getName(), "no", "name"))).append(")").append("</strong></font>"));
                } else {
                    userList.add(signPath.append("<font color=\"red\" size=\"4\"><strong>").append(((SignNode) field.getAnnotation(SignNode.class)).nodeName()).append("(").append(ReflectUtil.getFieldValue(entEquipmentNetwork, field.getName())).append("/").append(ReflectUtil.getFieldValue(entEquipmentNetwork, com.entfrm.core.base.util.StrUtil.replaceLast(field.getName(), "no", "name"))).append(")").append("</strong></font>"));
                }
            } else {
                if (config != null) {
                    userList.add(signPath.append(config.getColValue()).append("(").append(ReflectUtil.getFieldValue(entEquipmentNetwork, field.getName())).append("/").append(ReflectUtil.getFieldValue(entEquipmentNetwork, com.entfrm.core.base.util.StrUtil.replaceLast(field.getName(), "no", "name"))).append(")"));
                } else {
                    userList.add(signPath.append(((SignNode) field.getAnnotation(SignNode.class)).nodeName()).append("(").append(ReflectUtil.getFieldValue(entEquipmentNetwork, field.getName())).append("/").append(ReflectUtil.getFieldValue(entEquipmentNetwork, com.entfrm.core.base.util.StrUtil.replaceLast(field.getName(), "no", "name"))).append(")"));
                }
            }
        });
//		System.out.println(JSONUtil.toJsonStr(ArrayUtil.join(userList.toArray(), "-->")).replace("null",""));
        return R.ok(JSONUtil.toJsonStr(ArrayUtil.join(userList.toArray(), "-->")).replace("null", ""));
    }
    @SneakyThrows
    @OperLog("產線聯網設備入網申請單詳情数据导入")
    @PostMapping("/importEntEquipmentItems")
    public R importEntEquipmentItems(MultipartFile file, boolean updateSupport) {
        ExcelUtil<EntEquipmentItems> util = new ExcelUtil<EntEquipmentItems>(EntEquipmentItems.class);
        Map<String,List<Map<String,Object>>> dicts = new HashMap<String,List<Map<String,Object>>>();
        List<EntEquipmentItems> userList = util.importExcelNew(file.getInputStream(),dicts);
        return R.ok(userList);
    }
    @OperLog("通過類名動態獲取審核節點名稱")
    @GetMapping("/getSignConfigList")
    public R getSignConfigList() {
        return R.ok(signConfigService.getSignConfigList("EntEquipmentNetwork"));
    }
}
