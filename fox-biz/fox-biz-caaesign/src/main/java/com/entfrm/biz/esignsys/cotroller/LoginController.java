package com.entfrm.biz.esignsys.cotroller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.http.HttpUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.entfrm.biz.esignsys.dto.EsignTQhUserformhsDto;
import com.entfrm.biz.esignsys.dto.WebLoginDto;
import com.entfrm.biz.esignsys.entity.EsignTPubFileobjectEntity;
import com.entfrm.biz.esignsys.entity.EsignTQhUserformhsEntity;
import com.entfrm.biz.esignsys.entity.EsignUser;
import com.entfrm.biz.esignsys.service.EsignDictService;
import com.entfrm.biz.esignsys.service.EsignFileobjectService;
import com.entfrm.biz.esignsys.service.EsignTQhUserformhsService;
import com.entfrm.biz.esignsys.service.EsignUserService;
import com.entfrm.biz.feign.FeignDmzService;
import com.entfrm.biz.feign.FeignService;
import com.entfrm.biz.system.service.ConfigService;
import com.entfrm.biz.system.util.JsonUtils;
import com.entfrm.core.base.api.R;
import com.entfrm.core.base.util.AESUtil;
import com.entfrm.core.security.entity.EntfrmUser;
import com.entfrm.core.security.util.SecurityUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/3/14
 * @description token 管理
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/login")
public class LoginController {

    private final ConfigService configService;
    public static final String TOKEN = "Token";
    public static final String TIME = "Time";
    public static final String USERNO = "UserNO";

    @Autowired
    private EsignDictService dictService;
    @Autowired
    private EsignUserService esignUserService;
    @Autowired
    private FeignService feignService;
    @Autowired
    private EsignTQhUserformhsService tQhUserformhsService;
    private final EsignFileobjectService fileobjectService;
    @Autowired
    private FeignDmzService feignDmzService;


    /**
     * 方法描述: 愛口袋登錄
     * @Author: F1858847
     * @CreateDate:   2023/6/29  8:35
     * @Return
     **/
    @GetMapping("/webLogin")
    @ResponseBody
    public String webLogin() {
        HttpServletRequest servletRequest = ((ServletRequestAttributes) Objects
                .requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
        WebLoginDto webLoginDto = getArgsFromAesUrl(servletRequest);
        if(webLoginDto.getToken() != null && webLoginDto.getUserNo() != null) {
            if (dictService.get(webLoginDto.getToken())) {
                String str = getEsignLoginToken(webLoginDto.getUserNo());
                return str;
            }
        }
        return null;
    }

    private String getEsignLoginToken(String empNo) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("Authorization", "Basic ZXNpZ25BcHA6ZXNpZ25BcHA=");
        headers.add("Content-Type", "application/json;charset=UTF-8");

        MultiValueMap<String,String> parameters = new LinkedMultiValueMap<>();
        parameters.add("openId",empNo);
        parameters.add("grant_type","openId");
        String str = feignService.oauth2Login(parameters,headers);
        return str;
    }


    @RequestMapping("mobileLogin")
    @ResponseBody
    public Object mobileLogin(@RequestParam String empNo, @RequestParam String password) throws Exception {

        empNo = StringUtils.leftPad(empNo, 6, "0");
        //電子簽核平台登錄本地賬號
        boolean login = esignUserService.login(empNo, password);
        if (login) {
            EsignTQhUserformhsDto userInfo = getUserInfo(empNo);
            return R.ok(userInfo);
        }else{
            //使用一賬通登錄
            MultiValueMap<String,String> parameters = new LinkedMultiValueMap<>();
            parameters.add("username",empNo);
            parameters.add("password",password);
            // 构造头部信息(若有需要)
            HttpHeaders headers = new HttpHeaders();
            headers.add("Authorization", "Basic ZXNpZ25BcHA6ZXNpZ25BcHA=");
            headers.add("Content-Type", "application/json;charset=UTF-8");
//            R r = feignDmzService.ssoLogin(parameters,headers);
            HttpServletRequest request = ((ServletRequestAttributes) Objects
                    .requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
            String userIp = ServletUtil.getClientIP(request);
            R r = feignDmzService.ssoLogin(empNo,password,userIp);
//            System.out.println(r.getData().toString());
            if(R.SUCCESS == r.getCode()){
                EsignTQhUserformhsDto userInfo = getUserInfo(empNo);
                return R.ok(userInfo);
            }
        }
        return R.error();
    }

    private EsignTQhUserformhsDto getUserInfo(String empNo) {
        List<EsignTQhUserformhsEntity> esigntQhUserformhsEntitys = tQhUserformhsService.list(new QueryWrapper<EsignTQhUserformhsEntity>()
                .eq(StrUtil.isNotBlank(empNo), "EMPNO", empNo));
        EsignTQhUserformhsEntity tQhUserformhsEntity = new EsignTQhUserformhsEntity();
        if(esigntQhUserformhsEntitys.size() > 0){
            tQhUserformhsEntity = esigntQhUserformhsEntitys.get(0);
        }
        EsignTQhUserformhsDto esignTQhUserformhsDto = new EsignTQhUserformhsDto();
        BeanUtil.copyProperties(tQhUserformhsEntity,esignTQhUserformhsDto);
        String esignLoginToken = getEsignLoginToken(empNo);
        OAuth2AccessToken oAuth2AccessToken = JsonUtils.parseObject(esignLoginToken,OAuth2AccessToken.class);
        esignTQhUserformhsDto.setToken(oAuth2AccessToken.getValue());
        //獲取簽名地址
        List<EsignTPubFileobjectEntity> entitys = fileobjectService.list(new QueryWrapper<EsignTPubFileobjectEntity>()
                .eq(StrUtil.isNotBlank(empNo), "NAME", empNo));
        if (entitys != null && entitys.size() > 0) {
            esignTQhUserformhsDto.setSignFilePath("/caaesign/wffileesignprocess/showSignatureImg");
        }
        //獲取個人信息
        List<EsignUser>  sysusers=esignUserService.list(new QueryWrapper<EsignUser>()
                .eq(StrUtil.isNotBlank(empNo), "LOGIN_NAME", empNo));
        if (sysusers != null && sysusers.size() > 0) {
            esignTQhUserformhsDto.setEmail(sysusers.get(0).getEmail());
            esignTQhUserformhsDto.setPhone(sysusers.get(0).getPhone());
            esignTQhUserformhsDto.setNotification(sysusers.get(0).getNotification());
        }
        return esignTQhUserformhsDto;
    }

    public WebLoginDto getArgsFromAesUrl(HttpServletRequest request){
        String servletPath = request.getQueryString();
        WebLoginDto webLoginDto = new WebLoginDto();
        if(StrUtil.isNotBlank(servletPath)) {
            String urlDecode = AESUtil.decryptWithIv(servletPath, AESUtil.mobileLoginkey);
            Map<String, String> argMap = HttpUtil.decodeParamMap(urlDecode, CharsetUtil.UTF_8);
            webLoginDto.setUserNo(argMap.get(USERNO));
            webLoginDto.setToken(argMap.get(TOKEN));
            webLoginDto.setTime(argMap.get(TIME));
        }
        return webLoginDto;
    }



}
