package com.entfrm.biz.caaesign.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.entfrm.biz.activiti.dto.LeaveDto;
import com.entfrm.biz.caaesign.entity.EntGuestCancel;

/**
 * <AUTHOR>
 * @date 2024-10-17 08:03:26
 *
 * @description iPEBG移轉設備銷賬聯絡單Service接口
 */
public interface EntGuestCancelService extends IService<EntGuestCancel> {
    	/**
     * 启动流程
     *
     * @param entGuestCancel
     * @return
     */
	Boolean startProcess(EntGuestCancel entGuestCancel);
	/**
	 * 審核需要更新主表調用方法
	 *
	 * @param entGuestCancel
	 * @return
	 */
	public Boolean updateAndCheck(EntGuestCancel entGuestCancel, LeaveDto leaveDto);

	public boolean updateAttachids(EntGuestCancel entGuestCancel);
}
