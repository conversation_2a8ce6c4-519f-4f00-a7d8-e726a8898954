package com.entfrm.biz.caaesign.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
    import com.baomidou.mybatisplus.annotation.TableName;
import com.entfrm.biz.activiti.dto.WfConfigDto;
import com.entfrm.biz.activiti.entity.TQhAllRelation;
import com.entfrm.biz.activiti.service.TQhAllRelationService;
import com.entfrm.biz.activiti.service.WfConfigService;
import com.entfrm.core.base.annotation.SignNode;
import com.entfrm.core.security.util.SecurityUtil;
import com.google.common.collect.Maps;
import org.activiti.engine.task.Task;
import org.springframework.stereotype.Service;
import com.entfrm.biz.caaesign.mapper.EntRemoteEmpowermentItemsMapper;
import com.entfrm.biz.caaesign.entity.EntRemoteEmpowermentItems;
import com.entfrm.biz.caaesign.service.EntRemoteEmpowermentItemsService;
import com.entfrm.core.base.util.PinyinUtil;
import com.entfrm.core.data.util.SerialNumberTool;
import cn.hutool.core.date.DateUtil;
import lombok.AllArgsConstructor;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.runtime.ProcessInstance;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.transaction.annotation.Transactional;
import com.entfrm.core.base.enums.TaskStatusEnum;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Map;
import com.entfrm.core.data.constant.CommonConstant;

/**
 * <AUTHOR>
 * @date 2021-11-04 09:44:16
 *
 * @description 產線遠端賦權帳號申請單明細表Service业务层
 */
@Service
@AllArgsConstructor
public class EntRemoteEmpowermentItemsServiceImpl extends ServiceImpl<EntRemoteEmpowermentItemsMapper, EntRemoteEmpowermentItems> implements EntRemoteEmpowermentItemsService {
	private final RuntimeService runtimeService;
	private final org.activiti.engine.TaskService actTaskService;
	private final WfConfigService wfConfigService;
	private final TQhAllRelationService allRelationService;
	private final RabbitTemplate rabbitTemplate;
    	/**
	 * 启动流程
	 *
	 * @param entRemoteEmpowermentItems
	 * @return
	 */
	@Override
	public Boolean startProcess(EntRemoteEmpowermentItems entRemoteEmpowermentItems) {
        entRemoteEmpowermentItems.setWorkStatus(Integer.parseInt(TaskStatusEnum.CHECKING.getStatus()));
		String tableName = EntRemoteEmpowermentItems.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
		String procDefKey = wfConfigDto.getProcDefKey();
		String businessKey = procDefKey + CommonConstant.BREAK_BUSINESS_KEY + entRemoteEmpowermentItems.getId();
		Field fields[] = entRemoteEmpowermentItems.getClass().getDeclaredFields();
        Map variables = Maps.newHashMap();
        //設置填單人
        variables.put("maker", SecurityUtil.getUser().getUsername());
        variables.put("serialNo", entRemoteEmpowermentItems.getSerialno());
        variables.put("workflowName", wfConfigDto.getFormName());
        Arrays.stream(fields).forEach(field -> {
                    SignNode signNode = field.getAnnotation(SignNode.class);
                    if (ObjectUtil.isNotEmpty(signNode)) {
                        if (signNode.type().equals(SignNode.Type.COMMON)) {
                            variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entRemoteEmpowermentItems, field)) ? "AUTO_SIGN" : ReflectUtil.getFieldValue(entRemoteEmpowermentItems, field));
                        } else if (signNode.type().equals(SignNode.Type.HUIQIAN)) {
                            variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(entRemoteEmpowermentItems, field)) ? Arrays.asList("AUTO_SIGN") : Arrays.asList(ReflectUtil.getFieldValue(entRemoteEmpowermentItems, field).toString().split(",")));
                        }
                    }
                }
        );
		ProcessInstance pi = runtimeService.startProcessInstanceByKey(procDefKey, businessKey,variables);
        entRemoteEmpowermentItems.setProcessId(pi.getProcessInstanceId());
		super.updateById(entRemoteEmpowermentItems);

		TQhAllRelation relation = allRelationService.getOne(new QueryWrapper<TQhAllRelation>().eq("serialno",entRemoteEmpowermentItems.getSerialno()));
		relation.setWorkStatus(entRemoteEmpowermentItems.getWorkStatus());
		relation.setProcessId(pi.getProcessInstanceId());
        Task task = actTaskService.createTaskQuery().processInstanceId(pi.getProcessInstanceId()).singleResult();
        relation.setNodename(task.getName());
        relation.setDeptNo(entRemoteEmpowermentItems.getMakerdeptno());
		allRelationService.updateById(relation);
		//添加mq
		rabbitTemplate.convertAndSend("SignDirectExchange", "Sign.Direct.Routing", BeanUtil.beanToMap(relation));
		return Boolean.TRUE;
	}

	/**
     * 插入一条记录（选择字段，策略插入）
     *
     */
	@Override
	public boolean save(EntRemoteEmpowermentItems entRemoteEmpowermentItems) {
		boolean rs = super.save(entRemoteEmpowermentItems);
		String tableName = EntRemoteEmpowermentItems.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
		TQhAllRelation relation = new TQhAllRelation();
		relation.setDtoName(tableName);
		relation.setMakerNo(entRemoteEmpowermentItems.getMakerNo());
		relation.setMakerName(entRemoteEmpowermentItems.getMakerName());
		relation.setSerialno(entRemoteEmpowermentItems.getSerialno());
		relation.setWorkflowid(wfConfigDto.getProcDefKey());
		relation.setWfName(wfConfigDto.getFormName());
		relation.setClassPackage(entRemoteEmpowermentItems.getClass().getName());
		relation.setWorkStatus(0);
		allRelationService.save(relation);
		return rs;
	}
}
