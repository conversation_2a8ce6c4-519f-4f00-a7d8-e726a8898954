<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.entfrm.biz.esignsys.mapper.SignModelPositionMapper">

    <resultMap type="com.entfrm.biz.esignsys.dto.SignModelPositionDto" id="SignModelPositionResult">
        <result property="id"    column="id"    />
        <result property="applyId"    column="apply_id"    />
        <result property="signPoint"    column="sign_point"    />
        <result property="signNo"    column="sign_no"    />
        <result property="signName"    column="sign_name"    />
        <result property="showOrder"    column="show_order"    />
        <result property="positionX"    column="position_x"    />
        <result property="positionY"    column="position_y"    />
        <result property="signPointOrder"    column="sign_point_order"    />
        <result property="pageNumber"    column="page_number"    />
        <result property="modelId"    column="model_id"    />
        <result property="imgUrl"    column="imgUrl"    />
        <result property="signTime"    column="signTime"    />
        <result property="decrib"    column="decrib"    />
        <result property="remarkShow"    column="remark_show"    />
        <result property="whetherToSign"    column="whether_to_sign"    />
        <result property="proxyChargeno"    column="proxy_chargeno"    />
        <result property="proxyChargename"    column="proxy_chargename"    />
    </resultMap>


    <!--通过角色查询菜单-->
    <select id="findListByModelID"  resultMap="SignModelPositionResult">
        select s.*,t.url as imgUrl
          from SIGN_MODEL_POSITION s
          left join T_PUB_FILEOBJECT t
          on s.sign_no = t.name
         where model_id = #{modelId} and s.del_flag = '0' and s.sign_type = '0'
         order by s.show_order,s.sign_point_order
    </select>

    <!--查詢簽核時間-->
    <select id="findSignHistoryBySerialno"  resultMap="SignModelPositionResult">
        select s.*,t.url as ImgUrl,to_char(k.create_date,'yyyy/mm/dd HH24:mi') as signTime,k.decrib as decrib,'2' flag, k.ispass,
               k.whether_to_sign , k.proxy_chargeno , k.proxy_chargename
            from (select c.*,
                           case
                             when (select max(create_date)
                                     from T_QH_CHARGELOG
                                    where serialno = #{serialno}
                                      and ispass = '駁回') > c.create_date then
                              '1'
                             else
                              '0'
                           end as show_mark
                      from T_QH_CHARGELOG c
                     where serialno = #{serialno}) k
                     left join  (select * from WF_NODEINFO where workflowid = 'dzqh_wendangqianheshenqingdan') q
                     on q.nodename = k.chargenode
                     left join (select * from sign_model_position where apply_id = #{serialno} and del_flag = '0') s
                     on q.colname = s.sign_point and s.sign_no = k.chargeno
                     left join T_PUB_FILEOBJECT t
                     on k.proxy_chargeno = t.name
        where k.show_mark = '0'
          and s.sign_type = '0'
             order by s.show_order,s.sign_point_order
    </select>

    <select id="findAllBySerialno"  resultType="com.entfrm.biz.esignsys.dto.SignModelPositionDto">
        select s.id,
               s.APPLY_ID as applyId,
               s.SIGN_POINT as signPoint,
               s.SIGN_NO as signNo,
               s.SIGN_NAME as signName,
               s.SHOW_ORDER as showOrder,
               s.POSITION_X as positionX,
               s.POSITION_Y as positionY,
               s.SIGN_POINT_ORDER as signPointOrder,
               s.PAGE_NUMBER as pageNumber,
               s.MODEL_ID as modelId,
               s.REMARK_SHOW as remarkShow,
               s.IMG_SHOW_TYPE as imgShowType,
               s.IMG_WIDTH as imgWidth,
               t.url as ImgUrl
        from (select * from sign_model_position
              where apply_id = #{serialno} and del_flag = '0'
                and show_order &gt;= (select case when max(show_order) is null then 0 else  max(show_order) end as showOrder
                                      from sign_model_position
                                      where apply_id = #{serialno} and del_flag = '0' and sign_type = '1')
             ) s
                 left join T_PUB_FILEOBJECT t
                           on s.sign_no = t.name
        where s.sign_type = '0'
        order by s.show_order, s.sign_point_order
    </select>


    <select id="getEmpNoByFileID"  resultType="java.lang.String">
        select create_by
        from T_QH_WFFILESIGNPROCESS
        where attachids1 like '%'||#{modelId}||'%' or  attachids2 like '%'||#{modelId}||'%' or  attachids3 like '%'||#{modelId}||'%'
    </select>


    <select id="findReturnInfo"  resultType="java.lang.String">
        select *
        from T_QH_CHARGELOG
        where serialno = #{serialno}
          and ispass= '駁回'
    </select>

    <!--查詢簽核時間-->
    <select id="findReturnSignHistoryBySerialno1"  resultMap="SignModelPositionResult">
        select s.*,t.url as ImgUrl,to_char(k.create_date,'yyyy/mm/dd HH24:mi') as signTime,k.decrib as decrib,'2' flag, k.ispass,
               k.whether_to_sign , k.proxy_chargeno , k.proxy_chargename
        from (select c.*,'0' as show_mark
              from T_QH_CHARGELOG  c
              where serialno = #{serialno}
                and  create_date &gt; (select create_date
                                       from (select rownum r, create_date
                                             from (select create_date
                                                   from T_QH_CHARGELOG t
                                                   where serialno = #{serialno}
                                                     and ispass= '駁回'
                                                   order by create_date desc)
                                             where rownum &lt; 3 )  e
                                       where e.r &gt; 1)
             ) k
                 left join  (select * from WF_NODEINFO where workflowid = 'dzqh_wendangqianheshenqingdan') q
                            on q.nodename = k.chargenode
                 left join (select * from sign_model_position where apply_id = #{serialno} and del_flag = '0') s
                           on q.colname = s.sign_point and s.sign_no = k.chargeno
                 left join T_PUB_FILEOBJECT t
                           on k.proxy_chargeno = t.name
        where  k.show_mark = '0'
          and s.sign_type = '0'
        order by s.show_order,s.sign_point_order
    </select>

    <select id="findReturnSignHistoryBySerialno2"  resultMap="SignModelPositionResult">
        select s.*,t.url as ImgUrl,to_char(k.create_date,'yyyy/mm/dd HH24:mi') as signTime,k.decrib as decrib,'2' flag, k.ispass,
           k.whether_to_sign , k.proxy_chargeno , k.proxy_chargename
        from (select c.* ,'0' as show_mark from  T_QH_CHARGELOG c
              where serialno = #{serialno}) k
                 left join  (select * from WF_NODEINFO where workflowid = 'dzqh_wendangqianheshenqingdan') q
                            on q.nodename = k.chargenode
                 left join (select * from sign_model_position where apply_id = #{serialno} and del_flag = '0') s
                           on q.colname = s.sign_point and s.sign_no = k.chargeno
                 left join T_PUB_FILEOBJECT t
                           on k.proxy_chargeno = t.name
        where k.show_mark = '0'
          and s.sign_type = '0'
        order by s.show_order,s.sign_point_order
    </select>
</mapper>
