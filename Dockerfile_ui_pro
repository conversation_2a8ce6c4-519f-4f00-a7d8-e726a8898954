#V1.0   Dockerfile文件编写
# 基础镜像
FROM hub.ipebg.com/library/nginx
# 维护者信息
MAINTAINER <EMAIL>
LABEL version = "1.0"
LABEL description = "entfrm"
#数据卷
VOLUME /entfrm-ui
#解决时间
ENV TZ=Asia/TaiYuan
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime \
&&  echo $TZ > /etc/timezone\
#创建nginx运行基础目录
&&  mkdir -p /run/nginx /var/www/html/entfrm-ui \
#覆盖nginx配置
&&  echo "worker_processes  auto;\
          worker_rlimit_nofile 65530;\
          events {\
              use epoll;\
              multi_accept on;\
              worker_connections 65530;\
          }\
          http {\
              include       mime.types;\
              default_type  application/octet-stream;\
              sendfile        on;\
              keepalive_timeout  65;\
          upstream zltGateway{\
              server **************:9900 weight=1 max_fails=2 fail_timeout=15s;\
          } \
          server{\
              listen	80;\
              server_name localhost;\
              location / {\
                      root  /var/www/html/entfrm-ui/;\
                      try_files \$uri \$uri/ /index.html;\
                      index  index.html index.htm;\
              }\
              location /login{\
          	root /var/www/html/entfrm-ui/;\
                  rewrite /login /index.html;\
              }\
              location /pro/zlt{\
              	rewrite ^/pro/zlt/(.*)\$ /\$1 break;\
          	proxy_pass http://zltGateway/;\
                  proxy_connect_timeout 600;\
                  proxy_buffer_size 512k;\
                  proxy_buffers 16 512k;\
                  proxy_busy_buffers_size 512k;\
                  proxy_temp_file_write_size 512k;\
                  proxy_max_temp_file_size 512m;\
                  proxy_ignore_client_abort on;\
                  proxy_set_header Host \$host:80;\
                  proxy_set_header X-Real-IP \$remote_addr;\
                  proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;\
              }\
              location /pro{\
                  proxy_pass http://zltGateway/api-entfrm;\
                  proxy_connect_timeout 600;\
                  proxy_buffer_size 512k;\
                  proxy_buffers 16 512k;\
                  proxy_busy_buffers_size 512k;\
                  proxy_temp_file_write_size 512k;\
                  proxy_max_temp_file_size 512m;\
                  proxy_ignore_client_abort on;\
                  proxy_set_header Host \$host:80;\
                  proxy_set_header X-Real-IP \$remote_addr;\
                  proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;\
              }\
          } \
          }" > /etc/nginx/nginx.conf
#添加数据到entfrm-ui
ADD dist/ /var/www/html/entfrm-ui/