import Vue from 'vue'

import Cookies from 'js-cookie'

import 'normalize.css/normalize.css' // a modern alternative to CSS resets

import Element from 'element-ui'
import './assets/styles/element-variables.scss'
 import Cube from 'cube-ui'

import '@/assets/styles/index.scss' // global css
import '@/assets/styles/entfrm.scss' // entfrm css
import App from './App'
import store from './store'
import router from './router'
import permission from './directive/permission'
import './assets/styles/mobileSkin/index.scss'



import './assets/icons' // icon
import './permission' // permission control
import { getDicts,getOptions } from "@/api/system/dictData";
import { getInfoUserByEmpno  } from "@/api/zlt/info/user";
import { getByKey } from "@/api/system/config";
import {  getAppBaseApi,  getHeader} from "@/utils/entfrm";
import {
  parseTime, newPath, resetForm, addDateRange, selectDictLabel, selectDictLabels,selectDictLabelss,
  download, handleTree, selectDataLabel, isMobileFun, getBoundingClientRect,
  changeTagsView, closeFormDefault, downloadExcel,
  downloadWord,
  downloadZip,
  downloadHtml,
  downloadMarkdown, addDateSignRange,verifyMathematical,analysisMathematical
} from "@/utils/entfrm";
import Pagination from "@/components/Pagination";
import EntfrmSignForm from "@/components/Entfrm/EntfrmSignForm";
import EntfrmSignFormDuty from "@/components/Entfrm/EntfrmSignFormDuty";
import EntfrmSignFormAuditHq from "@/components/Entfrm/EntfrmSignFormAuditHq";
import EntfrmSignFormAddHq from "@/components/Entfrm/EntfrmSignFormAddHq";
import EntfrmSignFormByCondition from "@/components/Entfrm/EntfrmSignFormByCondition";
import EntfrmChildTableForm from "@/components/Entfrm/EntfrmChildTableForm";
import CalculateExpressionsBuilderDialog from "@/components/Entfrm/CalculateExpressionsBuilderDialog";
import LabelForm from "@/components/Entfrm/LabelForm";
import PageTopBar from "@/mobileComponents/cube-page.vue";
import PageTopBarEsign from "@/mobileComponents/cube-page-esign.vue";
// import FoxTable from "@/components/Fox/Fox-table";
import request from '@/utils/request'

import mavonEditor from 'mavon-editor'
import 'mavon-editor/dist/css/index.css'
import i18n from './i18n/config'
import db from "@/utils/localstorage"; // internationalization
import Print from 'vue-print-nb'
import './assets/font/fontPear/iconfont.css'
import CodeViewer from "@/components/code-viewer";


// 全局方法挂载
Vue.prototype.getDicts = getDicts
Vue.prototype.getOptions = getOptions
Vue.prototype.getInfoUserByEmpno = getInfoUserByEmpno
Vue.prototype.getHeader = getHeader
Vue.prototype.getAppBaseApi = getAppBaseApi
Vue.prototype.request = request

Vue.prototype.getByKey = getByKey
Vue.prototype.parseTime = parseTime
Vue.prototype.newPath = newPath
Vue.prototype.resetForm = resetForm
Vue.prototype.addDateRange = addDateRange
Vue.prototype.addDateSignRange = addDateSignRange
Vue.prototype.selectDictLabel = selectDictLabel
Vue.prototype.selectDictLabels = selectDictLabels
Vue.prototype.selectDictLabelss = selectDictLabelss
Vue.prototype.selectDataLabel = selectDataLabel
Vue.prototype.download = download
Vue.prototype.handleTree = handleTree
Vue.prototype.isMobileFun = isMobileFun
Vue.prototype.getBoundingClientRect = getBoundingClientRect
Vue.prototype.changeTagsView = changeTagsView
Vue.prototype.closeFormDefault = closeFormDefault
Vue.prototype.downloadExcel = downloadExcel
Vue.prototype.downloadWord = downloadWord
Vue.prototype.downloadHtml = downloadHtml
Vue.prototype.downloadMarkdown = downloadMarkdown
Vue.prototype.downloadZip = downloadZip
Vue.prototype.verifyMathematical = verifyMathematical
Vue.prototype.analysisMathematical = analysisMathematical




Vue.prototype.msgSuccess = function (msg) {
  this.$message({ showClose: true, message: msg, type: "success" });
}

Vue.prototype.msgWarning = function (msg) {
  this.$message({ showClose: true, message: msg, type: "warning" });
}

Vue.prototype.msgError = function (msg) {
  this.$message({ showClose: true, message: msg, type: "error" });
}

Vue.prototype.msgErrorUseHTMLString = function (msg) {
  this.$message({ showClose: true, message: msg, type: "error",dangerouslyUseHTMLString: true, });
}

Vue.prototype.msgInfo = function (msg) {
  this.$message.info(msg);
}

// 全局组件挂载
Vue.component('Pagination', Pagination)
Vue.component('EntfrmSignForm', EntfrmSignForm)
Vue.component('EntfrmSignFormDuty', EntfrmSignFormDuty)
Vue.component('EntfrmSignFormAuditHq', EntfrmSignFormAuditHq)
Vue.component('EntfrmSignFormAddHq', EntfrmSignFormAddHq)
Vue.component('EntfrmChildTableForm', EntfrmChildTableForm)
Vue.component('EntfrmSignFormByCondition',EntfrmSignFormByCondition)
Vue.component('CalculateExpressionsBuilderDialog', CalculateExpressionsBuilderDialog)

Vue.component('LabelForm', LabelForm)
Vue.component('PageTopBar', PageTopBar)
Vue.component('PageTopBarEsign', PageTopBarEsign)

Vue.use(permission)

Vue.use(mavonEditor)
Vue.use(Print);
Vue.use(CodeViewer);

/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online! ! !
 */

Vue.use(Element, {
  size: db.getItem('size') || 'medium' // set element-ui default size
})

Vue.use(Element, {
  i18n: (key, value) => i18n.t(key, value)
})
 Vue.use(Cube)

Vue.config.productionTip = false

new Vue({
  el: '#app',
  router,
  store,
  i18n,
  render: h => h(App)
})

// Vue.use(FoxTable)
