import request from '@/utils/request'

// 查询iPEBG移轉設備銷賬聯絡單列表
export function listEntGuestCancel(query) {
  return request({
    url: '/caaesign/entGuestCancel/list',
    method: 'get',
    params: query
  })
}

// 查询iPEBG移轉設備銷賬聯絡單手機端列表
export function listEntGuestCancelApp(query) {
  return request({
    url: '/caaesign/entGuestCancel/listApp',
    method: 'get',
    params: query
  })
}

// 查询iPEBG移轉設備銷賬聯絡單详细
export function getEntGuestCancel(id) {
  return request({
    url: '/caaesign/entGuestCancel/' + id,
    method: 'get'
  })
}

// 新增iPEBG移轉設備銷賬聯絡單
export function addEntGuestCancel(data) {
  return request({
    url: '/caaesign/entGuestCancel/save',
    method: 'post',
    data: data
  })
}

// 修改iPEBG移轉設備銷賬聯絡單
export function editEntGuestCancel(data) {
  return request({
    url: '/caaesign/entGuestCancel/update',
    method: 'put',
    data: data
  })
}
export function updateAttachids(data) {
  return request({
    url: '/caaesign/entGuestCancel/updateAttachids',
    method: 'put',
    data: data
  })
}
// 删除iPEBG移轉設備銷賬聯絡單
export function delEntGuestCancel(id) {
  return request({
    url: '/caaesign/entGuestCancel/remove/' + id,
    method: 'delete'
  })
}

// 启动请假流程
export function startProcess(id) {
  return request({
    url: '/caaesign/entGuestCancel/startProcess/' + id,
    method: 'get'
  })
}

// 导出iPEBG移轉設備銷賬聯絡單
export function exportEntGuestCancel(query) {
  return request({
    url: '/caaesign/entGuestCancel/export',
    method: 'get',
    params: query
  })
}


// 新增iPEBG移轉設備銷賬聯絡單并發起流程
export function addEntGuestCancelAndStartProcess(data) {
  return request({
    url: '/caaesign/entGuestCancel/saveAndStartProcess',
    method: 'post',
    data: data
  })
}

// 修改iPEBG移轉設備銷賬聯絡單并發起流程
export function editEntGuestCancelAndStartProcess(data) {
  return request({
    url: '/caaesign/entGuestCancel/updateAndStartProcess',
    method: 'put',
    data: data
  })
}

// 修改iPEBG移轉設備銷賬聯絡單,并重新提交
export function editEntGuestCancelAndResubmitProcess(data) {
  return request({
    url: '/caaesign/entGuestCancel/updateAndResubmitProcess',
    method: 'put',
    data: data
  })
}



//獲取簽核路勁信息
export function getSignPath(processId) {
  return request({
    url: '/caaesign/entGuestCancel/getSignPath/' + processId,
    method: 'get'
  })
}


//獲取簽核路勁信息
export function getSignPathApp(processId) {
  return request({
    url: '/caaesign/entGuestCancel/getSignPathApp/' + processId,
    method: 'get'
  })
}

//審核頁面數據變更簽核
export function updateAndCheck(data) {
  return request({
    url: '/caaesign/entGuestCancel/updateAndCheck',
    method: 'post',
    data: data
  })

}
// 导出數據庫服務申請單
export function exportEntGuestItems1(query) {
  return request({
    url: '/caaesign/entGuestCancel/exportEntGuestItems1Templet',
    method: 'get',
    params: query
  })
}
// 导出數據庫服務申請單
export function exportEntGuestItems2(query) {
  return request({
    url: '/caaesign/entGuestCancel/exportEntGuestItems2Templet',
    method: 'get',
    params: query
  })
}


//通過類名動態獲取審核節點名稱
export function getSignConfigList() {
  return request({
    url: '/caaesign/entGuestCancel/getSignConfigList',
    method: 'get'
  })
}
