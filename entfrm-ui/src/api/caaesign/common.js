import request from '@/utils/request'
export  function getNextInfo(data){
  return request({
    url: '/caaesign/esignTaskInfo/getNextMyTask?serialno='+data,
    method: 'post',
    data: data
  })
}


export  function auditComplete(data){
  getNextInfo(data).then(response => {
    if(JSON.parse(response.data).result=='success' && JSON.parse(response.data).url !=''){
      ESignApp.openURL(JSON.parse(response.data).url+'&title='+encodeURIComponent(JSON.parse(response.data).title)+'&a='+Math.random())
    }else{
      ESignApp.back()
    }
  });
}
export  function getNodeInfo(serialno){
  return request({
    url: '/caaesign/esignTaskInfo/getNodeInfo/'+serialno,
    method: 'get',
  })
}
export function skipTask(formNo){
  return request({
    url: '/caaesign/esignTaskInfo/notProcessingNow?formNo='+formNo,
    method: 'get',
  })
}
