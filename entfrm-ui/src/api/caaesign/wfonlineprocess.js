import request from '@/utils/request'

// 查询功能程式發佈申請單列表
export function totalNum() {
  return request({
    url: '/caaesign/wfonlineprocessOld/totalNum',
    method: 'get'
  })
}

// 待辦任務列表顯示
export function myTaskList() {
  return request({
    url: '/caaesign/wfonlineprocessOld/myTaskList',
    method: 'get',
  })
}

// 點擊單號查看詳情
export function showDetail(serialno) {
  return request({
    url: '/caaesign/wfonlineprocessOld/showDetail/'+serialno,
    method: 'get',
  })
}

// 審核任務
export function completeTask(data) {
  return request({
    url: '/caaesign/wfonlineprocessOld/completeTask',
    method: 'post',
    data: data
  })
}
export function skipTask(formNo){
  return request({
    url: '/caaesign/esignTaskInfo/notProcessingNow?formNo='+formNo,
    method: 'get',
  })
}

// 批量審核任務
export function completeTaskBatch(data,serialnos) {
  return request({
    url: '/caaesign/wfonlineprocessOld/completeTaskBatch/'+serialnos,
    method: 'post',
    data: data
  })
}

// 批量審核任務
export function allFactorys(data) {
  return request({
    url: '/caaesign/wfonlineprocessOld/allFactorys',
    method: 'post',
    data: data
  })
}
export  function getNodeInfo(serialno){
  return request({
    url: '/caaesign/esignTaskInfo/getNodeInfo/'+serialno,
    method: 'get',
  })
}
