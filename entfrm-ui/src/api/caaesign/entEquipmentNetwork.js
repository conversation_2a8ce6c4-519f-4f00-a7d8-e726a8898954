import request from '@/utils/request'

// 查询產線聯網設備入網申請單列表
export function listEntEquipmentNetwork(query) {
  return request({
    url: '/caaesign/entEquipmentNetwork/list',
    method: 'get',
    params: query
  })
}

// 查询產線聯網設備入網申請單详细
export function getEntEquipmentNetwork(id) {
  return request({
    url: '/caaesign/entEquipmentNetwork/' + id,
    method: 'get'
  })
}

// 新增產線聯網設備入網申請單
export function addEntEquipmentNetwork(data) {
  return request({
    url: '/caaesign/entEquipmentNetwork/save',
    method: 'post',
    data: data
  })
}

// 修改產線聯網設備入網申請單
export function editEntEquipmentNetwork(data) {
  return request({
    url: '/caaesign/entEquipmentNetwork/update',
    method: 'put',
    data: data
  })
}

// 删除產線聯網設備入網申請單
export function delEntEquipmentNetwork(id) {
  return request({
    url: '/caaesign/entEquipmentNetwork/remove/' + id,
    method: 'delete'
  })
}

// 启动请假流程
export function startProcess(id) {
  return request({
    url: '/caaesign/entEquipmentNetwork/startProcess/' + id,
    method: 'get'
  })
}

// 导出產線聯網設備入網申請單
export function exportEntEquipmentNetwork(query) {
  return request({
    url: '/caaesign/entEquipmentNetwork/export',
    method: 'get',
    params: query
  })
}


// 新增產線聯網設備入網申請單并發起流程
export function addEntEquipmentNetworkAndStartProcess(data) {
  return request({
    url: '/caaesign/entEquipmentNetwork/saveAndStartProcess',
    method: 'post',
    data: data
  })
}

// 修改產線聯網設備入網申請單并發起流程
export function editEntEquipmentNetworkAndStartProcess(data) {
  return request({
    url: '/caaesign/entEquipmentNetwork/updateAndStartProcess',
    method: 'put',
    data: data
  })
}

// 修改產線聯網設備入網申請單,并重新提交
export function editEntEquipmentNetworkAndResubmitProcess(data) {
  return request({
    url: '/caaesign/entEquipmentNetwork/updateAndResubmitProcess',
    method: 'put',
    data: data
  })
}



//獲取簽核路勁信息
export function getSignPath(processId) {
  return request({
    url: '/caaesign/entEquipmentNetwork/getSignPath/' + processId,
    method: 'get'
  })
}
//通過類名動態獲取審核節點名稱
export function getSignConfigList() {
  return request({
    url: '/caaesign/entEquipmentNetwork/getSignConfigList',
    method: 'get'
  })
}
