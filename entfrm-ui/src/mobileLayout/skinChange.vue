<template>
  <page-top-bar :title="$t(&quot;mobile_index.skin.title&quot;)">
      <div slot="content"  class="content-pannel">
        <el-scrollbar style="width:100%;" >
        <el-row style="padding:10px 0;">
             <el-col :span="8" v-for="(item, index) in allTheme" :key="item.code"  style="padding:5px 3px;"  >
                <el-card :body-style="{ padding: '0px' }" :shadow="item.code==themeName?'always':'never'"  @click.native="selectTheme(index,item)">
                    <div class="background-div" :style="showColor(item.color)"></div>
                    <div class="color-tab" :style="item.code==themeName?{'background': 'aliceblue'}:''"  >
                          <div style="text-align: center;height:29px;line-height: 29px;">{{ item.tip }}</div>
                          <i class="el-icon-check"  circle   :style="item.code==themeName?{'background': '#67C23A', 'color': '#ffffff'}:''"></i>
                    </div>
                </el-card>
             </el-col>
         </el-row>
        </el-scrollbar>
      </div>
  </page-top-bar>
</template>
<script>

  export default {
    components: {},
    props: [],
    data() {
      return {
          allTheme:[{"color":"#FFFFFF","tip":"默認","code":"default"},
                    {"color":"#00C2F7","tip":"雲中漫步","code":"blue"},
                    {"color":"#FFB854","tip":"秋色伊人","code":"orange"},
                    {"color":"#00D271","tip":"森林之光","code":"green"}],
          themeName: undefined,
        }
    },
    computed: {},
    watch: {},
    created() {
        this.themeName =  localStorage.getItem('themeColor')
        if(!this.themeName){
             this.themeName = "orange"
        }
        window.document.documentElement.setAttribute('data-theme', this.themeName);
    },
    methods: {
      showColor(color){
        if(color.indexOf("#")>=0){
          return {'background':color};
        }else{
          let url = ""
          if(color=="1"){
           /* url = "url("+require('../assets/images/1.jpeg')+")"*/
          }
          return {'background':url,'background-size':"100% 100%"};
        }

      },
      selectTheme(index,item){
           if(this.themeName != item.code){
             this.themeName = item.code
             localStorage.setItem('themeColor', item.code)
             window.document.documentElement.setAttribute('data-theme', this.themeName);
           }
      },
    }
  }

</script>
<style lang="scss" scoped>
  @import "~@/assets/styles/mobileSkin/mobileMixin.scss";
  .cube-page{
      /deep/.wrapper{
        @include backgroundColor('contentBgColor');
        @include fontColor('contentFontColor');
        /deep/.content-pannel{
            @include backgroundColor('contentBgColor');
            width:100%;
            height:calc(100vh - 45px);
            padding:0 15px 15px 15px;
        }
      }
  }
.background-div{
    width:100%;
    height:120px;
}
.el-icon-check{
    position: relative;
    border-radius: 50%;
    padding:3px;
    border: 1px solid #ccc;
}
.color-tab{
  padding: 14px 14px 8px;
  text-align: center;
  border-top: 1px solid #e6ebf5;
}

</style>
