<template>
  <page-top-bar :title="$t(&quot;mobile_index.index.wantToRepair&quot;)">
    <div slot="content" class="content-pannel" >
      <cube-scroll-nav  ref="componet" :current="current" class="head-bar">
        <cube-scroll-nav-panel
          v-for="item in data"
          :key="item.sysType"
          :label="item.sysType"
          class="scroll-bar"
        >
          <ul >
            <li v-for="applyType in item.children" class="clear-float" >
                 <div class="sys-name"><i class="el-icon-price-tag icon-tag"></i>{{applyType.name}}</div>
                 <div class="row-info" v-for="rowList in listTemp(applyType.children)">
                     <div v-for="applyItem in rowList" class="apply-item" @click="turnDetailPage(applyItem)">
                       <svg-icon :icon-class="applyItem.icon" class="sys-icon" />
                       <p >{{applyItem.name}}</p>
                     </div>
                 </div>
                 <!--<div v-for="applyItem in applyType.children" class="apply-item" @click="turnDetailPage(applyItem)">
                     <svg-icon :icon-class="applyItem.icon" class="sys-icon" />
                     <p class="item-explain">{{applyItem.name}}</p>
                 </div>-->
            </li>
          </ul>
        </cube-scroll-nav-panel>
      </cube-scroll-nav>
    </div>
  </page-top-bar>
</template>
<script>
  import {
    listInfo,
  } from "@/api/mobile/index";

  let defaultInfo =[
    {
      "sysType":"資訊表單",
      "value":"info_form",
      "sysName":"iPEBG電子簽核平台",
      "belongSys":"ipebg_elt_sign_platform",
      "name":"學生信息",
      "path":"testStudent",
      "component":"caaesign/testStudent/pc/index",
      "icon":"star",
      "children":[
        {
          "sysType":null,
          "value":null,
          "sysName":null,
          "belongSys":null,
          "name":"iPEBG電子簽核平台",
          "path":null,
          "component":null,
          "icon":null,
          "children":[
            {
              "sysType":null,
              "value":null,
              "sysName":null,
              "belongSys":null,
              "name":"學生信息",
              "path":"testStudent",
              "component":null,
              "icon":"star",
              "children":null
            },
            {
              "sysType":null,
              "value":null,
              "sysName":null,
              "belongSys":null,
              "name":"測試_學生信息App",
              "path":"testStudentApp",
              "component":null,
              "icon":"star",
              "children":null
            }
          ]
        },
        {
          "sysType":null,
          "value":null,
          "sysName":null,
          "belongSys":null,
          "name":"資訊管理平臺",
          "path":null,
          "component":null,
          "icon":null,
          "children":[
            {
              "sysType":null,
              "value":null,
              "sysName":null,
              "belongSys":null,
              "name":"專案事假明細",
              "path":"A_SHIJIATB",
              "component":null,
              "icon":"star",
              "children":null
            },
            {
              "sysType":null,
              "value":null,
              "sysName":null,
              "belongSys":null,
              "name":"專案事假",
              "path":"A_SHIJIA",
              "component":null,
              "icon":"star",
              "children":null
            }
          ]
        }
      ]
    }
  ]
  export default {
    components: {},
    props: [],
    data() {
      return {
        data: defaultInfo,  //默認數據防止高度
        current: undefined
        }
    },
    computed: {},
    watch: {},
    created() {
      let theme = localStorage.getItem('themeColor')
      if(!theme){
        theme = "default"
      }
      window.document.documentElement.setAttribute('data-theme', theme);
      this.getList();
    },
    methods: {
      getList(){
        listInfo().then(response => {
          this.data = response.data
          this.current = response.data[0].sysType
          this.$refs.componet.refresh()
        });
      },
      turnDetailPage(applyItem){
         this.$router.push("/"+applyItem.path)
      },
      listTemp:function(listInfo){
        let rowCount = 4;
        let reaultArr = new Array();

        listInfo.forEach((item,i)=>{
          if(reaultArr[parseInt(i/rowCount)]!=null){
            reaultArr[parseInt(i/rowCount)].push(item);
          }else{
            reaultArr[parseInt(i/rowCount)] = [];
            reaultArr[parseInt(i/rowCount)].push(item);
          }
        })
        return reaultArr;
      },
    }
  }

</script>
<style lang="scss" scoped>
  @import "~@/assets/styles/mobileSkin/mobileMixin.scss";
  .cube-page{
    /deep/.wrapper{
      @include backgroundColor('contentBgColor');
      @include fontColor('contentFontColor');
      .cube-scroll-nav-bar{
        @include backgroundColor('contentBgColor');
        @include fontColor('contentFontColor');
        .cube-scroll-nav-bar-item_active{
          @include fontColor('listIconColor');
        }
      }
      .content-pannel{
        background:#F3F4F6;
        width:100%;
        height:calc(100vh - 45px);
        .clear-float:after{
          clear: both;
          content: "";
          display: block;
        }
        .clear-float{
          padding:10px 0;
        }
      }
    }
  }


  /deep/.cube-scroll-nav-main .cube-sticky{
    margin-top:3vw;
    padding:0 15px 15px 15px;
    background:#ffffff;
  }

  /deep/.scroll-bar .cube-sticky-ele{
     padding:15px 0;
  }


  /deep/.head-bar .cube-sticky-content .cube-scroll-nav-bar_horizontal{
    font-size: 16px;
    font-weight: bold;
  }
  .sys-name{
    font-size: 14px;
    color:dimgrey;
    line-height: 20px;
  }
 .icon-tag{
    font-size: 16px;
    padding-right:5px;
    @include fontColor('listIconColor');
  }
 .apply-item{
     float:left;
     width:25%;
     text-align: center;
     padding:15px 5px;
     font-size:14px;
     /*.item-explain{
         font-size:3vw;
         height:3vw;
     }*/
  }
  .sys-icon{
    height:45px;
    width:45px;
    padding:10px;
    @include fontColor('listIconColor');
  }
  .row-info{
    width:100%;
  }
  .row-info:after{
      content:"";
      display: block;
      clear:both;
  }
</style>
