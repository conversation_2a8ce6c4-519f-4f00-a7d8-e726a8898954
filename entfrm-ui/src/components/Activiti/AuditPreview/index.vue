<template>      
<div class="container">  
        
        <el-col :span="24" :xs="24">
        <div class="dialog-footer" align="center" style="padding-top: 5px;padding-bottom: 5px" v-if="workStatus == 0 || workStatus == 4 ">
        <el-button
            v-if="workStatus == 0"
            type="success"
            @click="handleSubmit()"
        >
            {{$t('table.submit')}}
        </el-button>
        <el-button                        
            v-if="workStatus == 4"
            type="success"
            @click="handleTask(processId)"
        >
            {{$t('table.activity.reSubmit')}}
        </el-button>
        </div>
        </el-col>
        <el-col :span="24" :xs="24">
        <div class="dialog-footer" align="center" style="padding-top: 5px;padding-bottom: 5px" v-if="workStatus == 1">
            <el-button type="success" @click="handleTask(0)" >
            {{$t('table.activity.pass')}}
            </el-button>
            <el-button type="danger" @click="handleTask(1)" >
            {{$t('table.activity.rejection')}}
            </el-button>
            <el-button @click="handleTrack">{{$t('table.activity.flowTracing')}}</el-button>
        </div>
        </el-col>
        <!-- <audit-log
            :processId="processId"
            :isMobile="isMobile"
        /> -->
</div>
</template>

<script>
import AuditLog from '@/components/Activiti/AuditLog'  
import {
  startProcess,
}
from "@/api/design/preview"

export default {  
  props: [
    'isMobile',
    'procDefKey',
    ],
  components: {
    AuditLog,
  },
  data() {
    return {
      widgetFormContainerMobile: ["widget-form-container"],
      rules: {},
      processId: '',
      workStatus: 0,
    }
  },
  created(){
    // console.log(this.childTables)
    // console.log(this.drawingList)
    
  },
  methods: {
    handleSubmit: function(row, index) {
      this.workStatus = 1;
      console.log(this.procDefKey)
      startProcess(this.procDefKey).then(response =>{
        if (response.code === 0) {
            console.log(response.data)
        }
      })
        // this.$confirm(this.$t('tips.submitConfirm' , [row.id]), this.$t('tips.warm'), {
        //     confirmButtonText: this.$t('common.confirmTrim'),
        //     cancelButtonText: this.$t('common.cancelTrim'),
        //     type: 'warning'
        // }).then(function() {
        //     return startProcess(row.id)
        // }).then(() => {
        //     this.getList();
        //     this.msgSuccess(this.$t('tips.submitSuccess'));
        // })
    },
    handleTask: function (pass,processId) {
        this.isDisabled = true;
        this.form.pass = pass
        this.form.processId = processId
        checkTask(this.form).then(response => {
            if (response.code === 0) {
            this.msgSuccess(this.$t('tips.operationSuccessful'));
            this.getList();
            this.closeForm();
            } else {
            this.msgError(response.msg);
            }
        });
    },
    handleTrack() {
      this.imgUrl = process.env.VUE_APP_BASE_API + '/activiti/task/track/' + this.processId,
        this.showImgDialog = true
    },
  }
}
</script>

<style lang='scss'>

</style>
