<template>
  <div>
    <el-table v-show="isMobile"  border stripe ref="dragTableMobile" :data="dataList" row-key="id">
         <el-table-column :label="tableComment" type="index" min-width="90%" :width="dragTableMobileClientWidth">
            <template slot-scope="scope">
              <span>{{$t('common.serialNumber')}}:{{ scope.$index + 1 }}</span>
              <el-row v-for="col in cols" :key="col.javaField" :prop="col.javaField" :label="col.columnComment">
                {{col.columnComment}}:  <br />
                <el-input v-if="htmlTypeVisible(col.htmlType,'el-input')" v-model.trim="scope.row[col.javaField]" 
                  :placeholder="$t(&quot;common.placeholderDefault&quot;) + col.columnComment" > 
                </el-input>

                <el-select  v-else-if="htmlTypeVisible(col.htmlType,'el-select')" v-model.trim="scope.row[col.javaField]"
                  :placeholder="$t(&quot;common.placeholderDefault&quot;) + col.columnComment">
                  <el-option v-for="(item, index) in optionsList[col.javaField+'Options']" :key="index" :label="item.label"
                    :value="item.value" :disabled="item.disabled"></el-option>
                </el-select>

                <el-checkbox-group v-else-if="htmlTypeVisible(col.htmlType,'el-checkbox-group')" v-model.trim="scope.row[col.javaField]">
                  <el-checkbox v-for="(item, index) in optionsList[col.javaField+'Options']" :key="index" :label="item.value"
                    :disabled="item.disabled">{{item.label}}</el-checkbox>
                </el-checkbox-group>

                <el-radio-group v-else-if="htmlTypeVisible(col.htmlType,'el-radio-group')" v-model.trim="scope.row[col.javaField]">
                  <el-radio v-for="(item, index) in optionsList[col.javaField+'Options']" :key="index" :label="item.value"
                    :disabled="item.disabled">{{item.label}}</el-radio>
                </el-radio-group>

                <el-date-picker v-else-if="htmlTypeVisible(col.htmlType,'el-date-picker')" v-model.trim="scope.row[col.javaField]"
                  :placeholder="$t(&quot;common.placeholderDefault&quot;) + col.columnComment"></el-date-picker>
            </el-row>
            <el-button type="text" @click="handleDel(scope.$index, scope.row)" class="del-handler"
              icon="el-icon-delete"></el-button>
          </template>
        </el-table-column>
    </el-table>
    <el-table v-show="!isMobile" border stripe ref="dragTable" :data="commentList">
        <el-table-column :label="$t(&quot;table.activity.taskId&quot;)" align="center" prop="id" />
        <el-table-column :label="$t(&quot;table.activity.approvedBy&quot;)" align="center"
            prop="userId" :show-overflow-tooltip="true" />
        <el-table-column :label="$t(&quot;table.activity.approvalOpinions&quot;)" align="center"
            prop="fullMessage" :show-overflow-tooltip="true" />
        <el-table-column :label="$t(&quot;table.activity.operIp&quot;)" align="center" prop="operIp"
            :show-overflow-tooltip="true" />
        <el-table-column :label="$t(&quot;table.activity.auditStatus&quot;)" align="center"
            prop="status" :formatter="statusFormat" :show-overflow-tooltip="true" />
        <el-table-column :label="$t(&quot;table.activity.approvalTime&quot;)" align="center"
            prop="time" width="180">
            <template slot-scope="scope">
            <span>{{ parseTime(scope.row.time) }}</span>
            </template>
        </el-table-column>
    </el-table>
   </div>
</template>

<script>
import {
  listTask,
  getTask,
  checkTask,
  taskComment
}
from "@/api/activiti/task"
      
export default {
  components: {
  },
  props: [
    'processId',
    'isMobile',
  ],
  data() {
    return {
      tableComment: "",                     //字表的描述信息
      dragTableMobileClientWidth: 0,
      // 审批意见数据
      commentList: [],
    }
  },
  mounted () {
      if(this.$refs.dragTableMobile){
        this.dragTableMobileClientWidth = this.$refs.dragTableMobile.$el.clientWidth
      }
  },
  watch: {
    isMobile: {
      handler(val) {
        if(val){
          //子組件監聽父組件屬性,在v-show渲染完畢后執行下面方法
          this.$nextTick(function () {
              this.dragTableMobileClientWidth = this.$refs.dragTableMobile.$el.clientWidth
          })
        }
      },
      immediate: true
    }
  },
  created() {
    if(this.processId){
        taskComment(this.processId).then(response => {
            this.commentList = response.data;
        });
     }
  },
  methods:{
    statusFormat(row, column) {
      return this.selectDictLabel(this.auditStatus, row.status);
    },
  }
}
</script>
