<template>
  <div>
      <el-col :span="24" class="flowCol">
        <span>{{ label }}</span>
        <img :src="qhUserImg" class="qh-user" @click="handleQhUserCommonUsersSelect(dutyId)">
      </el-col>
      <el-col :span="24" class="flowCol">
        <el-form-item prop="businessManageChargeno" label-width="0px">
          <el-input :v-model="modelNo" readonly clearable ></el-input>
        </el-form-item>
        <label class="label-red" v-if="required" >*</label>/
        <el-form-item prop="businessManageChargename" label-width="0px">
          <el-input :v-model="modelName" readonly clearable ></el-input>
        </el-form-item>
      </el-col>
  </div>
</template>

<script>


import { getAuditCommonUsers, getAuditHqUsers } from '@/api/system/auditingNode'
import qhUserImg from '@/assets/images/qhUser.png'

export default {
  components: {
  },
  props: {
    label: {
      type: String,
      default: '簽核節點',
      required: true
    },
    modelNo: {
      type: String,
      default: '',
      required: true
    },
    modelName: {
      type: String,
      default: '',
      required: true
    },
    required: {
      type: Boolean,
      default: true,
      required: true
    },
    signFormType: {
      type: String,
      default: 'dutyId'
    },
    dutyId: {
      type: String,
      default: '',
      required: true
    },
    //申請人廠區
    applyFactoryId: {
      type: String,
      default: '',
      required: true
    },
    //申請人部門
    applyDeptNo: {
      type: String,
      default: '',
      required: true
    },
    //填單人廠區
    makerFactoryId: {
      type: String,
      default: '',
      required: true
    },
    //數據源別名
    alias: {
      type: String,
      default: '',
      required: true
    },
  },
  data() {
    return {
      qhUserImg: qhUserImg,
      //簽核人員查詢參數
      qhUserSelectParam: {},
    }
  },
  created() {
    // console.log(formData)
  },
  methods:{
    /** 簽核人員選擇按钮操作,根據簽核路徑獲取 */
    handleQhUserAuditHqSelect(empNoField,empNameField) {
      this.empNoField = empNoField;
      this.empNameField = empNameField;
      this.buildQhUserAuditHqSelectParam();
      this.open = true;
      getAuditHqUsers(this.qhUserSelectParam).then(response => {
        // console.log(response.data)
        this.qhUserList = response.data;
      })
    },
    /** 簽核人員選擇按钮操作,根據簽核角色獲取 */
    handleQhUserCommonUsersSelect(dutyId){
      this.buildQhUserCommonUserSelectParam(dutyId);
      this.open = true;
      getAuditCommonUsers(this.qhUserSelectParam).then(response => {
        this.qhUserList = response.data;
      })
    },
    /**
     * 創建簽核人員選擇查詢條件
     */
    buildQhUserAuditHqSelectParam(){
      let kchargeno,bchargeno,cchargeno;
      if("kchargeno" !== this.empNoField){
        kchargeno = this.formData.kchargeno
      }
      if("bchargeno" !== this.empNoField && "kchargeno" !== this.empNoField){
        bchargeno = this.formData.bchargeno
      }
      if("cchargeno" !== this.empNoField && "bchargeno" !== this.empNoField && "kchargeno" !== this.empNoField){
        cchargeno = this.formData.cchargeno
      }
      this.qhUserSelectParam = {
        "kchargeno": kchargeno,
        "bchargeno": bchargeno,
        "cchargeno": cchargeno,
        "chargeno": this.empNoField,
        "chargename": this.empNameField,
        "factoryid": this.applyFactoryId,
        "deptno": this.applyDeptNo,
        "alias": this.alias
      }
    },
    /**
     * 創建簽核人員選擇查詢條件
     */
    buildQhUserCommonUserSelectParam(dutyid){
      this.qhUserSelectParam = {
        "dutyid": dutyid,
        "factoryid": this.makerFactoryId,
        "alias": this.alias
      }
    },
  }
}
</script>

<style lang="scss" scoped>
/** -----------------簽核人員選擇------------------ */
.flowCol {
  text-align: center;
  line-height: 30px;
  display:flex;
  justify-content:center;/*主轴上居中*/
  align-items:center;/*侧轴上居中*/
  .qh-user {
    cursor: pointer;
    width: 25px;
    height: 25px;
    border: none;
  }
  .label-red{
    color: red;
  }
}


</style>
