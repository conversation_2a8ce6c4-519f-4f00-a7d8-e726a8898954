<template>
  <div>
    <div v-if="isMobile" class="mobile-sign-item">
      <el-col :span="8" class="flowCol">
        <label class="label-red" v-if="required" >*</label><span>{{ label }}</span>
      </el-col>
      <el-col :span="16" class="flowCol"  @click.native="handleQhUserAuditHqSelect()">
        <el-table  :data="multipleSelectionSelect" :show-header=false
                  :empty-text="$t('tips.signFormNoSelectedData')" >
          <el-table-column min-width="10%" >
            <template slot-scope="scope">
              <el-col :span="24" class="flowCol">
                <el-input class="sign-no-right"
                          v-model.trim=multipleSelectionSelect[scope.$index].empNo
                          readonly clearable
                ></el-input>
                /
                <el-input
                  v-model.trim=multipleSelectionSelect[scope.$index].empName
                  readonly clearable
                ></el-input>
              </el-col>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </div>
    <div  v-else  class="pc-sign-item">
      <el-col :span="24" class="flowCol">
        <label class="label-red" v-if="required" >*</label><span>{{ label }}</span>
        <img :src="qhUserImg" class="qh-user" @click="handleQhUserAuditHqSelect()">
      </el-col>
      <el-table stripe :data="multipleSelectionSelect" :show-header=false
                :empty-text="$t('tips.signFormNoSelectedData')" >
        <el-table-column min-width="10%" >
          <template slot-scope="scope">
            <el-col :span="24" class="flowCol">
              <el-input class="sign-no-right"
                v-model.trim=multipleSelectionSelect[scope.$index].empNo
                readonly clearable
              ></el-input>
              /
              <el-input
                v-model.trim=multipleSelectionSelect[scope.$index].empName
                readonly clearable
              ></el-input>
            </el-col>
          </template>
        </el-table-column>
      </el-table>
    </div>




    <!-- 選擇簽核人員對話框 -->
    <el-drawer
      v-if="isMobile"
      :withHeader="false"
      :visible.sync="open"
      direction="ltr"
      size="80%"
      :modal-append-to-body="false"
      :before-close="handleClose">
      <el-scrollbar style="width:100%;height:calc(100vh - 180px)" >
        <el-table border stripe
                  ref="multipleTable"
                  :data="qhUserList"
                  highlight-current-row
                  row-key="empNo"
                  @row-click="clickRow"
                  @selection-change="handleSelectionChange"
                  style="margin-top:60px;width:100%;"
                  class="signer-list"
        >
          <el-table-column
            type="selection"
            reserve-selection
            width="45"
          >
          </el-table-column>
          <el-table-column :label="title" label-position="left">
            <template slot-scope="scope">
              <el-form-item :label="$t('table.qhUserSelect.account')" >
                {{scope.row.empNo}}
              </el-form-item>
              <el-form-item :label="$t('table.qhUserSelect.accountName')" >
                {{scope.row.empName}}
              </el-form-item>
            </template>
          </el-table-column>
        </el-table>
      </el-scrollbar>
      <div  class="dialog-footer">
        <el-button type="primary" @click="handleQhUserSubmit">{{$t('common.confirm')}}</el-button>
        <el-button type="primary" @click="handleCleanQhUser">{{$t('common.cleanQhUser')}}</el-button>
        <el-button @click="handleQhUserCancel">{{$t('common.cancel')}}</el-button>
      </div>
    </el-drawer>




    <el-dialog v-else :title="title" :visible.sync="open" append-to-body>
      <el-table border stripe
                ref="multipleTable"
                :data="qhUserList"
                highlight-current-row
                row-key="empNo"
                @row-click="clickRow"
                @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          reserve-selection
          width="55">
        </el-table-column>
        <el-table-column :label="$t('table.qhUserSelect.serialNumber')"
                         align="center" type="index" :index="indexMethod" width="80"/>
        <el-table-column :label="$t('table.qhUserSelect.account')" align="center"  prop="empNo"
                         :show-overflow-tooltip="true" />
        <el-table-column :label="$t('table.qhUserSelect.accountName')" align="center"  prop="empName"
                         :show-overflow-tooltip="true" />
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleQhUserSubmit">{{$t('common.confirm')}}</el-button>
        <el-button type="primary" @click="handleCleanQhUser">{{$t('common.cleanQhUser')}}</el-button>
        <el-button @click="handleQhUserCancel">{{$t('common.cancel')}}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>


import { getAuditHqUsers } from '@/api/system/auditingNode'
import qhUserImg from '@/assets/images/qhUser.png'

const EVENT_SELECTED = 'onSignFormSelected'

export default {
  components: {
  },
  props: {
    label: {
      type: String,
      default: '簽核節點',
      required: true
    },
    //單選,或多選
    multipleSelect: {
      type: Boolean,
      default: true,
      required: false
    },
    modelNo: {
      type: String,
      default: '',
      required: true
    },
    modelName: {
      type: String,
      default: '',
      required: true
    },
    required: {
      type: Boolean,
      default: true,
      required: false
    },
    signFormType: {
      type: String,
      default: 'auditHq'
    },
    kchargeno: {
      type: String,
      default: '',
      required: false
    },
    bchargeno: {
      type: String,
      default: '',
      required: false
    },
    cchargeno: {
      type: String,
      default: '',
      required: false
    },
    applyDeptNo: {
      type: String,
      default: '',
      required: false
    },
    //填單人廠區
    applyFactoryId: {
      type: String,
      default: '',
      required: false
    },
    //數據源別名
    alias: {
      type: String,
      default: '',
      required: false
    },
    selectEmpProp: {
      type: Object,
      default: {empNo:'',empName:''},
    },
    isMobile:{
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      qhUserImg: qhUserImg,
      //簽核人員查詢參數
      qhUserSelectParam: {},
      qhUserList: [],
      multipleSelectionOld: [],
      multipleSelection: [],
      multipleSelectionSelect: [],
      open: false,
      title: this.$t('table.qhUserSelect.title') + ' - ' + (this.multipleSelect?this.$t('table.qhUserSelect.multiple'):this.$t('table.qhUserSelect.single')),
      selectEmp: {empNo:'',empName:''},
      rules: {
        empNo: [{
          required: this.required,
          message: this.label + '不能為空',
          trigger: 'change'
        }],
      }
    }
  },
  created() {
  },
  mounted(){

  },
  watch: {
    // eslint-disable-next-line func-names
    'selectEmpProp.empNo': {
      handler(newVal, oldVal) {
        if (!this.selectEmpProp.empNo || !this.selectEmpProp.empName || this.selectEmpProp.empNo === '' || this.selectEmpProp.empName === '') {
          return
        }
        if (this.multipleSelection.length > 0) {
          return;
        }
        var selectEmpNos = this.selectEmpProp.empNo.split(',')
        var selectEmpNames = this.selectEmpProp.empName.split(',')
        if (selectEmpNos.length > 0) {
          for (let i = 0; i < selectEmpNos.length && i < selectEmpNames.length; i++) {
            this.multipleSelection.push({ empNo: selectEmpNos[i], empName: selectEmpNames[i] })
          }
          this.multipleSelectionSelect = this.multipleSelection
        }
      },
    }
  },
  methods:{
    /** 簽核人員選擇按钮操作,根據簽核路徑獲取 */
    handleQhUserAuditHqSelect() {
      this.buildQhUserAuditHqSelectParam();
      this.open = true;
      this.multipleSelectionOld = this.multipleSelection
      this.toggleSelection()
      getAuditHqUsers(this.qhUserSelectParam).then(response => {
        this.qhUserList = response.data;
        this.updateQhUserList()
      })
    },
    /**
     * 創建簽核人員選擇查詢條件
     */
    buildQhUserAuditHqSelectParam(){
      let kchargeno,bchargeno,cchargeno;
      if("kchargeno" !== this.modelNo){
        kchargeno = this.kchargeno
      }
      if("bchargeno" !== this.modelNo && "kchargeno" !== this.modelNo){
        bchargeno = this.bchargeno
      }
      if("cchargeno" !== this.modelNo && "bchargeno" !== this.modelNo && "kchargeno" !== this.modelNo){
        cchargeno = this.cchargeno
      }
      this.qhUserSelectParam = {
        "kchargeno": kchargeno,
        "bchargeno": bchargeno,
        "cchargeno": cchargeno,
        "chargeno": this.modelNo,
        "chargename": this.modelName,
        "factoryid": this.applyFactoryId,
        "deptno": this.applyDeptNo,
        "alias": this.alias
      }
    },
    //置空該節點的簽核人員
    handleCleanQhUser(){
      this.$refs.multipleTable.clearSelection();
      this.multipleSelectionSelect = this.multipleSelection
      this.emitSelectEmp()
    },
    /** 簽核人員選擇提交按钮操作 */
    handleQhUserSubmit() {
      this.multipleSelectionSelect = this.multipleSelection
      this.$refs.multipleTable.clearSelection();
      this.emitSelectEmp()
      this.open = false;
    },
    /** 簽核人員選擇取消按钮操作 */
    handleQhUserCancel() {
      this.multipleSelection = this.multipleSelectionOld
      this.open = false;
    },
    // 点击一个单元格
    clickRow(row, column){
      this.$refs.multipleTable.toggleRowSelection(row)
    },
    //初始化選擇項
    toggleSelection() {
      //如果不為0,則認為是有選中的值
      if (this.multipleSelection.length > 0) {
        var multipleSelectionMap = new Map();
        this.multipleSelection.forEach(qhUser => {
          multipleSelectionMap.set(qhUser.empNo,qhUser)
        })
        if(this.qhUserList.length == 0) {
          this.multipleSelection.forEach(qhUser => {
            this.qhUserList.push(qhUser)
          })
        }
        this.$nextTick(()=> {
          this.qhUserList.forEach(qhUser => {
            if (multipleSelectionMap.get(qhUser.empNo)) {
              this.$refs.multipleTable.toggleRowSelection(qhUser, true);
            } else {
              this.$refs.multipleTable.toggleRowSelection(qhUser, false);
            }
          })
        })
      }else{
        if(this.$refs.multipleTable){
          this.$refs.multipleTable.clearSelection();
        }
      }
    },
    handleSelectionChange(val) {
      if(this.multipleSelect) {
        this.multipleSelection = val;
        this.updateQhUserList()
      }else{
        if(val.length > 1){
          this.$refs.multipleTable.clearSelection();
          this.$refs.multipleTable.toggleRowSelection(val.slice(-1)[0]);
        }
        this.multipleSelection = [];
        if(val.length > 0) {
          this.multipleSelection.push(val.pop())
        }
      }
    },
    //發送審核節點信息數據
    emitSelectEmp(){
      if(this.multipleSelectionSelect.length > 0){
        this.selectEmp.empNo = ''
        this.selectEmp.empName = ''
        this.multipleSelectionSelect.forEach(qhUser => {
          if(this.selectEmp.empNo === ''){
            this.selectEmp.empNo = qhUser.empNo
            this.selectEmp.empName = qhUser.empName
          }else{
            this.selectEmp.empNo = this.selectEmp.empNo + "," + qhUser.empNo
            this.selectEmp.empName = this.selectEmp.empName + "," + qhUser.empName
          }
        })
      }else{
        this.selectEmp.empNo = ''
        this.selectEmp.empName = ''
      }
      // console.log(this.selectEmp)
      this.$emit(EVENT_SELECTED, this.selectEmp,this.modelNo,this.modelName)
    },
    updateQhUserList() {
      if(this.qhUserList.length > 0) {
        for (let i = 0; i < this.multipleSelection.length; i++) {
          let index = this.getQhUserIndex(this.multipleSelection[i])
          if(index != -1){
            this.qhUserList.splice(index, 1)
          }
        }
        for (let i = this.multipleSelection.length - 1; i >= 0; i--) {
          this.qhUserList.unshift(this.multipleSelection[i])
        }
      }
    },
    getQhUserIndex(qhUser){
      for (let i = 0; i < this.qhUserList.length; i++) {
        if(this.qhUserList[i].empNo === qhUser.empNo){
          return i;
        }
      }
      return -1;
    },
    indexMethod(index) {
      return index + 1;
    },
    handleClose(done) {
      done()
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/assets/styles/mobileSkin/mobileMixin.scss";
  .pc-sign-item{
    /** -----------------簽核人員選擇------------------ */
    .flowCol {
      text-align: center;
      line-height: 30px;
      display:flex;
      justify-content:center;/*主轴上居中*/
      align-items:center;/*侧轴上居中*/
      .qh-user {
        cursor: pointer;
        width: 25px;
        height: 25px;
        border: none;
      }
      .label-red{
        color: red;
      }
    }
    /deep/.el-table .cell{
      padding-left: 0px;
      padding-right: 0px;
    }

    .signer-list/deep/.el-form-item__label{
      float: left;
      width: 80px;
    }
    .signer-list/deep/.el-form-item__content{
      margin-left:80px;
    }

    .signer-list/deep/.el-form-item{
      margin:0;
    }

  }

.mobile-sign-item{
    border-bottom: 1px solid #e6ebf5;
    .flowCol {
      text-align: center;
      line-height: 60px;
      display:flex;
      justify-content:center;/*主轴上居中*/
      align-items:center;/*侧轴上居中*/
      .label-red{
        color: red;
      }
      .el-table:before{
        background-color: white;
      }
      /deep/.el-table{
        .el-table__empty-block{
          justify-content: left;
        }
        td{
          padding:0;
        }
        .cell{
          padding-left: 0px;
          padding-right: 0px;
        }
      }
    }


    .signer-list/deep/.el-form-item__label{
      float: left;
      width: 80px;
    }
    .signer-list/deep/.el-form-item__content{
      margin-left:80px;
    }

    .signer-list/deep/.el-form-item{
      margin:0;
    }
}

  .el-drawer__wrapper{
    /deep/.el-checkbox__input.is-checked .el-checkbox__inner{
      @include borderColor('themeMainColor');
      @include backgroundColor('themeMainColor');
    }
    /deep/.el-checkbox__input.is-indeterminate .el-checkbox__inner{
      @include borderColor('themeMainColor');
      @include backgroundColor('themeMainColor');
    }
    .el-button--primary{
      @include borderColor('themeMainColor');
      @include backgroundColor('themeMainColor');
    }
  }


  .mobile-sign-item:after{
     clear:both;
     content:"";
     display: block;
  }
@media(max-width: 768px) {
  .sign-no-right>/deep/input{
    text-align: right;
  }
}
</style>
