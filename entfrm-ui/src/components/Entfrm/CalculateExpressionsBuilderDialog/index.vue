<template>
  <div class="expressions-dialog">
    <el-dialog
      v-bind="$attrs"
      width="980px"
      :modal-append-to-body="false"
      v-on="$listeners"
      @open="onOpen"
      @close="onClose"
      :close-on-click-modal=false
    >
      <el-row>

        <el-col :span="24" class="card-box">
          <el-card>
            <div slot="header"><span>基本信息</span></div>
            <div class="el-table el-table--enable-row-hover el-table--medium">
              <table cellspacing="0" style="width: 100%;">
                <tbody>
                <tr>
                  <td><div class="cell" style="text-align: right">{{$t('design_build_ExpressionsDialog.label.operator')}}</div></td>
                  <td><div class="cell">
                    <el-button-group>
                      <el-button type="primary" icon="iconfont icon-jiahao" @click="handlebuild('+')"></el-button>
                      <el-button type="primary" icon="iconfont icon-jianhao1" @click="handlebuild('-')"></el-button>
                      <el-button type="primary" icon="iconfont icon-cheng" @click="handlebuild('*')"></el-button>
                      <el-button type="primary" icon="iconfont icon-24gf-obelus" @click="handlebuild('/')"></el-button>
                      <el-button type="primary" icon="iconfont icon-zuokuohao1" @click="handlebuild('(')"></el-button>
                      <el-button type="primary" icon="iconfont icon-youkuohao1" @click="handlebuild(')')"></el-button>
                    </el-button-group></div></td>
                  <td><div class="cell" style="text-align: right">{{$t('design_build_ExpressionsDialog.label.columnValue')}}</div></td>
                  <td><div class="cell">
                      <el-select v-model="columnName"
                                 :placeholder="$t('common.placeholderDefault') +　$t('design_build_ExpressionsDialog.label.columnValue')"
                                 clearable :style="{width: '100%'}"
                                 @change="handlebuild"
                      >
                        <el-option v-for="(item, index) in columnOptions" :key="index" :label="item.javaField+'【'+item.columnComment+'】'"
                                   :value="item.javaField" ></el-option>
                      </el-select>
                  </div></td>
                </tr>

                <tr>
                  <td><div class="cell" style="text-align: right">{{$t('design_build_ExpressionsDialog.label.decimalScale')}}</div></td>
                  <td><div class="cell">
                    <el-input-number  :width="100" size="medium"  :min="0" :max="10"
                                      v-model="decimalScale"
                                      :placeholder="$t('common.placeholderDefault') +　$t('design_build_ExpressionsDialog.label.decimalScale')"
                    ></el-input-number>
                  </div></td>
                </tr>
                </tbody>
              </table>
            </div>
          </el-card>
        </el-col>

        <el-col :span="24" class="card-box">
          <el-card>
            <div slot="header"><span>表達式</span></div>
            <el-form size="small" ref="elForm">
              <el-form-item label-width="0px" >
                    <el-input v-model="expressions" type="textarea"
                              :autosize="{ minRows: 5}"> </el-input>
              </el-form-item>
            </el-form>
          </el-card>
        </el-col>
      </el-row>
      <div slot="footer">
        <el-button
          type="primary"
          @click="handelConfirm"
        >
          {{$t('common.confirmTrim')}}
        </el-button>
        <el-button @click="close">
          {{$t('common.cancelTrim')}}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>

export default {
  inheritAttrs: false,
  props: ['calculateExpressionsText','columnOptions','calculateExpressionsDecimalScale'],
  data() {
    return {
      expressions:"",
      columnName:"",
      decimalScale:"",
      columnOptionMap:{},
    }
  },
  watch: {
    // key(val) {
    //   if (val) {
    //     this.iconList = originList.filter(name => name.indexOf(val) > -1)
    //   } else {
    //     this.iconList = originList
    //   }
    // }
  },
  methods: {
    onOpen() {
      if(this.calculateExpressionsText){
        this.expressions = this.calculateExpressionsText
      }else{
        this.expressions = ''
      }
      if(this.calculateExpressionsDecimalScale){
        this.decimalScale = this.calculateExpressionsDecimalScale
      }else{
        this.decimalScale = 0
      }
      this.columnOptions.forEach(item => {
        this.columnOptionMap[item.javaField] = 1
      })
    },
    // validateMathematical(rule, value, callback){
    //   if(!this.verifyMathematical(value,this.columnOptionMap)){
    //     callback(new Error('運算表達式錯誤,請檢查!'));
    //   }else{
    //     callback();
    //   }
    // },
    onClose() {},
    handlebuild(str){
      this.expressions += ' ' + str
    },
    close() {
      this.$emit('update:visible', false)
    },
    handelConfirm() {
      if(this.expressions.trim() && !this.verifyMathematical(this.expressions.trim(),this.columnOptionMap)){
        this.msgError('運算表達式錯誤,請檢查!')
        return
      }
      this.$emit('commit', this.expressions,this.decimalScale)
      this.close()
    }
  }
}
</script>
<style lang="scss" scoped>
.el-row {
  margin-bottom: 20px;
  &:last-child {
    margin-bottom: 0;
  }
}
.icon-ul {
  margin: 0;
  padding: 0;
  font-size: 0;
  li {
    list-style-type: none;
    text-align: center;
    font-size: 14px;
    display: inline-block;
    width: 16.66%;
    box-sizing: border-box;
    height: 108px;
    padding: 15px 6px 6px 6px;
    cursor: pointer;
    overflow: hidden;
    &:hover {
      background: #f2f2f2;
    }
    &.active-item{
      background: #e1f3fb;
      color: #7a6df0
    }
    > i {
      font-size: 30px;
      line-height: 50px;
    }
  }
}
.expressions-dialog {
  ::v-deep .el-dialog {
    border-radius: 8px;
    margin-bottom: 0;
    margin-top: 4vh !important;
    display: flex;
    flex-direction: column;
    max-height: 92vh;
    overflow: hidden;
    box-sizing: border-box;
    .el-dialog__header {
      padding-top: 14px;
    }
    .el-dialog__body {
      margin: 0 20px 20px 20px;
      padding: 0;
      overflow: auto;
    }
  }
}
</style>
