<template>
  <div>
    <div v-if="isMobile" class="mobile-sign-item">
      <el-col :span="8" class="flowCol">
        <label class="label-red" v-if="required" >*</label><span>{{ label }}</span>&nbsp;
        <el-link type="primary" icon="el-icon-plus" @click="handleAdd()"></el-link>
      </el-col>
      <el-col :span="16" class="flowCol">
        <el-table  :data="multipleSelectionSelect" :show-header=false
                  :empty-text="$t('tips.signFormNoSelectedData')" >
          <el-table-column min-width="10%" >
            <template slot-scope="scope">
              <el-col :span="24" class="flowCol">
                <el-input class="sign-no-left"
                  v-model.trim=multipleSelectionSelect[scope.$index].empNo
                  clearable
                  @blur="getInfoUser(scope.$index, scope.row)"
                ></el-input>
                /
                <el-input class="sign-name-right"
                  v-model.trim=multipleSelectionSelect[scope.$index].empName
                  readonly clearable
                ></el-input>
                <i class="el-icon-delete del-handler" @click="handleDel(scope.$index, scope.row)"></i>
              </el-col>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </div>

    <div v-else class="pc-sign-item">
      <el-col :span="24" class="flowCol">
        <label class="label-red" v-if="required" >*</label><span>{{ label }}</span>&nbsp;
        <el-link type="primary" icon="el-icon-plus" @click="handleAdd()">{{$t('tips.addOne')}}</el-link>
      </el-col>
      <el-table stripe :data="multipleSelectionSelect" :show-header=false
                :empty-text="$t('tips.signFormNoSelectedData')" >
        <el-table-column min-width="10%" >
          <template slot-scope="scope">
            <el-col :span="24" class="flowCol">
              <el-input class="sign-no-left"
                        v-model.trim=multipleSelectionSelect[scope.$index].empNo
                        clearable
                        @blur="getInfoUser(scope.$index, scope.row)"
              ></el-input>
              /
              <el-input class="sign-name-right"
                        v-model.trim=multipleSelectionSelect[scope.$index].empName
                        readonly clearable
              ></el-input>
              <i class="el-icon-delete del-handler" @click="handleDel(scope.$index, scope.row)"></i>
            </el-col>
          </template>
        </el-table-column>
      </el-table>
    </div>



  </div>
</template>

<script>


import { getAuditHqUsers } from '@/api/system/auditingNode'
import qhUserImg from '@/assets/images/qhUser.png'

const EVENT_SELECTED = 'onSignFormSelected'

export default {
  components: {
  },
  props: {
    isMobile:{
      type: Boolean,
      default: false,
    },
    label: {
      type: String,
      default: '簽核節點',
      required: true
    },
    modelNo: {
      type: String,
      default: '',
      required: true
    },
    modelName: {
      type: String,
      default: '',
      required: true
    },
    required: {
      type: Boolean,
      default: true,
      required: false
    },
    signFormType: {
      type: String,
      default: 'addHq'
    },
    selectEmpProp: {
      type: Object,
      default: {empNo:'',empName:''},
    },
  },
  data() {
    return {
      qhUserImg: qhUserImg,
      //簽核人員查詢參數
      qhUserSelectParam: {},
      qhUserList: [],
      multipleSelectionOld: [],
      multipleSelection: [],
      multipleSelectionSelect: [],
      open: false,
      title: this.$t('table.qhUserSelect.title'),
      selectEmp: {empNo:'',empName:''},
      rules: {
        empNo: [{
          required: this.required,
          message: this.label + '不能為空',
          trigger: 'change'
        }],
      }
    }
  },
  created() {
    // console.log(this.isMobile);
  },
  mounted(){

  },
  watch: {
    // eslint-disable-next-line func-names
    selectEmpProp: function (newVal, oldVal) {
      if (!newVal.empNo || !newVal.empName || newVal.empNo === ''|| newVal.empName === '') {
        return
      }
      if (this.multipleSelection.length > 0) {
        return;
      }
      var selectEmpNos = newVal.empNo.split(',')
      var selectEmpNames = newVal.empName.split(',')
      if(selectEmpNos.length > 0){
        for (let i = 0; i < selectEmpNos.length && i < selectEmpNames.length; i++) {
          this.multipleSelection.push({empNo:selectEmpNos[i],empName:selectEmpNames[i]})
        }
        this.multipleSelectionSelect = this.multipleSelection
      }
    },
  },
  methods:{
    handleAdd() {
      var endRow = this.multipleSelectionSelect.slice(-1);
      if(endRow.length === 1 && (endRow[0].empNo === ''|| endRow[0].empName === '')){
        this.msgWarning(this.$t("tips.fillInCompletely"))
        return
      }
      const cloumn = {
        empNo: '',
        empName: '',
      };
      this.multipleSelectionSelect.splice(this.multipleSelectionSelect.length, 0, cloumn);
    },
    handleDel(index, row) {
      this.$confirm(
        this.$t("tips.removeOne", [row.empNo]),
        this.$t("tips.warm"),
        {
          confirmButtonText: this.$t("common.confirmTrim"),
          cancelButtonText: this.$t("common.cancelTrim"),
          type: "warning",
        }
      )
        .then(() => {
          this.multipleSelectionSelect.splice(index, 1);
          this.$message({ showClose: true, message: this.$t("tips.deleteSuccess"), type: "success", offset: 50});
          this.emitSelectEmp()
        })
        .catch(function (err) {
          console.log(err);
        });
    },
    getInfoUser(index, row) {
      if(row.empNo === ''){
        return
      }
      row.empNo = row.empNo.replace( /\W/g , '')
      this.getInfoUserByEmpno(row.empNo).then(response => {
        if (response.code !== 0) {
          this.msgError(response.msg);
        }
        else {
          if (response.data != null) {
            row.empName = response.data.empname
          }
          else {
            row.empName = ''
          }
          this.emitSelectEmp()
        }
      });
    },
    //發送審核節點信息數據
    emitSelectEmp(){
      if(this.multipleSelectionSelect.length > 0){
        this.selectEmp.empNo = ''
        this.selectEmp.empName = ''
        this.multipleSelectionSelect.forEach(qhUser => {
          if(qhUser.empNo === ''|| qhUser.empName === ''){
            return
          }
          if(this.selectEmp.empNo === ''){
            this.selectEmp.empNo = qhUser.empNo
            this.selectEmp.empName = qhUser.empName
          }else{
            this.selectEmp.empNo = this.selectEmp.empNo + "," + qhUser.empNo
            this.selectEmp.empName = this.selectEmp.empName + "," + qhUser.empName
          }
        })
      }else{
        this.selectEmp.empNo = ''
        this.selectEmp.empName = ''
      }
      this.$emit(EVENT_SELECTED, this.selectEmp,this.modelNo,this.modelName)
    },
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/assets/styles/mobileSkin/mobileMixin.scss";
/** -----------------簽核人員選擇------------------ */
  .pc-sign-item {
    .flowCol {
      text-align: center;
      line-height: 30px;
      display: flex;
      justify-content: center; /*主轴上居中*/
      align-items: center; /*侧轴上居中*/
      .qh-user {
        cursor: pointer;
        width: 25px;
        height: 25px;
        border: none;
      }

      .label-red {
        color: red;
      }
    }

    /deep/ .el-table .cell {
      padding-left: 0px;
      padding-right: 0px;
    }

    .del-handler {
      font-size: 20px;
      color: #ff4949;
      cursor: pointer;
    }

    .el-button {
      padding: 5px 0;
    }
  }

.mobile-sign-item{
  border-bottom: 1px solid #e6ebf5;
  .flowCol {
    text-align: center;
    line-height: 60px;
    display: flex;
    justify-content: center; /*主轴上居中*/
    align-items: center; /*侧轴上居中*/
    .qh-user {
      cursor: pointer;
      width: 25px;
      height: 25px;
      border: none;
    }
    .el-table:before{
      background-color: white;
    }
    /deep/ .el-table {
      .el-table__empty-block {
        justify-content: left;
      }

      td {
        padding: 0;
      }

      .cell {
        padding-left: 0px;
        padding-right: 0px;
      }
    }
    /deep/.el-icon-plus{
      @include fontColor('themeMainColor');
    }
    /deep/.el-link.el-link--primary:hover,/deep/.el-link.el-link--primary:active{
        @include fontColor('themeMainColor');
    }
    /deep/.el-icon-delete{
      font-size: 20px;
      cursor: pointer;
      @include fontColor('themeMainColor');
    }
    /deep/.el-link.is-underline:hover:after{
      border:0;
    }
    .label-red {
      color: red;
    }
  }
  .el-button {
    padding: 5px 0;
  }
}
.mobile-sign-item:after{
  clear:both;
  content:"";
  display: block;
}
@media(max-width: 768px) {
  .sign-no-left>/deep/input{
    text-align: right;
  }
  .sign-name-right{  //移動端 輸入框對齊
    width:calc(100% - 40px)
  }
}
</style>
