<template>
  <div class="child-talbe">
    <el-col :span="24" :xs="24" class="talbe-name-style">
      {{label}}<br/>
    </el-col>
    <el-col :span="24">
      <el-button
        type="primary"
        icon="el-icon-plus"
        size="mini"
        @click="handleAddColumn"
      >{{$t('entfrm_child_table_form.addColumn')}}</el-button>
    </el-col>
      <el-scrollbar style="width:100%">
    <el-col :span="24" :style="myChildTableWidth" >
      <el-table border stripe ref="dragTable" :data="dataList" row-key="id" :class="tableClass"
                :max-height="tableHeight" @header-click="handleHeaderChick">
        <el-table-column v-for="(col, index) in cols" :key="col.javaField" :prop="col.javaField" :label="col.columnComment"
                         :width="childAttributesMap?(childAttributesMap[col.javaField] ? childAttributesMap[col.javaField].columnWidth : ''):''"
        >
          <template slot="header" slot-scope="scope">
          {{ col.columnComment }}
          <i
            class="el-icon-remove"
            style="color:red;cursor:pointer;"
            @click="deleteColunm(col.columnName)"
          ></i>

            <i
              v-if="regIconShow(col.javaField)"
              class="el-icon-key"
              style="color:green;"
            ></i>

        </template>
          <template slot-scope="scope" >
            <el-input v-if="htmlTypeVisible(col.htmlType,'el-input')" v-model.trim="scope.row[col.javaField]"
                      :placeholder="$t('common.placeholderDefault') + col.columnComment" > </el-input>

            <el-select  v-else-if="htmlTypeVisible(col.htmlType,'el-select')" v-model.trim="scope.row[col.javaField]"
                        :placeholder="$t('common.placeholderDefault') + col.columnComment">
              <el-option v-for="(item, index) in optionsList[col.javaField+'Options']" :key="index" :label="item.label"
                         :value="item.value" :disabled="item.disabled"></el-option>
            </el-select>

            <el-checkbox-group v-else-if="htmlTypeVisible(col.htmlType,'el-checkbox-group')" v-model.trim="scope.row[col.javaField]">
              <el-checkbox v-for="(item, index) in optionsList[col.javaField+'Options']" :key="index" :label="item.value"
                           :disabled="item.disabled">{{item.label}}</el-checkbox>
            </el-checkbox-group>

            <el-radio-group v-else-if="htmlTypeVisible(col.htmlType,'el-radio-group')" v-model.trim="scope.row[col.javaField]">
              <el-radio v-for="(item, index) in optionsList[col.javaField+'Options']" :key="index" :label="item.value"
                        :disabled="item.disabled">{{item.label}}</el-radio>
            </el-radio-group>

            <el-date-picker v-else-if="htmlTypeVisible(col.htmlType,'el-date-picker')" v-model.trim="scope.row[col.javaField]"
                            :placeholder="$t('common.placeholderDefault') + col.columnComment"></el-date-picker>

            <el-input-number v-else-if="htmlTypeVisible(col.htmlType,'el-input-number')" v-model.trim="scope.row[col.javaField]"
                            :placeholder="$t('common.placeholderDefault') + col.columnComment"></el-input-number>

            <el-input v-else-if="htmlTypeVisible(col.htmlType,'el-input-password')" v-model.trim="scope.row[col.javaField]"
                             :placeholder="$t('common.placeholderDefault') + col.columnComment" type="password"></el-input>
            <el-switch v-else-if="htmlTypeVisible(col.htmlType,'el-switch')" v-model.trim="scope.row[col.javaField]"
                         :placeholder="$t('common.placeholderDefault') + col.columnComment"></el-switch>
            <el-cascader v-else-if="htmlTypeVisible(col.htmlType,'el-cascader')" v-model.trim="scope.row[col.javaField]"
                       :placeholder="$t('common.placeholderDefault') + col.columnComment"></el-cascader>
            <el-slider v-else-if="htmlTypeVisible(col.htmlType,'el-slider')" v-model.trim="scope.row[col.javaField]"
                         :placeholder="$t('common.placeholderDefault') + col.columnComment"></el-slider>
            <el-time-picker v-else-if="htmlTypeVisible(col.htmlType,'el-time-picker')" v-model.trim="scope.row[col.javaField]"
                       :placeholder="$t('common.placeholderDefault') + col.columnComment"></el-time-picker>
            <el-rate v-else-if="htmlTypeVisible(col.htmlType,'el-rate')" v-model.trim="scope.row[col.javaField]"
                       :placeholder="$t('common.placeholderDefault') + col.columnComment"></el-rate>
            <el-color-picker v-else-if="htmlTypeVisible(col.htmlType,'el-color-picker')" v-model.trim="scope.row[col.javaField]"
                     :placeholder="$t('common.placeholderDefault') + col.columnComment"></el-color-picker>



            <el-input v-if="htmlTypeVisible(col.htmlType,'el-input-textarea')" v-model.trim="scope.row[col.javaField]"
                      :placeholder="$t('common.placeholderDefault') + col.columnComment" type="textarea"> </el-input>

            <el-date-picker v-else-if="htmlTypeVisible(col.htmlType,'el-date-picker-range')" v-model.trim="scope.row[col.javaField]"
                            :placeholder="$t('common.placeholderDefault') + col.columnComment" type="daterange" style="width:250px"  :range-separator="$t('common.rangeSeparator')"
                            :start-placeholder="$t('common.beginDate')"
                            :end-placeholder="$t('common.endDate')"
                            value-format="yyyy-MM-dd"
                            format="yyyy-MM-dd"></el-date-picker>
            <el-time-picker v-else-if="htmlTypeVisible(col.htmlType,'el-time-picker-range')" v-model.trim="scope.row[col.javaField]"
                            :placeholder="$t('common.placeholderDefault') + col.columnComment"  is-range style="width:200px"  :range-separator="$t('common.rangeSeparator')"
                            :start-placeholder="$t('common.beginTime')"
                            :end-placeholder="$t('common.endTime')"
                            value-format="HH:mm:ss"
                            format="HH:mm:ss"></el-time-picker>


          </template>
        </el-table-column>

      </el-table>
    </el-col>
      </el-scrollbar>

    <el-col :span="24">
      <el-button
        type="primary"
        icon="el-icon-plus"
        size="mini"
        @click="handleAdd"
      >{{$t('common.add')}}</el-button>
    </el-col>

    <!-- 添加列對話框 -->
    <el-dialog :title="title" :visible.sync="open" append-to-body>
      <el-table border stripe
                ref="multipleTable"
                :data="multiplicableCols"
                highlight-current-row
                row-key="empNo"
                @row-click="clickRow"
                @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          reserve-selection
          width="55">
        </el-table-column>
        <el-table-column :label="$t('entfrm_child_table_form.serialNumber')"
                         align="center" type="index"  width="80"/>
        <el-table-column :label="$t('entfrm_child_table_form.columnName')" align="center"  prop="columnName"
                         :show-overflow-tooltip="true" />
        <el-table-column :label="$t('entfrm_child_table_form.columnComment')" align="center"  prop="columnComment"
                         :show-overflow-tooltip="true" />
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleMultiplicableColSubmit">{{$t('common.confirm')}}</el-button>
        <el-button @click="handleMultiplicableColClose">{{$t('common.cancel')}}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import Sortable from 'sortablejs';
import { htmlTypeConvertForm } from '@/utils/design/generator/config'

const EVENT_COL_SORTED = 'onColSorted'
const HEADER_CHICK = 'headerChick'

export default {
  components: {},
  props: [
    'element',
    'childTableElement',
    'label',
    'childTableWidth',
    'childAttributesMap',
    'childRegMap',
  ],
  data() {
    return {
      tableHeight: document.documentElement.scrollHeight - 245 + "px",
      dataList:[],                          //tabel綁定的數據
      cols: [],                             //動態綁定的列
      tableCols: [],                        //傳入的已排序的列
      columnObj: {},                        //新增時添加的初始化數據
      optionsList: [],                      //dictType所需要的數據集
      dragTableMobileClientWidth: 0,
      title: this.$t('entfrm_child_table_form.multiplicableCols'),
      open: false,
      multiplicableCols: [],                //可以增加的列
      multiplicableColsAll: [],             //可以增加的所有列
      colsHasAdded: new Set(),              //已展示的列
      multipleColSelection: [],             //彈出框中選中的列
      myChildTableWidth: 'width:'+ this.childTableWidth +'%',
      tableClass: 'entfrm-child-table-select' + '_' + this.element.tableName                       //區分css選擇器
    }
  },
  watch: {
    // eslint-disable-next-line func-names
    'element.tableName': function (newVal, oldVal) {
      if(newVal) {
        if(newVal !== oldVal) {
          this.tableClass = 'entfrm-child-table-select' + '_' + newVal
          this.clean()
          this.formInit()
        }
      }
    },
    childTableWidth: function (newVal, oldVal) {
      this.myChildTableWidth = 'width:'+ newVal +'%'
    },
  },
  created() {
    if(!this.element){
      return
    }
    this.formInit()
  },
  mounted () {
    this.columnDrop();
  },
  methods: {
    //數據初始化
    formInit(){
      if (!Array.isArray(this.element.columns)) return null
      if(this.element.tableCols && this.element.tableCols.length > 0){
        this.tableCols = this.element.tableCols
      }else{
        this.tableCols = this.element.columns
      }
      this.tableCols.map(column => {
        if(column.isAdd == '1'){
          // console.log(column)
          // if(this.childAttributesMap[column.columnName]){
          //   column.columnWidth = this.childAttributesMap[column.columnName].columnWidth
          // }
          //添加到動態綁定的列
          this.cols.push(column)
          this.colsHasAdded.add(column.columnName)
        }
      })
      this.element.columns.map(column => {
        if(column.isAdd == '1') {
          // console.log(column)
          //添加到動態綁定的列
          this.multiplicableColsAll.push(column)
          //初始化新增時添加的數據
          let defaultValue
          if (column.htmlType == 'checkbox') {
            defaultValue = []
          } else if (typeof (column.defValue) === 'string') {
            defaultValue = column.defValue
          } else if (column.defValue == null) {
            defaultValue = ''
          } else{
            defaultValue = column.defValue
          }
          this.columnObj[column.javaField] = defaultValue
          //初始化dictType所需要的數據集
          if(column.dictType && column.dictType != ''){
            if(this.optionsList[column.javaField+'Options'] === undefined){
              //填充數據
              this.getDicts(column.dictType).then(response => {
                this.optionsList[column.javaField+'Options'] = response.data;
              });
            }
          }
        }
      })
      this.$emit(EVENT_COL_SORTED,this.element, this.cols)
    },
    clean(){
      // this.dataList = []                         //tabel綁定的數據
      this.cols= []                            //動態綁定的列
      this.tableCols= []                            //傳入的已排序的列
      this.columnObj= {}                        //新增時添加的初始化數據
      // this.optionsList= []                      //dictType所需要的數據集
    },
    // 列拖拽
    columnDrop () {
      let that = this
      const wrapperTr = document.querySelector('.'+this.tableClass+' .el-table__header-wrapper tr');
      this.sortable = Sortable.create(wrapperTr, {
        animation: 180,
        delay: 0,
        onEnd: evt => {
          //拖拽结束发生该事件
          that.cols.splice(evt.newIndex, 0, that.cols.splice(evt.oldIndex, 1)[0]);
          var newArray = that.cols.slice(0);
          that.cols = [];
          that.$nextTick(function () {
            that.cols = newArray;
            this.$emit(EVENT_COL_SORTED,this.element, this.cols)
          });
        }
      });
    },
    handleAdd() {
      const column = JSON.parse(JSON.stringify(this.columnObj))
      this.dataList.splice(this.dataList.length, 0, column);
      for (let index in this.dataList) {
        this.dataList[index].sort = parseInt(index) + 1;
      }
    },
    htmlTypeVisible(htmlType,elementType){
      return htmlTypeConvertForm[htmlType] === elementType
    },
    deleteColunm(columnName){
      const index = this.cols.findIndex(col => col.columnName === columnName)
      let columns = this.cols.splice(index, 1)
      if(columns.length > 0){
        this.colsHasAdded.delete(columns[0].columnName)
      }
      // console.log(123)
      this.$emit(EVENT_COL_SORTED,this.element, this.cols)
    },
    /** 添加列窗口提交按钮操作 */
    handleMultiplicableColSubmit() {
      this.multipleColSelection.map(column =>{
        this.cols.splice(this.cols.length, 0, column);
        this.colsHasAdded.add(column.columnName)
      })
      this.handleMultiplicableColClose();
      this.$emit(EVENT_COL_SORTED,this.element, this.cols)
    },
    /** 添加列窗口取消按钮操作 */
    handleMultiplicableColClose() {
      this.multipleColSelection = []
      this.$refs.multipleTable.clearSelection();
      this.open = false
    },
    handleAddColumn(){
      this.multiplicableCols = []
      this.multiplicableColsAll.map(column =>{
        if(!this.colsHasAdded.has(column.columnName)){
          this.multiplicableCols.push(column)
        }
      })
      this.open = true;

    },
    // 点击一个单元格
    clickRow(row, column){
      this.$refs.multipleTable.toggleRowSelection(row)
    },
    handleSelectionChange(val) {
      this.multipleColSelection = val;
    },
    handleHeaderChick(column){
      this.$emit(HEADER_CHICK,this.element,column)
    },
    regIconShow(javaField){
      return this.childRegMap?(this.childRegMap[javaField] ? (this.childRegMap[javaField].length > 0) : false):false
    },
  }
}
</script>

<style lang="scss" scoped>

.talbe-name-style{
  font-size: 16px;
  font-weight: bold;
  color: #1DB8FF;
  line-height: 50px;
}


.drawing-item:hover>.child-talbe{
  background: #f6f7ff;
  border-radius: 6px;
}

.drawing-board .active-from-item>.child-talbe{
  background: #f6f7ff;
  border-radius: 6px;
}
</style>
