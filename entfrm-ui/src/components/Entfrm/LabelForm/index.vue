<template>
  <div :style="cssVars">
      <el-form-item :label="label" :label-width="labelWidth + 'px'" class="pannel-componet">
                 <div :class="textareaCss" style="white-space: pre-wrap;" v-html="textarea"></div>
      </el-form-item>

  </div>
</template>

<script>

export default {
  components: {},
  props: [
    'label',
    'labelWidth',
    'labelPosition',
    'labelSize',
    'labelColor',
    'textarea',
    'textareaCss',
    'textSize',
    'textColor',
  ],
  data() {
    return {
    }
  },
  watch: {
  },
  created() {
  },
  mounted () {
  },
  computed:{
    cssVars(){
      return {
        "--labelColor": this.labelColor,
        "--labelSize": this.labelSize + 'px',
        "--textColor": this.textColor,
        "--textSize": this.textSize + 'px',
        "--labelPosition": this.labelPosition,
      }
    }
  },
  methods: {
  }
}
</script>

<style lang="scss" scoped >
  /deep/.el-form-item__label{
    color: var(--labelColor);
    font-size: var(--labelSize);
    text-align: var(--labelPosition);
  }
  /deep/.el-form-item__content{
    color: var(--textColor);
    font-size: var(--textSize);
  }
/*  /deep/.pannel-componet{
    margin-bottom: 0 !important;
  }*/

  .drawing-item:hover .pannel-componet{
    background: #f6f7ff;
    border-radius: 6px;
  }

  .drawing-board .active-from-item .pannel-componet{
    background: #f6f7ff;
    border-radius: 6px;
  }

</style>
