<script>
import CodeEditor from "./code-editor.vue";
import <PERSON><PERSON>utt<PERSON> from "./button.vue";
import { debounce } from "throttle-debounce";
import { toggleClass } from "../utils/DOMhelper";
import { parseComponent } from "../utils/sfcParser/parser";
import { genStyleInjectionCode } from "../utils/sfcParser/styleInjection";
import Tooltip from "./tooltip";
import { isEmpty, extend, generateId } from "../utils/util";
import { addStylesClient } from "../utils/style-loader/addStylesClient";
// 字体图标
import "../fonts/iconfont.css";



import '@/assets/styles/design-build/design-add-view.scss'
import request from '@/utils/request'
// import {
//   getHeader
// }
//   from "@/utils/entfrm";
export default {
  name: "CodeViewer",
  components: {
    CodeEditor,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
  },
  props: {
    theme: { type: String, default: "dark" }, //light
    showCode: { type: Boolean, default: false },
    source: { type: String },
    renderToolbar: { type: Function },
    errorHandler: { type: Function },
    debounceDelay: {
      type: Number,
      default: 300,
    },
  },
  data() {
    return {
      code: ``,
      className: ["vue-code-viewer", "vue-app"], // page className
      dynamicComponent: {
        component: {
          template: "<div></div>",
        },
      },
      hasError: false,
      errorMessage: null,
      showCodeEditor: this.showCode,
      showCodeIcon: {},
    };
  },
  created() {
    this.viewId = `vcv-${generateId()}`; // vue-code-view => vcv
    this.debounceErrorHandler = debounce(this.debounceDelay, this.errorHandler);

    // this.update = debounce(this.debounceDelay, addStylesClient(this.viewId, {}));
    this.stylesUpdateHandler = addStylesClient(this.viewId, {});
  },
  mounted() {
    this._initialize();
    if(!this.source){ //頁面為移動端時
      let _self = this;
      //添加消息監聽
      window.addEventListener('message', function(messageEvent) {
        let data = messageEvent.data;
        _self.handleCodeChange(data.source);
      })
      //頁面加載成功時 調用父頁面方法發送數據
      window.parent.postMobileInfo()
    }
  },
  methods: {
    // 初始化
    _initialize() {
      // 传入初始值赋值  prop.source=>code
      this.handleCodeChange(this.source);
    },

    genComponent() {
      const { template, script, styles, customBlocks, errors } =
        this.sfcDescriptor;

      // console.log(this.sfcDescriptor);

      const templateCode = template ? template.content.trim() : ``;
      let scriptCode = script ? script.content.trim() : ``;
      const { styleCode, styleArray } = genStyleInjectionCode(styles);

      // 构建组件
      const demoComponent = {};

      // 组件 script
      if (!isEmpty(scriptCode)) {
        const componentScript = {};
        scriptCode = scriptCode.replace(
          /export\s+default/,
          "componentScript ="
        );
        eval(scriptCode);
        extend(demoComponent, componentScript);
      }

      // 组件 template
      // id="${componentId}"
      demoComponent.template = `
            <section class="component-wrapper" >
              ${templateCode}
            </section>
        `;

      // 组件 style
      // https://github.com/vuejs/vue-style-loader/blob/master/lib/addStylesClient.js
      this.stylesUpdateHandler(styleArray);

      // if (!isEmpty(styleCode)) {
      //   // beforeMount  动态创建样式 style
      //   demoComponent.beforeMount = function () {
      //     var hasDocument = typeof document !== "undefined";
      //     var head =
      //       hasDocument &&
      //       (document.head || document.getElementsByTagName("head")[0]);
      //     var styleElement = document.createElement("style");
      //     styleElement.type = "text/css";
      //     styleElement.innerHTML = `${styleCode} `;
      //     head.appendChild(styleElement);
      //   };
      // }

      extend(this.dynamicComponent, {
        name: this.codeViewerId,
        component: demoComponent,
      });
    },
    // 组件代码编辑器展示
    handleShowCode() {
      this.showCodeEditor = !this.showCodeEditor;
    },
    // 组件演示背景透明切换
    handleChangeTransparent() {
      toggleClass(this.$refs.codeViewer, "vue-code-transparent");
    },
    // 更新 code 内容
    handleCodeChange(val) {
      if(val!=null&&val!=""){
        this.code = val;
      }
    },

    renderPreview() {
      const { hasError, errorMessage } = this;
      // <div>{this.initialExample ? this.initialExample : <div>Loading...</div>}</div>
      if (hasError) {
        return <pre class="code-view-error">{errorMessage}</pre>;
      }

      const renderComponent = this.dynamicComponent.component;
      return (
        <div class="code-view">
          <renderComponent></renderComponent>
        </div>
      );
    },
    // 代码检查
    codeLint() {
      // 校验代码是否为空
      this.hasError = this.isCodeEmpty;
      this.errorMessage = this.isCodeEmpty ? "代码不能为空！" : null;
      // 代码为空 跳出检查
      if (this.isCodeEmpty) return;

      // 校验代码是否存在<template>
      const { template } = this.sfcDescriptor;
      const templateCode =
        template && template.content ? template.content.trim() : ``;
      const isTemplateEmpty = isEmpty(templateCode);

      this.hasError = isTemplateEmpty;
      this.errorMessage = isTemplateEmpty
        ? "代码格式错误，不存在 <template> ！"
        : null;
      // 代码为空 跳出检查
      if (this.isTemplateEmpty) return;
    },
    defaultButtonRender(showCodeButton, showTransparentButton) {
      return (
        <div>
          {showCodeButton} {showTransparentButton}
        </div>
      );
    },
  },
  computed: {
    // SFC Descriptor Object
    sfcDescriptor: function () {
      return parseComponent(this.code);
    },
    // 代码是否为空
    isCodeEmpty: function () {
      return !(this.code && !isEmpty(this.code.trim()));
    },
  },
  watch: {
    // eslint-disable-next-line no-unused-vars
    code(newSource, oldSource) {
      this.codeLint();
      // 错误事件处理
      this.hasError &&
        this.errorHandler &&
        this.debounceErrorHandler(this.errorMessage);

      if (!this.hasError) this.genComponent();
    },
  },

  render() {
    const { className, renderToolbar, theme } = this;
    const showCodeButton = (
      <Tooltip
        class="item"
        effect="dark"
        content="Show the source"
        placement="top"
      >
        <me-button
          icon="code"
          size="xs"
          circle
          onClick={this.handleShowCode}
        ></me-button>
      </Tooltip>
    );
    const showTransparentButton = (
      <Tooltip
        class="item"
        effect="dark"
        content="Transparent background"
        placement="top"
      >
        <me-button
          icon="transparent"
          size="xs"
          circle
          onClick={this.handleChangeTransparent}
        ></me-button>
      </Tooltip>
    );
    return (
      <div class={className} ref="codeViewer">
        <div class="code-view-wrapper">
          {/* --------- renderExample  --------- */}
          {this.renderPreview()}
          {/* --------- toolbar   --------- */}
          <div class="code-view-toolbar">
            {renderToolbar
              ? renderToolbar(showCodeButton, showTransparentButton)
              : this.defaultButtonRender(showCodeButton, showTransparentButton)}
          </div>
          {/* --------- CodeEditor   --------- */}
          {this.showCodeEditor && (
            <CodeEditor
              lineNumbers
              codeHandler={this.handleCodeChange}
              theme={`base16-${theme}`}
              value={this.source}
            />
          )}
        </div>
      </div>
    );
  },
};
</script>

<style lang="scss">
$code-view-wrapper-border-color: #f1f1f1;
$code-view-wrapper-bg: #ffffff;
$primary-color: #ffffff;
.vue-code-viewer{
  width: 100%;
  height: 100%;
}
.code-view-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  padding: 0;
  // border: 1px dashed #f1f1f1;
  border: 0px solid #ebebeb;

  border-color: $code-view-wrapper-border-color;
  background-color: $code-view-wrapper-bg;
  border-radius: 4px;
  transition: 0.3s linear border-color;

  &:hover {
    border: 0px dashed $primary-color;
  }

  .code-view {
    padding: 18px;
    // &:after {
    //   position: absolute;
    //   top: 18px;
    //   left: 18px;
    //   font-size: 12px;
    //   font-weight: 300;
    //   color: #959595;
    //   text-transform: uppercase;
    //   letter-spacing: 1px;
    // }
  }
  .code-view-error {
    padding: 18px;
    color: red;
    max-height: 200px;
  }

  .code-view-toolbar {
    padding: 8px;
    border-color: $code-view-wrapper-border-color;
    display: none;
    align-items: center;
    justify-content: flex-end;
    > .icon {
      font-size: 16px;
    }
  }
}

// CodeMirror
.CodeMirror {
  text-align: left;
  padding: 10px;
  // margin: 10px 0;
  height: auto !important;
  pre {
    padding: 0 20px;
  }
}

.vue-code-transparent .code-view {
  background-image: linear-gradient(
      45deg,
      rgb(249, 249, 250) 25%,
      transparent 25%
    ),
    linear-gradient(135deg, rgb(249, 249, 250) 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, rgb(249, 249, 250) 75%),
    linear-gradient(135deg, transparent 75%, rgb(249, 249, 250) 75%);
  background-size: 20px 20px;
  background-position: 0px 0px, 10px 0px, 10px -10px, 0px 10px;
}

  .vue-code-viewer .cube-page >.wrapper{
    margin-right: -15px;
  }
</style>

