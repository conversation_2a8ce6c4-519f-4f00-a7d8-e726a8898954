@font-face {
  font-family: "iconfont"; /* Project id 2877156 */
  src: url('iconfont.woff2?t=1634617219347') format('woff2'),
       url('iconfont.woff?t=1634617219347') format('woff'),
       url('iconfont.ttf?t=1634617219347') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.me-icon-code:before {
  content: "\e7fc";
}

.me-icon-transparent:before {
  content: "\f0de";
}

