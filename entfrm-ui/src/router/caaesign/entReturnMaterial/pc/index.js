export default [
    {
        path: '/caaesign/entReturnMaterial',
        component: (resolve) => require(['@/layout'], resolve),
        hidden: true,
        meta: {title: 'Return Material Authorization申請單'},
        children: [
            {
                path: 'EntReturnMaterialAdd',
                component: (resolve) => require(['@/views/caaesign/entReturnMaterial/pc/EntReturnMaterialAdd'], resolve),
                name: 'caaesign_entReturnMaterial_EntReturnMaterialAdd',
                meta: {title: 'Return Material Authorization申請單新增'}
            },
            {
                path: 'EntReturnMaterialEdit',
                    component: (resolve) => require(['@/views/caaesign/entReturnMaterial/pc/EntReturnMaterialAdd'], resolve),
                name: 'caaesign_entReturnMaterial_EntReturnMaterialEdit',
                meta: {title: 'Return Material Authorization申請單修改'}
            },
            {
                path: 'EntReturnMaterialAudit',
                component: (resolve) => require(['@/views/caaesign/entReturnMaterial/pc/EntReturnMaterialAudit'], resolve),
                name: 'caaesign_entReturnMaterial_EntReturnMaterialAudit',
                meta: { title: 'Return Material Authorization申請單审核' }
            },
            {
                path: 'EntReturnMaterialDetail',
                component:(resolve) => require(['@/views/caaesign/entReturnMaterial/pc/EntReturnMaterialDetail'], resolve),
                name:'caaesign_entReturnMaterial_EntReturnMaterialDetail',
                meta:{title: 'Return Material Authorization申請單详情'}
            },
            {
                path: 'EntReturnMaterialReject',
                    component:(resolve) => require(['@/views/caaesign/entReturnMaterial/pc/EntReturnMaterialReject'], resolve),
                name:'caaesign_entReturnMaterial_EntReturnMaterialReject',
                meta:{title: 'Return Material Authorization申請單修改'}
            }
        ]
    }
]
  