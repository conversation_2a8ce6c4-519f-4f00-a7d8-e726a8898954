export default [
    {
        path: '/caaesign/entReturnMaterialApp',
        component: (resolve) => require(['@/mobileLayout'], resolve),
        hidden: true,
        meta: {title: 'Return Material Authorization申請單'},
        children: [
            {
                path: 'EntReturnMaterialAddApp',
                component: (resolve) => require(['@/views/caaesign/entReturnMaterial/app/EntReturnMaterialAddApp'], resolve),
                name: 'caaesign_entReturnMaterial_EntReturnMaterialAddApp',
                meta: {title: 'Return Material Authorization申請單新增'}
            },
            {
                path: 'EntReturnMaterialEditApp',
                    component: (resolve) => require(['@/views/caaesign/entReturnMaterial/app/EntReturnMaterialAddApp'], resolve),
                name: 'caaesign_entReturnMaterial_EntReturnMaterialEditApp',
                meta: {title: 'Return Material Authorization申請單修改'}
            },
            {
                path: 'EntReturnMaterialAuditApp',
                component: (resolve) => require(['@/views/caaesign/entReturnMaterial/app/EntReturnMaterialAuditApp'], resolve),
                name: 'caaesign_entReturnMaterial_EntReturnMaterialAuditApp',
                meta: { title: 'Return Material Authorization申請單审核' }
            },
            {
                path: 'EntReturnMaterialDetailApp',
                component:(resolve) => require(['@/views/caaesign/entReturnMaterial/app/EntReturnMaterialDetailApp'], resolve),
                name:'caaesign_entReturnMaterial_EntReturnMaterialDetailApp',
                meta:{title: 'Return Material Authorization申請單详情'}
            },
            {
                path: 'EntReturnMaterialRejectApp',
                    component:(resolve) => require(['@/views/caaesign/entReturnMaterial/app/EntReturnMaterialRejectApp'], resolve),
                name:'caaesign_entReturnMaterial_EntReturnMaterialRejectApp',
                meta:{title: 'Return Material Authorization申請單修改'}
            }
        ]
    }
]
  