export default [
    {
        path: '/caaesign/entContentCheckApp',
        component: () => import('@/mobileLayout'),
        hidden: true,
        meta: {title: '系統發表資料內容檢查表'},
        children: [
            {
                path: 'EntContentCheckAddApp',
                component: () => import('@/views/caaesign/entContentCheck/app/EntContentCheckAddApp'),
                name: 'caaesign_entContentCheck_EntContentCheckAddApp',
                meta: {title: '系統發表資料內容檢查表新增'}
            },
            {
                path: 'EntContentCheckEditApp',
                    component: () => import('@/views/caaesign/entContentCheck/app/EntContentCheckAddApp'),
                name: 'caaesign_entContentCheck_EntContentCheckEditApp',
                meta: {title: '系統發表資料內容檢查表修改'}
            },
            {
                path: 'EntContentCheckAuditApp',
                component: () => import('@/views/caaesign/entContentCheck/app/EntContentCheckAuditApp'),
                name: 'caaesign_entContentCheck_EntContentCheckAuditApp',
                meta: { title: '系統發表資料內容檢查表审核' }
            },
            {
                path: 'EntContentCheckDetailApp',
                component:() => import('@/views/caaesign/entContentCheck/app/EntContentCheckDetailApp'),
                name:'caaesign_entContentCheck_EntContentCheckDetailApp',
                meta:{title: '系統發表資料內容檢查表详情'}
            },
            {
                path: 'EntContentCheckRejectApp',
                    component:() => import('@/views/caaesign/entContentCheck/app/EntContentCheckRejectApp'),
                name:'caaesign_entContentCheck_EntContentCheckRejectApp',
                meta:{title: '系統發表資料內容檢查表修改'}
            }
        ]
    }
]
