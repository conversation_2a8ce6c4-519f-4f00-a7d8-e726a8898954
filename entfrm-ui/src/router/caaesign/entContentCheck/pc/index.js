export default [
    {
        path: '/caaesign/entContentCheck',
        component: () => import('@/layout'),
        hidden: true,
        meta: {title: '系統發表資料內容檢查表'},
        children: [
            {
                path: 'EntContentCheckAdd',
                component: () => import('@/views/caaesign/entContentCheck/pc/EntContentCheckAdd'),
                name: 'caaesign_entContentCheck_EntContentCheckAdd',
                meta: {title: '系統發表資料內容檢查表新增'}
            },
            {
                path: 'EntContentCheckEdit',
                    component: () => import('@/views/caaesign/entContentCheck/pc/EntContentCheckAdd'),
                name: 'caaesign_entContentCheck_EntContentCheckEdit',
                meta: {title: '系統發表資料內容檢查表修改'}
            },
            {
                path: 'EntContentCheckAudit',
                component: () => import('@/views/caaesign/entContentCheck/pc/EntContentCheckAudit'),
                name: 'caaesign_entContentCheck_EntContentCheckAudit',
                meta: { title: '系統發表資料內容檢查表审核' }
            },
            {
                path: 'EntContentCheckDetail',
                component:() => import('@/views/caaesign/entContentCheck/pc/EntContentCheckDetail'),
                name:'caaesign_entContentCheck_EntContentCheckDetail',
                meta:{title: '系統發表資料內容檢查表详情'}
            },
            {
                path: 'EntContentCheckReject',
                    component:() => import('@/views/caaesign/entContentCheck/pc/EntContentCheckReject'),
                name:'caaesign_entContentCheck_EntContentCheckReject',
                meta:{title: '系統發表資料內容檢查表修改'}
            }
        ]
    }
]
