export default [
    {
        path: '/caaesign/entSpeApplyApp',
        component: () => import('@/mobileLayout'),
        hidden: true,
        meta: {title: '特殊就餐申請單'},
        children: [
            {
                path: 'EntSpeApplyAddApp',
                component: () => import('@/views/caaesign/entSpeApply/app/EntSpeApplyAddApp'),
                name: 'caaesign_entSpeApply_EntSpeApplyAddApp',
                meta: {title: '特殊就餐申請單新增'}
            },
            {
                path: 'EntSpeApplyEditApp',
                    component: () => import('@/views/caaesign/entSpeApply/app/EntSpeApplyAddApp'),
                name: 'caaesign_entSpeApply_EntSpeApplyEditApp',
                meta: {title: '特殊就餐申請單修改'}
            },
            {
                path: 'EntSpeApplyAuditApp',
                component: () => import('@/views/caaesign/entSpeApply/app/EntSpeApplyAuditApp'),
                name: 'caaesign_entSpeApply_EntSpeApplyAuditApp',
                meta: { title: '特殊就餐申請單审核' }
            },
            {
                path: 'EntSpeApplyDetailApp',
                component:() => import('@/views/caaesign/entSpeApply/app/EntSpeApplyDetailApp'),
                name:'caaesign_entSpeApply_EntSpeApplyDetailApp',
                meta:{title: '特殊就餐申請單详情'}
            },
            {
                path: 'EntSpeApplyRejectApp',
                    component:() => import('@/views/caaesign/entSpeApply/app/EntSpeApplyRejectApp'),
                name:'caaesign_entSpeApply_EntSpeApplyRejectApp',
                meta:{title: '特殊就餐申請單修改'}
            }
        ]
    }
]
