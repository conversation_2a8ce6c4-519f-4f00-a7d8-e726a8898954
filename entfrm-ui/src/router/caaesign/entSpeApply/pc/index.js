export default [
    {
        path: '/caaesign/entSpeApply',
        component: () => import('@/layout'),
        hidden: true,
        meta: {title: '特殊就餐申請單'},
        children: [
            {
                path: 'EntSpeApplyAdd',
                component: () => import('@/views/caaesign/entSpeApply/pc/EntSpeApplyAdd'),
                name: 'caaesign_entSpeApply_EntSpeApplyAdd',
                meta: {title: '特殊就餐申請單新增'}
            },
            {
                path: 'EntSpeApplyEdit',
                    component: () => import('@/views/caaesign/entSpeApply/pc/EntSpeApplyAdd'),
                name: 'caaesign_entSpeApply_EntSpeApplyEdit',
                meta: {title: '特殊就餐申請單修改'}
            },
            {
                path: 'EntSpeApplyAudit',
                component: () => import('@/views/caaesign/entSpeApply/pc/EntSpeApplyAudit'),
                name: 'caaesign_entSpeApply_EntSpeApplyAudit',
                meta: { title: '特殊就餐申請單审核' }
            },
            {
                path: 'EntSpeApplyDetail',
                component:() => import('@/views/caaesign/entSpeApply/pc/EntSpeApplyDetail'),
                name:'caaesign_entSpeApply_EntSpeApplyDetail',
                meta:{title: '特殊就餐申請單详情'}
            },
            {
                path: 'EntSpeApplyReject',
                    component:() => import('@/views/caaesign/entSpeApply/pc/EntSpeApplyReject'),
                name:'caaesign_entSpeApply_EntSpeApplyReject',
                meta:{title: '特殊就餐申請單修改'}
            }
        ]
    }
]
