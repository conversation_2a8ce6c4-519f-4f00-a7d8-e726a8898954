export default [
    {
        path: '/caaesign/entDatabaseForm',
        component: () => import('@/layout'),
        hidden: true,
        meta: {title: '數據庫服務申請單2'},
        children: [
            {
                path: 'EntDatabaseFormAdd',
                component: () => import('@/views/caaesign/entDatabaseForm/pc/EntDatabaseFormAdd'),
                name: 'caaesign_entDatabaseForm_EntDatabaseFormAdd',
                meta: {title: '數據庫服務申請單2新增'}
            },
            {
                path: 'EntDatabaseFormEdit',
                    component: () => import('@/views/caaesign/entDatabaseForm/pc/EntDatabaseFormAdd'),
                name: 'caaesign_entDatabaseForm_EntDatabaseFormEdit',
                meta: {title: '數據庫服務申請單2修改'}
            },
            {
                path: 'EntDatabaseFormAudit',
                component: () => import('@/views/caaesign/entDatabaseForm/pc/EntDatabaseFormAudit'),
                name: 'caaesign_entDatabaseForm_EntDatabaseFormAudit',
                meta: { title: '數據庫服務申請單2审核' }
            },
            {
                path: 'EntDatabaseFormDetail',
                component:() => import('@/views/caaesign/entDatabaseForm/pc/EntDatabaseFormDetail'),
                name:'caaesign_entDatabaseForm_EntDatabaseFormDetail',
                meta:{title: '數據庫服務申請單2详情'}
            },
            {
                path: 'EntDatabaseFormReject',
                    component:() => import('@/views/caaesign/entDatabaseForm/pc/EntDatabaseFormReject'),
                name:'caaesign_entDatabaseForm_EntDatabaseFormReject',
                meta:{title: '數據庫服務申請單2修改'}
            }
        ]
    }
]
