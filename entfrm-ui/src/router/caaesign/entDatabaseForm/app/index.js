export default [
    {
        path: '/caaesign/entDatabaseFormApp',
        component: () => import('@/mobileLayout'),
        hidden: true,
        meta: {title: '數據庫服務申請單2'},
        children: [
            {
                path: 'EntDatabaseFormAddApp',
                component: () => import('@/views/caaesign/entDatabaseForm/app/EntDatabaseFormAddApp'),
                name: 'caaesign_entDatabaseForm_EntDatabaseFormAddApp',
                meta: {title: '數據庫服務申請單2新增'}
            },
            {
                path: 'EntDatabaseFormEditApp',
                    component: () => import('@/views/caaesign/entDatabaseForm/app/EntDatabaseFormAddApp'),
                name: 'caaesign_entDatabaseForm_EntDatabaseFormEditApp',
                meta: {title: '數據庫服務申請單2修改'}
            },
            {
                path: 'EntDatabaseFormAuditApp',
                component: () => import('@/views/caaesign/entDatabaseForm/app/EntDatabaseFormAuditApp'),
                name: 'caaesign_entDatabaseForm_EntDatabaseFormAuditApp',
                meta: { title: '數據庫服務申請單2审核' }
            },
            {
                path: 'EntDatabaseFormDetailApp',
                component:() => import('@/views/caaesign/entDatabaseForm/app/EntDatabaseFormDetailApp'),
                name:'caaesign_entDatabaseForm_EntDatabaseFormDetailApp',
                meta:{title: '數據庫服務申請單2详情'}
            },
            {
                path: 'EntDatabaseFormRejectApp',
                    component:() => import('@/views/caaesign/entDatabaseForm/app/EntDatabaseFormRejectApp'),
                name:'caaesign_entDatabaseForm_EntDatabaseFormRejectApp',
                meta:{title: '數據庫服務申請單2修改'}
            }
        ]
    }
]
