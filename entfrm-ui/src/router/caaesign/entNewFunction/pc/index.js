export default [
    {
        path: '/caaesign/entNewFunction',
        component: () => import('@/layout'),
        hidden: true,
        meta: {title: '新功能服务需求申请单'},
        children: [
            {
                path: 'EntNewFunctionAdd',
                component: () => import('@/views/caaesign/entNewFunction/pc/EntNewFunctionAdd'),
                name: 'caaesign_entNewFunction_EntNewFunctionAdd',
                meta: {title: '新功能服务需求申请单新增'}
            },
            {
                path: 'EntNewFunctionEdit',
                    component: () => import('@/views/caaesign/entNewFunction/pc/EntNewFunctionAdd'),
                name: 'caaesign_entNewFunction_EntNewFunctionEdit',
                meta: {title: '新功能服务需求申请单修改'}
            },
            {
                path: 'EntNewFunctionAudit',
                component: () => import('@/views/caaesign/entNewFunction/pc/EntNewFunctionAudit'),
                name: 'caaesign_entNewFunction_EntNewFunctionAudit',
                meta: { title: '新功能服务需求申请单审核' }
            },
            {
                path: 'EntNewFunctionDetail',
                component:() => import('@/views/caaesign/entNewFunction/pc/EntNewFunctionDetail'),
                name:'caaesign_entNewFunction_EntNewFunctionDetail',
                meta:{title: '新功能服务需求申请单详情'}
            },
            {
                path: 'EntNewFunctionReject',
                    component:() => import('@/views/caaesign/entNewFunction/pc/EntNewFunctionReject'),
                name:'caaesign_entNewFunction_EntNewFunctionReject',
                meta:{title: '新功能服务需求申请单修改'}
            }
        ]
    }
]
