export default [
    {
        path: '/caaesign/entNewFunctionApp',
        component: () => import('@/mobileLayout'),
        hidden: true,
        meta: {title: '新功能服务需求申请单'},
        children: [
            {
                path: 'EntNewFunctionAddApp',
                component: () => import('@/views/caaesign/entNewFunction/app/EntNewFunctionAddApp'),
                name: 'caaesign_entNewFunction_EntNewFunctionAddApp',
                meta: {title: '新功能服务需求申请单新增'}
            },
            {
                path: 'EntNewFunctionEditApp',
                    component: () => import('@/views/caaesign/entNewFunction/app/EntNewFunctionAddApp'),
                name: 'caaesign_entNewFunction_EntNewFunctionEditApp',
                meta: {title: '新功能服务需求申请单修改'}
            },
            {
                path: 'EntNewFunctionAuditApp',
                component: () => import('@/views/caaesign/entNewFunction/app/EntNewFunctionAuditApp'),
                name: 'caaesign_entNewFunction_EntNewFunctionAuditApp',
                meta: { title: '新功能服务需求申请单审核' }
            },
            {
                path: 'EntNewFunctionDetailApp',
                component:() => import('@/views/caaesign/entNewFunction/app/EntNewFunctionDetailApp'),
                name:'caaesign_entNewFunction_EntNewFunctionDetailApp',
                meta:{title: '新功能服务需求申请单详情'}
            },
            {
                path: 'EntNewFunctionRejectApp',
                    component:() => import('@/views/caaesign/entNewFunction/app/EntNewFunctionRejectApp'),
                name:'caaesign_entNewFunction_EntNewFunctionRejectApp',
                meta:{title: '新功能服务需求申请单修改'}
            }
        ]
    }
]
