export default [
    {
        path: '/caaesign/entChargeAccountApp',
        component: () => import('@/mobileLayout'),
        hidden: true,
        meta: {title: '經管類系統賬號申請表'},
        children: [
            {
                path: 'EntChargeAccountAddApp',
                component: () => import('@/views/caaesign/entChargeAccount/app/EntChargeAccountAddApp'),
                name: 'caaesign_entChargeAccount_EntChargeAccountAddApp',
                meta: {title: '經管類系統賬號申請表新增'}
            },
            {
                path: 'EntChargeAccountEditApp',
                    component: () => import('@/views/caaesign/entChargeAccount/app/EntChargeAccountAddApp'),
                name: 'caaesign_entChargeAccount_EntChargeAccountEditApp',
                meta: {title: '經管類系統賬號申請表修改'}
            },
            {
                path: 'EntChargeAccountAuditApp',
                component: () => import('@/views/caaesign/entChargeAccount/app/EntChargeAccountAuditApp'),
                name: 'caaesign_entChargeAccount_EntChargeAccountAuditApp',
                meta: { title: '經管類系統賬號申請表审核' }
            },
            {
                path: 'EntChargeAccountDetailApp',
                component:() => import('@/views/caaesign/entChargeAccount/app/EntChargeAccountDetailApp'),
                name:'caaesign_entChargeAccount_EntChargeAccountDetailApp',
                meta:{title: '經管類系統賬號申請表详情'}
            },
            {
                path: 'EntChargeAccountRejectApp',
                    component:() => import('@/views/caaesign/entChargeAccount/app/EntChargeAccountRejectApp'),
                name:'caaesign_entChargeAccount_EntChargeAccountRejectApp',
                meta:{title: '經管類系統賬號申請表修改'}
            }
        ]
    }
]
