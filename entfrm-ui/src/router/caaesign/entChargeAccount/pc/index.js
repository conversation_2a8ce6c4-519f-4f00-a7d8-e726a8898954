export default [
    {
        path: '/caaesign/entChargeAccount',
        component: () => import('@/layout'),
        hidden: true,
        meta: {title: '經管類系統賬號申請表'},
        children: [
            {
                path: 'EntChargeAccountAdd',
                component: () => import('@/views/caaesign/entChargeAccount/pc/EntChargeAccountAdd'),
                name: 'caaesign_entChargeAccount_EntChargeAccountAdd',
                meta: {title: '經管類系統賬號申請表新增'}
            },
            {
                path: 'EntChargeAccountEdit',
                    component: () => import('@/views/caaesign/entChargeAccount/pc/EntChargeAccountAdd'),
                name: 'caaesign_entChargeAccount_EntChargeAccountEdit',
                meta: {title: '經管類系統賬號申請表修改'}
            },
            {
                path: 'EntChargeAccountAudit',
                component: () => import('@/views/caaesign/entChargeAccount/pc/EntChargeAccountAudit'),
                name: 'caaesign_entChargeAccount_EntChargeAccountAudit',
                meta: { title: '經管類系統賬號申請表审核' }
            },
            {
                path: 'EntChargeAccountDetail',
                component:() => import('@/views/caaesign/entChargeAccount/pc/EntChargeAccountDetail'),
                name:'caaesign_entChargeAccount_EntChargeAccountDetail',
                meta:{title: '經管類系統賬號申請表详情'}
            },
            {
                path: 'EntChargeAccountReject',
                    component:() => import('@/views/caaesign/entChargeAccount/pc/EntChargeAccountReject'),
                name:'caaesign_entChargeAccount_EntChargeAccountReject',
                meta:{title: '經管類系統賬號申請表修改'}
            }
        ]
    }
]
