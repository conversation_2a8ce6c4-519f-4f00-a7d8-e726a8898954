export default [
    {
        path: '/caaesign/entDineMeal',
        component: () => import('@/layout'),
        hidden: true,
        meta: {title: 'iPEBG-iPEG濟源廠區就餐申請表'},
        children: [
            {
                path: 'EntDineMealAdd',
                component: () => import('@/views/caaesign/entDineMeal/pc/EntDineMealAdd'),
                name: 'caaesign_entDineMeal_EntDineMealAdd',
                meta: {title: 'iPEBG-iPEG濟源廠區就餐申請表新增'}
            },
            {
                path: 'EntDineMealEdit',
                    component: () => import('@/views/caaesign/entDineMeal/pc/EntDineMealAdd'),
                name: 'caaesign_entDineMeal_EntDineMealEdit',
                meta: {title: 'iPEBG-iPEG濟源廠區就餐申請表修改'}
            },
            {
                path: 'EntDineMealAudit',
                component: () => import('@/views/caaesign/entDineMeal/pc/EntDineMealAudit'),
                name: 'caaesign_entDineMeal_EntDineMealAudit',
                meta: { title: 'iPEBG-iPEG濟源廠區就餐申請表审核' }
            },
            {
                path: 'EntDineMealDetail',
                component:() => import('@/views/caaesign/entDineMeal/pc/EntDineMealDetail'),
                name:'caaesign_entDineMeal_EntDineMealDetail',
                meta:{title: 'iPEBG-iPEG濟源廠區就餐申請表详情'}
            },
            {
                path: 'EntDineMealReject',
                    component:() => import('@/views/caaesign/entDineMeal/pc/EntDineMealReject'),
                name:'caaesign_entDineMeal_EntDineMealReject',
                meta:{title: 'iPEBG-iPEG濟源廠區就餐申請表修改'}
            }
        ]
    }
]
