export default [
    {
        path: '/caaesign/entEquipmentNetwork',
        component: () => import('@/layout'),
        hidden: true,
        meta: {title: '產線聯網設備入網申請單'},
        children: [
            {
                path: 'EntEquipmentNetworkAdd',
                component: () => import('@/views/caaesign/entEquipmentNetwork/EntEquipmentNetworkAdd'),
                name: 'caaesign_entEquipmentNetwork_EntEquipmentNetworkAdd',
                meta: {title: '產線聯網設備入網申請單新增'}
            },
            {
                path: 'EntEquipmentNetworkEdit',
                    component: () => import('@/views/caaesign/entEquipmentNetwork/EntEquipmentNetworkAdd'),
                name: 'caaesign_entEquipmentNetwork_EntEquipmentNetworkEdit',
                meta: {title: '產線聯網設備入網申請單修改'}
            },
            {
                path: 'EntEquipmentNetworkAudit',
                component: () => import('@/views/caaesign/entEquipmentNetwork/EntEquipmentNetworkAudit'),
                name: 'caaesign_entEquipmentNetwork_EntEquipmentNetworkAudit',
                meta: { title: '產線聯網設備入網申請單审核' }
            },
            {
                path: 'EntEquipmentNetworkDetail',
                component:() => import('@/views/caaesign/entEquipmentNetwork/EntEquipmentNetworkDetail'),
                name:'caaesign_entEquipmentNetwork_EntEquipmentNetworkDetail',
                meta:{title: '產線聯網設備入網申請單详情'}
            },
            {
                path: 'EntEquipmentNetworkReject',
                    component:() => import('@/views/caaesign/entEquipmentNetwork/EntEquipmentNetworkReject'),
                name:'caaesign_entEquipmentNetwork_EntEquipmentNetworkReject',
                meta:{title: '產線聯網設備入網申請單修改'}
            }
        ]
    }
]
