export default [
    {
        path: '/caaesign/aaaCantingApp',
        component: () => import('@/mobileLayout'),
        hidden: true,
        meta: {title: '餐廳就餐費用結算'},
        children: [
            {
                path: 'AaaCantingAddApp',
                component: () => import('@/views/caaesign/aaaCanting/app/AaaCantingAddApp'),
                name: 'caaesign_aaaCanting_AaaCantingAddApp',
                meta: {title: '餐廳就餐費用結算新增'}
            },
            {
                path: 'AaaCantingEditApp',
                    component: () => import('@/views/caaesign/aaaCanting/app/AaaCantingAddApp'),
                name: 'caaesign_aaaCanting_AaaCantingEditApp',
                meta: {title: '餐廳就餐費用結算修改'}
            },
            {
                path: 'AaaCantingAuditApp',
                component: () => import('@/views/caaesign/aaaCanting/app/AaaCantingAuditApp'),
                name: 'caaesign_aaaCanting_AaaCantingAuditApp',
                meta: { title: '餐廳就餐費用結算审核' }
            },
            {
                path: 'AaaCantingDetailApp',
                component:() => import('@/views/caaesign/aaaCanting/app/AaaCantingDetailApp'),
                name:'caaesign_aaaCanting_AaaCantingDetailApp',
                meta:{title: '餐廳就餐費用結算详情'}
            },
            {
                path: 'AaaCantingRejectApp',
                    component:() => import('@/views/caaesign/aaaCanting/app/AaaCantingRejectApp'),
                name:'caaesign_aaaCanting_AaaCantingRejectApp',
                meta:{title: '餐廳就餐費用結算修改'}
            }
        ]
    }
]
