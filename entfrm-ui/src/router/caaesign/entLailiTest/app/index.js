export default [
    {
        path: '/caaesign/entLailiTestApp',
        component: () => import('@/mobileLayout'),
        hidden: true,
        meta: {title: '來麗測試'},
        children: [
            {
                path: 'EntLailiTestAddApp',
                component: () => import('@/views/caaesign/entLailiTest/app/EntLailiTestAddApp'),
                name: 'caaesign_entLailiTest_EntLailiTestAddApp',
                meta: {title: '來麗測試新增'}
            },
            {
                path: 'EntLailiTestEditApp',
                    component: () => import('@/views/caaesign/entLailiTest/app/EntLailiTestAddApp'),
                name: 'caaesign_entLailiTest_EntLailiTestEditApp',
                meta: {title: '來麗測試修改'}
            },
            {
                path: 'EntLailiTestAuditApp',
                component: () => import('@/views/caaesign/entLailiTest/app/EntLailiTestAuditApp'),
                name: 'caaesign_entLailiTest_EntLailiTestAuditApp',
                meta: { title: '來麗測試审核' }
            },
            {
                path: 'EntLailiTestDetailApp',
                component:() => import('@/views/caaesign/entLailiTest/app/EntLailiTestDetailApp'),
                name:'caaesign_entLailiTest_EntLailiTestDetailApp',
                meta:{title: '來麗測試详情'}
            },
            {
                path: 'EntLailiTestRejectApp',
                    component:() => import('@/views/caaesign/entLailiTest/app/EntLailiTestRejectApp'),
                name:'caaesign_entLailiTest_EntLailiTestRejectApp',
                meta:{title: '來麗測試修改'}
            }
        ]
    }
]
