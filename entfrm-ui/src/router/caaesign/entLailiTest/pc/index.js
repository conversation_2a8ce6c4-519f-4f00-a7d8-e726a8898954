export default [
    {
        path: '/caaesign/entLailiTest',
        component: () => import('@/layout'),
        hidden: true,
        meta: {title: '來麗測試'},
        children: [
            {
                path: 'EntLailiTestAdd',
                component: () => import('@/views/caaesign/entLailiTest/pc/EntLailiTestAdd'),
                name: 'caaesign_entLailiTest_EntLailiTestAdd',
                meta: {title: '來麗測試新增'}
            },
            {
                path: 'EntLailiTestEdit',
                    component: () => import('@/views/caaesign/entLailiTest/pc/EntLailiTestAdd'),
                name: 'caaesign_entLailiTest_EntLailiTestEdit',
                meta: {title: '來麗測試修改'}
            },
            {
                path: 'EntLailiTestAudit',
                component: () => import('@/views/caaesign/entLailiTest/pc/EntLailiTestAudit'),
                name: 'caaesign_entLailiTest_EntLailiTestAudit',
                meta: { title: '來麗測試审核' }
            },
            {
                path: 'EntLailiTestDetail',
                component:() => import('@/views/caaesign/entLailiTest/pc/EntLailiTestDetail'),
                name:'caaesign_entLailiTest_EntLailiTestDetail',
                meta:{title: '來麗測試详情'}
            },
            {
                path: 'EntLailiTestReject',
                    component:() => import('@/views/caaesign/entLailiTest/pc/EntLailiTestReject'),
                name:'caaesign_entLailiTest_EntLailiTestReject',
                meta:{title: '來麗測試修改'}
            }
        ]
    }
]
