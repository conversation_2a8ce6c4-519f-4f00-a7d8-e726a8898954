export default [
    {
        path: '/caaesign/entGeneralVyborgApp',
        component: () => import('@/mobileLayout'),
        hidden: true,
        meta: {title: '總務維保維修申請單'},
        children: [
            {
                path: 'EntGeneralVyborgAddApp',
                component: () => import('@/views/caaesign/entGeneralVyborg/app/EntGeneralVyborgAddApp'),
                name: 'caaesign_entGeneralVyborg_EntGeneralVyborgAddApp',
                meta: {title: '總務維保維修申請單新增'}
            },
            {
                path: 'EntGeneralVyborgEditApp',
                    component: () => import('@/views/caaesign/entGeneralVyborg/app/EntGeneralVyborgAddApp'),
                name: 'caaesign_entGeneralVyborg_EntGeneralVyborgEditApp',
                meta: {title: '總務維保維修申請單修改'}
            },
            {
                path: 'EntGeneralVyborgAuditApp',
                component: () => import('@/views/caaesign/entGeneralVyborg/app/EntGeneralVyborgAuditApp'),
                name: 'caaesign_entGeneralVyborg_EntGeneralVyborgAuditApp',
                meta: { title: '總務維保維修申請單审核' }
            },
            {
                path: 'EntGeneralVyborgDetailApp',
                component:() => import('@/views/caaesign/entGeneralVyborg/app/EntGeneralVyborgDetailApp'),
                name:'caaesign_entGeneralVyborg_EntGeneralVyborgDetailApp',
                meta:{title: '總務維保維修申請單详情'}
            },
            {
                path: 'EntGeneralVyborgRejectApp',
                    component:() => import('@/views/caaesign/entGeneralVyborg/app/EntGeneralVyborgRejectApp'),
                name:'caaesign_entGeneralVyborg_EntGeneralVyborgRejectApp',
                meta:{title: '總務維保維修申請單修改'}
            }
        ]
    }
]
