export default [
    {
        path: '/caaesign/entGeneralVyborg',
        component: () => import('@/layout'),
        hidden: true,
        meta: {title: '總務維保維修申請單'},
        children: [
            {
                path: 'EntGeneralVyborgAdd',
                component: () => import('@/views/caaesign/entGeneralVyborg/pc/EntGeneralVyborgAdd'),
                name: 'caaesign_entGeneralVyborg_EntGeneralVyborgAdd',
                meta: {title: '總務維保維修申請單新增'}
            },
            {
                path: 'EntGeneralVyborgEdit',
                    component: () => import('@/views/caaesign/entGeneralVyborg/pc/EntGeneralVyborgAdd'),
                name: 'caaesign_entGeneralVyborg_EntGeneralVyborgEdit',
                meta: {title: '總務維保維修申請單修改'}
            },
            {
                path: 'EntGeneralVyborgAudit',
                component: () => import('@/views/caaesign/entGeneralVyborg/pc/EntGeneralVyborgAudit'),
                name: 'caaesign_entGeneralVyborg_EntGeneralVyborgAudit',
                meta: { title: '總務維保維修申請單审核' }
            },
            {
                path: 'EntGeneralVyborgDetail',
                component:() => import('@/views/caaesign/entGeneralVyborg/pc/EntGeneralVyborgDetail'),
                name:'caaesign_entGeneralVyborg_EntGeneralVyborgDetail',
                meta:{title: '總務維保維修申請單详情'}
            },
            {
                path: 'EntGeneralVyborgReject',
                    component:() => import('@/views/caaesign/entGeneralVyborg/pc/EntGeneralVyborgReject'),
                name:'caaesign_entGeneralVyborg_EntGeneralVyborgReject',
                meta:{title: '總務維保維修申請單修改'}
            }
        ]
    }
]
