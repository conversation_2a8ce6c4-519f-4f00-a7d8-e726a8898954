export default [
    {
        path: '/caaesign/entRecruitSalaryApp',
        component: () => import('@/mobileLayout'),
        hidden: true,
        meta: {title: 'iPEG試用期滿薪資異動匯總審批表'},
        children: [
            {
                path: 'EntRecruitSalaryAddApp',
                component: () => import('@/views/caaesign/entRecruitSalary/app/EntRecruitSalaryAddApp'),
                name: 'caaesign_entRecruitSalary_EntRecruitSalaryAddApp',
                meta: {title: 'iPEG試用期滿薪資異動匯總審批表新增'}
            },
            {
                path: 'EntRecruitSalaryEditApp',
                    component: () => import('@/views/caaesign/entRecruitSalary/app/EntRecruitSalaryAddApp'),
                name: 'caaesign_entRecruitSalary_EntRecruitSalaryEditApp',
                meta: {title: 'iPEG試用期滿薪資異動匯總審批表修改'}
            },
            {
                path: 'EntRecruitSalaryAuditApp',
                component: () => import('@/views/caaesign/entRecruitSalary/app/EntRecruitSalaryAuditApp'),
                name: 'caaesign_entRecruitSalary_EntRecruitSalaryAuditApp',
                meta: { title: 'iPEG試用期滿薪資異動匯總審批表审核' }
            },
            {
                path: 'EntRecruitSalaryDetailApp',
                component:() => import('@/views/caaesign/entRecruitSalary/app/EntRecruitSalaryDetailApp'),
                name:'caaesign_entRecruitSalary_EntRecruitSalaryDetailApp',
                meta:{title: 'iPEG試用期滿薪資異動匯總審批表详情'}
            },
            {
                path: 'EntRecruitSalaryRejectApp',
                    component:() => import('@/views/caaesign/entRecruitSalary/app/EntRecruitSalaryRejectApp'),
                name:'caaesign_entRecruitSalary_EntRecruitSalaryRejectApp',
                meta:{title: 'iPEG試用期滿薪資異動匯總審批表修改'}
            }
        ]
    }
]
