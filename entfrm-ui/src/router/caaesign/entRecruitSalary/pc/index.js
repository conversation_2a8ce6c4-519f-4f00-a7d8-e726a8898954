export default [
    {
        path: '/caaesign/entRecruitSalary',
        component: () => import('@/layout'),
        hidden: true,
        meta: {title: 'iPEG試用期滿薪資異動匯總審批表'},
        children: [
            {
                path: 'EntRecruitSalaryAdd',
                component: () => import('@/views/caaesign/entRecruitSalary/pc/EntRecruitSalaryAdd'),
                name: 'caaesign_entRecruitSalary_EntRecruitSalaryAdd',
                meta: {title: 'iPEG試用期滿薪資異動匯總審批表新增'}
            },
            {
                path: 'EntRecruitSalaryEdit',
                    component: () => import('@/views/caaesign/entRecruitSalary/pc/EntRecruitSalaryAdd'),
                name: 'caaesign_entRecruitSalary_EntRecruitSalaryEdit',
                meta: {title: 'iPEG試用期滿薪資異動匯總審批表修改'}
            },
            {
                path: 'EntRecruitSalaryAudit',
                component: () => import('@/views/caaesign/entRecruitSalary/pc/EntRecruitSalaryAudit'),
                name: 'caaesign_entRecruitSalary_EntRecruitSalaryAudit',
                meta: { title: 'iPEG試用期滿薪資異動匯總審批表审核' }
            },
            {
                path: 'EntRecruitSalaryDetail',
                component:() => import('@/views/caaesign/entRecruitSalary/pc/EntRecruitSalaryDetail'),
                name:'caaesign_entRecruitSalary_EntRecruitSalaryDetail',
                meta:{title: 'iPEG試用期滿薪資異動匯總審批表详情'}
            },
            {
                path: 'EntRecruitSalaryReject',
                    component:() => import('@/views/caaesign/entRecruitSalary/pc/EntRecruitSalaryReject'),
                name:'caaesign_entRecruitSalary_EntRecruitSalaryReject',
                meta:{title: 'iPEG試用期滿薪資異動匯總審批表修改'}
            }
        ]
    }
]
