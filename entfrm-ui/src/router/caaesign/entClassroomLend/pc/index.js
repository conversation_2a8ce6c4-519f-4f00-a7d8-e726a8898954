export default [
    {
        path: '/caaesign/entClassroomLend',
        component: () => import('@/layout'),
        hidden: true,
        meta: {title: '教育訓練室&視訊會議室借用申請'},
        children: [
            {
                path: 'EntClassroomLendAdd',
                component: () => import('@/views/caaesign/entClassroomLend/pc/EntClassroomLendAdd'),
                name: 'caaesign_entClassroomLend_EntClassroomLendAdd',
                meta: {title: '教育訓練室&視訊會議室借用申請新增'}
            },
            {
                path: 'EntClassroomLendEdit',
                    component: () => import('@/views/caaesign/entClassroomLend/pc/EntClassroomLendAdd'),
                name: 'caaesign_entClassroomLend_EntClassroomLendEdit',
                meta: {title: '教育訓練室&視訊會議室借用申請修改'}
            },
            {
                path: 'EntClassroomLendAudit',
                component: () => import('@/views/caaesign/entClassroomLend/pc/EntClassroomLendAudit'),
                name: 'caaesign_entClassroomLend_EntClassroomLendAudit',
                meta: { title: '教育訓練室&視訊會議室借用申請审核' }
            },
            {
                path: 'EntClassroomLendDetail',
                component:() => import('@/views/caaesign/entClassroomLend/pc/EntClassroomLendDetail'),
                name:'caaesign_entClassroomLend_EntClassroomLendDetail',
                meta:{title: '教育訓練室&視訊會議室借用申請详情'}
            },
            {
                path: 'EntClassroomLendReject',
                    component:() => import('@/views/caaesign/entClassroomLend/pc/EntClassroomLendReject'),
                name:'caaesign_entClassroomLend_EntClassroomLendReject',
                meta:{title: '教育訓練室&視訊會議室借用申請修改'}
            }
        ]
    }
]
