export default [
    {
        path: '/caaesign/entClassroomLendApp',
        component: () => import('@/mobileLayout'),
        hidden: true,
        meta: {title: '教育訓練室&視訊會議室借用申請'},
        children: [
            {
                path: 'EntClassroomLendAddApp',
                component: () => import('@/views/caaesign/entClassroomLend/app/EntClassroomLendAddApp'),
                name: 'caaesign_entClassroomLend_EntClassroomLendAddApp',
                meta: {title: '教育訓練室&視訊會議室借用申請新增'}
            },
            {
                path: 'EntClassroomLendEditApp',
                    component: () => import('@/views/caaesign/entClassroomLend/app/EntClassroomLendAddApp'),
                name: 'caaesign_entClassroomLend_EntClassroomLendEditApp',
                meta: {title: '教育訓練室&視訊會議室借用申請修改'}
            },
            {
                path: 'EntClassroomLendAuditApp',
                component: () => import('@/views/caaesign/entClassroomLend/app/EntClassroomLendAuditApp'),
                name: 'caaesign_entClassroomLend_EntClassroomLendAuditApp',
                meta: { title: '教育訓練室&視訊會議室借用申請审核' }
            },
            {
                path: 'EntClassroomLendDetailApp',
                component:() => import('@/views/caaesign/entClassroomLend/app/EntClassroomLendDetailApp'),
                name:'caaesign_entClassroomLend_EntClassroomLendDetailApp',
                meta:{title: '教育訓練室&視訊會議室借用申請详情'}
            },
            {
                path: 'EntClassroomLendRejectApp',
                    component:() => import('@/views/caaesign/entClassroomLend/app/EntClassroomLendRejectApp'),
                name:'caaesign_entClassroomLend_EntClassroomLendRejectApp',
                meta:{title: '教育訓練室&視訊會議室借用申請修改'}
            }
        ]
    }
]
