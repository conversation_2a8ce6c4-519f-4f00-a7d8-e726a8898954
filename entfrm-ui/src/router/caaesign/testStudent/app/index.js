export default [
    {
        path: '/caaesign/testStudentApp',
        component: () => import('@/mobileLayout'),
        hidden: true,
        meta: {title: '測試_學生信息'},
        children: [
            {
                path: 'TestStudentAddApp',
                component: () => import('@/views/caaesign/testStudent/app/TestStudentAddApp'),
                name: 'caaesign_testStudent_TestStudentAddApp',
                meta: {title: '測試_學生信息新增'}
            },
            {
                path: 'TestStudentEditApp',
                    component: () => import('@/views/caaesign/testStudent/app/TestStudentAddApp'),
                name: 'caaesign_testStudent_TestStudentEditApp',
                meta: {title: '測試_學生信息修改'}
            },
            {
                path: 'TestStudentAuditApp',
                component: () => import('@/views/caaesign/testStudent/app/TestStudentAuditApp'),
                name: 'caaesign_testStudent_TestStudentAuditApp',
                meta: { title: '測試_學生信息审核' }
            },
            {
                path: 'TestStudentDetailApp',
                component:() => import('@/views/caaesign/testStudent/app/TestStudentDetailApp'),
                name:'caaesign_testStudent_TestStudentDetailApp',
                meta:{title: '測試_學生信息详情'}
            },
            {
                path: 'TestStudentRejectApp',
                    component:() => import('@/views/caaesign/testStudent/app/TestStudentRejectApp'),
                name:'caaesign_testStudent_TestStudentRejectApp',
                meta:{title: '測試_學生信息修改'}
            }
        ]
    }
]
