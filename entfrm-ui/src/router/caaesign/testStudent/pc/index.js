export default [
    {
        path: '/caaesign/testStudent',
        component: () => import('@/layout'),
        hidden: true,
        meta: {title: '測試_學生信息'},
        children: [
            {
                path: 'TestStudentAdd',
                component: () => import('@/views/caaesign/testStudent/pc/TestStudentAdd'),
                name: 'caaesign_testStudent_TestStudentAdd',
                meta: {title: '測試_學生信息新增'}
            },
            {
                path: 'TestStudentEdit',
                    component: () => import('@/views/caaesign/testStudent/pc/TestStudentAdd'),
                name: 'caaesign_testStudent_TestStudentEdit',
                meta: {title: '測試_學生信息修改'}
            },
            {
                path: 'TestStudentAudit',
                component: () => import('@/views/caaesign/testStudent/pc/TestStudentAudit'),
                name: 'caaesign_testStudent_TestStudentAudit',
                meta: { title: '測試_學生信息审核' }
            },
            {
                path: 'TestStudentDetail',
                component:() => import('@/views/caaesign/testStudent/pc/TestStudentDetail'),
                name:'caaesign_testStudent_TestStudentDetail',
                meta:{title: '測試_學生信息详情'}
            },
            {
                path: 'TestStudentReject',
                    component:() => import('@/views/caaesign/testStudent/pc/TestStudentReject'),
                name:'caaesign_testStudent_TestStudentReject',
                meta:{title: '測試_學生信息修改'}
            }
        ]
    }
]
