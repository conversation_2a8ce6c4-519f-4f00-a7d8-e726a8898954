export default [
    {
        path: '/caaesign/entGroupLiaison',
        component: () => import('@/layout'),
        hidden: true,
        meta: {title: 'iPEBG事業群聯絡單'},
        children: [
            {
                path: 'EntGroupLiaisonAdd',
                component: () => import('@/views/caaesign/entGroupLiaison/pc/EntGroupLiaisonAdd'),
                name: 'caaesign_entGroupLiaison_EntGroupLiaisonAdd',
                meta: {title: 'iPEBG事業群聯絡單新增'}
            },
            {
                path: 'EntGroupLiaisonEdit',
                    component: () => import('@/views/caaesign/entGroupLiaison/pc/EntGroupLiaisonAdd'),
                name: 'caaesign_entGroupLiaison_EntGroupLiaisonEdit',
                meta: {title: 'iPEBG事業群聯絡單修改'}
            },
            {
                path: 'EntGroupLiaisonAudit',
                component: () => import('@/views/caaesign/entGroupLiaison/pc/EntGroupLiaisonAudit'),
                name: 'caaesign_entGroupLiaison_EntGroupLiaisonAudit',
                meta: { title: 'iPEBG事業群聯絡單审核' }
            },
            {
                path: 'EntGroupLiaisonDetail',
                component:() => import('@/views/caaesign/entGroupLiaison/pc/EntGroupLiaisonDetail'),
                name:'caaesign_entGroupLiaison_EntGroupLiaisonDetail',
                meta:{title: 'iPEBG事業群聯絡單详情'}
            },,
          {
            path: 'EntGroupLiaisonPrint',
            component:() => import('@/views/caaesign/entGroupLiaison/pc/EntGroupLiaisonPrint'),
            name:'caaesign_entGroupLiaison_EntGroupLiaisonPrint',
            meta:{title: 'iPEBG事業群聯絡單列印'}
          },
            {
                path: 'EntGroupLiaisonReject',
                    component:() => import('@/views/caaesign/entGroupLiaison/pc/EntGroupLiaisonReject'),
                name:'caaesign_entGroupLiaison_EntGroupLiaisonReject',
                meta:{title: 'iPEBG事業群聯絡單修改'}
            }
        ]
    }
]
