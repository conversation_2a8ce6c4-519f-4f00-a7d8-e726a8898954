export default [
    {
        path: '/caaesign/entGroupLiaisonApp',
        component: () => import('@/mobileLayout'),
        hidden: true,
        meta: {title: 'iPEBG事業群聯絡單'},
        children: [
            {
                path: 'EntGroupLiaisonAddApp',
                component: () => import('@/views/caaesign/entGroupLiaison/app/EntGroupLiaisonAddApp'),
                name: 'caaesign_entGroupLiaison_EntGroupLiaisonAddApp',
                meta: {title: 'iPEBG事業群聯絡單新增'}
            },
            {
                path: 'EntGroupLiaisonEditApp',
                    component: () => import('@/views/caaesign/entGroupLiaison/app/EntGroupLiaisonAddApp'),
                name: 'caaesign_entGroupLiaison_EntGroupLiaisonEditApp',
                meta: {title: 'iPEBG事業群聯絡單修改'}
            },
            {
                path: 'EntGroupLiaisonAuditApp',
                component: () => import('@/views/caaesign/entGroupLiaison/app/EntGroupLiaisonAuditApp'),
                name: 'caaesign_entGroupLiaison_EntGroupLiaisonAuditApp',
                meta: { title: 'iPEBG事業群聯絡單审核' }
            },
            {
                path: 'EntGroupLiaisonDetailApp',
                component:() => import('@/views/caaesign/entGroupLiaison/app/EntGroupLiaisonDetailApp'),
                name:'caaesign_entGroupLiaison_EntGroupLiaisonDetailApp',
                meta:{title: 'iPEBG事業群聯絡單详情'}
            },
            {
                path: 'EntGroupLiaisonRejectApp',
                    component:() => import('@/views/caaesign/entGroupLiaison/app/EntGroupLiaisonRejectApp'),
                name:'caaesign_entGroupLiaison_EntGroupLiaisonRejectApp',
                meta:{title: 'iPEBG事業群聯絡單修改'}
            }
        ]
    }
]
