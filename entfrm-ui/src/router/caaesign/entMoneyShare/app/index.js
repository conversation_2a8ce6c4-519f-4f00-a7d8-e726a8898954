export default [
    {
        path: '/caaesign/entMoneyShareApp',
        component: () => import('@/mobileLayout'),
        hidden: true,
        meta: {title: '费用分摊表'},
        children: [
            {
                path: 'EntMoneyShareAddApp',
                component: () => import('@/views/caaesign/entMoneyShare/app/EntMoneyShareAddApp'),
                name: 'caaesign_entMoneyShare_EntMoneyShareAddApp',
                meta: {title: '费用分摊表新增'}
            },
            {
                path: 'EntMoneyShareEditApp',
                    component: () => import('@/views/caaesign/entMoneyShare/app/EntMoneyShareAddApp'),
                name: 'caaesign_entMoneyShare_EntMoneyShareEditApp',
                meta: {title: '费用分摊表修改'}
            },
            {
                path: 'EntMoneyShareAuditApp',
                component: () => import('@/views/caaesign/entMoneyShare/app/EntMoneyShareAuditApp'),
                name: 'caaesign_entMoneyShare_EntMoneyShareAuditApp',
                meta: { title: '费用分摊表审核' }
            },
            {
                path: 'EntMoneyShareDetailApp',
                component:() => import('@/views/caaesign/entMoneyShare/app/EntMoneyShareDetailApp'),
                name:'caaesign_entMoneyShare_EntMoneyShareDetailApp',
                meta:{title: '费用分摊表详情'}
            },
            {
                path: 'EntMoneyShareRejectApp',
                    component:() => import('@/views/caaesign/entMoneyShare/app/EntMoneyShareRejectApp'),
                name:'caaesign_entMoneyShare_EntMoneyShareRejectApp',
                meta:{title: '费用分摊表修改'}
            }
        ]
    }
]
