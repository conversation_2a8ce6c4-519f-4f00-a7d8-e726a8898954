export default [
    {
        path: '/caaesign/entMoneyShare',
        component: () => import('@/layout'),
        hidden: true,
        meta: {title: '费用分摊表'},
        children: [
            {
                path: 'EntMoneyShareAdd',
                component: () => import('@/views/caaesign/entMoneyShare/pc/EntMoneyShareAdd'),
                name: 'caaesign_entMoneyShare_EntMoneyShareAdd',
                meta: {title: '费用分摊表新增'}
            },
            {
                path: 'EntMoneyShareEdit',
                    component: () => import('@/views/caaesign/entMoneyShare/pc/EntMoneyShareAdd'),
                name: 'caaesign_entMoneyShare_EntMoneyShareEdit',
                meta: {title: '费用分摊表修改'}
            },
            {
                path: 'EntMoneyShareAudit',
                component: () => import('@/views/caaesign/entMoneyShare/pc/EntMoneyShareAudit'),
                name: 'caaesign_entMoneyShare_EntMoneyShareAudit',
                meta: { title: '费用分摊表审核' }
            },
            {
                path: 'EntMoneyShareDetail',
                component:() => import('@/views/caaesign/entMoneyShare/pc/EntMoneyShareDetail'),
                name:'caaesign_entMoneyShare_EntMoneyShareDetail',
                meta:{title: '费用分摊表详情'}
            },
            {
                path: 'EntMoneyShareReject',
                    component:() => import('@/views/caaesign/entMoneyShare/pc/EntMoneyShareReject'),
                name:'caaesign_entMoneyShare_EntMoneyShareReject',
                meta:{title: '费用分摊表修改'}
            }
        ]
    }
]
