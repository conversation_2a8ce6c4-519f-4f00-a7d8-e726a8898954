export default [
  {
    path: '/caaesign/wfonlineprocessApp',
    component: () => import('@/mobileLayout'),
    hidden: true,
    meta: { title: '功能程式發佈申請單申請單' },
    children: [
      {
        path: 'wfonlineprocessAllCountApp',
        component: () => import('@/views/caaesign/wfonlineprocess/app/allCount'),
        name: 'caaesign_wfonlineprocess_wfonlineprocessAllCountApp',
        meta: { title: '功能程式發佈申請單總數' }
      },
      {
        path: 'wfonlineprocessTaskListApp',
        component: () => import('@/views/caaesign/wfonlineprocess/app/wfonlineprocessTaskListApp'),
        name: 'caaesign_wfonlineprocess_wfonlineprocessTaskListApp',
        meta: { title: '功能程式發佈申請單任務列表' }
      },
      {
        path: 'wfonlineprocessAuditAppH5',
        component: () => import('@/views/caaesign/wfonlineprocess/app/wfonlineprocessAuditAppH5'),
        name: 'caaesign_wfonlineprocess_wfonlineprocessAuditAppH5',
        meta: { title: '功能程式發佈申請單申請單审核' }
      },
      {
        path: 'wfonlineprocessAuditApp',
        component: () => import('@/views/caaesign/wfonlineprocess/app/wfonlineprocessAuditApp'),
        name: 'caaesign_wfonlineprocess_wfonlineprocessAuditApp',
        meta: { title: '功能程式發佈申請單申請單审核' }
      },
      {
        path: 'wfonlineprocessDetailApp',
        component: () => import('@/views/caaesign/wfonlineprocess/app/wfonlineprocessDetailApp'),
        name: 'caaesign_wfonlineprocess_wfonlineprocessDetailApp',
        meta: { title: '功能程式發佈申請單申請單詳情' }
      },
      {
        path: '404App',
        component: () => import('@/views/caaesign/wfonlineprocess/app/404App'),
        name: 'caaesign_wfonlineprocess_404App',
        meta: { title: '功能程式發佈申請單申請單404頁面' }
      },
    ]
  },
  {
    //愛口袋登錄
    path: '/webLogin',
    component: () => import('@/views/caaesign/wfonlineprocess/app/allCount'),
  }
]
