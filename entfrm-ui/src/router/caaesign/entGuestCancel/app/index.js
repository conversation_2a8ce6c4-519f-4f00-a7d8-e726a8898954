export default [
    {
        path: '/caaesign/entGuestCancelApp',
        component: () => import('@/mobileLayout'),
        hidden: true,
        meta: {title: 'iPEBG移轉設備銷賬聯絡單'},
        children: [
            {
                path: 'EntGuestCancelAddApp',
                component: () => import('@/views/caaesign/entGuestCancel/app/EntGuestCancelAddApp'),
                name: 'caaesign_entGuestCancel_EntGuestCancelAddApp',
                meta: {title: 'iPEBG移轉設備銷賬聯絡單新增'}
            },
            {
                path: 'EntGuestCancelEditApp',
                    component: () => import('@/views/caaesign/entGuestCancel/app/EntGuestCancelAddApp'),
                name: 'caaesign_entGuestCancel_EntGuestCancelEditApp',
                meta: {title: 'iPEBG移轉設備銷賬聯絡單修改'}
            },
            {
                path: 'EntGuestCancelAuditApp',
                component: () => import('@/views/caaesign/entGuestCancel/app/EntGuestCancelAuditApp'),
                name: 'caaesign_entGuestCancel_EntGuestCancelAuditApp',
                meta: { title: 'iPEBG移轉設備銷賬聯絡單审核' }
            },
            {
                path: 'EntGuestCancelDetailApp',
                component:() => import('@/views/caaesign/entGuestCancel/app/EntGuestCancelDetailApp'),
                name:'caaesign_entGuestCancel_EntGuestCancelDetailApp',
                meta:{title: 'iPEBG移轉設備銷賬聯絡單详情'}
            },
            {
                path: 'EntGuestCancelRejectApp',
                    component:() => import('@/views/caaesign/entGuestCancel/app/EntGuestCancelRejectApp'),
                name:'caaesign_entGuestCancel_EntGuestCancelRejectApp',
                meta:{title: 'iPEBG移轉設備銷賬聯絡單修改'}
            }
        ]
    }
]
