export default [
  {
    path: '/caaesign/wfspecialnetprocessApp',
    component: () => import('@/mobileLayout'),
    hidden: true,
    meta: { title: '特殊網絡需求申請單' },
    children: [
      {
        path: 'wfspecialnetprocessAuditApp',
        component: () => import('@/views/caaesign/wfspecialnetprocess/app/wfspecialnetprocessAuditApp'),
        name: 'caaesign_wfspecialnetprocess_wfspecialnetprocessAuditApp',
        meta: { title: '特殊網絡需求申請單审核' }
      },
      {
        path: 'wfspecialnetprocessDetailApp',
        component: () => import('@/views/caaesign/wfspecialnetprocess/app/wfspecialnetprocessDetailApp'),
        name: 'caaesign_wfspecialnetprocess_wfspecialnetprocessDetailApp',
        meta: { title: '特殊網絡需求申請單詳情' }
      },
      {
        path: '404App',
        component: () => import('@/views/caaesign/wfonlineprocess/app/404App'),
        name: 'caaesign_wfonlineprocess_404App',
        meta: { title: '特殊網絡需求申請單404頁面' }
      },
    ]
  },
]
