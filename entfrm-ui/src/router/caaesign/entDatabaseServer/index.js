export default [
    {
        path: '/caaesign/entDatabaseServer',
        component: () => import('@/layout'),
        hidden: true,
        meta: {title: '數據庫服務申請單'},
        children: [
            {
                path: 'EntDatabaseServerAdd',
                component: () => import('@/views/caaesign/entDatabaseServer/EntDatabaseServerAdd'),
                name: 'caaesign_entDatabaseServer_EntDatabaseServerAdd',
                meta: {title: '數據庫服務申請單新增'}
            },
            {
                path: 'EntDatabaseServerEdit',
                    component: () => import('@/views/caaesign/entDatabaseServer/EntDatabaseServerAdd'),
                name: 'caaesign_entDatabaseServer_EntDatabaseServerEdit',
                meta: {title: '數據庫服務申請單修改'}
            },
            {
                path: 'EntDatabaseServerAudit',
                component: () => import('@/views/caaesign/entDatabaseServer/EntDatabaseServerAudit'),
                name: 'caaesign_entDatabaseServer_EntDatabaseServerAudit',
                meta: { title: '數據庫服務申請單审核' }
            },
            {
                path: 'EntDatabaseServerDetail',
                component:() => import('@/views/caaesign/entDatabaseServer/EntDatabaseServerDetail'),
                name:'caaesign_entDatabaseServer_EntDatabaseServerDetail',
                meta:{title: '數據庫服務申請單详情'}
            },
            {
                path: 'EntDatabaseServerReject',
                    component:() => import('@/views/caaesign/entDatabaseServer/EntDatabaseServerReject'),
                name:'caaesign_entDatabaseServer_EntDatabaseServerReject',
                meta:{title: '數據庫服務申請單修改'}
            }
        ]
    }
]
