export default [
    {
        path: '/caaesign/drsVipApply',
        component: () => import('@/layout'),
        hidden: true,
        meta: {title: 'VIP就餐申請'},
        children: [
            {
                path: 'DrsVipApplyAdd',
                component: () => import('@/views/caaesign/drsVipApply/pc/DrsVipApplyAdd'),
                name: 'caaesign_drsVipApply_DrsVipApplyAdd',
                meta: {title: 'VIP就餐申請新增'}
            },
            {
                path: 'DrsVipApplyEdit',
                    component: () => import('@/views/caaesign/drsVipApply/pc/DrsVipApplyAdd'),
                name: 'caaesign_drsVipApply_DrsVipApplyEdit',
                meta: {title: 'VIP就餐申請修改'}
            },
            {
                path: 'DrsVipApplyAudit',
                component: () => import('@/views/caaesign/drsVipApply/pc/DrsVipApplyAudit'),
                name: 'caaesign_drsVipApply_DrsVipApplyAudit',
                meta: { title: 'VIP就餐申請审核' }
            },
            {
                path: 'DrsVipApplyDetail',
                component:() => import('@/views/caaesign/drsVipApply/pc/DrsVipApplyDetail'),
                name:'caaesign_drsVipApply_DrsVipApplyDetail',
                meta:{title: 'VIP就餐申請详情'}
            },
            {
                path: 'DrsVipApplyReject',
                    component:() => import('@/views/caaesign/drsVipApply/pc/DrsVipApplyReject'),
                name:'caaesign_drsVipApply_DrsVipApplyReject',
                meta:{title: 'VIP就餐申請修改'}
            }
        ]
    }
]
