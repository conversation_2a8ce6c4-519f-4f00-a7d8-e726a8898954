export default [
    {
        path: '/caaesign/drsVipApplyApp',
        component: () => import('@/mobileLayout'),
        hidden: true,
        meta: {title: 'VIP就餐申請'},
        children: [
            {
                path: 'DrsVipApplyAddApp',
                component: () => import('@/views/caaesign/drsVipApply/app/DrsVipApplyAddApp'),
                name: 'caaesign_drsVipApply_DrsVipApplyAddApp',
                meta: {title: 'VIP就餐申請新增'}
            },
            {
                path: 'DrsVipApplyEditApp',
                    component: () => import('@/views/caaesign/drsVipApply/app/DrsVipApplyAddApp'),
                name: 'caaesign_drsVipApply_DrsVipApplyEditApp',
                meta: {title: 'VIP就餐申請修改'}
            },
            {
                path: 'DrsVipApplyAuditApp',
                component: () => import('@/views/caaesign/drsVipApply/app/DrsVipApplyAuditApp'),
                name: 'caaesign_drsVipApply_DrsVipApplyAuditApp',
                meta: { title: 'VIP就餐申請审核' }
            },
            {
                path: 'DrsVipApplyDetailApp',
                component:() => import('@/views/caaesign/drsVipApply/app/DrsVipApplyDetailApp'),
                name:'caaesign_drsVipApply_DrsVipApplyDetailApp',
                meta:{title: 'VIP就餐申請详情'}
            },
            {
                path: 'DrsVipApplyRejectApp',
                    component:() => import('@/views/caaesign/drsVipApply/app/DrsVipApplyRejectApp'),
                name:'caaesign_drsVipApply_DrsVipApplyRejectApp',
                meta:{title: 'VIP就餐申請修改'}
            }
        ]
    }
]
