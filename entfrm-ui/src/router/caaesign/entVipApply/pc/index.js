export default [
    {
        path: '/caaesign/entVipApply',
        component: () => import('@/layout'),
        hidden: true,
        meta: {title: 'VIP就餐申請單'},
        children: [
            {
                path: 'EntVipApplyAdd',
                component: () => import('@/views/caaesign/entVipApply/pc/EntVipApplyAdd'),
                name: 'caaesign_entVipApply_EntVipApplyAdd',
                meta: {title: 'VIP就餐申請單新增'}
            },
            {
                path: 'EntVipApplyEdit',
                    component: () => import('@/views/caaesign/entVipApply/pc/EntVipApplyAdd'),
                name: 'caaesign_entVipApply_EntVipApplyEdit',
                meta: {title: 'VIP就餐申請單修改'}
            },
            {
                path: 'EntVipApplyAudit',
                component: () => import('@/views/caaesign/entVipApply/pc/EntVipApplyAudit'),
                name: 'caaesign_entVipApply_EntVipApplyAudit',
                meta: { title: 'VIP就餐申請單审核' }
            },
            {
                path: 'EntVipApplyDetail',
                component:() => import('@/views/caaesign/entVipApply/pc/EntVipApplyDetail'),
                name:'caaesign_entVipApply_EntVipApplyDetail',
                meta:{title: 'VIP就餐申請單详情'}
            },
            {
                path: 'EntVipApplyReject',
                    component:() => import('@/views/caaesign/entVipApply/pc/EntVipApplyReject'),
                name:'caaesign_entVipApply_EntVipApplyReject',
                meta:{title: 'VIP就餐申請單修改'}
            }
        ]
    }
]
