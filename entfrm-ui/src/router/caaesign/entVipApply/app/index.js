export default [
    {
        path: '/caaesign/entVipApplyApp',
        component: () => import('@/mobileLayout'),
        hidden: true,
        meta: {title: 'VIP就餐申請單'},
        children: [
            {
                path: 'EntVipApplyAddApp',
                component: () => import('@/views/caaesign/entVipApply/app/EntVipApplyAddApp'),
                name: 'caaesign_entVipApply_EntVipApplyAddApp',
                meta: {title: 'VIP就餐申請單新增'}
            },
            {
                path: 'EntVipApplyEditApp',
                    component: () => import('@/views/caaesign/entVipApply/app/EntVipApplyAddApp'),
                name: 'caaesign_entVipApply_EntVipApplyEditApp',
                meta: {title: 'VIP就餐申請單修改'}
            },
            {
                path: 'EntVipApplyAuditApp',
                component: () => import('@/views/caaesign/entVipApply/app/EntVipApplyAuditApp'),
                name: 'caaesign_entVipApply_EntVipApplyAuditApp',
                meta: { title: 'VIP就餐申請單审核' }
            },
            {
                path: 'EntVipApplyDetailApp',
                component:() => import('@/views/caaesign/entVipApply/app/EntVipApplyDetailApp'),
                name:'caaesign_entVipApply_EntVipApplyDetailApp',
                meta:{title: 'VIP就餐申請單详情'}
            },
            {
                path: 'EntVipApplyRejectApp',
                    component:() => import('@/views/caaesign/entVipApply/app/EntVipApplyRejectApp'),
                name:'caaesign_entVipApply_EntVipApplyRejectApp',
                meta:{title: 'VIP就餐申請單修改'}
            }
        ]
    }
]
