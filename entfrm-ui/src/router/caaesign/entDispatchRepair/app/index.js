export default [
    {
        path: '/caaesign/entDispatchRepairApp',
        component: () => import('@/mobileLayout'),
        hidden: true,
        meta: {title: '總務零星修繕派工單'},
        children: [
            {
                path: 'EntDispatchRepairAddApp',
                component: () => import('@/views/caaesign/entDispatchRepair/app/EntDispatchRepairAddApp'),
                name: 'caaesign_entDispatchRepair_EntDispatchRepairAddApp',
                meta: {title: '總務零星修繕派工單新增'}
            },
            {
                path: 'EntDispatchRepairEditApp',
                    component: () => import('@/views/caaesign/entDispatchRepair/app/EntDispatchRepairAddApp'),
                name: 'caaesign_entDispatchRepair_EntDispatchRepairEditApp',
                meta: {title: '總務零星修繕派工單修改'}
            },
            {
                path: 'EntDispatchRepairAuditApp',
                component: () => import('@/views/caaesign/entDispatchRepair/app/EntDispatchRepairAuditApp'),
                name: 'caaesign_entDispatchRepair_EntDispatchRepairAuditApp',
                meta: { title: '總務零星修繕派工單审核' }
            },
            {
                path: 'EntDispatchRepairDetailApp',
                component:() => import('@/views/caaesign/entDispatchRepair/app/EntDispatchRepairDetailApp'),
                name:'caaesign_entDispatchRepair_EntDispatchRepairDetailApp',
                meta:{title: '總務零星修繕派工單详情'}
            },
            {
                path: 'EntDispatchRepairRejectApp',
                    component:() => import('@/views/caaesign/entDispatchRepair/app/EntDispatchRepairRejectApp'),
                name:'caaesign_entDispatchRepair_EntDispatchRepairRejectApp',
                meta:{title: '總務零星修繕派工單修改'}
            }
        ]
    }
]
