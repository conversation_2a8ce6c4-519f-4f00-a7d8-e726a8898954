export default [
    {
        path: '/caaesign/entMachineCheckApp',
        component: () => import('@/mobileLayout'),
        hidden: true,
        meta: {title: '機台資安點檢暨抽檢匯總表'},
        children: [
            {
                path: 'EntMachineCheckAddApp',
                component: () => import('@/views/caaesign/entMachineCheck/app/EntMachineCheckAddApp'),
                name: 'caaesign_entMachineCheck_EntMachineCheckAddApp',
                meta: {title: '機台資安點檢暨抽檢匯總表新增'}
            },
            {
                path: 'EntMachineCheckEditApp',
                    component: () => import('@/views/caaesign/entMachineCheck/app/EntMachineCheckAddApp'),
                name: 'caaesign_entMachineCheck_EntMachineCheckEditApp',
                meta: {title: '機台資安點檢暨抽檢匯總表修改'}
            },
            {
                path: 'EntMachineCheckAuditApp',
                component: () => import('@/views/caaesign/entMachineCheck/app/EntMachineCheckAuditApp'),
                name: 'caaesign_entMachineCheck_EntMachineCheckAuditApp',
                meta: { title: '機台資安點檢暨抽檢匯總表审核' }
            },
            {
                path: 'EntMachineCheckDetailApp',
                component:() => import('@/views/caaesign/entMachineCheck/app/EntMachineCheckDetailApp'),
                name:'caaesign_entMachineCheck_EntMachineCheckDetailApp',
                meta:{title: '機台資安點檢暨抽檢匯總表详情'}
            },
            {
                path: 'EntMachineCheckRejectApp',
                    component:() => import('@/views/caaesign/entMachineCheck/app/EntMachineCheckRejectApp'),
                name:'caaesign_entMachineCheck_EntMachineCheckRejectApp',
                meta:{title: '機台資安點檢暨抽檢匯總表修改'}
            }
        ]
    }
]
