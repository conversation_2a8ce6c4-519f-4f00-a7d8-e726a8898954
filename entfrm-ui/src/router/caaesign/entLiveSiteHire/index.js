export default [
    {
        path: '/caaesign/entLiveSiteHire',
        component: () => import('@/layout'),
        hidden: true,
        meta: {title: '生活服務區場地佔用申請單'},
        children: [
            {
                path: 'EntLiveSiteHireAdd',
                component: () => import('@/views/caaesign/entLiveSiteHire/EntLiveSiteHireAdd'),
                name: 'caaesign_entLiveSiteHire_EntLiveSiteHireAdd',
                meta: {title: '生活服務區場地佔用申請單新增'}
            },
            {
                path: 'EntLiveSiteHireEdit',
                    component: () => import('@/views/caaesign/entLiveSiteHire/EntLiveSiteHireAdd'),
                name: 'caaesign_entLiveSiteHire_EntLiveSiteHireEdit',
                meta: {title: '生活服務區場地佔用申請單修改'}
            },
            {
                path: 'EntLiveSiteHireAudit',
                component: () => import('@/views/caaesign/entLiveSiteHire/EntLiveSiteHireAudit'),
                name: 'caaesign_entLiveSiteHire_EntLiveSiteHireAudit',
                meta: { title: '生活服務區場地佔用申請單审核' }
            },
            {
                path: 'EntLiveSiteHireDetail',
                component:() => import('@/views/caaesign/entLiveSiteHire/EntLiveSiteHireDetail'),
                name:'caaesign_entLiveSiteHire_EntLiveSiteHireDetail',
                meta:{title: '生活服務區場地佔用申請單详情'}
            },
          {
            path: 'EntLiveSiteHireReject',
            component: () => import('@/views/caaesign/entLiveSiteHire/EntLiveSiteHireReject'),
            name: 'caaesign_entLiveSiteHire_EntLiveSiteHireReject',
            meta: {title: '生活服務區場地佔用申請單重新提交'}
          }
        ]
    }
]
