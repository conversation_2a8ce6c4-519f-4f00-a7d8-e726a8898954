export default [
    {
        path: '/caaesign/entDmsAccessApp',
        component: () => import('@/mobileLayout'),
        hidden: true,
        meta: {title: '宿舍區門禁權限管制申請表'},
        children: [
            {
                path: 'EntDmsAccessAddApp',
                component: () => import('@/views/caaesign/entDmsAccess/app/EntDmsAccessAddApp'),
                name: 'caaesign_entDmsAccess_EntDmsAccessAddApp',
                meta: {title: '宿舍區門禁權限管制申請表新增'}
            },
            {
                path: 'EntDmsAccessEditApp',
                    component: () => import('@/views/caaesign/entDmsAccess/app/EntDmsAccessAddApp'),
                name: 'caaesign_entDmsAccess_EntDmsAccessEditApp',
                meta: {title: '宿舍區門禁權限管制申請表修改'}
            },
            {
                path: 'EntDmsAccessAuditApp',
                component: () => import('@/views/caaesign/entDmsAccess/app/EntDmsAccessAuditApp'),
                name: 'caaesign_entDmsAccess_EntDmsAccessAuditApp',
                meta: { title: '宿舍區門禁權限管制申請表审核' }
            },
            {
                path: 'EntDmsAccessDetailApp',
                component:() => import('@/views/caaesign/entDmsAccess/app/EntDmsAccessDetailApp'),
                name:'caaesign_entDmsAccess_EntDmsAccessDetailApp',
                meta:{title: '宿舍區門禁權限管制申請表详情'}
            },
            {
                path: 'EntDmsAccessRejectApp',
                    component:() => import('@/views/caaesign/entDmsAccess/app/EntDmsAccessRejectApp'),
                name:'caaesign_entDmsAccess_EntDmsAccessRejectApp',
                meta:{title: '宿舍區門禁權限管制申請表修改'}
            }
        ]
    }
]
