export default [
    {
        path: '/caaesign/entDmsAccess',
        component: () => import('@/layout'),
        hidden: true,
        meta: {title: '宿舍區門禁權限管制申請表'},
        children: [
            {
                path: 'EntDmsAccessAdd',
                component: () => import('@/views/caaesign/entDmsAccess/pc/EntDmsAccessAdd'),
                name: 'caaesign_entDmsAccess_EntDmsAccessAdd',
                meta: {title: '宿舍區門禁權限管制申請表新增'}
            },
            {
                path: 'EntDmsAccessEdit',
                    component: () => import('@/views/caaesign/entDmsAccess/pc/EntDmsAccessAdd'),
                name: 'caaesign_entDmsAccess_EntDmsAccessEdit',
                meta: {title: '宿舍區門禁權限管制申請表修改'}
            },
            {
                path: 'EntDmsAccessAudit',
                component: () => import('@/views/caaesign/entDmsAccess/pc/EntDmsAccessAudit'),
                name: 'caaesign_entDmsAccess_EntDmsAccessAudit',
                meta: { title: '宿舍區門禁權限管制申請表审核' }
            },
            {
                path: 'EntDmsAccessDetail',
                component:() => import('@/views/caaesign/entDmsAccess/pc/EntDmsAccessDetail'),
                name:'caaesign_entDmsAccess_EntDmsAccessDetail',
                meta:{title: '宿舍區門禁權限管制申請表详情'}
            },
            {
                path: 'EntDmsAccessReject',
                    component:() => import('@/views/caaesign/entDmsAccess/pc/EntDmsAccessReject'),
                name:'caaesign_entDmsAccess_EntDmsAccessReject',
                meta:{title: '宿舍區門禁權限管制申請表修改'}
            }
        ]
    }
]
