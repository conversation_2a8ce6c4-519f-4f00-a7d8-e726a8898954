export default [
    {
        path: '/caaesign/drsSpeApplyApp',
        component: () => import('@/mobileLayout'),
        hidden: true,
        meta: {title: '特殊就餐申請表'},
        children: [
            {
                path: 'DrsSpeApplyAddApp',
                component: () => import('@/views/caaesign/drsSpeApply/app/DrsSpeApplyAddApp'),
                name: 'caaesign_drsSpeApply_DrsSpeApplyAddApp',
                meta: {title: '特殊就餐申請表新增'}
            },
            {
                path: 'DrsSpeApplyEditApp',
                    component: () => import('@/views/caaesign/drsSpeApply/app/DrsSpeApplyAddApp'),
                name: 'caaesign_drsSpeApply_DrsSpeApplyEditApp',
                meta: {title: '特殊就餐申請表修改'}
            },
            {
                path: 'DrsSpeApplyAuditApp',
                component: () => import('@/views/caaesign/drsSpeApply/app/DrsSpeApplyAuditApp'),
                name: 'caaesign_drsSpeApply_DrsSpeApplyAuditApp',
                meta: { title: '特殊就餐申請表审核' }
            },
            {
                path: 'DrsSpeApplyDetailApp',
                component:() => import('@/views/caaesign/drsSpeApply/app/DrsSpeApplyDetailApp'),
                name:'caaesign_drsSpeApply_DrsSpeApplyDetailApp',
                meta:{title: '特殊就餐申請表详情'}
            },
            {
                path: 'DrsSpeApplyRejectApp',
                    component:() => import('@/views/caaesign/drsSpeApply/app/DrsSpeApplyRejectApp'),
                name:'caaesign_drsSpeApply_DrsSpeApplyRejectApp',
                meta:{title: '特殊就餐申請表修改'}
            }
        ]
    }
]
