export default [
    {
        path: '/caaesign/drsSpeApply',
        component: () => import('@/layout'),
        hidden: true,
        meta: {title: '特殊就餐申請表'},
        children: [
            {
                path: 'DrsSpeApplyAdd',
                component: () => import('@/views/caaesign/drsSpeApply/pc/DrsSpeApplyAdd'),
                name: 'caaesign_drsSpeApply_DrsSpeApplyAdd',
                meta: {title: '特殊就餐申請表新增'}
            },
            {
                path: 'DrsSpeApplyEdit',
                    component: () => import('@/views/caaesign/drsSpeApply/pc/DrsSpeApplyAdd'),
                name: 'caaesign_drsSpeApply_DrsSpeApplyEdit',
                meta: {title: '特殊就餐申請表修改'}
            },
            {
                path: 'DrsSpeApplyAudit',
                component: () => import('@/views/caaesign/drsSpeApply/pc/DrsSpeApplyAudit'),
                name: 'caaesign_drsSpeApply_DrsSpeApplyAudit',
                meta: { title: '特殊就餐申請表审核' }
            },
            {
                path: 'DrsSpeApplyDetail',
                component:() => import('@/views/caaesign/drsSpeApply/pc/DrsSpeApplyDetail'),
                name:'caaesign_drsSpeApply_DrsSpeApplyDetail',
                meta:{title: '特殊就餐申請表详情'}
            },
            {
                path: 'DrsSpeApplyReject',
                    component:() => import('@/views/caaesign/drsSpeApply/pc/DrsSpeApplyReject'),
                name:'caaesign_drsSpeApply_DrsSpeApplyReject',
                meta:{title: '特殊就餐申請表修改'}
            }
        ]
    }
]
