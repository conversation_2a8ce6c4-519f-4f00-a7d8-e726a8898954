export default [
  {
    path: '/caaesign/wfsystemprocessApp',
    component: () => import('@/mobileLayout'),
    hidden: true,
    meta: { title: '系統需求申請單' },
    children: [
      {
        path: 'wfsystemprocessAuditApp',
        component: () => import('@/views/caaesign/wfsystemprocess/app/wfsystemprocessAuditApp'),
        name: 'caaesign_wfsystemprocess_wfsystemprocessAuditApp',
        meta: { title: '系統需求申請單申請單审核' }
      },
      {
        path: 'wfsystemprocessDetailApp',
        component: () => import('@/views/caaesign/wfsystemprocess/app/wfsystemprocessDetailApp'),
        name: 'caaesign_wfsystemprocess_wfsystemprocessDetailApp',
        meta: { title: '系統需求申請單詳情' }
      },
      {
        path: '404App',
        component: () => import('@/views/caaesign/wfonlineprocess/app/404App'),
        name: 'caaesign_wfonlineprocess_404App',
        meta: { title: '系統需求申請單404頁面' }
      },
    ]
  },
]
