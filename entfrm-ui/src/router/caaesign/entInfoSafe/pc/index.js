export default [
    {
        path: '/caaesign/entInfoSafe',
        component: () => import('@/layout'),
        hidden: true,
        meta: {title: '應用系統資安評估申請表'},
        children: [
            {
                path: 'EntInfoSafeAdd',
                component: () => import('@/views/caaesign/entInfoSafe/pc/EntInfoSafeAdd'),
                name: 'caaesign_entInfoSafe_EntInfoSafeAdd',
                meta: {title: '應用系統資安評估申請表新增'}
            },
            {
                path: 'EntInfoSafeEdit',
                    component: () => import('@/views/caaesign/entInfoSafe/pc/EntInfoSafeAdd'),
                name: 'caaesign_entInfoSafe_EntInfoSafeEdit',
                meta: {title: '應用系統資安評估申請表修改'}
            },
            {
                path: 'EntInfoSafeAudit',
                component: () => import('@/views/caaesign/entInfoSafe/pc/EntInfoSafeAudit'),
                name: 'caaesign_entInfoSafe_EntInfoSafeAudit',
                meta: { title: '應用系統資安評估申請表审核' }
            },
            {
                path: 'EntInfoSafeDetail',
                component:() => import('@/views/caaesign/entInfoSafe/pc/EntInfoSafeDetail'),
                name:'caaesign_entInfoSafe_EntInfoSafeDetail',
                meta:{title: '應用系統資安評估申請表详情'}
            },
            {
                path: 'EntInfoSafeReject',
                    component:() => import('@/views/caaesign/entInfoSafe/pc/EntInfoSafeReject'),
                name:'caaesign_entInfoSafe_EntInfoSafeReject',
                meta:{title: '應用系統資安評估申請表修改'}
            }
        ]
    }
]
