export default [
    {
        path: '/caaesign/entInfoSafeApp',
        component: () => import('@/mobileLayout'),
        hidden: true,
        meta: {title: '應用系統資安評估申請表'},
        children: [
            {
                path: 'EntInfoSafeAddApp',
                component: () => import('@/views/caaesign/entInfoSafe/app/EntInfoSafeAddApp'),
                name: 'caaesign_entInfoSafe_EntInfoSafeAddApp',
                meta: {title: '應用系統資安評估申請表新增'}
            },
            {
                path: 'EntInfoSafeEditApp',
                    component: () => import('@/views/caaesign/entInfoSafe/app/EntInfoSafeAddApp'),
                name: 'caaesign_entInfoSafe_EntInfoSafeEditApp',
                meta: {title: '應用系統資安評估申請表修改'}
            },
            {
                path: 'EntInfoSafeAuditApp',
                component: () => import('@/views/caaesign/entInfoSafe/app/EntInfoSafeAuditApp'),
                name: 'caaesign_entInfoSafe_EntInfoSafeAuditApp',
                meta: { title: '應用系統資安評估申請表审核' }
            },
            {
                path: 'EntInfoSafeDetailApp',
                component:() => import('@/views/caaesign/entInfoSafe/app/EntInfoSafeDetailApp'),
                name:'caaesign_entInfoSafe_EntInfoSafeDetailApp',
                meta:{title: '應用系統資安評估申請表详情'}
            },
            {
                path: 'EntInfoSafeRejectApp',
                    component:() => import('@/views/caaesign/entInfoSafe/app/EntInfoSafeRejectApp'),
                name:'caaesign_entInfoSafe_EntInfoSafeRejectApp',
                meta:{title: '應用系統資安評估申請表修改'}
            }
        ]
    }
]
