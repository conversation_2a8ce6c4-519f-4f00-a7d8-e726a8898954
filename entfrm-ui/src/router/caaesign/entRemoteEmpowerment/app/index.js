export default [
    {
        path: '/caaesign/entRemoteEmpowermentApp',
        component: () => import('@/mobileLayout'),
        hidden: true,
        meta: {title: '產線遠端賦權帳號申請單'},
        children: [
            {
                path: 'EntRemoteEmpowermentAddApp',
                component: () => import('@/views/caaesign/entRemoteEmpowerment/app/EntRemoteEmpowermentAddApp'),
                name: 'caaesign_entRemoteEmpowerment_EntRemoteEmpowermentAddApp',
                meta: {title: '產線遠端賦權帳號申請單新增'}
            },
            {
                path: 'EntRemoteEmpowermentEditApp',
                    component: () => import('@/views/caaesign/entRemoteEmpowerment/app/EntRemoteEmpowermentAddApp'),
                name: 'caaesign_entRemoteEmpowerment_EntRemoteEmpowermentEditApp',
                meta: {title: '產線遠端賦權帳號申請單修改'}
            },
            {
                path: 'EntRemoteEmpowermentAuditApp',
                component: () => import('@/views/caaesign/entRemoteEmpowerment/app/EntRemoteEmpowermentAuditApp'),
                name: 'caaesign_entRemoteEmpowerment_EntRemoteEmpowermentAuditApp',
                meta: { title: '產線遠端賦權帳號申請單审核' }
            },
            {
                path: 'EntRemoteEmpowermentDetailApp',
                component:() => import('@/views/caaesign/entRemoteEmpowerment/app/EntRemoteEmpowermentDetailApp'),
                name:'caaesign_entRemoteEmpowerment_EntRemoteEmpowermentDetailApp',
                meta:{title: '產線遠端賦權帳號申請單详情'}
            },
            {
                path: 'EntRemoteEmpowermentRejectApp',
                    component:() => import('@/views/caaesign/entRemoteEmpowerment/app/EntRemoteEmpowermentRejectApp'),
                name:'caaesign_entRemoteEmpowerment_EntRemoteEmpowermentRejectApp',
                meta:{title: '產線遠端賦權帳號申請單修改'}
            }
        ]
    }
]
