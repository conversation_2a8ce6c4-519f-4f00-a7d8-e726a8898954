export default [
    {
        path: '/caaesign/entRemoteEmpowerment',
        component: () => import('@/layout'),
        hidden: true,
        meta: {title: '產線遠端賦權帳號申請單'},
        children: [
            {
                path: 'EntRemoteEmpowermentAdd',
                component: () => import('@/views/caaesign/entRemoteEmpowerment/pc/EntRemoteEmpowermentAdd'),
                name: 'caaesign_entRemoteEmpowerment_EntRemoteEmpowermentAdd',
                meta: {title: '產線遠端賦權帳號申請單新增'}
            },
            {
                path: 'EntRemoteEmpowermentEdit',
                    component: () => import('@/views/caaesign/entRemoteEmpowerment/pc/EntRemoteEmpowermentAdd'),
                name: 'caaesign_entRemoteEmpowerment_EntRemoteEmpowermentEdit',
                meta: {title: '產線遠端賦權帳號申請單修改'}
            },
            {
                path: 'EntRemoteEmpowermentAudit',
                component: () => import('@/views/caaesign/entRemoteEmpowerment/pc/EntRemoteEmpowermentAudit'),
                name: 'caaesign_entRemoteEmpowerment_EntRemoteEmpowermentAudit',
                meta: { title: '產線遠端賦權帳號申請單审核' }
            },
            {
                path: 'EntRemoteEmpowermentDetail',
                component:() => import('@/views/caaesign/entRemoteEmpowerment/pc/EntRemoteEmpowermentDetail'),
                name:'caaesign_entRemoteEmpowerment_EntRemoteEmpowermentDetail',
                meta:{title: '產線遠端賦權帳號申請單详情'}
            },
            {
                path: 'EntRemoteEmpowermentReject',
                    component:() => import('@/views/caaesign/entRemoteEmpowerment/pc/EntRemoteEmpowermentReject'),
                name:'caaesign_entRemoteEmpowerment_EntRemoteEmpowermentReject',
                meta:{title: '產線遠端賦權帳號申請單修改'}
            }
        ]
    }
]
