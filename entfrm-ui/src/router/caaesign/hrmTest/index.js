export default [
    {
        path: '/caaesign/hrmTest',
        component: () => import('@/layout'),
        hidden: true,
        meta: {title: '新功能表單測試流程'},
        children: [
            {
                path: 'HrmTestAdd',
                component: () => import('@/views/caaesign/hrmTest/HrmTestAdd'),
                name: 'caaesign_hrmTest_HrmTestAdd',
                meta: {title: '新功能表單測試流程新增'}
            },
            {
                path: 'HrmTestEdit',
                    component: () => import('@/views/caaesign/hrmTest/HrmTestAdd'),
                name: 'caaesign_hrmTest_HrmTestEdit',
                meta: {title: '新功能表單測試流程修改'}
            },
            {
                path: 'HrmTestAudit',
                component: () => import('@/views/caaesign/hrmTest/HrmTestAudit'),
                name: 'caaesign_hrmTest_HrmTestAudit',
                meta: { title: '新功能表單測試流程审核' }
            },
            {
                path: 'HrmTestDetail',
                component:() => import('@/views/caaesign/hrmTest/HrmTestDetail'),
                name:'caaesign_hrmTest_HrmTestDetail',
                meta:{title: '新功能表單測試流程详情'}
            },
            {
                path: 'HrmTestReject',
                    component:() => import('@/views/caaesign/hrmTest/HrmTestReject'),
                name:'caaesign_hrmTest_HrmTestReject',
                meta:{title: '新功能表單測試流程修改'}
            }
        ]
    }
]
