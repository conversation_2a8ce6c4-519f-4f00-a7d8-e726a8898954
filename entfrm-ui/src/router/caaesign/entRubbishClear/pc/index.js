export default [
    {
        path: '/caaesign/entRubbishClear',
        component: () => import('@/layout'),
        hidden: true,
        meta: {title: '太原周邊總處垃圾清運服務申請單'},
        children: [
            {
                path: 'EntRubbishClearAdd',
                component: () => import('@/views/caaesign/entRubbishClear/pc/EntRubbishClearAdd'),
                name: 'caaesign_entRubbishClear_EntRubbishClearAdd',
                meta: {title: '太原周邊總處垃圾清運服務申請單新增'}
            },
            {
                path: 'EntRubbishClearEdit',
                    component: () => import('@/views/caaesign/entRubbishClear/pc/EntRubbishClearAdd'),
                name: 'caaesign_entRubbishClear_EntRubbishClearEdit',
                meta: {title: '太原周邊總處垃圾清運服務申請單修改'}
            },
            {
                path: 'EntRubbishClearAudit',
                component: () => import('@/views/caaesign/entRubbishClear/pc/EntRubbishClearAudit'),
                name: 'caaesign_entRubbishClear_EntRubbishClearAudit',
                meta: { title: '太原周邊總處垃圾清運服務申請單审核' }
            },
            {
                path: 'EntRubbishClearDetail',
                component:() => import('@/views/caaesign/entRubbishClear/pc/EntRubbishClearDetail'),
                name:'caaesign_entRubbishClear_EntRubbishClearDetail',
                meta:{title: '太原周邊總處垃圾清運服務申請單详情'}
            },
            {
                path: 'EntRubbishClearReject',
                    component:() => import('@/views/caaesign/entRubbishClear/pc/EntRubbishClearReject'),
                name:'caaesign_entRubbishClear_EntRubbishClearReject',
                meta:{title: '太原周邊總處垃圾清運服務申請單修改'}
            }
        ]
    }
]
