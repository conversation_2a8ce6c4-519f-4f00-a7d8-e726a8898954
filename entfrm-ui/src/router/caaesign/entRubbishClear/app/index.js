export default [
    {
        path: '/caaesign/entRubbishClearApp',
        component: () => import('@/mobileLayout'),
        hidden: true,
        meta: {title: '太原周邊總處垃圾清運服務申請單'},
        children: [
            {
                path: 'EntRubbishClearAddApp',
                component: () => import('@/views/caaesign/entRubbishClear/app/EntRubbishClearAddApp'),
                name: 'caaesign_entRubbishClear_EntRubbishClearAddApp',
                meta: {title: '太原周邊總處垃圾清運服務申請單新增'}
            },
            {
                path: 'EntRubbishClearEditApp',
                    component: () => import('@/views/caaesign/entRubbishClear/app/EntRubbishClearAddApp'),
                name: 'caaesign_entRubbishClear_EntRubbishClearEditApp',
                meta: {title: '太原周邊總處垃圾清運服務申請單修改'}
            },
            {
                path: 'EntRubbishClearAuditApp',
                component: () => import('@/views/caaesign/entRubbishClear/app/EntRubbishClearAuditApp'),
                name: 'caaesign_entRubbishClear_EntRubbishClearAuditApp',
                meta: { title: '太原周邊總處垃圾清運服務申請單审核' }
            },
            {
                path: 'EntRubbishClearDetailApp',
                component:() => import('@/views/caaesign/entRubbishClear/app/EntRubbishClearDetailApp'),
                name:'caaesign_entRubbishClear_EntRubbishClearDetailApp',
                meta:{title: '太原周邊總處垃圾清運服務申請單详情'}
            },
            {
                path: 'EntRubbishClearRejectApp',
                    component:() => import('@/views/caaesign/entRubbishClear/app/EntRubbishClearRejectApp'),
                name:'caaesign_entRubbishClear_EntRubbishClearRejectApp',
                meta:{title: '太原周邊總處垃圾清運服務申請單修改'}
            }
        ]
    }
]
