import {AppMain} from "@/layout/components";

export default [
    {
        path: '/LJAPPLY/WCSQ',
        component: () => import('@/layout'),
        hidden: true,
        meta: {title: 'WCSQ'},
        children: [
          {
            path: '/',
            component: () => import('@/views/LJAPPLY/WCSQ/index'),
            name: 'index',
            meta: { title: 'WCSQ列表' }
          },
            {
                path: 'WCSQAdd',
                component: () => import('@/views/LJAPPLY/WCSQ/WCSQAdd'),
                name: 'WCSQAdd',
                meta: {title: 'WCSQ新增'}
            },
            {
                path: 'WCSQAudit',
                component: () => import('@/views/LJAPPLY/WCSQ/WCSQAudit'),
                name: 'WCSQAudit',
                meta: { title: 'WCSQ审核' }
            },
            {
                path: 'WCSQDetail',
                component:() => import('@/views/LJAPPLY/WCSQ/WCSQDetail'),
                name:'WCSQDetail',
                meta:{title: 'WCSQ详情'}
            }
        ]
    }
]
