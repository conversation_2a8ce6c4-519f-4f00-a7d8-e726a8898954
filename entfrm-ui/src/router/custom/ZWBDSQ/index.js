export default [
    {
        path: '/custom/ZWBDSQ',
        component: () => import('@/layout'),
        hidden: true,
        meta: {title: 'ZWBDSQ'},
        children: [
            {
                path: 'ZWBDSQAdd',
                component: () => import('@/views/custom/ZWBDSQ/ZWBDSQAdd'),
                name: '<PERSON><PERSON>B<PERSON>QAdd',
                meta: {title: '<PERSON><PERSON><PERSON><PERSON>Q新增'}
            },
            {
                path: 'ZWBDSQAudit',
                component: () => import('@/views/custom/ZWBDSQ/ZWBDSQAudit'),
                name: 'ZWBDSQAudit',
                meta: { title: 'ZWBDSQ审核' }
            },
            {
                path: 'ZWBDSQDetail',
                component:() => import('@/views/custom/ZWBDSQ/ZWBDSQDetail'),
                name:'ZWBDSQDetail',
                meta:{title: 'ZWBDSQ详情'}
            }
        ]
    }
]
