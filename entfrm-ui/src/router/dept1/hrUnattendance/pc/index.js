export default [
    {
        path: '/dept1/hrUnattendance',
        component: () => import('@/layout'),
        hidden: true,
        meta: {title: '考勤異常表單'},
        children: [
            {
                path: 'HrUnattendanceAdd',
                component: () => import('@/views/dept1/hrUnattendance/pc/HrUnattendanceAdd'),
                name: 'dept1_hrUnattendance_HrUnattendanceAdd',
                meta: {title: '考勤異常表單新增'}
            },
            {
                path: 'HrUnattendanceEdit',
                    component: () => import('@/views/dept1/hrUnattendance/pc/HrUnattendanceAdd'),
                name: 'dept1_hrUnattendance_HrUnattendanceEdit',
                meta: {title: '考勤異常表單修改'}
            },
            {
                path: 'HrUnattendanceAudit',
                component: () => import('@/views/dept1/hrUnattendance/pc/HrUnattendanceAudit'),
                name: 'dept1_hrUnattendance_HrUnattendanceAudit',
                meta: { title: '考勤異常表單审核' }
            },
            {
                path: 'HrUnattendanceDetail',
                component:() => import('@/views/dept1/hrUnattendance/pc/HrUnattendanceDetail'),
                name:'dept1_hrUnattendance_HrUnattendanceDetail',
                meta:{title: '考勤異常表單详情'}
            },
            {
                path: 'HrUnattendanceReject',
                    component:() => import('@/views/dept1/hrUnattendance/pc/HrUnattendanceReject'),
                name:'dept1_hrUnattendance_HrUnattendanceReject',
                meta:{title: '考勤異常表單修改'}
            }
        ]
    }
]
