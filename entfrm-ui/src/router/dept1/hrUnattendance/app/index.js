export default [
    {
        path: '/dept1/hrUnattendanceApp',
        component: () => import('@/mobileLayout'),
        hidden: true,
        meta: {title: '考勤異常表單'},
        children: [
            {
                path: 'HrUnattendanceAddApp',
                component: () => import('@/views/dept1/hrUnattendance/app/HrUnattendanceAddApp'),
                name: 'dept1_hrUnattendance_HrUnattendanceAddApp',
                meta: {title: '考勤異常表單新增'}
            },
            {
                path: 'HrUnattendanceEditApp',
                    component: () => import('@/views/dept1/hrUnattendance/app/HrUnattendanceAddApp'),
                name: 'dept1_hrUnattendance_HrUnattendanceEditApp',
                meta: {title: '考勤異常表單修改'}
            },
            {
                path: 'HrUnattendanceAuditApp',
                component: () => import('@/views/dept1/hrUnattendance/app/HrUnattendanceAuditApp'),
                name: 'dept1_hrUnattendance_HrUnattendanceAuditApp',
                meta: { title: '考勤異常表單审核' }
            },
            {
                path: 'HrUnattendanceDetailApp',
                component:() => import('@/views/dept1/hrUnattendance/app/HrUnattendanceDetailApp'),
                name:'dept1_hrUnattendance_HrUnattendanceDetailApp',
                meta:{title: '考勤異常表單详情'}
            },
            {
                path: 'HrUnattendanceRejectApp',
                    component:() => import('@/views/dept1/hrUnattendance/app/HrUnattendanceRejectApp'),
                name:'dept1_hrUnattendance_HrUnattendanceRejectApp',
                meta:{title: '考勤異常表單修改'}
            }
        ]
    }
]
