export default [
    {
        path: '/dept1/aaaCeshi',
        component: () => import('@/layout'),
        hidden: true,
        meta: {title: '附件上傳功能測試'},
        children: [
            {
                path: 'AaaCeshiAdd',
                component: () => import('@/views/dept1/aaaCeshi/AaaCeshiAdd'),
                name: '<PERSON>aa<PERSON>eshiAdd',
                meta: {title: '附件上傳功能測試新增'}
            },
            {
                path: 'AaaCeshiEdit',
                    component: () => import('@/views/dept1/aaaCeshi/AaaCeshiAdd'),
                name: 'AaaCeshiEdit',
                meta: {title: '附件上傳功能測試修改'}
            },
            {
                path: 'AaaCeshiAudit',
                component: () => import('@/views/dept1/aaaCeshi/AaaCeshiAudit'),
                name: 'Aaa<PERSON>eshi<PERSON>udi<PERSON>',
                meta: { title: '附件上傳功能測試审核' }
            },
            {
                path: 'AaaCeshiDetail',
                component:() => import('@/views/dept1/aaaCeshi/AaaCeshiDetail'),
                name:'Aaa<PERSON>eshiDetail',
                meta:{title: '附件上傳功能測試详情'}
            }
        ]
    }
]
