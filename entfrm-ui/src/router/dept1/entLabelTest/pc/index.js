export default [
    {
        path: '/dept1/entLabelTest',
        component: () => import('@/layout'),
        hidden: true,
        meta: {title: '動態內容測試'},
        children: [
            {
                path: 'EntLabelTestAdd',
                component: () => import('@/views/dept1/entLabelTest/pc/EntLabelTestAdd'),
                name: 'dept1_entLabelTest_EntLabelTestAdd',
                meta: {title: '動態內容測試新增'}
            },
            {
                path: 'EntLabelTestEdit',
                    component: () => import('@/views/dept1/entLabelTest/pc/EntLabelTestAdd'),
                name: 'dept1_entLabelTest_EntLabelTestEdit',
                meta: {title: '動態內容測試修改'}
            },
            {
                path: 'EntLabelTestAudit',
                component: () => import('@/views/dept1/entLabelTest/pc/EntLabelTestAudit'),
                name: 'dept1_entLabelTest_EntLabelTestAudit',
                meta: { title: '動態內容測試审核' }
            },
            {
                path: 'EntLabelTestDetail',
                component:() => import('@/views/dept1/entLabelTest/pc/EntLabelTestDetail'),
                name:'dept1_entLabelTest_EntLabelTestDetail',
                meta:{title: '動態內容測試详情'}
            },
            {
                path: 'EntLabelTestReject',
                    component:() => import('@/views/dept1/entLabelTest/pc/EntLabelTestReject'),
                name:'dept1_entLabelTest_EntLabelTestReject',
                meta:{title: '動態內容測試修改'}
            }
        ]
    }
]
