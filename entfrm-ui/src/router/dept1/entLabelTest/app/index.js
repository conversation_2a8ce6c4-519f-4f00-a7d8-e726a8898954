export default [
    {
        path: '/dept1/entLabelTestApp',
        component: () => import('@/mobileLayout'),
        hidden: true,
        meta: {title: '動態內容測試'},
        children: [
            {
                path: 'EntLabelTestAddApp',
                component: () => import('@/views/dept1/entLabelTest/app/EntLabelTestAddApp'),
                name: 'dept1_entLabelTest_EntLabelTestAddApp',
                meta: {title: '動態內容測試新增'}
            },
            {
                path: 'EntLabelTestEditApp',
                    component: () => import('@/views/dept1/entLabelTest/app/EntLabelTestAddApp'),
                name: 'dept1_entLabelTest_EntLabelTestEditApp',
                meta: {title: '動態內容測試修改'}
            },
            {
                path: 'EntLabelTestAuditApp',
                component: () => import('@/views/dept1/entLabelTest/app/EntLabelTestAuditApp'),
                name: 'dept1_entLabelTest_EntLabelTestAuditApp',
                meta: { title: '動態內容測試审核' }
            },
            {
                path: 'EntLabelTestDetailApp',
                component:() => import('@/views/dept1/entLabelTest/app/EntLabelTestDetailApp'),
                name:'dept1_entLabelTest_EntLabelTestDetailApp',
                meta:{title: '動態內容測試详情'}
            },
            {
                path: 'EntLabelTestRejectApp',
                    component:() => import('@/views/dept1/entLabelTest/app/EntLabelTestRejectApp'),
                name:'dept1_entLabelTest_EntLabelTestRejectApp',
                meta:{title: '動態內容測試修改'}
            }
        ]
    }
]
