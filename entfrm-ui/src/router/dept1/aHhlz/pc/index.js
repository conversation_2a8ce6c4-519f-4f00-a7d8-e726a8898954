export default [
    {
        path: '/dept1/aHhlz',
        component: () => import('@/layout'),
        hidden: true,
        meta: {title: '花卉綠植申請單'},
        children: [
            {
                path: 'AHhlzAdd',
                component: () => import('@/views/dept1/aHhlz/pc/AHhlzAdd'),
                name: 'dept1_aHhlz_AHhlzAdd',
                meta: {title: '花卉綠植申請單新增'}
            },
            {
                path: 'AHhlzEdit',
                    component: () => import('@/views/dept1/aHhlz/pc/AHhlzAdd'),
                name: 'dept1_aHhlz_AHhlzEdit',
                meta: {title: '花卉綠植申請單修改'}
            },
            {
                path: 'AHhlzAudit',
                component: () => import('@/views/dept1/aHhlz/pc/AHhlzAudit'),
                name: 'dept1_aHhlz_AHhlzAudit',
                meta: { title: '花卉綠植申請單审核' }
            },
            {
                path: 'AHhlzDetail',
                component:() => import('@/views/dept1/aHhlz/pc/AHhlzDetail'),
                name:'dept1_aHhlz_AHhlzDetail',
                meta:{title: '花卉綠植申請單详情'}
            },
            {
                path: 'AHhlzReject',
                    component:() => import('@/views/dept1/aHhlz/pc/AHhlzReject'),
                name:'dept1_aHhlz_AHhlzReject',
                meta:{title: '花卉綠植申請單修改'}
            }
        ]
    }
]
