export default [
    {
        path: '/dept1/aaaRenshiMs',
        component: () => import('@/layout'),
        hidden: true,
        meta: {title: '人事批量導入表'},
        children: [
            {
                path: 'AaaRenshiMsAdd',
                component: () => import('@/views/dept1/aaaRenshiMs/pc/AaaRenshiMsAdd'),
                name: 'dept1_aaaRenshiMs_AaaRenshiMsAdd',
                meta: {title: '人事批量導入表新增'}
            },
            {
                path: 'AaaRenshiMsEdit',
                    component: () => import('@/views/dept1/aaaRenshiMs/pc/AaaRenshiMsAdd'),
                name: 'dept1_aaaRenshiMs_AaaRenshiMsEdit',
                meta: {title: '人事批量導入表修改'}
            },
            {
                path: 'AaaRenshiMsAudit',
                component: () => import('@/views/dept1/aaaRenshiMs/pc/AaaRenshiMsAudit'),
                name: 'dept1_aaaRenshiMs_AaaRenshiMsAudit',
                meta: { title: '人事批量導入表审核' }
            },
            {
                path: 'AaaRenshiMsDetail',
                component:() => import('@/views/dept1/aaaRenshiMs/pc/AaaRenshiMsDetail'),
                name:'dept1_aaaRenshiMs_AaaRenshiMsDetail',
                meta:{title: '人事批量導入表详情'}
            },
            {
                path: 'AaaRenshiMsReject',
                    component:() => import('@/views/dept1/aaaRenshiMs/pc/AaaRenshiMsReject'),
                name:'dept1_aaaRenshiMs_AaaRenshiMsReject',
                meta:{title: '人事批量導入表修改'}
            }
        ]
    }
]
