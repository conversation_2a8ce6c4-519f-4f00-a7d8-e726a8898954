export default [
    {
        path: '/dept1/bbbJhApp',
        component: () => import('@/mobileLayout'),
        hidden: true,
        meta: {title: '緊急申請'},
        children: [
            {
                path: 'BbbJhAddApp',
                component: () => import('@/views/dept1/bbbJh/app/BbbJhAddApp'),
                name: 'dept1_bbbJh_BbbJhAddApp',
                meta: {title: '緊急申請新增'}
            },
            {
                path: 'BbbJhEditApp',
                    component: () => import('@/views/dept1/bbbJh/app/BbbJhAddApp'),
                name: 'dept1_bbbJh_BbbJhEditApp',
                meta: {title: '緊急申請修改'}
            },
            {
                path: 'BbbJhAuditApp',
                component: () => import('@/views/dept1/bbbJh/app/BbbJhAuditApp'),
                name: 'dept1_bbbJh_BbbJhAuditApp',
                meta: { title: '緊急申請审核' }
            },
            {
                path: 'BbbJhDetailApp',
                component:() => import('@/views/dept1/bbbJh/app/BbbJhDetailApp'),
                name:'dept1_bbbJh_BbbJhDetailApp',
                meta:{title: '緊急申請详情'}
            },
            {
                path: 'BbbJhRejectApp',
                    component:() => import('@/views/dept1/bbbJh/app/BbbJhRejectApp'),
                name:'dept1_bbbJh_BbbJhRejectApp',
                meta:{title: '緊急申請修改'}
            }
        ]
    }
]
