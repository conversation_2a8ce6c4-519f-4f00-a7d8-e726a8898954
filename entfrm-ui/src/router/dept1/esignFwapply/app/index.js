export default [
    {
        path: '/dept1/esignFwapplyApp',
        component: () => import('@/mobileLayout'),
        hidden: true,
        meta: {title: '清潔服務申請單2.0'},
        children: [
            {
                path: 'EsignFwapplyAddApp',
                component: () => import('@/views/dept1/esignFwapply/app/EsignFwapplyAddApp'),
                name: 'dept1_esignFwapply_EsignFwapplyAddApp',
                meta: {title: '清潔服務申請單2.0新增'}
            },
            {
                path: 'EsignFwapplyEditApp',
                    component: () => import('@/views/dept1/esignFwapply/app/EsignFwapplyAddApp'),
                name: 'dept1_esignFwapply_EsignFwapplyEditApp',
                meta: {title: '清潔服務申請單2.0修改'}
            },
            {
                path: 'EsignFwapplyAuditApp',
                component: () => import('@/views/dept1/esignFwapply/app/EsignFwapplyAuditApp'),
                name: 'dept1_esignFwapply_EsignFwapplyAuditApp',
                meta: { title: '清潔服務申請單2.0审核' }
            },
            {
                path: 'EsignFwapplyDetailApp',
                component:() => import('@/views/dept1/esignFwapply/app/EsignFwapplyDetailApp'),
                name:'dept1_esignFwapply_EsignFwapplyDetailApp',
                meta:{title: '清潔服務申請單2.0详情'}
            },
            {
                path: 'EsignFwapplyRejectApp',
                    component:() => import('@/views/dept1/esignFwapply/app/EsignFwapplyRejectApp'),
                name:'dept1_esignFwapply_EsignFwapplyRejectApp',
                meta:{title: '清潔服務申請單2.0修改'}
            }
        ]
    }
]
