export default [
    {
        path: '/dept1/esignFwapply',
        component: () => import('@/layout'),
        hidden: true,
        meta: {title: '清潔服務申請單2.0'},
        children: [
            {
                path: 'EsignFwapplyAdd',
                component: () => import('@/views/dept1/esignFwapply/pc/EsignFwapplyAdd'),
                name: 'dept1_esignFwapply_EsignFwapplyAdd',
                meta: {title: '清潔服務申請單2.0新增'}
            },
            {
                path: 'EsignFwapplyEdit',
                    component: () => import('@/views/dept1/esignFwapply/pc/EsignFwapplyAdd'),
                name: 'dept1_esignFwapply_EsignFwapplyEdit',
                meta: {title: '清潔服務申請單2.0修改'}
            },
            {
                path: 'EsignFwapplyAudit',
                component: () => import('@/views/dept1/esignFwapply/pc/EsignFwapplyAudit'),
                name: 'dept1_esignFwapply_EsignFwapplyAudit',
                meta: { title: '清潔服務申請單2.0审核' }
            },
            {
                path: 'EsignFwapplyDetail',
                component:() => import('@/views/dept1/esignFwapply/pc/EsignFwapplyDetail'),
                name:'dept1_esignFwapply_EsignFwapplyDetail',
                meta:{title: '清潔服務申請單2.0详情'}
            },
            {
                path: 'EsignFwapplyReject',
                    component:() => import('@/views/dept1/esignFwapply/pc/EsignFwapplyReject'),
                name:'dept1_esignFwapply_EsignFwapplyReject',
                meta:{title: '清潔服務申請單2.0修改'}
            }
        ]
    }
]
