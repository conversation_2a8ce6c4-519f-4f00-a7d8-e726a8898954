export default [
    {
        path: '/dept1/aaaPtalk',
        component: () => import('@/layout'),
        hidden: true,
        meta: {title: 'p_talk申請'},
        children: [
            {
                path: 'AaaPtalkAdd',
                component: () => import('@/views/dept1/aaaPtalk/AaaPtalkAdd'),
                name: 'AaaPtalkAdd',
                meta: {title: 'p_talk申請新增'}
            },
            {
                path: 'AaaPtalkEdit',
                    component: () => import('@/views/dept1/aaaPtalk/AaaPtalkAdd'),
                name: 'AaaPtalkEdit',
                meta: {title: 'p_talk申請修改'}
            },
            {
                path: 'AaaPtalkAudit',
                component: () => import('@/views/dept1/aaaPtalk/AaaPtalkAudit'),
                name: 'AaaPtalkAudit',
                meta: { title: 'p_talk申請审核' }
            },
            {
                path: 'AaaPtalkDetail',
                component:() => import('@/views/dept1/aaaPtalk/AaaPtalkDetail'),
                name:'AaaPtalkDetail',
                meta:{title: 'p_talk申請详情'}
            }
        ]
    }
]
