export default [
    {
        path: '/dept1/caaSh',
        component: () => import('@/layout'),
        hidden: true,
        meta: {title: '異常申請單'},
        children: [
            {
                path: 'CaaShAdd',
                component: () => import('@/views/dept1/caaSh/pc/CaaShAdd'),
                name: 'dept1_caaSh_CaaShAdd',
                meta: {title: '異常申請單新增'}
            },
            {
                path: 'CaaShEdit',
                    component: () => import('@/views/dept1/caaSh/pc/CaaShAdd'),
                name: 'dept1_caaSh_CaaShEdit',
                meta: {title: '異常申請單修改'}
            },
            {
                path: 'CaaShAudit',
                component: () => import('@/views/dept1/caaSh/pc/CaaShAudit'),
                name: 'dept1_caaSh_CaaShAudit',
                meta: { title: '異常申請單审核' }
            },
            {
                path: 'CaaShDetail',
                component:() => import('@/views/dept1/caaSh/pc/CaaShDetail'),
                name:'dept1_caaSh_CaaShDetail',
                meta:{title: '異常申請單详情'}
            },
            {
                path: 'CaaShReject',
                    component:() => import('@/views/dept1/caaSh/pc/CaaShReject'),
                name:'dept1_caaSh_CaaShReject',
                meta:{title: '異常申請單修改'}
            }
        ]
    }
]
