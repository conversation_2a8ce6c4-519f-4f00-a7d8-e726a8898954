export default [
    {
        path: '/dept1/aaaQingjiefw',
        component: () => import('@/layout'),
        hidden: true,
        meta: {title: '清潔服務申請單'},
        children: [
            {
                path: 'AaaQingjiefwAdd',
                component: () => import('@/views/dept1/aaaQingjiefw/pc/AaaQingjiefwAdd'),
                name: 'dept1_aaaQingjiefw_AaaQingjiefwAdd',
                meta: {title: '清潔服務申請單新增'}
            },
            {
                path: 'AaaQingjiefwEdit',
                    component: () => import('@/views/dept1/aaaQingjiefw/pc/AaaQingjiefwAdd'),
                name: 'dept1_aaaQingjiefw_AaaQingjiefwEdit',
                meta: {title: '清潔服務申請單修改'}
            },
            {
                path: 'AaaQingjiefwAudit',
                component: () => import('@/views/dept1/aaaQingjiefw/pc/AaaQingjiefwAudit'),
                name: 'dept1_aaaQingjiefw_AaaQingjiefwAudit',
                meta: { title: '清潔服務申請單审核' }
            },
            {
                path: 'AaaQingjiefwDetail',
                component:() => import('@/views/dept1/aaaQingjiefw/pc/AaaQingjiefwDetail'),
                name:'dept1_aaaQingjiefw_AaaQingjiefwDetail',
                meta:{title: '清潔服務申請單详情'}
            },
            {
                path: 'AaaQingjiefwReject',
                    component:() => import('@/views/dept1/aaaQingjiefw/pc/AaaQingjiefwReject'),
                name:'dept1_aaaQingjiefw_AaaQingjiefwReject',
                meta:{title: '清潔服務申請單修改'}
            }
        ]
    }
]
