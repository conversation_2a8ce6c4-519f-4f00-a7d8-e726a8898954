export default [
    {
        path: '/dept1/aaaQingjiefwApp',
        component: () => import('@/mobileLayout'),
        hidden: true,
        meta: {title: '清潔服務申請單'},
        children: [
            {
                path: 'AaaQingjiefwAddApp',
                component: () => import('@/views/dept1/aaaQingjiefw/app/AaaQingjiefwAddApp'),
                name: 'dept1_aaaQingjiefw_AaaQingjiefwAddApp',
                meta: {title: '清潔服務申請單新增'}
            },
            {
                path: 'AaaQingjiefwEditApp',
                    component: () => import('@/views/dept1/aaaQingjiefw/app/AaaQingjiefwAddApp'),
                name: 'dept1_aaaQingjiefw_AaaQingjiefwEditApp',
                meta: {title: '清潔服務申請單修改'}
            },
            {
                path: 'AaaQingjiefwAuditApp',
                component: () => import('@/views/dept1/aaaQingjiefw/app/AaaQingjiefwAuditApp'),
                name: 'dept1_aaaQingjiefw_AaaQingjiefwAuditApp',
                meta: { title: '清潔服務申請單审核' }
            },
            {
                path: 'AaaQingjiefwDetailApp',
                component:() => import('@/views/dept1/aaaQingjiefw/app/AaaQingjiefwDetailApp'),
                name:'dept1_aaaQingjiefw_AaaQingjiefwDetailApp',
                meta:{title: '清潔服務申請單详情'}
            },
            {
                path: 'AaaQingjiefwRejectApp',
                    component:() => import('@/views/dept1/aaaQingjiefw/app/AaaQingjiefwRejectApp'),
                name:'dept1_aaaQingjiefw_AaaQingjiefwRejectApp',
                meta:{title: '清潔服務申請單修改'}
            }
        ]
    }
]
