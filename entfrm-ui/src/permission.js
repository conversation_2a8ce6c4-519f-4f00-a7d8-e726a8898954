import router from './router'
import store from './store'
import { Message } from 'element-ui'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { isMobileFun } from '@/utils/entfrm'
import { getAccessToken, getRefreshToken } from '@/utils/auth'

NProgress.configure({ showSpinner: false })

const whiteList = ['/login', '/thirdLogin','/mobileLogin','/auth-redirect', '/bind', '/register', '/webLogin','/noticeShow']

router.beforeEach((to, from, next) => {
  if (to.query.appId != undefined && to.query.secretKey != undefined && to.query.empNo != undefined) {
    store.dispatch('app/setAppType', 'thirdApplication')
  }
  NProgress.start()
  // console.log(to.path)
  if (getAccessToken()) {
    /* has token*/
    if (to.path === '/login') {
      next({ path: '/' })
      NProgress.done()
    } else {
      if (!store.getters.roles || (store.getters.roles && store.getters.roles.length === 0)) {
        // 判断当前用户是否已拉取完user_info信息
        store.dispatch('GetInfo').then(res => {
          // 拉取user_info
          // debugger
          if (to.query.appId != undefined && to.query.secretKey != undefined && to.query.empNo != undefined) {
            if(res.data.userName !== to.query.empNo){
              store.dispatch('LogOut').then(() => {
                next(
                  {
                    path: '/thirdLogin?appId='+to.query.appId+'&secretKey='+to.query.secretKey+'&empNo='+to.query.empNo+'&redrictUrl='+to.path,
                    query: to.query,
                  }
                )
                NProgress.done()
              })
            }
          }
            const roles = res.data.roles
            store.dispatch('GenerateRoutes', { roles }).then(accessRoutes => {
            // 测试 默认静态页面
            // store.dispatch('permission/generateRoutes', { roles }).then(accessRoutes => {
              // 根据roles权限生成可访问的路由表
              router.addRoutes(accessRoutes) // 动态添加可访问路由表
              next({ ...to, replace: true }) // hack方法 确保addRoutes已完成
            })
        })
        .catch(err => {
          store.dispatch('FedLogOut').then(() => {
            Message.error(err)
            next({ path: '/' })
          })
        })
      } else {
        next()
        // 没有动态改变权限的需求可直接next() 删除下方权限判断 ↓
        // if (hasPermission(store.getters.roles, to.meta.roles)) {
        //   next()
        // } else {
        //   next({ path: '/401', replace: true, query: { noGoBack: true }})
        // }
        // 可删 ↑
      }
    }
  } else {
    // console.log(to)
    // console.log(router)
    // 没有token
    if (whiteList.indexOf(to.path) !== -1) {
      // 在免登录白名单，直接进入
      next()
    } else {
      if (to.query.appId != undefined && to.query.secretKey != undefined && to.query.empNo != undefined) {
        //關閉子頁面時,後退兩層
        to.query.routerHistory = -2
        next(
          {
            path: '/thirdLogin?appId='+to.query.appId+'&secretKey='+to.query.secretKey+'&empNo='+to.query.empNo+'&redrictUrl='+to.path,
            query: to.query,
          }
        )
        NProgress.done()
      } else {
        if(isMobileFun()){
          next(`/mobileLogin`) // 否则全部重定向到登录页
        }else {
          next(`/login`) // 否则全部重定向到登录页
        }
        NProgress.done()
      }
    }
  }
})

router.afterEach(() => {
  NProgress.done()
})
