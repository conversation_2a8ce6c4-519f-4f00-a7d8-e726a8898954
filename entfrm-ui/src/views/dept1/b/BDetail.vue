<template>
  <div class="ant-modal-content">
    <div class="ant-modal-body">
      <el-form ref="elForm" class="ant-form ant-form-horizontal" :model="formData" :rules="rules"
        size="medium" label-width="100px" :label-position="labelPosition">
        <div class="ant-card ant-card-bordered" id="staffCard">
          <div class="ant-card-body">
            <el-row :gutter="15">
              <span id="staffEvectionTitle" style="margin-bottom: 10px">主从测试b</span>
              <el-col :span="12" :xs="24">
                <el-form-item label="多选框组" prop="empNo">
                  {{formData.empNo}}
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24">
                <el-form-item label="日期选择" prop="bDate">
                  {{formData.bDate}}
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24" class="talbe-name-style"> 子表 </el-col>
              <el-scrollbar style="width:100%;">
                <el-col :span="24" :xs="24" style="padding: 0px;width:100%">
                  <el-table v-if="isMobile" border stripe ref="c2DragTableMobile" :data="formData.c2Lists"
                    row-key="id">
                    <el-table-column label="c2" type="index" min-width="90%"
                      :width="c2DragTableMobileClientWidth">
                      <template slot-scope="scope">
                        <span>{{$t('common.serialNumber')}}:{{ scope.$index + 1 }}</span>
                        <el-form-item :prop="'c2Lists.' + scope.$index + '.remarks'" label="備註">
                          <el-input v-model.trim="scope.row.remarks"
                            :placeholder="$t('common.placeholderDefault') + '備註'" :disabled='true'></el-input>
                        </el-form-item>
                        <el-form-item :prop="'c2Lists.' + scope.$index + '.c21'" :rules="rules.c2_c21"
                          label="c21">
                          <el-checkbox-group v-model.trim="scope.row.c21" :disabled='true'>
                            <el-checkbox v-for="(item, index) in c21Options" :key="index" :label="item.value"
                              :disabled="item.disabled">{{item.label}}</el-checkbox>
                          </el-checkbox-group>
                        </el-form-item>
                        <el-form-item :prop="'c2Lists.' + scope.$index + '.c22'" :rules="rules.c2_c22"
                          label="c22">
                          <el-date-picker v-model.trim="scope.row.c22"
                            :placeholder="$t('common.placeholderDefault') + 'c22'" :disabled='true'>
                          </el-date-picker>
                        </el-form-item>
                        <el-button type="text" @click="handleDel_c2(scope.$index, scope.row)"
                          class="del-handler" icon="el-icon-delete"></el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                  <el-table v-else border stripe ref="c2DragTable" :data="formData.c2Lists" row-key="id"
                    :max-height="tableHeight">
                    <el-table-column label="編號" type="index" min-width="5%" />
                    <el-table-column label="備註" min-width="10%" prop="remarks">
                      <template slot-scope="scope">
                        <el-form-item :prop="'c2Lists.' + scope.$index + '.remarks'" label-width="0px">
                          <el-input v-model.trim="scope.row.remarks"
                            :placeholder="$t('common.placeholderDefault') + '備註'" :disabled='true'></el-input>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column label="c21" min-width="10%" prop="c21">
                      <template slot-scope="scope">
                        <el-form-item :prop="'c2Lists.' + scope.$index + '.c21'" :rules="rules.c2_c21"
                          label-width="0px">
                          <el-checkbox-group v-model.trim="scope.row.c21" :disabled='true'>
                            <el-checkbox v-for="(item, index) in c21Options" :key="index" :label="item.value"
                              :disabled="item.disabled">{{item.label}}</el-checkbox>
                          </el-checkbox-group>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column label="c22" min-width="10%" prop="c22">
                      <template slot-scope="scope">
                        <el-form-item :prop="'c2Lists.' + scope.$index + '.c22'" :rules="rules.c2_c22"
                          label-width="0px">
                          <el-date-picker v-model.trim="scope.row.c22"
                            :placeholder="$t('common.placeholderDefault') + 'c22'" :disabled='true'>
                          </el-date-picker>
                        </el-form-item>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-col>
              </el-scrollbar>
              <el-col :span="24" :xs="24" class="talbe-name-style"> 子表 </el-col>
              <el-scrollbar style="width:100%;">
                <el-col :span="24" :xs="24" style="padding: 0px;width:100%">
                  <el-table v-if="isMobile" border stripe ref="c1DragTableMobile" :data="formData.c1Lists"
                    row-key="id">
                    <el-table-column label="c1" type="index" min-width="90%"
                      :width="c1DragTableMobileClientWidth">
                      <template slot-scope="scope">
                        <span>{{$t('common.serialNumber')}}:{{ scope.$index + 1 }}</span>
                        <el-form-item :prop="'c1Lists.' + scope.$index + '.c11'" :rules="rules.c1_c11"
                          label="c11">
                          <el-select v-model.trim="scope.row.c11"
                            :placeholder="$t('common.placeholderDefault') + 'c11'" :disabled='true'>
                            <el-option v-for="(item, index) in c11Options" :key="index" :label="item.label"
                              :value="item.value" :disabled="item.disabled"></el-option>
                          </el-select>
                        </el-form-item>
                        <el-form-item :prop="'c1Lists.' + scope.$index + '.c12'" :rules="rules.c1_c12"
                          label="c12">
                          <el-input v-model.trim="scope.row.c12"
                            :placeholder="$t('common.placeholderDefault') + 'c12'" :disabled='true'>
                          </el-input>
                        </el-form-item>
                        <el-button type="text" @click="handleDel_c1(scope.$index, scope.row)"
                          class="del-handler" icon="el-icon-delete"></el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                  <el-table v-else border stripe ref="c1DragTable" :data="formData.c1Lists" row-key="id"
                    :max-height="tableHeight">
                    <el-table-column label="編號" type="index" min-width="5%" />
                    <el-table-column label="c11" min-width="10%" prop="c11">
                      <template slot-scope="scope">
                        <el-form-item :prop="'c1Lists.' + scope.$index + '.c11'" :rules="rules.c1_c11"
                          label-width="0px">
                          <el-select v-model.trim="scope.row.c11"
                            :placeholder="$t('common.placeholderDefault') + 'c11'" :disabled='true'>
                            <el-option v-for="(item, index) in c11Options" :key="index" :label="item.label"
                              :value="item.value" :disabled="item.disabled"></el-option>
                          </el-select>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column label="c12" min-width="10%" prop="c12">
                      <template slot-scope="scope">
                        <el-form-item :prop="'c1Lists.' + scope.$index + '.c12'" :rules="rules.c1_c12"
                          label-width="0px">
                          <el-input v-model.trim="scope.row.c12"
                            :placeholder="$t('common.placeholderDefault') + 'c12'" :disabled='true'>
                          </el-input>
                        </el-form-item>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-col>
              </el-scrollbar>
              <el-col :span="24" :xs="24">
                <div class="dialog-footer" align="center" style="padding-top: 5px;padding-bottom: 5px">
                  <el-button type="danger" @click="closeForm">{{$t('common.close')}}</el-button>
                  <el-button type="info" @click="handleTrack"
                    v-if="formData.workStatus == 2||formData.workStatus ==3||formData.workStatus ==4">
                    {{$t('table.activity.flowTracing')}}</el-button>
                </div>
              </el-col>
            </el-row>
            <!-- 簽核線 -->
            <el-col :span="24" class="print-hide-div">
              <div class="dialog-footer" style="padding-top: 5px;padding-bottom: 5px">
                <div v-html="signPath"></div>
              </div>
            </el-col>
            <!-- 审核记录 -->
            <el-table border stripe :data="commentList" class="print-hide-div">
              <el-table-column :label="$t('table.activity.taskId')" align="center" prop="id" />
              <el-table-column :label="$t('table.activity.approvedBy')" align="center" prop="userId"
                :show-overflow-tooltip="true" />
              <el-table-column :label="$t('table.activity.approvalOpinions')" align="center"
                prop="fullMessage" :show-overflow-tooltip="true" />
              <el-table-column :label="$t('table.activity.operIp')" align="center" prop="operIp"
                :show-overflow-tooltip="true" />
              <el-table-column :label="$t('table.activity.auditStatus')" align="center" prop="status"
                :formatter="statusFormat" :show-overflow-tooltip="true" />
              <el-table-column :label="$t('table.activity.approvalTime')" align="center" prop="time"
                width="180">
                <template slot-scope="scope">
                  <span>{{ parseTime(scope.row.time) }}</span>
                </template>
              </el-table-column>
            </el-table>
            <!-- 任务跟踪对话框 -->
            <el-dialog :title="$t('table.activity.taskMap')" :visible.sync="showImgDialog" width="760px">
              <img :src="imgUrl" style="padding-bottom: 60px;">
            </el-dialog>
          </div>
        </div>
      </el-form>
    </div>
  </div>
</template>
<script>
import {
  getB,
  addB,
  editB,
  getSignPath
}
from "@/api/dept1/b"
import '@/assets/styles/design-build/design-add-view.scss'
import {
  listTask,
  getTask,
  checkTask,
  taskComment
}
from "@/api/activiti/task"
import {
  getAccessToken,
  getZltAccessToken
}
from "@/utils/auth";
import {
  previewFile,
  getHeader
}
from "@/utils/entfrm";
import {
  listFileInfo,
  getFileInfo,
  delFileInfo,
  addFileInfo,
  editFileInfo,
  getByKey
}
from "@/api/system/fileInfo";
export default {
  components: {},
  props: [],
  data() {
    return {
      formData: {
        empNo: [],
        bDate: null,
        c2Lists: [],
        c1Lists: [],
      },
      rules: {
        empNo: [{
          required: true,
          type: 'array',
          message: '請至少選擇一個多选框组',
          trigger: 'change'
        }],
        bDate: [{
          required: true,
          message: '请选择日期选择',
          trigger: 'change'
        }],
        c2_c21: [{
          required: true,
          type: 'array',
          message: '請至少選擇一個c21',
          trigger: 'change'
        }],
        c2_c22: [{
          required: true,
          message: 'c22不能為空',
          trigger: 'change'
        }, {
          pattern: /^1(3|4|5|7|8|9)\d{9}$/,
          message: '11111',
          trigger: 'change'
        }, {
          pattern: /^1(3|4|5|7|8|9)\d{9}$/,
          message: '22222',
          trigger: 'change'
        }, {
          pattern: /^1(3|4|5|7|8|9)\d{9}$/,
          message: '3333',
          trigger: 'change'
        }],
        c1_c11: [{
          required: true,
          message: 'c11不能為空',
          trigger: 'change'
        }],
        c1_c12: [{
          required: true,
          message: 'c12不能為空',
          trigger: 'blur'
        }],
      },
      tableHeight: document.documentElement.scrollHeight - 245 + "px",
      // 审批意见数据
      commentList: [],
      //簽核路徑
      signPath: [],
      //任务图url
      imgUrl: '',
      isDisabled: false,
      // 是否显示任务图
      showImgDialog: false,
      auditStatus: [],
      empNoOptions: [{
        "label": "选项一",
        "value": 1
      }, {
        "label": "选项二",
        "value": 2
      }],
      c21Options: [],
      c23Options: [],
      c11Options: [],
      c2DragTableMobileClientWidth: 0,
      c1DragTableMobileClientWidth: 0,
      isMobile: false,
      labelPosition: 'left',
    }
  },
  computed: {},
  watch: {},
  created() {
    const id = this.$route.query.id;
    this.changeTagsView(this.$route.query);
    this.isMobile = this.isMobileFun()
    if (this.isMobile) {
      this.labelPosition = 'top'
    }
    if (id != null && id != undefined) {
      getB(id).then(response => {
        this.formData = response.data;
        taskComment(this.formData.processId).then(response => {
          this.commentList = response.data;
        });
        getSignPath(this.formData.processId).then(response => {
          this.signPath = response.data;
        });
        if (this.formData.attachids) {
          let a = this.formData.attachids.split(',');
          if (a.length > 0) {
            a.forEach(item => {
              if (item) {
                getByKey(item).then(response => {
                  this.upload.fileList.push({
                    name: response.data.orignalName,
                    url: response.data.name
                  });
                })
              }
            })
          }
        }
      });
    }
    this.getEmpNoOptions()
    this.getC21Options()
    this.getC23Options()
    this.getC11Options()
    this.getDicts("sign_status").then(response => {
      this.auditStatus = response.data;
    });
    this.getDicts("application_type").then(response => {
      this.formData.empNo = JSON.parse(this.formData.empNo).join()
      response.data.forEach(item => {
        if (this.formData.empNo) {
          this.formData.empNo = this.formData.empNo.replace(item.value, item.label);
        }
      })
    });
  },
  mounted() {
    this.$nextTick(function() {
      if (this.isMobile) {
        this.c2DragTableMobileClientWidth = this.$refs.c2DragTableMobile.$el.clientWidth
        this.c1DragTableMobileClientWidth = this.$refs.c1DragTableMobile.$el.clientWidth
      }
    })
  },
  methods: {
    closeForm() {
      //关闭子页面
      if (this.$store.state.settings.tagsView) {
        this.$router.go(-1) // 返回
        this.$store.state.tagsView.visitedViews.splice(this.$store.state.tagsView.visitedViews.findIndex(
          item => item.path === this.$route.path), 1)
        this.$router.push(this.$store.state.tagsView.visitedViews[this.$store.state.tagsView.visitedViews
          .length - 1].path)
      }
      else {
        parent.postMessage("closeCurrentTabMessage", '*');
      }
    },
    handleTrack() {
      this.imgUrl = process.env.VUE_APP_BASE_API + '/activiti/task/track/' + this.formData.processId,
        this.showImgDialog = true
    },
    statusFormat(row, column) {
      return this.selectDictLabel(this.auditStatus, row.status);
    },
    getEmpNoOptions() {
      this.getDicts("application_type").then(response => {
        this.empNoOptions = response.data;
      });
    },
    checkBoxParse() {
      if (this.formData.empNo) {
        this.formData.empNo = JSON.parse(this.formData.empNo)
      }
      for (let index in this.formData.c2Lists) {
        if (this.formData.c2Lists[index].c21) {
          this.formData.c2Lists[index].c21 = JSON.parse(this.formData.c2Lists[index].c21)
        }
      }
    },
    cascaderParse() {},
    handleAdd_c2() {
      const cloumn = {
        remarks: '',
        c21: [],
        c22: '',
        c23: ''
      };
      this.formData.c2Lists.splice(this.formData.c2Lists.length, 0, cloumn);
      for (let index in this.formData.c2Lists) {
        this.formData.c2Lists[index].sort = parseInt(index) + 1;
      }
    },
    handleDel_c2(index, row) {
      let functionName = this.$t('b_1.default.functionName');
      this.$confirm(this.$t('tips.deleteConfirm', ['c2', row.id]), this.$t('tips.warm'), {
        confirmButtonText: this.$t("common.confirmTrim"),
        cancelButtonText: this.$t("common.cancelTrim"),
        type: "warning",
      }).then(() => {
        this.formData.c2Lists.splice(index, 1);
        this.msgSuccess(this.$t("tips.deleteSuccess"));
      }).catch(function(err) {
        console.log(err);
      });
    },
    getC21Options() {
      this.getDicts("leave_type").then(response => {
        this.c21Options = response.data;
      });
    },
    getC23Options() {
      this.getDicts("user_sex").then(response => {
        this.c23Options = response.data;
      });
    },
    handleAdd_c1() {
      const cloumn = {
        c11: '',
        c12: ''
      };
      this.formData.c1Lists.splice(this.formData.c1Lists.length, 0, cloumn);
      for (let index in this.formData.c1Lists) {
        this.formData.c1Lists[index].sort = parseInt(index) + 1;
      }
    },
    handleDel_c1(index, row) {
      let functionName = this.$t('b_1.default.functionName');
      this.$confirm(this.$t('tips.deleteConfirm', ['c1', row.id]), this.$t('tips.warm'), {
        confirmButtonText: this.$t("common.confirmTrim"),
        cancelButtonText: this.$t("common.cancelTrim"),
        type: "warning",
      }).then(() => {
        this.formData.c1Lists.splice(index, 1);
        this.msgSuccess(this.$t("tips.deleteSuccess"));
      }).catch(function(err) {
        console.log(err);
      });
    },
    getC11Options() {
      this.getDicts("user_sex").then(response => {
        this.c11Options = response.data;
      });
    },
  }
}

</script>
<style scoped>
@media print {
  .print-hide-div {
    display: none;
  }

  .el-col:not(.el-col-no-border) {
    border: 1px solid #ccc;
    margin-top: -1px;
  }

  #printContent {
    box-sizing: border-box;
    width: 1000px;
  }

  .el-form-item {
    margin-top: 10px;
    margin-bottom: 10px;
  }

  /deep/.el-form-item__label {
    text-align: left;
  }

  .el-form-item {
    min-height: 36px;
  }

  * {
    box-sizing: border-box;
  }

  /deep/.el-table {
    border: 1px solid #dfe6ec;
  }

  /deep/.el-table__header {
    table-layout: auto;
  }

  .table-max/deep/.el-table__body,
  .table-max/deep/.el-table__header {
    width: 100% !important;
  }

  .table-max/deep/.el-table__body .cell {
    width: 100% !important;
  }

  .table-max/deep/col {
    width: calc(100% / 6) !important;
  }

  html {
    background-color: #FFFFFF;
    margin: 0;
  }

  #staffEvectionTitle {
    margin-bottom: 20px;
  }
}

/*去除页眉页脚*/
@page {
  size: auto;
  margin: 3mm;
}

.del-handler {
  font-size: 20px;
  color: #ff4949;
}

.el-dialog {
  position: relative;
  margin: 0 auto 0px;
  background: #FFFFFF;
  border-radius: 2px;
  -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 50%;
  height: 60%;
}

.el-dialog__body {
  border-top: 1px solid #dcdfe6;
  border-bottom: 1px solid #dcdfe6;
  max-height: 85% !important;
  min-height: 70%;
  overflow-y: auto;
}

.el-slider>/deep/.el-slider__runway {
  margin: 15px 0;
}

.el-checkbox-group {
  font-size: inherit;
}

.el-color-picker {
  display: inherit;
}

.talbe-name-style {
  font-size: 16px;
  font-weight: bold;
  color: #1DB8FF;
  line-height: 50px;
}

</style>
