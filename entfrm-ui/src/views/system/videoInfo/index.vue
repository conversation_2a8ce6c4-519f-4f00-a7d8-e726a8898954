<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item :label="$t('video_info.label.name')" prop="name">
        <el-input
          v-model="queryParams.name"
          :placeholder="$t('common.placeholderDefault') +　$t('video_info.label.name')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('video_info.default.createDate')">
        <el-date-picker v-model="dateRange"
                        size="small"
                        style="width: 240px"
                        value-format="yyyy-MM-dd"
                        type="daterange"
                        range-separator="-"
                        :start-placeholder="$t('video_info.default.beginDate')"
                        :end-placeholder="$t('video_info.default.endDate')"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">{{ $t('table.search') }}
        </el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{ $t('table.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPerm="['videoInfo_add']"
        >{{ $t('table.add') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleEdit"
          v-hasPerm="['videoInfo_edit']"
        >{{ $t('table.edit') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDel"
          v-hasPerm="['videoInfo_del']"
        >{{ $t('table.delete') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPerm="['videoInfo_export']"
        >{{ $t('table.export') }}
        </el-button>
      </el-col>
      <div class="top-right-btn">
        <el-tooltip class="item" effect="dark" :content="$t('table.refresh')" placement="top">
          <el-button size="mini" circle icon="el-icon-refresh" @click="handleQuery"/>
        </el-tooltip>
        <el-tooltip class="item" effect="dark" :content="showSearch ? $t('common.hideSearch') : $t('common.showSearch')"
                    placement="top">
          <el-button size="mini" circle icon="el-icon-search" @click="showSearch=!showSearch"/>
        </el-tooltip>
      </div>
    </el-row>

    <el-table v-loading="loading" :data="videoInfoList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column :label="$t(&quot;table.role.id&quot;)" type="index" :index="indexMethod" width="80" />
      <el-table-column :label="$t('video_info.label.name')" align="center" prop="name"/>
      <el-table-column :label="$t('video_info.label.background')" align="center" prop="background"/>
      <el-table-column :label="$t('video_info.label.length')" align="center" prop="length"/>
      <el-table-column :label="$t('video_info.label.size')" align="center" prop="size"/>
      <el-table-column :label="$t('video_info.label.location')" align="center" prop="location"/>
      <el-table-column :label="$t('video_info.label.sort')" align="center" prop="sort"/>
      <el-table-column :label="$t('video_info.label.createTime')" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('video_info.label.remarks')" align="center" prop="remarks"/>
      <el-table-column :label="$t('table.operation')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleEdit(scope.row)"
            v-hasPerm="['videoInfo_edit']"
          >{{ $t('table.edit') }}
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDel(scope.row)"
            v-hasPerm="['videoInfo_del']"
          >{{ $t('table.delete') }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.current"
      :limit.sync="queryParams.size"
      @pagination="getList"
    />

    <!-- 添加或修改video_info对话框 -->
    <el-dialog :title="title" :visible.sync="open">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="24">
            <el-form-item :label="$t('video_info.label.name')" prop="name">
              <el-input v-model="form.name"
                        :placeholder="$t('common.placeholderDefault') +　$t('video_info.label.name')"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('video_info.label.background')" prop="background">
              <el-input v-model="form.background" type="textarea" :placeholder="$t('common.pleaseInputContent')+　$t('video_info.label.background')"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('video_info.label.length')" prop="length">
              <el-input v-model="form.length"
                        :placeholder="$t('common.placeholderDefault') +　$t('video_info.label.length')"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('video_info.label.size')" prop="size">
              <el-input v-model="form.size"
                        :placeholder="$t('common.placeholderDefault') +　$t('video_info.label.size')"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('video_info.label.location')" prop="location">
              <el-input v-model="form.location"
                        :placeholder="$t('common.placeholderDefault') +　$t('video_info.label.location')"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('video_info.label.sort')" prop="location">
              <el-input v-model="form.sort"
                        :placeholder="$t('common.placeholderDefault') +　$t('video_info.label.sort')"/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{ $t('common.confirm') }}</el-button>
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listVideoInfo,
  getVideoInfo,
  delVideoInfo,
  addVideoInfo,
  editVideoInfo,
  exportVideoInfo
} from "@/api/system/videoInfo";

export default {
  name: "VideoInfo",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // video_info表格数据
      videoInfoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 日期范围
      dateRange: [],
      // 視頻名称字典
      nameOptions: [],
      // 視頻背景字典
      backgroundOptions: [],
      // 時長字典
      lengthOptions: [],
      // 大小字典
      sizeOptions: [],
      // 地址字典
      locationOptions: [],
      // 排序字典
      sortOptions: [],
      // 创建者字典
      createByOptions: [],
      // 创建时间字典
      createTimeOptions: [],
      // 更新者字典
      updateByOptions: [],
      // 更新时间字典
      updateTimeOptions: [],
      // 备注字典
      remarksOptions: [],
      // 删除标记字典
      delFlagOptions: [],
      // 编号字典
      idOptions: [],
      // 查询参数
      queryParams: {
        current: 1,
        size: 10,
        name: undefined,
        createTime: undefined,
      },
      // 显示搜索条件
      showSearch: true,
      // 表单参数
      form: {},
      // 表单校验
      rules: {}
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询video_info列表 */
    getList() {
      this.loading = true;
      listVideoInfo(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.videoInfoList = response.data;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        name: undefined,
        background: undefined,
        length: undefined,
        size: undefined,
        location: undefined,
        sort: undefined,
        createBy: undefined,
        createTime: undefined,
        updateBy: undefined,
        updateTime: undefined,
        remarks: undefined,
        delFlag: undefined,
        id: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.current = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length != 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.$t('common.add') + this.$t('video_info.default.functionName');
    },
    /** 修改按钮操作 */
    handleEdit(row) {
      this.reset();
      const id = row.id || this.ids
      getVideoInfo(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = this.$t('common.edit') + this.$t('video_info.default.functionName');
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != undefined) {
            editVideoInfo(this.form).then(response => {
              if (response.code === 0) {
                this.msgSuccess(this.$t('tips.updateSuccess'));
                this.open = false;
                this.getList();
              } else {
                this.msgError(response.msg);
              }
            });
          } else {
            addVideoInfo(this.form).then(response => {
              if (response.code === 0) {
                this.msgSuccess(this.$t('tips.createSuccess'));
                this.open = false;
                this.getList();
              } else {
                this.msgError(response.msg);
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDel(row) {
      const ids = row.id || this.ids;
      let functionName = this.$t('video_info.default.functionName');
      this.$confirm(this.$t('tips.deleteConfirm', [functionName, ids]), this.$t('tips.warm'), {
        confirmButtonText: this.$t('common.confirmTrim'),
        cancelButtonText: this.$t('common.cancelTrim'),
        type: "warning"
      }).then(function () {
        return delVideoInfo(ids);
      }).then(() => {
        this.getList();
        this.msgSuccess(this.$t('tips.deleteSuccess'));
      }).catch(function () {
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      let functionName = this.$t('video_info.default.functionName');
      this.$confirm(this.$t('tips.exportConfirm', [functionName]), this.$t('tips.warm'), {
        confirmButtonText: this.$t('common.confirmTrim'),
        cancelButtonText: this.$t('common.cancelTrim'),
        type: "warning"
      }).then(function () {
        return exportVideoInfo(queryParams);
      }).then(response => {
        this.download(response.data);
      }).catch(function () {
      });
    },
    indexMethod(index){
      return index + 1 + (this.queryParams.current - 1)*this.queryParams.size;
    }
  }
};
</script>
