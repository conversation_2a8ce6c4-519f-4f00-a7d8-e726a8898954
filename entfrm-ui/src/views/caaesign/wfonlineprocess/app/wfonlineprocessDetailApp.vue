<template>
<!--  <page-top-bar-esign title="電簽平台">-->
    <div class="ant-modal-content" slot="content">
      <el-form ref="elForm" :model="formData" :rules="rules" size="medium"  :label-position="labelPosition" class="spHeight">
        <div class="form-info">
          <a style="text-decoration:none;">
            <div class="rating__text"></div>
            <span class="course-head">表單信息</span>
          </a>
        </div>
        <div class="profile-info-table">
          <div class="profile-info-block">
            <div>表單編號：</div>
            <div>{{formData.wfonlineprocess.serialno}}</div>
          </div>
        </div>
        <div class="space"></div>
        <div class="form-info" @click="isActive = !isActive">
          <a style="text-decoration:none;">
            <div class="rating__text"></div>
            <span class="course-head">申請基本信息</span>
            <img class="imgUrlArrowhead" :src="imgUrlArrowhead2" v-if="isActive==true">
            <img class="imgUrlArrowhead2" :src="imgUrlArrowhead2" v-if="isActive==false">
          </a>
        </div>
        <div class="ant-modal-body">
          <collapse>
            <div class="profile-info-table" v-show="isActive">
              <div class="content-small">
                <div class="profile-info-block">
                  <div>申請工號：</div>
                  <div>{{formData.wfonlineprocess.applyno}}</div>
                </div>
                <div class="profile-info-block">
                  <div>申請姓名：</div>
                  <div>{{formData.wfonlineprocess.applyname}}</div>
                </div>
                <div class="profile-info-block">
                  <div>聯繫電話：</div>
                  <div>{{formData.wfonlineprocess.applytel}}</div>
                </div>
                <div class="profile-info-block">
                  <div>所在廠區：</div>
                  <div>{{formData.wfonlineprocess.applyfactoryname}}</div>
                </div>
                <div class="profile-info-block">
                  <div>單位名稱：</div>
                  <div>{{formData.wfonlineprocess.applydepartname}}</div>
                </div>
                <div class="profile-info-block">
                  <div>系統名稱：</div>
                  <div>{{formData.wfonlineprocess.systemname}}</div>
                </div>
                <div class="profile-info-block">
                  <div>需求類型：</div>
                  <div>{{formData.wfonlineprocess.requiretypename}}</div>
                </div>
                <div class="profile-info-block">
                  <div>測試說明：</div>
                  <div>
                    <el-form-item prop="wfonlineprocess.pttestname" v-if="formData.wfonlineprocess.pttestname!=null && formData.wfonlineprocess.pttestname != undefined">
                      {{formData.wfonlineprocess.pttestname}}：{{formData.wfonlineprocess.ptUser}} {{parseTime(formData.wfonlineprocess.ptDateStart,'{y}-{m}-{d}')}}~{{parseTime(formData.wfonlineprocess.ptDateEnd,'{y}-{m}-{d}')}}
                    </el-form-item>
                    <el-form-item prop="wfonlineprocess.uattestname" v-if="formData.wfonlineprocess.pttestname!=null && formData.wfonlineprocess.pttestname != undefined">
                      {{formData.wfonlineprocess.uattestname}}：{{formData.wfonlineprocess.uatUser}} {{parseTime(formData.wfonlineprocess.uatDateStart,'{y}-{m}-{d}')}}~{{parseTime(formData.wfonlineprocess.uatDateEnd,'{y}-{m}-{d}')}}
                    </el-form-item>
                  </div>
                </div>
                <div class="profile-info-block">
                  <div>資源點檢：</div>
                  <div>{{formData.wfonlineprocess.resourcesname}}</div>
                </div>
              </div>
              <div class="content-medium">
                <div class="content-medium-block">
                  <div><span>申請工號：</span>{{formData.wfonlineprocess.applyno}}</div>
                  <div><span>申請姓名：</span>{{formData.wfonlineprocess.applyname}}</div>
                  <div><span>所在廠區：</span>{{formData.wfonlineprocess.applyfactoryname}}</div>
                </div>
                <div class="content-medium-block">
                  <div><span>單位名稱：</span>{{formData.wfonlineprocess.applydepartname}}</div>
                </div>
                <div class="content-medium-block">
                  <div><span>聯繫電話：</span>{{formData.wfonlineprocess.applytel}}</div>
                  <div><span>需求類型：</span>{{formData.wfonlineprocess.requiretypename}}</div>
                  <div></div>
                </div>
                <div class="content-medium-block">
                  <div><span>系統名稱：</span>{{formData.wfonlineprocess.systemname}}</div>
                </div>
                <div class="content-medium-block">
                  <div class="text-area-style">
                    <span>測試說明：</span>
                    <div>
                      <div>{{formData.wfonlineprocess.pttestname}}：{{formData.wfonlineprocess.ptUser}} {{parseTime(formData.wfonlineprocess.ptDateStart,'{y}-{m}-{d}')}}~{{parseTime(formData.wfonlineprocess.ptDateEnd,'{y}-{m}-{d}')}}</div>
                      <div>{{formData.wfonlineprocess.uattestname }}：{{formData.wfonlineprocess.uatUser}} {{parseTime(formData.wfonlineprocess.uatDateStart,'{y}-{m}-{d}')}}~{{parseTime(formData.wfonlineprocess.uatDateEnd,'{y}-{m}-{d}')}}</div>
                    </div>
                  </div>
                </div>
                <div class="content-medium-block">
                  <div><span>資源點檢：</span>{{formData.wfonlineprocess.resourcesname}}</div>
                </div>
              </div>
            </div>

          </collapse>
        </div>
        <div class="space"></div>
        <div class="form-info" @click="isActive2 = !isActive2">
          <a style="text-decoration:none;">
            <div class="rating__text"></div>
            <span class="course-head">需求廠區及期望完成日期</span>
            <img class="imgUrlArrowhead" :src="imgUrlArrowhead2" v-if="isActive2==true">
            <img class="imgUrlArrowhead2" :src="imgUrlArrowhead2" v-if="isActive2==false">
          </a>
        </div>
        <div class="ant-modal-body">
          <collapse>
            <div class="profile-info-table" v-show="isActive2">
              <div class="content-small">
                <div class="profile-info-block" v-if="formData.wfonlineprocess.kfqrfactoryname!= null&&formData.wfonlineprocess.kfqrfactoryname!=undefined">
                  <div>需求廠區：</div>
                  <div>{{formData.wfonlineprocess.kfqrfactoryname}}</div>
                </div>
                <div class="profile-info-block" v-else>
                  <div>需求廠區：</div>
                  <div>{{formData.wfonlineprocess.requirefactoryname}}</div>
                </div>

                <div class="profile-info-block" v-if="formData.wfonlineprocess.kfqrFloor!= null&&formData.wfonlineprocess.kfqrFloor!=undefined">
                  <div>需求樓層：</div>
                  <div>{{formData.wfonlineprocess.kfqrFloor}}</div>
                </div>
                <div class="profile-info-block" v-else>
                  <div>需求樓層：</div>
                  <div>{{formData.wfonlineprocess.requireFloor}}</div>
                </div>

                <div class="profile-info-block" v-if="formData.wfonlineprocess.kfqrCompleteDate!=null && formData.wfonlineprocess.kfqrCompleteDate != undefined">
                  <div>上線日期：</div>
                  <div>
                    {{parseTime(formData.wfonlineprocess.kfqrCompleteDate,'{y}-{m}-{d}')}}
                  </div>
                </div>
                <div class="profile-info-block" v-else>
                  <div>上線日期：</div>
                  <div>
                    {{parseTime(formData.wfonlineprocess.requireCompleteDate,'{y}-{m}-{d}')}}
                  </div>
                </div>
<!--                <div class="profile-info-block">
                  <div>需求描述：</div>
                  <div>{{formData.wfonlineprocess.requireDetail}}</div>
                </div>-->
                <div class="profile-info-block">
                  <div>需求描述：</div>
                  <div class="text-area-type">
<!--                    <el-input v-model="formData.wfonlineprocess.requireDetail" type="textarea"
                              :autosize="{minRows: 3, maxRows: 3}" :style="{width: '90%'}" readonly>
                    </el-input>-->
                    <div  style="height: 10vmin;overflow: hidden;width:100%;text-align: left">
                      <div class="scroll-box" v-html="formData.wfonlineprocess.requireDetail">
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="content-medium">
                <div class="content-medium-block">
                  <div v-if="formData.wfonlineprocess.kfqrfactoryname!= null&&formData.wfonlineprocess.kfqrfactoryname!=undefined"><span>需求廠區：</span>{{formData.wfonlineprocess.kfqrfactoryname}}</div>
                  <div v-else><span>需求廠區：</span>{{formData.wfonlineprocess.requirefactoryname}}</div>
                  <div v-if="formData.wfonlineprocess.kfqrFloor!= null&&formData.wfonlineprocess.kfqrFloor!=undefined"><span>需求樓層：</span>{{formData.wfonlineprocess.kfqrFloor}}</div>
                  <div v-else><span>需求樓層：</span>{{formData.wfonlineprocess.requireFloor}}</div>
                  <div v-if="formData.wfonlineprocess.kfqrCompleteDate!=null && formData.wfonlineprocess.kfqrCompleteDate != undefined">
                    <span>上線日期：</span>
                    {{parseTime(formData.wfonlineprocess.kfqrCompleteDate,'{y}-{m}-{d}')}}
                  </div>
                  <div v-else>
                    <span>上線日期：</span>
                    {{parseTime(formData.wfonlineprocess.requireCompleteDate,'{y}-{m}-{d}')}}
                  </div>
                </div>
<!--                <div class="content-medium-block">
                  <div class="text-area-style">
                    <span>需求描述：</span>
                    <div>
                      {{formData.wfonlineprocess.requireDetail}}
                    </div>
                  </div>
                </div>-->
                <div class="content-medium-block">
                  <div class="text-area-type text-area-style">
                    <span>需求描述：</span>
<!--                    <el-input v-model="formData.wfonlineprocess.requireDetail" type="textarea"
                              :autosize="{minRows: 3, maxRows: 3}" :style="{width: '90%'}" readonly>
                    </el-input>-->
                    <div  style="height: 10vmin;overflow: hidden;width:90%;">
                      <div class="scroll-box" v-html="formData.wfonlineprocess.requireDetail">
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </collapse>
        </div>
        <div class="space"></div>
        <div class="form-info" @click="isActive3 = !isActive3">
          <a style="text-decoration:none;">
            <div class="rating__text"></div>
            <span class="course-head">發佈功能/程式及順序</span>
            <img class="imgUrlArrowhead" :src="imgUrlArrowhead2" v-if="isActive3==true">
            <img class="imgUrlArrowhead2" :src="imgUrlArrowhead2" v-if="isActive3==false">
          </a>
        </div>
        <div class="ant-modal-body">
          <collapse>
            <div class="profile-info-table" v-for="(item, index) in formData.wfonlineitems" :key="index" v-show="isActive3">
              <div class="content-small">
                <div class="profile-info-block">
                  <div>功能模組：</div>
                  <div>{{item.modulename}}</div>
                </div>
                <div class="profile-info-block">
                  <div>發佈時間：</div>
                  <div>{{item.onlineDate}}</div>
                </div>
                <div class="profile-info-block">
                  <div>發佈 URL：</div>
                  <div>
                    <div v-html="item.onlineUrl" style="word-break:break-all"></div>
                  </div>
                </div>
                <div class="profile-info-block">
                  <div>開發 DRI：</div>
                  <div>{{item.xtkfDriName}}</div>
                </div>
              </div>
              <div class="content-medium">
                <div class="content-medium-block">
                  <div><span>功能模組：</span>{{item.modulename}}</div>
                  <div><span>發佈時間：</span>{{item.onlineDate}}</div>
                  <div><span>開發 DRI：</span>{{item.xtkfDriName}}</div>
                </div>
                <div class="content-medium-block">
                  <div><span>發佈 URL：</span><span v-html="item.onlineUrl" style="word-break:break-all"></span></div>
                </div>
              </div>
            </div>
          </collapse>
        </div>
        <div class="space" v-if="formData.wfonlinerelates.length>0"></div>
        <div class="form-info" v-if="formData.wfonlinerelates.length>0" @click="isActive4 = !isActive4">
          <a style="text-decoration:none;">
            <div class="rating__text"></div>
            <span class="course-head">關聯系統功能說明</span>
            <img class="imgUrlArrowhead" :src="imgUrlArrowhead2" v-if="isActive4==true">
            <img class="imgUrlArrowhead2" :src="imgUrlArrowhead2" v-if="isActive4==false">
          </a>
        </div>
        <div class="ant-modal-body" v-if="formData.wfonlinerelates.length>0">
          <collapse>
            <div class="profile-info-table" v-show="isActive4">
              <div v-for="(item, index) in formData.wfonlinerelates" :key="index">
                <div class="content-small">
                  <div class="profile-info-block">
                    <div>關聯系統名稱：</div>
                    <div>{{item.glztname}}</div>
                  </div>
                  <div class="profile-info-block">
                    <div>發佈 URL：</div>
                    <div>{{item.onlineUrl}}</div>
                  </div>
                  <div class="profile-info-block">
                    <div>開發 DRI：</div>
                    <div>{{item.xtkfDriName}}</div>
                  </div>
                  <div class="profile-info-block">
                    <div>開發課級主管：</div>
                    <div>{{item.xtkfkjname}}</div>
                  </div>
                  <div class="profile-info-block">
                    <div>開發部級主管：</div>
                    <div>{{item.xtkfbjname}}</div>
                  </div>
                </div>
                <div class="content-medium">
                  <div class="content-medium-block">
                    <div><span>系統名稱：</span>{{item.glztname}}</div>
                  </div>
                  <div class="content-medium-block">
                    <div><span>開發 DRI：</span>{{item.xtkfDriName}}</div>
                    <div><span>課級主管：</span>{{item.xtkfkjname}}</div>
                    <div><span>部級主管：</span>{{item.xtkfbjname}}</div>
                  </div>
                  <div class="content-medium-block">
                    <div><span>發佈 URL：</span>{{item.onlineUrl}}</div>
                  </div>
                </div>
              </div>
            </div>
          </collapse>
        </div>
        <div class="space" v-if="formData.wfonlineprocess.bpmpz!=null && formData.wfonlineprocess.bpmpz != undefined && formData.wfonlineprocess.bpmpz != 'undefined'"></div>
        <div class="form-info" @click="isActive7 = !isActive7" v-if="formData.wfonlineprocess.bpmpz!=null && formData.wfonlineprocess.bpmpz != undefined && formData.wfonlineprocess.bpmpz != 'undefined'">
          <a style="text-decoration:none;">
            <div class="rating__text"></div>
            <span class="course-head">評估說明</span>
            <img class="imgUrlArrowhead" :src="imgUrlArrowhead2" v-if="isActive7==true">
            <img class="imgUrlArrowhead2" :src="imgUrlArrowhead2" v-if="isActive7==false">
          </a>
        </div>
        <div class="ant-modal-body" v-if="formData.wfonlineprocess.bpmpz!=null && formData.wfonlineprocess.bpmpz != undefined && formData.wfonlineprocess.bpmpz != 'undefined'">
          <collapse>
            <div class="profile-info-table" v-show="isActive7">
              <div class="content-small">
                <div class="profile-info-block" v-if="formData.wfonlineprocess.bpmpz!=null && formData.wfonlineprocess.bpmpz != undefined && formData.wfonlineprocess.bpmpz != 'undefined'">
                  <div>BPM補充說明：</div>
                  <div>{{formData.wfonlineprocess.bpmpz}}</div>
                </div>
                <div class="profile-info-block" v-if="formData.wfonlineprocess.dbapz!=null && formData.wfonlineprocess.dbapz != undefined && formData.wfonlineprocess.dbapz != 'undefined'">
                  <div>服務器資源評估說明(DBA填寫)：</div>
                  <div>{{formData.wfonlineprocess.dbapz}}</div>
                </div>
              </div>
              <div class="content-medium">
                <div class="content-medium-block" v-if="formData.wfonlineprocess.bpmpz!=null && formData.wfonlineprocess.bpmpz != undefined && formData.wfonlineprocess.bpmpz != 'undefined'">
                  <div><span>BPM補充說明：</span>{{formData.wfonlineprocess.bpmpz}}</div>
                </div>
                <div class="content-medium-block" v-if="formData.wfonlineprocess.dbapz!=null && formData.wfonlineprocess.dbapz != undefined && formData.wfonlineprocess.dbapz != 'undefined'">
                  <div><span>服務器資源評估說明(DBA填寫)：</span>{{formData.wfonlineprocess.dbapz}}</div>
                </div>
              </div>
            </div>
          </collapse>
        </div>
        <div class="space"></div>
        <div class="form-info" @click="isActive5 = !isActive5">
          <a style="text-decoration:none;">
            <div class="rating__text"></div>
            <span class="course-head">簽核路徑</span>
            <img class="imgUrlArrowhead" :src="imgUrlArrowhead2" v-if="isActive5==true">
            <img class="imgUrlArrowhead2" :src="imgUrlArrowhead2" v-if="isActive5==false">
          </a>
        </div>
        <div class="ant-modal-body">
          <collapse>
            <div v-show="isActive5">
              <div style="padding:16px;font-size: 15px;">
                <div v-html="formData.chargeNodeInfo"></div>
              </div>
            </div>
          </collapse>
        </div>
        <div class="space"></div>
        <div class="form-info" @click="isActive8 = !isActive8">
          <a style="text-decoration:none;">
            <div class="rating__text"></div>
            <span class="course-head">簽核記錄</span>
            <img class="imgUrlArrowhead" :src="imgUrlArrowhead2" v-if="isActive8==true">
            <img class="imgUrlArrowhead2" :src="imgUrlArrowhead2" v-if="isActive8==false">
          </a>
        </div>
        <div class="ant-modal-body">
          <collapse>
            <div v-show="isActive8">
              <div style="padding:0px;">
                <div class="table-responsive">
                  <table class="table">
                    <thead>
                    <tr>
                      <th>序號</th>
                      <th>簽核時間</th>
                      <th>簽核節點</th>
                      <th>簽核主管</th>
                      <th>簽核意見</th>
                      <th>批註</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr v-for="(item, index) in formData.tQhChargelogs" :key="index">
                      <th>{{index+1}}</th>
                      <th>{{parseTime(item.createtime,'{y}-{m}-{d} {h}:{i}:{s}')}}</th>
                      <th>{{item.chargenode}}</th>
                      <th>{{item.chargename}}</th>
                      <th>{{item.ispass}}</th>
                      <th>{{item.decrib}}</th>
                    </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </collapse>
        </div>
        <div class="ant-modal-body" style="text-align: center;margin-top: 10px;">
          <div class="dialog-footer" align="center" style="padding-top: 5px;padding-bottom: 5px">
            <el-button @click="returnForm" :style="{width: '80px'}">返回</el-button>
          </div>
        </div>
      </el-form>
    </div>
<!--  </page-top-bar-esign>-->
</template>
<script>
import {getAccessToken} from '@/utils/auth';
import {showDetail,allFactorys} from "@/api/caaesign/wfonlineprocess";
import collapse from "@/utils/collapse.js";
import imgUrlArrowhead from '@/assets/images/t02.png';
import '@/assets/styles/design-build/design-add-view.scss'
import {previewFile, getHeader} from "@/utils/entfrm";
import imgUrlArrowhead2 from "@/assets/images/arrow.svg";
export default {
  components: {
    collapse
  },
  props: [],
  data() {
    return {
      imgUrlArrowhead:imgUrlArrowhead,
      imgUrlArrowhead2:imgUrlArrowhead2,
      formData: {
        chargeNodeInfo:undefined,
        fileName:undefined,
        nodeName:undefined,
        nodeOrder:undefined,
        processId:undefined,
        tQhChargelogs:[{
          chargename:undefined,
          createtime:undefined,
          ispass:undefined,
          decrib:undefined,
        }],
        wfonlineitems:[{
          modulename:undefined,
          onlineDate:undefined,
          onlineUrl:undefined,
          xtkfDriName:undefined,
        }],
        wfonlineprocess:{
          serialno:undefined,
          applyno:undefined,
          applyname:undefined,
          applytel:undefined,
          applyfactoryname:undefined,
          applydepartname:undefined,
          systemname:undefined,
          requiretypename:undefined,
          pttestname:undefined,
          ptUser:undefined,
          ptDateStart:undefined,
          ptDateEnd:undefined,
          resourcesname:undefined,
          requirefactoryname:undefined,
          requireFloor:undefined,
          requireCompleteDate:undefined,
          kfqrFactory:undefined,
          kfqrfactoryname:undefined,
          kfqrFloor:undefined,
          kfqrCompleteDate:undefined,
          requireDetail:undefined,
          systemTypeOther:undefined,
          bpmpz:undefined,
          dbapz:undefined,
        },
        wfonlinerelates:[{
          glztname:undefined,
          onlineUrl:undefined,
          xtkfDriName:undefined,
          xtkfkjname:undefined,
          xtkfbjname:undefined,
        }],
      },
      rules:{
        wfonlineprocess_kfqrFactory: [{required: true, message: '請選擇廠區', trigger: 'change'}],
        wfonlineprocess_kfqrFloor: [{required: true, message: '請選擇樓層', trigger: 'change'}],
        wfonlineprocess_systemTypeOther: [{required: true, message: '請輸入', trigger: 'blur'}],
        wfonlineprocess_kfqrCompleteDate: [{required: true, message: '請選擇正式環境完成日期', trigger: 'change'}],
        wfonlineprocess_bpmpz:[{required: true, message: "請輸入補充說明", trigger: "blur" }],
        wfonlineprocess_dbapz:[{required: true, message: "請輸入服務器資源評估說明", trigger: "blur" }],
      },
      tableHeight: document.documentElement.scrollHeight - 245 + "px",
      // 审批意见数据
      commentList: [],
      //簽核路徑
      signPath: [],
      activeCount: -1,
      signPathActive: -1,
      //任务图url
      imgUrl: '',
      isDisable: false,
      isDisable2: true,
      // 是否显示任务图
      showImgDialog: false,
      auditStatus: [],
      // 文件上传参数
      upload: {
        // 是否显示弹出层
        open: false,
        fileList: [],
        fileNameList: [],
        // 弹出层标题
        title: "",
        multiple: false,
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: getHeader(),
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/fileInfo/upload"
      },
      makerfactoryidOptions: [],
      makerfactoryidValue: null,
      userApplyTypeOptions: [],
      entRemoteEmpowerItemsActiveNames: ["1"],
      entRemoteEmpowerItemsMainActiveNames: [""],
      isMobile: true,
      labelPosition: 'left',
      isActive: true,
      isActive2: true,
      isActive3: true,
      isActive4: true,
      isActive5: false,
      isActive7: true,
      isActive8: true,
    }
  },
  computed: {},
  watch: {},
  created() {
    const serialno = this.$route.query.serialno;
    this.changeTagsView(this.$route.query);
    this.isMobile = this.isMobileFun()
    if (serialno != null && serialno != undefined) {
      if (getAccessToken()) {
        showDetail(serialno).then(response => {
          this.formData = JSON.parse(response.data);
        });
      }else{
        this.$router.push( '/caaesign/wfonlineprocessApp/404App')
      }
    }
    this.getMakerfactoryidOptions()
    this.getDicts("sign_status").then(response => {
      this.auditStatus = response.data;
    });
    ESignApp.onPageFinished()
  },
  methods: {
    closeForm() {
      //关闭子页面
      if (this.$store.state.settings.tagsView) {
        this.$router.go(-1) // 返回
      } else {
        parent.postMessage("closeCurrentTabMessage", '*');
      }
    },
    returnForm(){
      ESignApp.back()
    },
    handleTrack() {
      this.imgUrl = process.env.VUE_APP_BASE_API + '/activiti/task/track/' + this.formData.processId,
        this.showImgDialog = true
    },
    statusFormat(row, column) {
      return this.selectDictLabel(this.auditStatus, row.status);
    },
    attachidsBeforeUpload(file) {
      let isRightSize = file.size / 1024 / 1024 < 2
      if (!isRightSize) {
        this.$message.error('文件大小超过 2MB')
      }
      return isRightSize
    },
    // 文件上传成功处理
    uploadsucces(response, file, fileList) {
      if (response.data.name != undefined && response.data.name != "undefined") {
        this.upload.fileList.push({
          name: file.name,
          url: response.data.name
        });
        this.formData.attachids += response.data.name + ",";
      }
    },
    handleChange(file, fileList) {},
    handleExceed(file, fileList) {},
    handleRemove(file, fileList) {
      this.$emit("delUploadImage", file.name);
      const index = this.upload.fileList.indexOf(file);
      this.upload.fileList.splice(index, 1);
      if (this.formData.attachids) {
        this.formData.attachids = this.formData.attachids.replace(file.url + ",", "");
      }
      this.upload.fileNameList.push(file.url);
      // delFileInfo(file.url);
    },
    handlePreview(file) {
      previewFile(file.url)
    },
    checkBoxParse() {},
    cascaderParse() {},
    getMakerfactoryidOptions() {
      allFactorys().then(response => {
        this.makerfactoryidOptions=JSON.parse(response.data);
      });
    },
  }
}

</script>
<style scoped lang="scss">
.el-form-item {
  margin-top: 0px;
  margin-bottom: 0px;
}
.space{
  height: 15px;
  background-color: #F7F7F7;
}
.course-head {
  font-size: 15px;
  color: #1E2233;
  font-weight: bold;
}
.rating__text{
  font-weight:normal;
  width:4px;
  height:17px;
  background: #4B8AF8;
  border-radius: 4px;
  float:left;
  margin-top: 11px;
  margin-right: 10px;
}
.form-info{
  background-color:#EAEEFF;
  padding-left:15px;
  line-height: 38px;
}
.imgUrlArrowhead{
  float:right;
  margin-top: 13px;
  width: 16px;
  height: 16px;
  margin-right: 14px;
  transform: scaleX(-1) rotate(-90deg);
}
.imgUrlArrowhead2{
  float:right;
  margin-top: 13px;
  width: 16px;
  height: 16px;
  margin-right: 14px;
  transform: scaleX(-1) rotate(90deg);
}
.ant-modal-body {
  padding: 0px;
  font-size: 15px;
  word-wrap: break-word
}
.profile-info-table {
  font-size: 14px;
  color: #06142D;
  border-radius: 10px;
  background-color: #FFFFFF;
  font-family: Microsoft YaHei;

  .profile-info-block {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 11px 15px;
    border-bottom: 1px solid #F7F7F7;
    >div:nth-child(1){
      flex-basis: 35%;
      color: #57585A;
    }
    >div:nth-child(2){
      flex-basis: 65%;
      text-align: right;
    }
  }
  .profile-info-block__value a svg {
    width: 15px;
    height: 15px;
  }
  .content-medium-block{
    display: flex;
    align-items: center;
    padding: 11px 15px;
    border-bottom: 1px solid #F7F7F7;
    div{
      flex: 1;
      span{
        color: #57585A;
      }
    }
  }
}

/*=====  End of profile  ======*/
.el-button--success{
  color: #fff;
  background-color: #1E80F9;
  border-color: #1E80F9;

}
.el-button--medium{
  border-radius: inherit;
  font-family: Microsoft YaHei;
  font-size: 14px;
  padding:15px 0px;
}

.el-button+.el-button {
  margin-left: 0;
  margin-top: 0px;
}

.el-button--success.is-disabled, .el-button--success.is-disabled:hover, .el-button--success.is-disabled:focus, .el-button--success.is-disabled:active {
  color: #fff;
  background-color: #B4D6FF;
  border-color: #B4D6FF;
}
::v-deep .el-form-item__error {
  color: #ff4949;
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
  position:absolute;
  top: 100%;
  right: 0;
}
/*-------------*/
.table-responsive {
  min-height: .01%;
  overflow-x: auto;
  .table {
    width: 100%;
    font-size: 14px;
    td,th {
      text-align: center;
      padding: 8px;
      border: 1px solid #ddd;
      white-space: nowrap
    }
    tbody>tr:nth-of-type(odd) {
      background-color: #f9f9f9
    }
  }
}

.file-style{
  padding:0px 16px 10px 16px;
}
/deep/.file-style .el-upload{
  display: none;
}
.text-area-style{
  display: flex;
  align-items: center;
}
.text-area-type /deep/.el-textarea__inner{
  border: 0px;
  padding: 0px;
  vertical-align: center;
  color: black;
  overflow-y: scroll;
}
.scroll-box{
  height: 100%;
  overflow-y:scroll;
  -webkit-overflow-scrolling: touch;
  overflow-x: hidden;
  overflow-wrap: anywhere;
  white-space: pre-wrap;
}
/* 针对 iOS 设备 */
@supports (-webkit-overflow-scrolling: touch) {
  .scroll-box {
    height: 100%;
    overflow-y:scroll;
    -webkit-overflow-scrolling: touch;
    overflow-x: hidden;
    overflow-wrap: anywhere;
    white-space: pre-wrap;
  }
}
.content-small,.content-medium{
  display: none;
}
/* 小屏幕設備 */
@media (max-width: 767px){
  .content-small{
    display: block;
  }

}
/* 中等屏幕設備 */
@media (min-width: 768px){
  .content-medium{
    display: block;
  }
}
</style>
