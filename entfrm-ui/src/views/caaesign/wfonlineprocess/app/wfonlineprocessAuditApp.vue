<template>
<!--  <page-top-bar-esign title="電簽平台">-->
    <div class="ant-modal-content" slot="content">
      <el-form ref="elForm" :model="formData" :rules="rules" size="medium"  :label-position="labelPosition" class="spHeight">
        <div class="form-info">
          <a style="text-decoration:none;">
            <div class="rating__text"></div>
            <span class="course-head">表單信息</span>
          </a>
        </div>
        <div class="profile-body-area">
          <div class="profile-body">
            <div class="profile-info-table">
              <div class="profile-info-block">
                <div class="profile-info-block__title">表單編號：</div>
                <div class="profile-info-block__value">
                  <el-form-item prop="wfonlineprocess.serialno">{{formData.wfonlineprocess.serialno}}</el-form-item>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="space"></div>
        <div class="form-info" @click="isActive = !isActive">
          <a style="text-decoration:none;">
            <div class="rating__text"></div>
            <span class="course-head">申請基本信息</span>
            <img class="imgUrlArrowhead" :src="imgUrlArrowhead2" v-if="isActive==true">
            <img class="imgUrlArrowhead2" :src="imgUrlArrowhead2" v-if="isActive==false">
          </a>
        </div>
        <div class="ant-modal-body">
          <collapse>
            <div class="profile-info-table" v-show="isActive">
              <div class="content-small">
                <div class="profile-info-block">
                  <div>申請工號：</div>
                  <div>{{formData.wfonlineprocess.applyno}}</div>
                </div>
                <div class="profile-info-block">
                  <div>申請姓名：</div>
                  <div>{{formData.wfonlineprocess.applyname}}</div>
                </div>
                <div class="profile-info-block">
                  <div>聯繫電話：</div>
                  <div>{{formData.wfonlineprocess.applytel}}</div>
                </div>
                <div class="profile-info-block">
                  <div>所在廠區：</div>
                  <div>{{formData.wfonlineprocess.applyfactoryname}}</div>
                </div>
                <div class="profile-info-block">
                  <div>單位名稱：</div>
                  <div>{{formData.wfonlineprocess.applydepartname}}</div>
                </div>
                <div class="profile-info-block">
                  <div>系統名稱：</div>
                  <div>{{formData.wfonlineprocess.systemname}}</div>
                </div>
                <div class="profile-info-block">
                  <div>需求類型：</div>
                  <div>{{formData.wfonlineprocess.requiretypename}}</div>
                </div>
                <div class="profile-info-block">
                  <div>測試說明：</div>
                  <div>
                    <el-form-item prop="wfonlineprocess.pttestname" v-if="formData.wfonlineprocess.pttestname!=null && formData.wfonlineprocess.pttestname != undefined">
                      {{formData.wfonlineprocess.pttestname}}：{{formData.wfonlineprocess.ptUser}} {{parseTime(formData.wfonlineprocess.ptDateStart,'{y}-{m}-{d}')}}~{{parseTime(formData.wfonlineprocess.ptDateEnd,'{y}-{m}-{d}')}}
                    </el-form-item>
                    <el-form-item prop="wfonlineprocess.uattestname" v-if="formData.wfonlineprocess.pttestname!=null && formData.wfonlineprocess.pttestname != undefined">
                      {{formData.wfonlineprocess.uattestname}}：{{formData.wfonlineprocess.uatUser}} {{parseTime(formData.wfonlineprocess.uatDateStart,'{y}-{m}-{d}')}}~{{parseTime(formData.wfonlineprocess.uatDateEnd,'{y}-{m}-{d}')}}
                    </el-form-item>
                  </div>
                </div>
                <div class="profile-info-block">
                  <div>資源點檢：</div>
                  <div>{{formData.wfonlineprocess.resourcesname}}</div>
                </div>
              </div>
              <div class="content-medium">
                <div class="content-medium-block">
                  <div><span>申請工號：</span>{{formData.wfonlineprocess.applyno}}</div>
                  <div><span>申請姓名：</span>{{formData.wfonlineprocess.applyname}}</div>
                  <div><span>所在廠區：</span>{{formData.wfonlineprocess.applyfactoryname}}</div>
                </div>
                <div class="content-medium-block">
                  <div><span>單位名稱：</span>{{formData.wfonlineprocess.applydepartname}}</div>
                </div>
                <div class="content-medium-block">
                  <div><span>聯繫電話：</span>{{formData.wfonlineprocess.applytel}}</div>
                  <div><span>需求類型：</span>{{formData.wfonlineprocess.requiretypename}}</div>
                  <div></div>
                </div>
                <div class="content-medium-block">
                  <div><span>系統名稱：</span>{{formData.wfonlineprocess.systemname}}</div>
                </div>
                <div class="content-medium-block">
                  <div class="text-area-style">
                    <span>測試說明：</span>
                    <div>
                      <div>{{formData.wfonlineprocess.pttestname}}：{{formData.wfonlineprocess.ptUser}} {{parseTime(formData.wfonlineprocess.ptDateStart,'{y}-{m}-{d}')}}~{{parseTime(formData.wfonlineprocess.ptDateEnd,'{y}-{m}-{d}')}}</div>
                      <div>{{formData.wfonlineprocess.uattestname }}：{{formData.wfonlineprocess.uatUser}} {{parseTime(formData.wfonlineprocess.uatDateStart,'{y}-{m}-{d}')}}~{{parseTime(formData.wfonlineprocess.uatDateEnd,'{y}-{m}-{d}')}}</div>
                    </div>
                  </div>
                </div>
                <div class="content-medium-block">
                  <div><span>資源點檢：</span>{{formData.wfonlineprocess.resourcesname}}</div>
                </div>
              </div>
            </div>
          </collapse>
        </div>
        <div class="space"></div>
        <div class="form-info" @click="isActive2 = !isActive2">
          <a style="text-decoration:none;">
            <div class="rating__text"></div>
            <span class="course-head">需求廠區及期望完成日期</span>
            <img class="imgUrlArrowhead" :src="imgUrlArrowhead2" v-if="isActive2==true">
            <img class="imgUrlArrowhead2" :src="imgUrlArrowhead2" v-if="isActive2==false">
          </a>
        </div>
        <div class="ant-modal-body">
          <collapse>
            <div class="profile-info-table" v-show="isActive2">
              <div class="content-small">
                <div class="profile-info-block" v-if="formData.nodeOrder <= 10">
                  <div>需求廠區：</div>
                  <div>{{formData.wfonlineprocess.requirefactoryname}}</div>
                </div>
                <div class="profile-info-block" v-else>
                  <div>需求廠區：</div>
                  <div>{{formData.wfonlineprocess.kfqrfactoryname}}</div>
                </div>
                <div class="profile-info-block" v-if="formData.nodeOrder <= 10">
                  <div>需求樓層：</div>
                  <div>{{formData.wfonlineprocess.requireFloor}}</div>
                </div>
                <div class="profile-info-block" v-else>
                  <div>需求樓層：</div>
                  <div>{{formData.wfonlineprocess.kfqrFloor}}</div>
                </div>
                <div class="profile-info-block" v-if="formData.nodeOrder <= 10">
                  <div>上線日期：</div>
                  <div>
                    <span v-if="formData.wfonlineprocess.requireCompleteDate!=null && formData.wfonlineprocess.requireCompleteDate != undefined">
                      {{parseTime(formData.wfonlineprocess.requireCompleteDate,'{y}-{m}-{d}')}}
                    </span>
                  </div>
                </div>
                <div class="profile-info-block" v-else>
                  <div>上線日期：</div>
                  <div>
                    <span v-if="formData.wfonlineprocess.kfqrCompleteDate!=null && formData.wfonlineprocess.kfqrCompleteDate != undefined">
                      {{parseTime(formData.wfonlineprocess.kfqrCompleteDate,'{y}-{m}-{d}')}}
                    </span>
                  </div>
                </div>
<!--                <div class="profile-info-block">
                  <div>需求描述</div>
                  <div>{{formData.wfonlineprocess.requireDetail}}</div>
                </div>-->
                <div class="profile-info-block">
                  <div>需求描述：</div>
                  <div class="text-area-type">
<!--                    <el-input v-model="formData.wfonlineprocess.requireDetail" type="textarea"
                              :autosize="{minRows: 3, maxRows: 3}" :style="{width: '90%'}" readonly>
                    </el-input>-->
                    <div  style="height: 10vmin;overflow: hidden;width:100%;text-align: left">
                      <div class="scroll-box" v-html="formData.wfonlineprocess.requireDetail">
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="content-medium">
                <div class="content-medium-block">
                  <div v-if="formData.nodeOrder <= 10"><span>需求廠區：</span>{{formData.wfonlineprocess.requirefactoryname}}</div>
                  <div v-else><span>需求廠區：</span>{{formData.wfonlineprocess.kfqrfactoryname}}</div>
                  <div v-if="formData.nodeOrder <= 10"><span>需求樓層：</span>{{formData.wfonlineprocess.requireFloor}}</div>
                  <div v-else><span>需求樓層：</span>{{formData.wfonlineprocess.kfqrFloor}}</div>
                  <div v-if="formData.nodeOrder <= 10">
                    <span>上線日期：</span>
                    {{parseTime(formData.wfonlineprocess.requireCompleteDate,'{y}-{m}-{d}')}}
                  </div>
                  <div v-else>
                    <span>上線日期：</span>
                    {{parseTime(formData.wfonlineprocess.kfqrCompleteDate,'{y}-{m}-{d}')}}
                  </div>
                </div>
                <div class="content-medium-block">
                  <div class="text-area-type text-area-style">
                    <span>需求描述：</span>
<!--                    <el-input v-model="formData.wfonlineprocess.requireDetail" type="textarea"
                              :autosize="{minRows: 3, maxRows: 3}" :style="{width: '90%'}" readonly>
                    </el-input>-->
                    <div  style="height: 10vmin;overflow: hidden;width:90%;">
                      <div class="scroll-box" v-html="formData.wfonlineprocess.requireDetail">
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </collapse>
        </div>
        <div class="space"></div>
        <div class="form-info" @click="isActive3 = !isActive3">
          <a style="text-decoration:none;">
            <div class="rating__text"></div>
            <span class="course-head">發佈功能/程式及順序</span>
            <img class="imgUrlArrowhead" :src="imgUrlArrowhead2" v-if="isActive3==true">
            <img class="imgUrlArrowhead2" :src="imgUrlArrowhead2" v-if="isActive3==false">
          </a>
        </div>
        <div class="ant-modal-body">
          <collapse>
            <div class="profile-info-table" v-for="(item, index) in formData.wfonlineitems" :key="index" v-show="isActive3">
              <div class="content-small">
                <div class="profile-info-block">
                  <div>功能模組：</div>
                  <div>{{item.modulename}}</div>
                </div>
                <div class="profile-info-block">
                  <div>發佈時間：</div>
                  <div>{{item.onlineDate}}</div>
                </div>
                <div class="profile-info-block">
                  <div>發佈 URL：</div>
                  <div>
                    <div v-html="item.onlineUrl" style="word-break:break-all"></div>
                  </div>
                </div>
                <div class="profile-info-block">
                  <div>開發 DRI：</div>
                  <div>{{item.xtkfDriName}}</div>
                </div>
              </div>
              <div class="content-medium">
                <div class="content-medium-block">
                  <div><span>功能模組：</span>{{item.modulename}}</div>
                  <div><span>發佈時間：</span>{{item.onlineDate}}</div>
                  <div><span>開發 DRI：</span>{{item.xtkfDriName}}</div>
                </div>
                <div class="content-medium-block">
                  <div><span>發佈 URL：</span><span v-html="item.onlineUrl" style="word-break:break-all"></span></div>
                </div>
              </div>
            </div>
          </collapse>
        </div>
        <div class="space"></div>
        <div class="form-info" @click="isActive4 = !isActive4">
          <a style="text-decoration:none;">
            <div class="rating__text"></div>
            <span class="course-head">關聯系統功能說明</span>
            <img class="imgUrlArrowhead" :src="imgUrlArrowhead2" v-if="isActive4==true">
            <img class="imgUrlArrowhead2" :src="imgUrlArrowhead2" v-if="isActive4==false">
          </a>
        </div>
        <div class="ant-modal-body">
          <collapse>
            <div class="profile-info-table" v-show="isActive4">
              <div v-for="(item, index) in formData.wfonlinerelates" :key="index">
                <div class="content-small">
                  <div class="profile-info-block">
                    <div>系統名稱：</div>
                    <div>{{item.glztname}}</div>
                  </div>
                  <div class="profile-info-block">
                    <div>發佈 URL：</div>
                    <div>{{item.onlineUrl}}</div>
                  </div>
                  <div class="profile-info-block">
                    <div>開發 DRI：</div>
                    <div>{{item.xtkfDriName}}</div>
                  </div>
                  <div class="profile-info-block">
                    <div>開發課級主管：</div>
                    <div class="profile-info-block__value">{{item.xtkfkjname}}</div>
                  </div>
                  <div class="profile-info-block">
                    <div>開發部級主管：</div>
                    <div>{{item.xtkfbjname}}</div>
                  </div>
                </div>
                <div class="content-medium">
                  <div class="content-medium-block">
                    <div><span>系統名稱：</span>{{item.glztname}}</div>
                  </div>
                  <div class="content-medium-block">
                    <div><span>開發 DRI：</span>{{item.xtkfDriName}}</div>
                    <div><span>課級主管：</span>{{item.xtkfkjname}}</div>
                    <div><span>部級主管：</span>{{item.xtkfbjname}}</div>
                  </div>
                  <div class="content-medium-block">
                    <div><span>發佈 URL：</span>{{item.onlineUrl}}</div>
                  </div>
                </div>
              </div>
              <div class="profile-info-block" v-if="formData.nodeName=='BPM簽核' && formData.nodeName != null && formData.nodeName != undefined">
                <div><font style="color: red">*</font> BPM補充說明：</div>
                <div>
                  <el-form-item prop="wfonlineprocess.bpmpz" class="bpmsh" :rules="rules.wfonlineprocess_bpmpz" >
                    <el-input v-model="formData.wfonlineprocess.bpmpz" type="textarea" placeholder="請輸入補充說明"
                              :autosize="{minRows: 3, maxRows: 3}" ></el-input>
                  </el-form-item>
                </div>
              </div>
              <div class="content-small">
                <div class="profile-info-block" v-if="formData.nodeName!='BPM簽核'&&formData.wfonlineprocess.bpmpz!=null && formData.wfonlineprocess.bpmpz != undefined && formData.wfonlineprocess.bpmpz != 'undefined'">
                  <div>BPM補充說明：</div>
                  <div>{{formData.wfonlineprocess.bpmpz}}</div>
                </div>
              </div>
              <div class="content-medium">
                <div class="content-medium-block" v-if="formData.nodeName!='BPM簽核'&&formData.wfonlineprocess.bpmpz!=null && formData.wfonlineprocess.bpmpz != undefined && formData.wfonlineprocess.bpmpz != 'undefined'">
                  <div><span>BPM補充說明：</span>{{formData.wfonlineprocess.bpmpz}}</div>
                </div>
              </div>
            </div>
          </collapse>
        </div>
        <div class="space" v-if="formData.nodeName=='系統開發工程師確認' && formData.nodeName != null && formData.nodeName != undefined"></div>
        <div class="form-info" @click="isActive6 = !isActive6" v-if="formData.nodeName=='系統開發工程師確認' && formData.nodeName != null && formData.nodeName != undefined">
          <a style="text-decoration:none;">
            <div class="rating__text"></div>
            <span class="course-head">需求廠區及期望完成日期</span>
            <img class="imgUrlArrowhead" :src="imgUrlArrowhead2" v-if="isActive6==true">
            <img class="imgUrlArrowhead2" :src="imgUrlArrowhead2" v-if="isActive6==false">
          </a>
        </div>
        <div class="ant-modal-body" v-if="formData.nodeName=='系統開發工程師確認' && formData.nodeName != null && formData.nodeName != undefined">
          <collapse>
            <div class="profile-info-table" v-show="isActive6">
              <div class="profile-info-block">
                <div><font style="color: red">*</font> 廠區：</div>
                <div>
                  <el-form-item prop="wfonlineprocess.kfqrFactory" :rules="rules.wfonlineprocess_kfqrFactory" class="xtkfgcsLabel">
                    <el-select v-model="formData.wfonlineprocess.kfqrFactory" placeholder="請選擇所屬廠區" clearable :style="{width: '80%'}">
                      <el-option v-for="(item, index) in makerfactoryidOptions" :key="index" :label="item.factoryname"
                                 :value="item.factoryid" :disabled="item.disabled"></el-option>
                    </el-select>
                  </el-form-item>
                </div>
              </div>
              <div class="profile-info-block">
                <div><font style="color: red">*</font> 樓層：</div>
                <div>
                  <el-form-item prop="wfonlineprocess.kfqrFloor" :rules="rules.wfonlineprocess_kfqrFloor" class="xtkfgcsLabel" >
                    <el-radio-group v-model="formData.wfonlineprocess.kfqrFloor" size="medium">
                      <el-radio label="ALL" border>ALL</el-radio>
                      <el-radio label="other" border>其他</el-radio>
                    </el-radio-group>
                    <el-input v-if="formData.wfonlineprocess.kfqrFloor==='other'" v-model="formData.wfonlineprocess.systemTypeOther" placeholder="請輸入樓層" clearable :style="{width: '220px'}"></el-input>
                  </el-form-item>
                </div>
              </div>
              <div class="profile-info-block">
                <div><font style="color: red">*</font> 正式環境完成日期：</div>
                <div>
                  <el-form-item prop="wfonlineprocess.kfqrCompleteDate" :rules="rules.wfonlineprocess_kfqrCompleteDate" class="xtkfgcsLabel">
                    <el-date-picker v-model="formData.wfonlineprocess.kfqrCompleteDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd" :style="{width: '80%'}"
                                    placeholder="请選擇正式環境完成日期" clearable></el-date-picker>
                  </el-form-item>
                </div>
              </div>
            </div>
          </collapse>
        </div>
        <div class="space" v-if="formData.nodeOrder>=18"></div>
        <div class="form-info" @click="isActive7 = !isActive7" v-if="formData.nodeOrder>=18">
          <a style="text-decoration:none;">
            <div class="rating__text"></div>
            <span class="course-head">服務器資源評估說明</span>
            <img class="imgUrlArrowhead" :src="imgUrlArrowhead2" v-if="isActive7==true">
            <img class="imgUrlArrowhead2" :src="imgUrlArrowhead2" v-if="isActive7==false">
          </a>
        </div>
        <div class="ant-modal-body" v-if="formData.nodeOrder>=18">
          <collapse>
            <div class="profile-info-table" v-show="isActive7">
              <div class="profile-info-block" v-if="formData.nodeName=='DBA作業人員' && formData.nodeName != null && formData.nodeName != undefined">
                <div><font style="color: red">*</font> 服務器資源評估說明(DBA填寫)：</div>
                <div>
                  <el-form-item prop="wfonlineprocess.dbapz" class="bpmsh" :rules="rules.wfonlineprocess_dbapz">
                    <el-input v-model="formData.wfonlineprocess.dbapz" type="textarea" placeholder="服務器資源評估說明"
                              :autosize="{minRows: 3, maxRows: 3}" ></el-input>
                  </el-form-item>
                </div>
              </div>
              <div class="content-small">
                <div class="profile-info-block" v-if="formData.nodeOrder>18">
                  <div>服務器資源評估說明(DBA填寫)：</div>
                  <div>{{formData.wfonlineprocess.dbapz}}</div>
                </div>
              </div>
              <div class="content-medium">
                <div class="content-medium-block" v-if="formData.nodeOrder>18">
                  <div><span>服務器資源評估說明(DBA填寫)：</span>{{formData.wfonlineprocess.dbapz}}</div>
                </div>
              </div>
            </div>
          </collapse>
        </div>
        <div class="space"></div>
        <div class="form-info" @click="isActive5 = !isActive5">
          <a style="text-decoration:none;">
            <div class="rating__text"></div>
            <span class="course-head">簽核路徑</span>
            <img class="imgUrlArrowhead" :src="imgUrlArrowhead2" v-if="isActive5==true">
            <img class="imgUrlArrowhead2" :src="imgUrlArrowhead2" v-if="isActive5==false">
          </a>
        </div>
        <div class="ant-modal-body">
          <collapse>
            <div v-show="isActive5">
              <div style="padding:16px;font-size: 14px;">
                <div v-html="formData.chargeNodeInfo"></div>
              </div>
            </div>
          </collapse>
        </div>
        <div class="space"></div>
        <div class="form-info" @click="isActive8 = !isActive8">
          <a style="text-decoration:none;">
            <span class="course-head">簽核記錄</span>
            <div class="rating__text"></div>
            <img class="imgUrlArrowhead" :src="imgUrlArrowhead2" v-if="isActive8==true">
            <img class="imgUrlArrowhead2" :src="imgUrlArrowhead2" v-if="isActive8==false">
          </a>
        </div>
        <div class="ant-modal-body">
          <collapse>
            <div v-show="isActive8">
              <div style="padding:0px;">
              <div class="table-responsive">
                <table class="table table-bordered table-striped">
                  <thead>
                    <tr>
                      <th>序號</th>
                      <th>簽核時間</th>
                      <th>簽核節點</th>
                      <th>簽核主管</th>
                      <th>簽核意見</th>
                      <th>批註</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="(item, index) in formData.tQhChargelogs" :key="index">
                      <th>{{index+1}}</th>
                      <th>{{item.createtime|formatDate('yyyy-MM-dd HH:mm:ss')}}</th>
                      <th>{{item.chargenode}}</th>
                      <th>{{item.chargename}}</th>
                      <th>{{item.ispass}}</th>
                      <th>{{item.decrib}}</th>
                    </tr>
                  </tbody>
                </table>
              </div>
              </div>
            </div>
          </collapse>
        </div>
        <div class="ant-modal-body" style="text-align: center;margin-top: 10px;">
          <el-form-item label="">
            <el-input v-model="formData.comment" type="textarea" rows="3" :style="{width: '90%'}"
                      :placeholder="$t('table.activity.inputApprovalOpinions')"></el-input>
          </el-form-item>
          <div class="dialog-footer" align="center" style="padding:10px;display: flex;justify-content:space-around;">
            <el-button @click="handleTask(1)" :disabled="isDisable" style="flex:1;background-color: #EC6464;color: white;border-radius: 10px;"  v-if="(this.$store.state.user.empNo)==currentAuditUser && formData.nodeName!='DBA作業' && formData.nodeName != null && formData.nodeName != undefined">
              {{$t('table.activity.rejection')}}
            </el-button>
            <el-button @click="skipTask" :disabled="isDisable" style="flex:1;background-color:#4084FF;color: white;border-radius: 10px;margin: 0 10px" v-if="this.$store.state.user.empNo==currentAuditUser && formData.nodeName!='DBA作業' && formData.nodeName != null && formData.nodeName != undefined">跳過</el-button>
            <el-button @click="handleTask(0)" :disabled="isDisable" style="flex:1;background-color:#01D4CB;color: white;border-radius: 10px"  v-if="this.$store.state.user.empNo==currentAuditUser && formData.nodeName!='DBA作業' && formData.nodeName != null && formData.nodeName != undefined">
              {{$t('table.activity.pass')}}
            </el-button>
            <el-button @click="handleTask(0)" :disabled="isDisable" style="flex:1;background-color:#01D4CB;color: white;border-radius: 10px"  v-if="this.$store.state.user.empNo==currentAuditUser && formData.nodeName=='DBA作業' && formData.nodeName != null && formData.nodeName != undefined">確認辦理</el-button>
<!--            <el-button @click="returnForm" :style="{width: '80px'}">返回</el-button>-->
          </div>
        </div>
      </el-form>
    </div>
<!--  </page-top-bar-esign>-->
</template>
<script>
import {getAccessToken} from '@/utils/auth';
import {showDetail, completeTask, allFactorys, getNodeInfo, skipTask} from "@/api/caaesign/wfonlineprocess";
import {auditComplete} from "@/api/caaesign/common";
import collapse from "@/utils/collapse.js";
import imgUrlArrowhead from '@/assets/images/t02.png';
import imgUrlArrowhead2 from '@/assets/images/arrow.svg';
import '@/assets/styles/design-build/design-add-view.scss'
import {previewFile, getHeader} from "@/utils/entfrm";
export default {
  components: {
    collapse
  },
  props: [],
  data() {
    return {
      imgUrlArrowhead:imgUrlArrowhead,
      imgUrlArrowhead2:imgUrlArrowhead2,
      formData: {
        chargeNodeInfo:undefined,
        fileName:undefined,
        nodeName:undefined,
        nodeOrder:undefined,
        processId:undefined,
        tQhChargelogs:[{
          chargename:undefined,
          createtime:undefined,
          ispass:undefined,
          decrib:undefined,
          chargenode:undefined,
        }],
        wfonlineitems:[{
          modulename:undefined,
          onlineDate:undefined,
          onlineUrl:undefined,
          xtkfDriName:undefined,
        }],
        wfonlineprocess:{
          serialno:undefined,
          applyno:undefined,
          applyname:undefined,
          applytel:undefined,
          applyfactoryname:undefined,
          applydepartname:undefined,
          systemname:undefined,
          requiretypename:undefined,
          pttestname:undefined,
          ptUser:undefined,
          uattestname:undefined,
          uatUser:undefined,
          ptDateStart:undefined,
          ptDateEnd:undefined,
          uatDateStart:undefined,
          uatDateEnd:undefined,
          resourcesname:undefined,
          requirefactoryname:undefined,
          requireFloor:undefined,
          requireCompleteDate:undefined,
          kfqrFactory:undefined,
          kfqrfactoryname:undefined,
          kfqrFloor:undefined,
          kfqrCompleteDate:undefined,
          requireDetail:undefined,
          systemTypeOther:undefined,
          bpmpz:undefined,
          dbapz:undefined,
        },
        wfonlinerelates:[{
          glztname:undefined,
          onlineUrl:undefined,
          xtkfDriName:undefined,
          xtkfkjname:undefined,
          xtkfbjname:undefined,
        }],
      },
      rules:{
        wfonlineprocess_kfqrFactory: [{required: true, message: '請選擇廠區', trigger: 'change'}],
        wfonlineprocess_kfqrFloor: [{required: true, message: '請選擇樓層', trigger: 'change'}],
        wfonlineprocess_systemTypeOther: [{required: true, message: '請輸入', trigger: 'blur'}],
        wfonlineprocess_kfqrCompleteDate: [{required: true, message: '請選擇正式環境完成日期', trigger: 'change'}],
        wfonlineprocess_bpmpz:[{required: true, message: "請輸入補充說明", trigger: "blur" }],
        wfonlineprocess_dbapz:[{required: true, message: "請輸入服務器資源評估說明", trigger: "blur" }],
      },
      tableHeight: document.documentElement.scrollHeight - 245 + "px",
      // 审批意见数据
      commentList: [],
      //簽核路徑
      signPath: [],
      activeCount: -1,
      signPathActive: -1,
      //任务图url
      imgUrl: '',
      isDisable: false,
      isDisable2: true,
      currentAuditUser:undefined,
      // 是否显示任务图
      showImgDialog: false,
      auditStatus: [],
      // 文件上传参数
      upload: {
        // 是否显示弹出层
        open: false,
        fileList: [],
        fileNameList: [],
        // 弹出层标题
        title: "",
        multiple: false,
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: getHeader(),
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/fileInfo/upload"
      },
      makerfactoryidOptions: [],
      makerfactoryidValue: null,
      userApplyTypeOptions: [],
      entRemoteEmpowerItemsActiveNames: ["1"],
      entRemoteEmpowerItemsMainActiveNames: [""],
      isMobile: true,
      labelPosition: 'left',
      isActive: true,
      isActive2: true,
      isActive3: true,
      isActive4: true,
      isActive5: false,
      isActive6: true,
      isActive7: true,
      isActive8: true,
    }
  },
  filters:{
    formatDate: function(value,args) {
      var dt = new Date(value);
      if(args == 'yyyy-M-d') {// yyyy-M-d
        let year = dt.getFullYear();
        let month = dt.getMonth() + 1;
        let date = dt.getDate();
        return `${year}-${month}-${date}`;
      } else if(args == 'yyyy-M-d H:m:s'){// yyyy-M-d H:m:s
        let year = dt.getFullYear();
        let month = dt.getMonth() + 1;
        let date = dt.getDate();
        let hour = dt.getHours();
        let minute = dt.getMinutes();
        let second = dt.getSeconds();
        return `${year}-${month}-${date} ${hour}:${minute}:${second}`;
      } else if(args == 'yyyy-MM-dd') {// yyyy-MM-dd
        let year = dt.getFullYear();
        let month = (dt.getMonth() + 1).toString().padStart(2,'0');
        let date = dt.getDate().toString().padStart(2,'0');
        return `${year}-${month}-${date}`;
      } else {// yyyy-MM-dd HH:mm:ss
        let year = dt.getFullYear();
        let month = (dt.getMonth() + 1).toString().padStart(2,'0');
        let date = dt.getDate().toString().padStart(2,'0');
        let hour = dt.getHours().toString().padStart(2,'0');
        let minute = dt.getMinutes().toString().padStart(2,'0');
        let second = dt.getSeconds().toString().padStart(2,'0');
        return `${year}-${month}-${date} ${hour}:${minute}:${second}`;
      }
    }
  },
  computed: {},
  watch: {},
  created() {
    const serialno = this.$route.query.serialno;
    this.changeTagsView(this.$route.query);
    this.isMobile = this.isMobileFun()
    if (serialno != null && serialno != undefined) {
      if (getAccessToken()) {
        showDetail(serialno).then(response => {
          this.formData = JSON.parse(response.data);
        });
        getNodeInfo(serialno).then(response => {
          this.currentAuditUser = JSON.parse(response.data).auditUser;
        });
      }else{
        this.$router.push( '/caaesign/wfonlineprocessApp/404App')
      }
    }
    this.getMakerfactoryidOptions()
    this.getDicts("sign_status").then(response => {
      this.auditStatus = response.data;
    });
    ESignApp.onPageFinished()
  },
  methods: {
    closeForm() {
      //关闭子页面
      ESignApp.back()
    },
    returnNext(){
      auditComplete(this.formData.wfonlineprocess.serialno)
    },
    returnForm(){
      ESignApp.back()
    },
    onChange_kfqrFloor(){
      if(this.formData.nodeName=='系統開發工程師確認'&&this.formData.wfonlineprocess.kfqrFloor=='other'&&(this.formData.wfonlineprocess.systemTypeOther==''||this.formData.wfonlineprocess.systemTypeOther==undefined)){
        alert("請輸入樓層！")
        this.isDisable2 = false;
      }else{
        this.isDisable2 = true;
      }
    },
    handleTrack() {
      this.imgUrl = process.env.VUE_APP_BASE_API + '/activiti/task/track/' + this.formData.processId,
        this.showImgDialog = true
    },
    statusFormat(row, column) {
      return this.selectDictLabel(this.auditStatus, row.status);
    },
    skipTask:function(){
      skipTask(this.formData.wfonlineprocess.serialno).then(response => {
        this.$message({
          message: this.$t('tips.operationSuccessful'),
          type: 'success',
          iconClass: ' ',
          center:true,
          offset: window.screen.height / 2,
          customClass: 'msgbox'
        });
        this.returnNext();
      });
    },
    handleTask: function(pass) {
      if(pass == 0){
        this.$refs["elForm"].validate(valid => {
          this.onChange_kfqrFloor();
          if (valid && this.isDisable2) {
            this.isDisable = true;
            this.formData.pass = pass
            this.formData.serialno=this.formData.wfonlineprocess.serialno;
            if(this.formData.nodeName=='BPM簽核'){
              this.formData.bpmpz=this.formData.wfonlineprocess.bpmpz;
            } else if(this.formData.nodeName=='系統開發工程師確認'){
              this.formData.kfqrFactory=this.formData.wfonlineprocess.kfqrFactory;
              this.formData.kfqrCompleteDate=this.formData.wfonlineprocess.kfqrCompleteDate
              if(this.formData.wfonlineprocess.systemTypeOther!=''&&this.formData.wfonlineprocess.systemTypeOther != undefined && this.formData.wfonlineprocess.systemTypeOther != "undefined"){
                this.formData.kfqrFloor=this.formData.wfonlineprocess.systemTypeOther;
              }else {
                this.formData.kfqrFloor = this.formData.wfonlineprocess.kfqrFloor;
              }
            }else if(this.formData.nodeName=='DBA作業人員'){
              this.formData.dbapz=this.formData.wfonlineprocess.dbapz;
            }
            completeTask(this.formData).then(response => {
              if (response.code === 0) {
               /* this.msgSuccess(this.$t('tips.operationSuccessful'));*/
               /* this.$message.success(this.$t('tips.operationSuccessful'))*/
                this.$message({
                  message: this.$t('tips.operationSuccessful'),
                  type: 'success',
                  iconClass: ' ',
                  center:true,
                  offset: window.screen.height / 2,
                  customClass: 'msgbox'
                });
                this.returnNext();
              } else {
                this.msgError(response.msg);
              }
            });
          }
        });
      }else if(pass == 1 && this.formData.comment != null){
        this.isDisable = true;
        this.formData.pass = pass
        this.formData.serialno=this.formData.wfonlineprocess.serialno;
        completeTask(this.formData).then(response => {
          if (response.code === 0) {
            /*this.msgSuccess(this.$t('tips.operationSuccessful'));*/
            this.$message({
              message: this.$t('tips.operationSuccessful'),
              type: 'success',
              iconClass: ' ',
              center:true,
              offset: window.screen.height / 2,
              customClass: 'msgbox'
            });
            this.returnNext();
          } else {
            this.msgError(response.msg);
          }
        });
      }else {
        this.isDisable = false;
        this.msgInfo(this.$t('table.activity.inputApprovalOpinions'));
      }
    },
    attachidsBeforeUpload(file) {
      let isRightSize = file.size / 1024 / 1024 < 2
      if (!isRightSize) {
        this.$message.error('文件大小超过 2MB')
      }
      return isRightSize
    },
    // 文件上传成功处理
    uploadsucces(response, file, fileList) {
      if (response.data.name != undefined && response.data.name != "undefined") {
        this.upload.fileList.push({
          name: file.name,
          url: response.data.name
        });
        this.formData.attachids += response.data.name + ",";
      }
    },
    handleChange(file, fileList) {},
    handleExceed(file, fileList) {},
    handleRemove(file, fileList) {
      this.$emit("delUploadImage", file.name);
      const index = this.upload.fileList.indexOf(file);
      this.upload.fileList.splice(index, 1);
      if (this.formData.attachids) {
        this.formData.attachids = this.formData.attachids.replace(file.url + ",", "");
      }
      this.upload.fileNameList.push(file.url);
      // delFileInfo(file.url);
    },
    handlePreview(file) {
      previewFile(file.url)
    },
    checkBoxParse() {},
    cascaderParse() {},
    handleAdd_ent_remote_empower_items() {
      const cloumn = {
        remarks: '',
        makerdeptno: '',
        makerfactoryid: '',
        userEmpNo: '',
        userEmpName: '',
        userProcess: '',
        userApplyType: '',
        userApplyCause: ''
      };
      this.formData.entRemoteEmpowerItemsLists.splice(this.formData.entRemoteEmpowerItemsLists.length, 0,
        cloumn);
      for (let index in this.formData.entRemoteEmpowerItemsLists) {
        this.formData.entRemoteEmpowerItemsLists[index].sort = parseInt(index) + 1;
      }
    },
    handleDel_ent_remote_empower_items(index, row) {
      let functionName = this.$t(
        'ent_remote_empowerment_8040b6a9d6905ebfb822b67dd57f1936.default.functionName');
      this.$confirm(this.$t('tips.deleteConfirm', ['產線遠端賦權賬號申請單明細表', row.sort]), this.$t('tips.warm'), {
        confirmButtonText: this.$t("common.confirmTrim"),
        cancelButtonText: this.$t("common.cancelTrim"),
        type: "warning",
      }).then(() => {
        this.formData.entRemoteEmpowerItemsLists.splice(index, 1);
        this.$message({
          showClose: true,
          message: this.$t("tips.deleteSuccess"),
          type: "success",
          offset: 50,
        });
      }).catch(function(err) {
        console.log(err);
      });
    },
    getMakerfactoryidOptions() {
      allFactorys().then(response => {
        this.makerfactoryidOptions=JSON.parse(response.data);
      });
    },
    getUserApplyTypeOptions() {
      this.getDicts("user_apply_type").then(response => {
        this.userApplyTypeOptions = response.data;
        this.userApplyTypeOptions.forEach(item => {
          this.formData.entRemoteEmpowerItemsLists.forEach((selectInfo, n) => {
            if (item.value == selectInfo.userApplyType) {
              this.formData.entRemoteEmpowerItemsLists[n].userApplyType = item.label
            }
          })
        })
      });
    },
  }
}

</script>
<style scoped lang="scss">
.el-form-item {
  margin-top: 0px;
  margin-bottom: 0px;
}
.space{
  height: 15px;
  background-color: #F7F7F7;
}
.course-head {
  font-size: 15px;
  color: #1E2233;
  font-weight: bold;
}
.rating__text{
  font-weight:normal;
  width:4px;
  height:17px;
  background: #4B8AF8;
  border-radius: 4px;
  float:left;
  margin-top: 11px;
  margin-right: 10px;
}
.form-info{
  background-color:#EAEEFF;
  padding-left:15px;
  line-height: 38px;
}
.imgUrlArrowhead{
  float:right;
  margin-top: 13px;
  width: 16px;
  height: 16px;
  margin-right: 14px;
  transform: scaleX(-1) rotate(-90deg);
}
.imgUrlArrowhead2{
  float:right;
  margin-top: 13px;
  width: 16px;
  height: 16px;
  margin-right: 14px;
  transform: scaleX(-1) rotate(90deg);
}
.ant-modal-body {
  padding: 0px;
  font-size: 15px;
  word-wrap: break-word
}
.profile-info-table {
  font-size: 14px;
  color: #06142D;
  border-radius: 10px;
  background-color: #FFFFFF;
  font-family: Microsoft YaHei;

  .profile-info-block {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 11px 15px;
    border-bottom: 1px solid #F7F7F7;
    >div:nth-child(1){
      flex-basis: 35%;
      color: #57585A;
    }
    >div:nth-child(2){
      flex-basis: 65%;
      text-align: right;
    }
  }
  .profile-info-block__value a svg {
    width: 15px;
    height: 15px;
  }
  .content-medium-block{
    display: flex;
    align-items: center;
    padding: 11px 15px;
    border-bottom: 1px solid #F7F7F7;
    div{
      flex: 1;
      span{
        color: #57585A;
      }
    }
  }
}

/*=====  End of profile  ======*/
.el-button--success{
  color: #fff;
  background-color: #1E80F9;
  border-color: #1E80F9;

}
.el-button--medium{
  border-radius: inherit;
  font-family: Microsoft YaHei;
  font-size: 14px;
  padding:15px 0px;
}

.el-button+.el-button {
  margin-left: 0;
  margin-top: 0px;
}

.el-button--success.is-disabled, .el-button--success.is-disabled:hover, .el-button--success.is-disabled:focus, .el-button--success.is-disabled:active {
  color: #fff;
  background-color: #B4D6FF;
  border-color: #B4D6FF;
}
::v-deep .el-form-item__error {
  color: #ff4949;
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
  position:absolute;
  top: 100%;
  right: 0;
}
/*-------------*/
.table-responsive {
  min-height: .01%;
  overflow-x: auto;
  .table {
    width: 100%;
    font-size: 14px;
    td,th {
      text-align: center;
      padding: 8px;
      border: 1px solid #ddd;
      white-space: nowrap
    }
    tbody>tr:nth-of-type(odd) {
      background-color: #f9f9f9
    }
  }
}

.file-style{
  padding:0px 16px 10px 16px;
}
/deep/.file-style .el-upload{
  display: none;
}
.text-area-style{
  display: flex;
  align-items: center;
}
.text-area-type /deep/.el-textarea__inner{
  border: 0px;
  padding: 0px;
  vertical-align: center;
  color: black;
  overflow-y: scroll;
}
.scroll-box{
  height: 100%;
  overflow-y:scroll;
  -webkit-overflow-scrolling: touch;
  overflow-x: hidden;
  overflow-wrap: anywhere;
  white-space: pre-wrap;
}
/* 针对 iOS 设备 */
@supports (-webkit-overflow-scrolling: touch) {
  .scroll-box {
    height: 100%;
    overflow-y:scroll;
    -webkit-overflow-scrolling: touch;
    overflow-x: hidden;
    overflow-wrap: anywhere;
    white-space: pre-wrap;
  }
}
.content-small,.content-medium{
  display: none;
}
/* 小屏幕設備 */
@media (max-width: 767px){
  .content-small{
    display: block;
  }

}
/* 中等屏幕設備 */
@media (min-width: 768px){
  .content-medium{
    display: block;
  }
}
</style>
<style>
.msgbox {
  min-width: 120px !important;
  background-color: #D7D7D7;
}
.el-message--success {
  background-color: #D7D7D7;
  border: 0px;
}

.el-message .el-icon-success {
  font-size: 14px;
  border-color: #D7D7D7;
}

.el-message--success .el-message__content {
  font-size: 14px;
  color:black !important;
}
</style>
