<template>
  <div class="ant-modal-content">
    <div class="ant-modal-body">
      <el-form ref="elForm" class="ant-form ant-form-horizontal" :model="formData" :rules="rules"
        size="medium" label-width="100px" :label-position="labelPosition">
        <div class="ant-card ant-card-bordered" id="staffCard">
          <div class="ant-card-body" id="printContent">
            <el-row :gutter="15">
              <span id="staffEvectionTitle" style="margin-bottom: 10px">園區周邊輔助維修/機電維修委託單</span>
              <el-col :span="10" :xs="24" class="el-col-no-border" id="serialno">
                任務編碼:{{formData.serialno?formData.serialno:'提交表單后自動生成'}}
              </el-col>
              <el-col :span="8" :xs="24" class="el-col-no-border" id="createTime">
                填單時間:{{formData.createTime}}
              </el-col>
              <el-col :span="6" :xs="24" class="el-col-no-border" id="makerNo">
                填單人:{{formData.makerNo +' / '+ formData.makerName}}
              </el-col>
              <el-col :span="24" :xs="24" class="talbe-name-style">
                委託人基本信息
              </el-col>
              <el-col :span="6" :xs="24">
                <el-form-item label-width="110px" label="委託人工號" prop="applyEmpNo">
                  {{formData.applyEmpNo}}
                </el-form-item>
              </el-col>
              <el-col :span="6" :xs="24">
                <el-form-item label="委託人" prop="applyEmpName">
                  {{formData.applyEmpName}}
                </el-form-item>
              </el-col>
              <el-col :span="6" :xs="24">
                <el-form-item label="聯絡電話" prop="applyTel">
                  {{formData.applyTel}}
                </el-form-item>
              </el-col>
              <el-col :span="6" :xs="24">
                <el-form-item label="廠區" prop="makerfactoryid">
                  {{formData.makerfactoryid}}
                </el-form-item>
              </el-col>
              <el-col :span="6" :xs="24">
                <el-form-item label-width="110px" label="費用代碼" prop="costNo">
                  {{formData.costNo}}
                </el-form-item>
              </el-col>
              <el-col :span="6" :xs="24">
                <el-form-item label="部門代碼" prop="applyDeptNo">
                  {{formData.applyDeptNo}}
                </el-form-item>
              </el-col>
              <el-col :span="12" :xs="24">
                <el-form-item label="部門名稱" prop="applyDeptName">
                  {{formData.applyDeptName}}
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24" class="talbe-name-style">
                委託維修基本資料
              </el-col>
              <el-col :span="24" :xs="24">
                <el-form-item label-width="110px" label="委託單名稱" prop="trustDeedName">
                  {{formData.trustDeedName}}
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24">
                <el-form-item label-width="110px" label="維修類別" prop="repairType">
                  {{formData.repairType}}
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24">
                <el-form-item label-width="110px" label="緊急度" prop="urgencyDegree">
                  {{formData.urgencyDegree}}
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24">
                <el-form-item label-width="110px" label="維修主體用途" prop="repairPurpose">
                  {{formData.repairPurpose}}
                </el-form-item>
              </el-col>
              <el-col :span="8" :xs="24">
                <el-form-item label-width="110px" label="期望開工時間" prop="expectDateStart">
                  <el-date-picker type="datetime" v-model="formData.expectDateStart"
                    format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" :style="{width: '100%'}"
                    placeholder="请選擇期望開工時間" clearable :disabled='true'></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="8" :xs="24">
                <el-form-item label-width="110px" label="期望完工時間" prop="expectDateEnd">
                  <el-date-picker type="datetime" v-model="formData.expectDateEnd"
                    format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" :style="{width: '100%'}"
                    placeholder="请選擇期望完工時間" clearable :disabled='true'></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="8" :xs="24">
                <el-form-item label="維修地點" prop="repairPlace">
                  {{formData.repairPlace}}
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24">
                <el-form-item label-width="110px" label="維修委託說明" prop="repairTrustInfo">
                  {{formData.repairTrustInfo}}
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24" class="print-hide-div">
                <el-form-item label-width="110px" label="申請附件" prop="attachids">
                  <el-upload :file-list="upload.fileList" :show-file-list="true" :on-preview="handlePreview"
                    :headers="upload.headers" :action="upload.url" list-type="text" :disabled='true'>
                    <el-button size="small" type="primary" icon="el-icon-upload" :disabled='true'>點擊上傳文件
                    </el-button>
                  </el-upload>
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24" class="talbe-name-style">
                自制維修材料明細
              </el-col>

                <table class="table-style">
                  <tr>
                    <td>項次</td>
                    <td>材料名稱</td>
                    <td>規格</td>
                    <td>單位</td>
                    <td>數量</td>
                    <td>備註</td>
                  </tr>
                  <tr>
                    <td>1</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                  </tr>
                  <tr>
                    <td>2</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                  </tr>
                  <tr>
                    <td>3</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                  </tr>
                  <tr>
                    <td>4</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                  </tr>
                  <tr>
                    <td>5</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                  </tr>
                  <tr>
                    <td>6</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                  </tr>
                  <tr>
                    <td>7</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                  </tr>
                  <tr>
                    <td>8</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                  </tr>
                  <tr>
                    <td colspan="3" style="text-align: left;padding-left: 20px">需求單位：</td>
                    <td colspan="3" style="text-align: left;padding-left: 20px">維修人員：</td>
                  </tr>
                </table>
              <el-col :span="24" :xs="24" class="print-hide-div">
                <div class="dialog-footer" align="center" style="padding-top: 5px;padding-bottom: 5px">
                  <el-button type="danger" @click="closeForm">{{$t('common.close')}}</el-button>
                  <el-button type="primary" v-print="'#printContent'" >打印</el-button>
                  <el-button type="info" @click="handleTrack"
                    v-if="formData.workStatus == 2||formData.workStatus ==3||formData.workStatus ==4">
                    {{$t('table.activity.flowTracing')}}</el-button>
                </div>
              </el-col>

            <!-- 簽核線 -->
            <el-col :span="24" class="print-hide-div">
              <div class="dialog-footer" style="padding-top: 5px;padding-bottom: 5px">
                <div v-html="signPath"></div>
              </div>
            </el-col>
            <!-- 审核记录 -->
            <el-table border stripe :data="commentList" class="table-max">
              <el-table-column :label="$t('table.activity.taskId')" align="center" prop="id" />
              <el-table-column :label="$t('table.activity.approvedBy')" align="center" prop="userId"
                :show-overflow-tooltip="true" />
              <el-table-column :label="$t('table.activity.approvalOpinions')" align="center"
                prop="fullMessage" :show-overflow-tooltip="true" />
              <el-table-column :label="$t('table.activity.operIp')" align="center" prop="operIp"
                :show-overflow-tooltip="true" />
              <el-table-column :label="$t('table.activity.auditStatus')" align="center" prop="status"
                :formatter="statusFormat" :show-overflow-tooltip="true" />
              <el-table-column :label="$t('table.activity.approvalTime')" align="center" prop="time"
                width="180">
                <template slot-scope="scope">
                  <span>{{ scope.row.time }}</span>
                </template>
              </el-table-column>
            </el-table>
            </el-row>
            <!-- 任务跟踪对话框 -->
            <el-dialog :title="$t('table.activity.taskMap')" :visible.sync="showImgDialog" width="760px">
              <img :src="imgUrl" style="padding-bottom: 60px;">
            </el-dialog>
          </div>
        </div>
      </el-form>
    </div>
  </div>
</template>
<script>
import {
  getEntAssistRepair,
  addEntAssistRepair,
  editEntAssistRepair,
  getSignPath
}
from "@/api/caaesign/entAssistRepair"
import '@/assets/styles/design-build/design-add-view.scss'
import {
  listTask,
  getTask,
  checkTask,
  taskComment
}
from "@/api/activiti/task"
import {
  getAccessToken,
  getZltAccessToken
}
from "@/utils/auth";
import {
  previewFile,
  getHeader, previewFileOos
}
from "@/utils/entfrm";
import {
  listFileInfo,
  getFileInfo,
  delFileInfo,
  addFileInfo,
  editFileInfo,
  getByKey
}
from "@/api/system/fileInfo";
export default {
  components: {},
  props: [],
  data() {
    return {
      formData: {
        applyEmpNo: undefined,
        applyEmpName: undefined,
        applyTel: undefined,
        makerfactoryid: undefined,
        costNo: undefined,
        applyDeptNo: undefined,
        applyDeptName: undefined,
        trustDeedName: undefined,
        repairType: undefined,
        urgencyDegree: undefined,
        repairPurpose: undefined,
        expectDateStart: '',
        expectDateEnd: '',
        repairPlace: undefined,
        repairTrustInfo: undefined,
        attachids: "",
      },
      rules: {
        applyEmpNo: [{
          required: true,
          message: '請輸入委託人工號',
          trigger: 'blur'
        }],
        applyEmpName: [{
          required: true,
          message: '請輸入委託人',
          trigger: 'blur'
        }],
        applyTel: [{
          required: true,
          message: '請輸入聯絡電話',
          trigger: 'blur'
        }],
        makerfactoryid: [{
          required: true,
          message: '請選擇廠區',
          trigger: 'change'
        }],
        costNo: [{
          required: true,
          message: '請輸入費用代碼',
          trigger: 'blur'
        }],
        applyDeptNo: [{
          required: true,
          message: '請輸入部門代碼',
          trigger: 'blur'
        }],
        applyDeptName: [{
          required: true,
          message: '請輸入部門名稱',
          trigger: 'blur'
        }],
        trustDeedName: [{
          required: true,
          message: '請輸入委託單名稱',
          trigger: 'blur'
        }],
        repairType: [{
          required: true,
          message: '維修類別不能為空',
          trigger: 'change'
        }],
        urgencyDegree: [{
          required: true,
          message: '緊急度不能為空',
          trigger: 'change'
        }],
        repairPurpose: [{
          required: true,
          message: '請輸入維修主體用途',
          trigger: 'blur'
        }],
        expectDateStart: [{
          required: true,
          message: '请選擇期望開工時間',
          trigger: 'change'
        }],
        expectDateEnd: [{
          required: true,
          message: '请選擇期望完工時間',
          trigger: 'change'
        }],
        repairPlace: [{
          required: true,
          message: '請輸入維修地點',
          trigger: 'blur'
        }],
        repairTrustInfo: [{
          required: true,
          message: '請輸入維修委託說明',
          trigger: 'blur'
        }],
        attachids: [],
      },
      tableHeight: document.documentElement.scrollHeight - 245 + "px",
      // 审批意见数据
      commentList: [],
      //簽核路徑
      signPath: [],
      //任务图url
      imgUrl: '',
      isDisabled: false,
      // 是否显示任务图
      showImgDialog: false,
      auditStatus: [],
      // 文件上传参数
      upload: {
        // 是否显示弹出层
        open: false,
        fileList: [],
        fileNameList: [],
        data: {workflowKey: "dzqh_assistRepairEntrustForm"},
        // 弹出层标题
        title: "",
        multiple: false,
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: getHeader(),
        // 上传的地址
        url: this.getAppBaseApi() + "/system/fileInfo/uploadWithOos"
      },
      makerfactoryidOptions: [],
      repairTypeOptions: [{
        "createBy": "S6073061",
        "createTime": "2021-08-10 10:52:57",
        "updateBy": null,
        "updateTime": "2021-08-10 10:52:15",
        "remarks": null,
        "id": "a16e3f24c63f47fb5784e9d7b608ec64",
        "dictType": "caaesign_repair_type",
        "label": "輔助維修",
        "value": "1",
        "sort": 0,
        "beginDate": null,
        "endDate": null,
        "beginSignDate": null,
        "endSignDate": null
      }, {
        "createBy": "S6073061",
        "createTime": "2021-08-10 10:53:11",
        "updateBy": null,
        "updateTime": "2021-08-10 10:52:29",
        "remarks": null,
        "id": "dd740b4baa405f8727600556f98b602f",
        "dictType": "caaesign_repair_type",
        "label": "機電維修",
        "value": "2",
        "sort": 1,
        "beginDate": null,
        "endDate": null,
        "beginSignDate": null,
        "endSignDate": null
      }],
      urgencyDegreeOptions: [{
        "createBy": "S6073061",
        "createTime": "2021-08-10 10:54:10",
        "updateBy": null,
        "updateTime": "2021-08-10 10:53:28",
        "remarks": null,
        "id": "86d42559072a9ff2ab2c2d724168c3dc",
        "dictType": "caaesign_urgency_degree",
        "label": "正常",
        "value": "1",
        "sort": 0,
        "beginDate": null,
        "endDate": null,
        "beginSignDate": null,
        "endSignDate": null
      }, {
        "createBy": "S6073061",
        "createTime": "2021-08-10 10:54:30",
        "updateBy": null,
        "updateTime": "2021-08-10 10:53:48",
        "remarks": null,
        "id": "39563da742d130f02ffaba468cb7fa36",
        "dictType": "caaesign_urgency_degree",
        "label": "緊急",
        "value": "2",
        "sort": 1,
        "beginDate": null,
        "endDate": null,
        "beginSignDate": null,
        "endSignDate": null
      }, {
        "createBy": "S6073061",
        "createTime": "2021-08-10 10:54:48",
        "updateBy": null,
        "updateTime": "2021-08-10 10:54:06",
        "remarks": null,
        "id": "33954d12baf26266795bd6cb9b5adb82",
        "dictType": "caaesign_urgency_degree",
        "label": "其他",
        "value": "3",
        "sort": 2,
        "beginDate": null,
        "endDate": null,
        "beginSignDate": null,
        "endSignDate": null
      }],
      isMobile: false,
      labelPosition: 'left',
    }
  },
  computed: {},
  watch: {},
  created() {
    const id = this.$route.query.id;
    this.changeTagsView(this.$route.query);
    this.isMobile = this.isMobileFun()
    if (this.isMobile) {
      this.labelPosition = 'top'
    }
    if (id != null && id != undefined) {
      getEntAssistRepair(id).then(response => {
        this.formData = response.data;
        this.checkBoxParse()
        this.cascaderParse()
        taskComment(this.formData.processId).then(response => {
          this.commentList = response.data;
        });
        getSignPath(this.formData.processId).then(response => {
          this.signPath = response.data;
        });
        if (this.formData.attachids) {
          let a = this.formData.attachids.split(',');
          if (a.length > 0) {
            a.forEach(item => {
              if (item) {
                getByKey(item).then(response => {
                  this.upload.fileList.push({
                    name: response.data.orignalName,
                    url: response.data.name
                  });
                })
              }
            })
          }
        }
      });
    }
    this.getMakerfactoryidOptions()
    this.getRepairTypeOptions()
    this.getUrgencyDegreeOptions()
    this.getDicts("sign_status").then(response => {
      this.auditStatus = response.data;
    });



  },
  mounted() {
    this.$nextTick(function() {
      if (this.isMobile) {}
    })
  },
  methods: {
    closeForm() {
      //关闭子页面
      if (this.$store.state.settings.tagsView) {
        this.$router.go(-1) // 返回
        this.$store.state.tagsView.visitedViews.splice(this.$store.state.tagsView.visitedViews.findIndex(
          item => item.path === this.$route.path), 1)
        this.$router.push(this.$store.state.tagsView.visitedViews[this.$store.state.tagsView.visitedViews
          .length - 1].path)
      }
      else {
        // parent.postMessage("closeCurrentTabMessage",'*');
        parent.postMessage("closeCurrentTabMessage", '*');
      }
    },
    handleTrack() {
      this.imgUrl = process.env.VUE_APP_BASE_API + '/activiti/task/track/' + this.formData.processId,
        this.showImgDialog = true
    },
    statusFormat(row, column) {
      return this.selectDictLabel(this.auditStatus, row.status);
    },
    getMakerfactoryidOptions() {
      this.getDicts("caaesign_factory").then(response => {
        this.makerfactoryidOptions = response.data;
        this.getDicts("caaesign_factory").then(response => {
          let _self = this;
          response.data.forEach(function(item) {
            _self.formData.makerfactoryid = _self.formData.makerfactoryid.replace(item.value, item.label);
          })
        });
      });
    },
    getRepairTypeOptions() {
      this.getDicts("caaesign_repair_type").then(response => {
        this.repairTypeOptions = response.data;
        this.getDicts("caaesign_repair_type").then(response => {
          let _self = this;
          response.data.forEach(function(item) {
            _self.formData.repairType = _self.formData.repairType.replace(item.value, item.label);
          })
        });
      });
    },
    getUrgencyDegreeOptions() {
      this.getDicts("caaesign_urgency_degree").then(response => {
        this.urgencyDegreeOptions = response.data;
        this.getDicts("caaesign_urgency_degree").then(response => {
          let _self = this;
          response.data.forEach(function(item) {
            _self.formData.urgencyDegree = _self.formData.urgencyDegree.replace(item.value, item.label);
          })
        });
      });
    },
    attachidsBeforeUpload(file) {
      let isRightSize = file.size / 1024 / 1024 < 2
      if (!isRightSize) {
        this.$message.error('文件大小超过 2MB')
      }
      return isRightSize
    },
    // 文件上传成功处理
    uploadsucces(response, file, fileList) {
      if (response.data.name != undefined && response.data.name != "undefined") {
        this.upload.fileList.push({
          name: file.name,
          url: response.data.name
        });
        this.formData.attachids += response.data.name + ",";
      }
    },
    handleChange(file, fileList) {},
    handleExceed(file, fileList) {},
    handleRemove(file, fileList) {
      this.$emit("delUploadImage", file.name);
      const index = this.upload.fileList.indexOf(file);
      this.upload.fileList.splice(index, 1);
      this.formData.attachids = this.formData.attachids.replace(file.url + ",", "");
      this.upload.fileNameList.push(file.url);
      // delFileInfo(file.url);
    },
    handlePreview(file) {
      previewFileOos(file.url)
    },
    checkBoxParse() {},
    cascaderParse() {},
  }
}

</script>
<style scoped>
.el-upload__tip {
  line-height: 1.2;
}

@media print {
  .print-hide-div {
    display: none;
  }
}

.del-handler {
  font-size: 20px;
  color: #ff4949;
}

.el-dialog {
  position: relative;
  margin: 0 auto 0px;
  background: #FFFFFF;
  border-radius: 2px;
  -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 50%;
  height: 60%;
}

.el-dialog__body {
  border-top: 1px solid #dcdfe6;
  border-bottom: 1px solid #dcdfe6;
  max-height: 85% !important;
  min-height: 70%;
  overflow-y: auto;
}
.talbe-name-style{
  font-size: 16px;
  font-weight: bold;
  color: #1DB8FF;
  line-height: 50px;
}

@media print{
  .print-hide-div{
    display:none;
  }
  .el-col:not(.el-col-no-border) {
    border: 1px solid #ccc;
    margin-top: -1px;
  }
  #printContent{
    box-sizing: border-box;
    width: 1000px;
  }
  .el-form-item {
    margin-top: 12px;
    margin-bottom: 12px;
  }
  .el-form-item__label{
    text-align: left;
  }
  .el-form-item{
    min-height:36px;
  }
  #serialno,#makerNo,#createTime{
    padding:10px 0;
  }
  *{
    box-sizing: border-box;
  }
  .el-table{
    border: 1px solid #dfe6ec;
  }
  html{
    background-color: #FFFFFF;
    margin: 0;
  }

  .el-table__header{
    table-layout: auto;
  }
  .table-max .el-table__body,.table-max .el-table__header{
    width:100% !important;
  }
  .table-max  col{
    width:calc(100% / 6)  !important;
  }
  .table-max .el-table__body .cell{
    width:100% !important;
  }


}
/*去除页眉页脚*/
@page{
  size:  auto;
  margin: 3mm;
}
  .table-style{
    text-align: center;
    border-collapse: collapse;
    border-spacing: 0;
    width: 100%;
  }
.table-style td{
  border:1px solid #ccc;
  line-height: 30px;
}

</style>
