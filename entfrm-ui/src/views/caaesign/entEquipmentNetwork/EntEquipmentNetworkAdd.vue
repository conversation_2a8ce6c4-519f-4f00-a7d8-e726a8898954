<template>
  <div class="ant-modal-content">
    <div class="ant-modal-body">
      <el-form ref="elForm" class="ant-form ant-form-horizontal" :model="formData" :rules="rules"
               size="medium" label-width="100px" :label-position="labelPosition">
        <div class="ant-card ant-card-bordered" id="staffCard">
          <div class="ant-card-body">
            <span id="staffEvectionTitle" style="margin-bottom: 10px">{{formConfigData.title}}</span>
            <el-col :span="8" :xs="24" class="el-col-no-border">
              任務編碼:{{formData.serialno ? formData.serialno : '提交表單后自動生成'}}
            </el-col>
            <el-col :span="8" :xs="24" class="el-col-no-border"> 填單時間:{{formData.createTime}}
            </el-col>
            <el-col :span="8" :xs="24" class="el-col-no-border">
              填單人:{{formData.makerNo + ' / ' + formData.makerName}}
            </el-col>
            <el-row :gutter="15">
              <el-col :span="24" :xs="24" class="talbe-name-style">
                承辦人基本信息
              </el-col>
              <el-col :span="6" :xs="24">
                <el-form-item label="承辦人工號" prop="applyEmpNo">
                  <el-input v-model="formData.applyEmpNo" placeholder="請輸入承辦人工號" clearable
                            :style="{width: '100%'}" @change='applyEmpNo_getInfoUserByEmpno'></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6" :xs="24">
                <el-form-item label="承辦人" prop="applyEmpName">
                  <el-input v-model="formData.applyEmpName" placeholder="請輸入承辦人" readonly clearable
                            :style="{width: '100%'}"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6" :xs="24">
                <el-form-item label="單位代碼" prop="applyDeptNo">
                  <el-input v-model="formData.applyDeptNo" placeholder="請輸入單位代碼" readonly clearable
                            :style="{width: '100%'}"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6" :xs="24">
                <el-form-item label="費用代碼" prop="applyCostNo">
                  <el-input v-model="formData.applyCostNo" placeholder="請輸入費用代碼" readonly clearable
                            :style="{width: '100%'}"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12" :xs="24">
                <el-form-item label="單位" prop="applyDeptName">
                  <el-input v-model="formData.applyDeptName" placeholder="請輸入單位" clearable
                            :style="{width: '100%'}"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12" :xs="24">
                <el-form-item label="聯繫郵箱" prop="applyMail">
                  <el-input v-model="formData.applyMail" placeholder="請輸入聯繫郵箱" clearable
                            :style="{width: '100%'}"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12" :xs="24">
                <el-form-item label="聯繫方式" prop="applyTel">
                  <el-input v-model="formData.applyTel" placeholder="請輸入聯繫方式" clearable
                            :style="{width: '70%'}"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12" :xs="24">
                <el-form-item label-width="120px" label="廠區/區域/樓棟" prop="makerfactoryid">
                  <el-cascader v-model="formData.makerfactoryid" :options="makerfactoryidOptions" change-on-select
                               :props="makerfactoryidProps" :style="{width: '100%'}" placeholder="請選擇廠區/區域/樓棟" @change="changeMakerfactory"
                               clearable>
                  </el-cascader>
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24" class="talbe-name-style">
                申請信息
              </el-col>
              <div style="overflow-x: auto;width: 100%;">
                <el-scrollbar style="width:130%;">
                  <el-col :span="24" :xs="24" style="padding: 0px;">
                    <el-table v-if="isMobile" border stripe ref="entEquipmentItemsDragTableMobile" :data="formData.entEquipmentItemsLists" row-key="id">
                      <el-table-column label="產線聯網設備入網明細表" type="index" min-width="90%" :width="entEquipmentItemsDragTableMobileClientWidth">
                        <template slot-scope="scope">
                          <span>{{$t('common.serialNumber')}}:{{ scope.$index + 1 }}</span>
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.applyarea'" label="區域">
                            <el-input v-model.trim="scope.row.applyarea" :placeholder="$t('common.placeholderDefault') + '區域'"></el-input>
                          </el-form-item>
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.applybuilding'" label="樓棟">
                            <el-input v-model.trim="scope.row.applybuilding" :placeholder="$t('common.placeholderDefault') + '樓棟'"></el-input>
                          </el-form-item>
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.applyfloor'" label="樓層">
                            <el-input v-model.trim="scope.row.applyfloor" :placeholder="$t('common.placeholderDefault') + '樓層'"></el-input>
                          </el-form-item>
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.applyline'" label="線別">
                            <el-input v-model.trim="scope.row.applyline" :placeholder="$t('common.placeholderDefault') + '線別'"></el-input>
                          </el-form-item>
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.deviceVendors'" label="設備廠商">
                            <el-input v-model.trim="scope.row.deviceVendors" :placeholder="$t('common.placeholderDefault') + '設備廠商'"></el-input>
                          </el-form-item>
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.deviceName'" label="設備名稱">
                            <el-input v-model.trim="scope.row.deviceName" :placeholder="$t('common.placeholderDefault') + '設備名稱'"></el-input>
                          </el-form-item>
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.deviceNumber'" label="設備編號">
                            <el-input v-model.trim="scope.row.deviceNumber" :placeholder="$t('common.placeholderDefault') + '設備編號'"></el-input>
                          </el-form-item>
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.operateSystem'" label="操作系統">
                            <el-input v-model.trim="scope.row.operateSystem" :placeholder="$t('common.placeholderDefault') + '操作系統'"></el-input>
                          </el-form-item>
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.macAddress'" label="MAC地址">
                            <el-input v-model.trim="scope.row.macAddress" :placeholder="$t('common.placeholderDefault') + 'MAC地址'"></el-input>
                          </el-form-item>
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.applyReason'" label="入網理由">
                            <el-input v-model.trim="scope.row.applyReason" :placeholder="$t('common.placeholderDefault') + '入網理由'"></el-input>
                          </el-form-item>
                          <el-button type="text" @click="handleDel_ent_equipment_items(scope.$index, scope.row)" class="del-handler" icon="el-icon-delete"></el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                    <el-table v-else border stripe ref="entEquipmentItemsDragTable" :data="formData.entEquipmentItemsLists" row-key="id" :max-height="tableHeight">
                      <el-table-column label="編號" type="index" min-width="5%"/>
                      <el-table-column label="區域" min-width="10%" prop="applyarea">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.applyarea'" label-width="0px"  :rules="[{ required: true, message: '區域不能为空', trigger: 'blur' }]">
                            <el-input v-model.trim="scope.row.applyarea" :placeholder="$t('common.placeholderDefault') + '區域'"></el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="樓棟" min-width="10%" prop="applybuilding">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.applybuilding'" label-width="0px" :rules="[{ required: true, message: '樓棟不能为空', trigger: 'blur' }]">
                            <el-input v-model.trim="scope.row.applybuilding" :placeholder="$t('common.placeholderDefault') + '樓棟'"></el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="樓層" min-width="10%" prop="applyfloor">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.applyfloor'" label-width="0px" :rules="[{ required: true, message: '樓層不能为空', trigger: 'blur' }]">
                            <el-input v-model.trim="scope.row.applyfloor" :placeholder="$t('common.placeholderDefault') + '樓層'"></el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="線別" min-width="10%" prop="applyline">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.applyline'" label-width="0px" :rules="[{ required: true, message: '線別不能为空', trigger: 'blur' }]">
                            <el-input v-model.trim="scope.row.applyline" :placeholder="$t('common.placeholderDefault') + '線別'"></el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="設備廠商" min-width="10%" prop="deviceVendors">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.deviceVendors'" label-width="0px" :rules="[{ required: true, message: '設備廠商不能为空', trigger: 'blur' }]">
                            <el-input v-model.trim="scope.row.deviceVendors" :placeholder="$t('common.placeholderDefault') + '設備廠商'"></el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="設備名稱" min-width="10%" prop="deviceName">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.deviceName'" label-width="0px" :rules="[{ required: true, message: '設備名稱不能为空', trigger: 'blur' }]">
                            <el-input v-model.trim="scope.row.deviceName" :placeholder="$t('common.placeholderDefault') + '設備名稱'"></el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="設備編號" min-width="10%" prop="deviceNumber">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.deviceNumber'" label-width="0px" :rules="[{ required: true, message: '設備編號不能为空', trigger: 'blur' }]">
                            <el-input v-model.trim="scope.row.deviceNumber" :placeholder="$t('common.placeholderDefault') + '設備編號'"></el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="操作系統" min-width="10%" prop="operateSystem">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.operateSystem'" label-width="0px" :rules="[{ required: true, message: '操作系統不能为空', trigger: 'blur' }]">
                            <el-input v-model.trim="scope.row.operateSystem" :placeholder="$t('common.placeholderDefault') + '操作系統'"></el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="MAC地址" min-width="10%" prop="macAddress">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.macAddress'" label-width="0px" :rules="moreRules.macAddress">
                            <el-input v-model.trim="scope.row.macAddress" :placeholder="$t('common.placeholderDefault') + 'MAC地址'"></el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="入網理由" min-width="10%" prop="applyReason">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.applyReason'" label-width="0px" :rules="[{ required: true, message: '入網理由不能为空', trigger: 'blur' }]">
                            <el-input v-model.trim="scope.row.applyReason" :placeholder="$t('common.placeholderDefault') + '入網理由'"></el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column align="center" :label="$t('table.operation')" width="80">
                        <template slot-scope="scope">
                          <el-button type="text" @click="handleDel_ent_equipment_items(scope.$index, scope.row)" class="del-handler" icon="el-icon-delete"></el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </el-col>
                </el-scrollbar>
              </div>
              <el-col :span="24" :xs="24" style="padding-top: 5px;padding-bottom: 5px">
                <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd_ent_equipment_items">{{$t('ent_equipment_network_8040b6a9d6905ebfb822b67dd57f1936.default.add')}}</el-button>
              </el-col>
              <el-col :span="3" :xs="24" style="padding-top: 5px;padding-bottom: 5px">
                <el-button type="text" icon="el-icon-download" size="mini" @click="handlePreview1">下載模版</el-button>
              </el-col>
              <el-col :span="21" :xs="24" style="padding-top: 5px;padding-bottom: 5px">
                <el-upload :file-list="upload.fileList" :show-file-list="false" :on-change="handleChange"
                           :on-exceed="handleExceed" :on-preview="handlePreview" :on-remove="handleRemove"
                           :on-success="uploadsucces1" :headers="upload.headers" :action="upload.url1"
                           list-type="text">
                  <el-button size="small" type="primary" icon="el-icon-upload2">{{ $t('table.import') }}</el-button>
                </el-upload>
              </el-col>
              <el-col :span="24" :xs="24">
                <el-form-item label="需求說明" prop="applyExplain">
                  <el-input v-model="formData.applyExplain" type="textarea" placeholder="請輸入需求說明"
                            :autosize="{minRows: 4, maxRows: 4}" :style="{width: '100%'}"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24">
                <el-form-item label="附件" prop="attachids">
                  <el-upload :file-list="upload.fileList" :show-file-list="true" :on-change="handleChange"
                             :on-exceed="handleExceed" :on-preview="handlePreview" :on-remove="handleRemove"
                             :on-success="uploadsucces" :headers="upload.headers" :action="upload.url" :data="upload.data"
                             list-type="text">
                    <el-button size="small" type="primary" icon="el-icon-upload">點擊上傳</el-button>
                  </el-upload>
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24">
                <el-row :gutter="15">
                  <el-col :span="6" :xs="24">
<!--                    <el-form-item prop="zxywqrchargeno" label-width="0">
                      <entfrm-sign-form-duty label="資訊運維課級主管" :required='true' duty-id="22" :alias="this.alias"
                                             :applyFactoryId="this.formData.applyMakerFactoryId" :multipleSelect=false
                                             :selectEmpProp="{empNo:this.formData.zxywqrchargeno,empName:this.formData.zxywqrchargename}"
                                             model-no="zxywqrchargeno" model-name="zxywqrchargename"
                                             @onSignFormSelected="onSignFormSelected"></entfrm-sign-form-duty>
                    </el-form-item>-->
                    <el-form-item prop="zxywqrchargeno" label-width="0">
                      <entfrm-sign-form-duty :label="formConfigData.zxywqrchargeno"
                                             :required='formConfigData.zxywqrchargeno_required' duty-id="22" :alias="this.alias"
                                             :applyFactoryId="this.formData.applyMakerFactoryId" :isMobile=this.isMobile
                                             :multipleSelect=true
                                             :selectEmpProp="{empNo:this.formData.zxywqrchargeno,empName:this.formData.zxywqrchargename}"
                                             model-no="zxywqrchargeno" model-name="zxywqrchargename"
                                             @onSignFormSelected="onSignFormSelected"></entfrm-sign-form-duty>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6" :xs="24">
                    <el-form-item prop="bchargeno" label-width="0">
                      <entfrm-sign-form-audit-hq :label="formConfigData.bchargeno" :required='formConfigData.bchargeno_required' :alias="this.alias"
                                                 :applyFactoryId="this.formData.applyFactoryId"
                                                 :applyDeptNo="this.formData.applyDeptNo" :multipleSelect=true
                                                 :selectEmpProp="{empNo:this.formData.bchargeno,empName:this.formData.bchargename}"
                                                 :kchargeno="this.formData.kchargeno" :bchargeno="this.formData.bchargeno"
                                                 :cchargeno="this.formData.cchargeno" model-no="bchargeno" model-name="bchargename"
                                                 @onSignFormSelected="onSignFormSelected"></entfrm-sign-form-audit-hq>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6" :xs="24">
<!--                    <el-form-item prop="zxywshchargeno" label-width="0">
                      <entfrm-sign-form-duty label="資訊運維廠區主管" :required='true' duty-id="25" :alias="this.alias"
                                             :applyFactoryId="this.formData.applyMakerFactoryId" :multipleSelect=true
                                             :selectEmpProp="{empNo:this.formData.zxywshchargeno,empName:this.formData.zxywshchargename}"
                                             model-no="zxywshchargeno" model-name="zxywshchargename"
                                             @onSignFormSelected="onSignFormSelected"></entfrm-sign-form-duty>
                    </el-form-item>-->
                    <el-form-item prop="zxywshchargeno" label-width="0">
                      <entfrm-sign-form-duty :label="formConfigData.zxywshchargeno"
                                             :required='formConfigData.zxywshchargeno_required' duty-id="25" :alias="this.alias"
                                             :applyFactoryId="this.formData.applyMakerFactoryId" :isMobile=this.isMobile
                                             :multipleSelect=true
                                             :selectEmpProp="{empNo:this.formData.zxywshchargeno,empName:this.formData.zxywshchargename}"
                                             model-no="zxywshchargeno" model-name="zxywshchargename"
                                             @onSignFormSelected="onSignFormSelected"></entfrm-sign-form-duty>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6" :xs="24">
<!--                    <el-form-item prop="sbrwchargeno" label-width="0">
                      <entfrm-sign-form-duty label="設備入網作業" :required='true' duty-id="639" :alias="this.alias"
                                             :applyFactoryId="this.formData.applyMakerFactoryId" :multipleSelect=false
                                             :selectEmpProp="{empNo:this.formData.sbrwchargeno,empName:this.formData.sbrwchargename}"
                                             model-no="sbrwchargeno" model-name="sbrwchargename"
                                             @onSignFormSelected="onSignFormSelected"></entfrm-sign-form-duty>
                    </el-form-item>-->
                    <el-form-item prop="sbrwchargeno" label-width="0">
                      <entfrm-sign-form-duty :label="formConfigData.sbrwchargeno"
                                             :required='formConfigData.sbrwchargeno_required' duty-id="639" :alias="this.alias"
                                             :applyFactoryId="this.formData.applyMakerFactoryId" :isMobile=this.isMobile
                                             :multipleSelect=true
                                             :selectEmpProp="{empNo:this.formData.sbrwchargeno,empName:this.formData.sbrwchargename}"
                                             model-no="sbrwchargeno" model-name="sbrwchargename"
                                             @onSignFormSelected="onSignFormSelected"></entfrm-sign-form-duty>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-col>
              <el-col :span="24" :xs="24">
                <div class="dialog-footer" align="center" style="padding-top: 5px;padding-bottom: 5px">
                  <!--          <el-form-item size="large">-->
                  <el-button type="success" @click="handleSubmit">{{$t('table.submit')}}</el-button>
                  <el-button type="primary" @click="submitForm">{{$t('common.save')}}</el-button>
                  <el-button @click="closeForm">{{$t('common.cancel')}}</el-button>
                  <!--        </el-form-item>-->
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-form>
    </div>
  </div>
</template>
<script>
  import {
    getEntEquipmentNetwork,
    addEntEquipmentNetwork,
    editEntEquipmentNetwork,
    addEntEquipmentNetworkAndStartProcess,
    editEntEquipmentNetworkAndStartProcess,
    getSignConfigList,
  }
    from "@/api/caaesign/entEquipmentNetwork"
  import '@/assets/styles/design-build/design-add-view.scss'
  import {
    getAccessToken,
    getZltAccessToken
  }
    from "@/utils/auth";
  import {
    previewFile,
    getHeader, previewFileOos
  }
    from "@/utils/entfrm";
  import {
    listFileInfo,
    getFileInfo,
    delFileInfo,
    addFileInfo,
    editFileInfo,
    getByKey
  }
    from "@/api/system/fileInfo";
  import axios from "axios";
  import {resolveBlob} from "@/utils/zipdownload";

  export default {
    components: {},
    props: [],
    data() {
      return {
        formData: {
          applyEmpNo: undefined,
          applyEmpName: undefined,
          applyDeptNo: undefined,
          applyCostNo: undefined,
          applyDeptName: undefined,
          applyMail: undefined,
          applyTel: undefined,
          makerfactoryid: [],
          applyExplain: undefined,
          attachids: "",
          bchargeno: undefined,
          itsjqrchargeno: undefined,
          itsjfschargeno: undefined,
          zxywqrchargeno: undefined,
          zxywshchargeno: undefined,
          sbrwchargeno: undefined,
          entEquipmentItemsLists: [
            {applyarea: '',
              applybuilding: '',
              applyfloor: '',
              applyline: '',
              deviceVendors: '',
              deviceName: '',
              deviceNumber: '',
              operateSystem: '',
              macAddress: '',
              applyReason: '',
              closePort: '',
              antivirus: '',
              antivirusOther: '',
              antivirusEdition: '',
              virusDate: '',
              ifDomain: '',
              systemPatch: '',
              sweepDrug: '',
              ifNetworking: '',
              ifNpi: '',
              ipAddress: ''
            }
          ],
          makerNo: this.$store.state.user.empNo,
          makerName: this.$store.state.user.name,
        },
        formConfigData: {
          title: "產線聯網設備入網申請單",
          zxywqrchargeno: "資訊運維課級主管",
          zxywqrchargeno_required: true,
          bchargeno: "部級主管",
          bchargeno_required: true,
          zxywshchargeno: "資訊運維廠區主管",
          zxywshchargeno_required: true,
          sbrwchargeno: "設備入網作業",
          sbrwchargeno_required: true,
        },
        //动态表单验证
        moreRules: {
          macAddress: [
            {required: true, message: "请输入MAC地址", trigger: "blur" },
            {
              validator: (rule, value, callback) => {
                if (value) {
                  const reg=/^[A-Fa-f0-9]{12}$/;
                  if (!reg.test(value)) {
                    callback(new Error("请输入正确的MAC地址"));
                  } else {
                    callback();
                  }
                } else {
                  callback(new Error("MAC地址不能为空"));
                }
              },
              trigger: 'change',
            }
          ],
        },
        tableHeight: document.documentElement.scrollHeight - 245 + "px",
        rules: {
          applyEmpNo: [{
            required: true,
            message: '請輸入承辦人工號',
            trigger: 'blur'
          }],
          applyEmpName: [{
            required: true,
            message: '請輸入承辦人',
            trigger: 'blur'
          }],
          applyDeptNo: [{
            required: true,
            message: '請輸入單位代碼',
            trigger: 'blur'
          }],
          applyCostNo: [{
            required: true,
            message: '請輸入費用代碼',
            trigger: 'blur'
          }],
          applyDeptName: [{
            required: true,
            message: '請輸入單位',
            trigger: 'blur'
          }],
          applyMail: [{
            required: true,
            message: '請輸入聯繫郵箱',
            trigger: 'blur'
          }],
          applyTel: [{
            required: true,
            message: '請輸入聯繫方式',
            trigger: 'blur'
          }],
          makerfactoryid: [{
            required: true,
            type: 'array',
            message: '請選擇所在廠區/區域/樓棟',
            trigger: 'change'
          }],
          applyExplain: [{
            required: true,
            message: '請輸入需求說明',
            trigger: 'blur'
          }],
          /*attachids: [],*/
          bchargeno: [{
            required: true,
            message: '部級主管不能為空',
            trigger: 'change'
          }],
          /*itsjqrchargeno: [{
            required: true,
            message: 'IT生技確認不能為空',
            trigger: 'change'
          }],
          itsjfschargeno: [{
            required: true,
            message: 'IT生技複審不能為空',
            trigger: 'change'
          }],*/
          zxywqrchargeno: [{
            required: true,
            message: '資訊運維課級主管不能為空',
            trigger: 'change'
          }],
          zxywshchargeno: [{
            required: true,
            message: '資訊運維廠區主管不能為空',
            trigger: 'change'
          }],
          sbrwchargeno: [{
            required: true,
            message: '設備入網作業不能為空',
            trigger: 'change'
          }],
        },
        // 文件上传参数
        upload: {
          // 是否显示弹出层
          open: false,
          fileList: [],
          fileNameList: [],
          data: {workflowKey: "dzqh_equipment_network"},
          // 弹出层标题
          title: "",
          multiple: false,
          // 是否禁用上传
          isUploading: false,
          // 设置上传的请求头部
          headers: getHeader(),
          // 上传的地址
          //url: process.env.VUE_APP_BASE_API + "/system/fileInfo/upload",
          url: this.getAppBaseApi() + "/system/fileInfo/uploadWithOos",
          url1: process.env.VUE_APP_BASE_API + "/caaesign/entEquipmentNetwork/importEntEquipmentItems"
        },
        makerfactoryidOptions: [],
        closePortOptions: [],
        antivirusOptions: [],
        antivirusEditionOptions: [],
        ifDomainOptions: [],
        systemPatchOptions: [],
        sweepDrugOptions: [],
        ifNetworkingOptions: [],
        ifNpiOptions: [],
        makerfactoryidProps: {
          "multiple": false,
          "expandTrigger": "hover",
        },
        entEquipmentItemsDragTableMobileClientWidth: 0,
        isMobile: false,
        labelPosition: 'right',
        alias: "oracle$_$hrsign_ipebg_test$_$8040b6a9d6905ebfb822b67dd57f1936%_%design"
      }
    },
    computed: {},
    watch: {},
    created() {
      const id = this.$route.query.id;
      this.changeTagsView(this.$route.query);
      this.isMobile = this.isMobileFun()
      if (this.isMobile) {
        this.labelPosition = 'top'
      }
      getSignConfigList().then(response => {
        if (response.code === 0) {
          response.data.forEach(element => {
            const colKey = element.colKey.split('_')
            if (colKey.length > 1) {
              if (this.rules[colKey[0]] && colKey[1] === 'required') {
                this.rules[colKey[0]][0]['required'] = element.colValue === 'true'
                this.formConfigData[element.colKey] = element.colValue === 'true'
              }
              else {
                this.formConfigData[element.colKey] = element.colValue
              }
            }
            else {
              this.formConfigData[element.colKey] = element.colValue
            }
          });
        }
      })
      if (id != null && id != undefined) {
        getEntEquipmentNetwork(id).then(response => {
          this.formData = response.data;
          this.checkBoxParse()
          this.cascaderParse()
          this.timeRangParse()
          if (this.formData.attachids) {
            let a = this.formData.attachids.split(',');
            if (a.length > 0) {
              a.forEach(item => {
                if (item) {
                  getByKey(item).then(response => {
                    this.upload.fileList.push({
                      name: response.data.orignalName,
                      url: response.data.name
                    });
                  })
                }
              })
            }
          }
        });
      }
      this.getMakerfactoryidOptions()
      this.getClosePortOptions()
      this.getAntivirusOptions()
      this.getAntivirusEditionOptions()
      this.getIfDomainOptions()
      this.getSystemPatchOptions()
      this.getSweepDrugOptions()
      this.getIfNetworkingOptions()
      this.getIfNpiOptions()
    },
    mounted() {
      this.$nextTick(function () {
        if (this.isMobile) {
          this.entEquipmentItemsDragTableMobileClientWidth = this.$refs.entEquipmentItemsDragTableMobile.$el
            .clientWidth
        }
      })
    },
    methods: {
      submitForm() {
        this.$refs['elForm'].validate(valid => {
          if (valid) {
            const formData = JSON.parse(JSON.stringify(this.formData))
            formData.makerfactoryid = JSON.stringify(formData.makerfactoryid)
            if (formData.id != undefined) {
              editEntEquipmentNetwork(formData).then(response => {
                if (response.code === 0) {
                  this.msgSuccess(this.$t('tips.updateSuccess'));
                  this.closeForm();
                }
                else {
                  this.msgError(response.msg);
                }
              });
            }
            else {
              addEntEquipmentNetwork(formData).then(response => {
                if (response.code === 0) {
                  this.msgSuccess(this.$t('tips.createSuccess'));
                  this.closeForm();
                }
                else {
                  this.msgError(response.msg);
                }
              });
            }
            /**
             * 提交的時候刪除被標記的需要刪除的附件
             */
            if (this.formData.attachids) {
              this.upload.fileNameList.forEach(item => {
                if (item) {
                  delFileInfo(item);
                }
              })
            }
          }
        })
      },
      handleSubmit: function () {
        this.$refs["elForm"].validate(valid => {
          if (valid) {
            const formData = JSON.parse(JSON.stringify(this.formData))
            formData.makerfactoryid = JSON.stringify(formData.makerfactoryid)
            if (formData.id != undefined) {
              editEntEquipmentNetworkAndStartProcess(formData).then(response => {
                if (response.code === 0) {
                  this.msgSuccess(this.$t('tips.updateSuccess'));
                  this.closeForm();
                }
                else {
                  this.msgError(response.msg);
                }
              });
            }
            else {
              addEntEquipmentNetworkAndStartProcess(formData).then(response => {
                if (response.code === 0) {
                  this.msgSuccess(this.$t('tips.createSuccess'));
                  this.closeForm();
                }
                else {
                  this.msgError(response.msg);
                }
              });
            }
            /**
             * 提交的時候刪除被標記的需要刪除的附件
             */
            if (this.formData.attachids) {
              this.upload.fileNameList.forEach(item => {
                if (item) {
                  delFileInfo(item);
                }
              })
            }
          }
        })
      },
      resetForm() {
        this.$refs['elForm'].resetFields()
      },
      closeForm() {
        //关闭子页面
        if (this.$store.state.settings.tagsView) {
          this.$router.go(-1) // 返回
          this.$store.state.tagsView.visitedViews.splice(this.$store.state.tagsView.visitedViews.findIndex(
            item => item.path === this.$route.path), 1)
          this.$router.push(this.$store.state.tagsView.visitedViews[this.$store.state.tagsView.visitedViews
            .length - 1].path)
        }
        else {
          // parent.postMessage("closeCurrentTabMessage",'*');
          parent.postMessage("closeCurrentTabMessage", '*');
        }
      },
      getMakerfactoryidOptions() {
        this.getDicts("caaesign_factory").then(response => {
          this.makerfactoryidOptions = response.data;
          if (response.data.length > 0) {
            response.data.forEach(element => {
              this.getOptions(element);
            });
          }
        });
      },

      checkBoxParse() {
      },
      cascaderParse() {
        if (this.formData.makerfactoryid) {
          this.formData.makerfactoryid = JSON.parse(this.formData.makerfactoryid)
        }
      },
      timeRangParse() {
      },
      applyEmpNo_getInfoUserByEmpno(data) {
        this.getInfoUserByEmpno(data).then(response => {
          if (response.code !== 0) {
            this.msgError(response.msg);
          } else {
            if (response.data != null) {
              this.formData.applyEmpName = response.data.empname
              this.formData.makerfactoryid = response.data.factoryid
              this.formData.applyDeptNo = response.data.deptno
              this.formData.applyCostNo = response.data.deptcostno
              this.formData.applyDeptName = response.data.deptname
              this.formData.applyMail = response.data.email
              this.formData.applyFactoryId = response.data.factoryid
              this.formData.applyMakerFactoryId=response.data.factoryid
            } else {
              this.formData.applyEmpName = ''
              this.formData.applyDeptNo = ''
              this.formData.applyCostNo = ''
              this.formData.applyDeptName = ''
              this.formData.applyMail = ''
              this.formData.makerfactoryid = ''
              this.formData.applyFactoryId = ''
              this.formData.applyMakerFactoryId=''
            }
          }
        });
      },
      changeMakerfactory(data){
        this.formData.applyMakerFactoryId=data[0]
      },
      onSignFormSelected(selectEmp, modelNo, modelName) {
        this.$set(this.formData, modelNo, selectEmp.empNo)
        this.$set(this.formData, modelName, selectEmp.empName)
      },
      handleAdd_ent_equipment_items() {
        const cloumn = {
          applyarea: '',
          applybuilding: '',
          applyfloor: '',
          applyline: '',
          deviceVendors: '',
          deviceName: '',
          deviceNumber: '',
          operateSystem: '',
          macAddress: '',
          applyReason: '',
          closePort: '',
          antivirus: '',
          antivirusOther: '',
          antivirusEdition: '',
          virusDate: '',
          ifDomain: '',
          systemPatch: '',
          sweepDrug: '',
          ifNetworking: '',
          ifNpi: '',
          ipAddress: ''
        };
        this.formData.entEquipmentItemsLists.splice(this.formData.entEquipmentItemsLists.length, 0, cloumn);
        for (let index in this.formData.entEquipmentItemsLists) {
          this.formData.entEquipmentItemsLists[index].sort = parseInt(index) + 1;
        }
      },
      handleDel_ent_equipment_items(index, row) {
        let functionName = this.$t(
          'ent_equipment_network_8040b6a9d6905ebfb822b67dd57f1936.default.functionName');
        this.$confirm(this.$t('tips.deleteConfirm', ['產線聯網設備入網明細表', row.id]), this.$t('tips.warm'), {
          confirmButtonText: this.$t("common.confirmTrim"),
          cancelButtonText: this.$t("common.cancelTrim"),
          type: "warning",
        }).then(() => {
          if (this.formData.entEquipmentItemsLists.length !== 1){
            this.formData.entEquipmentItemsLists.splice(index, 1);
            this.msgSuccess(this.$t("tips.deleteSuccess"));
          }else{
            this.msgError('至少保留一筆！');
          }
        }).catch(function (err) {
          console.log(err);
        });
      },
      getClosePortOptions() {
        this.getDicts("caaesign_yn").then(response => {
          this.closePortOptions = response.data;
        });
      },
      getAntivirusOptions() {
        this.getDicts("caaesign_antivirus_type").then(response => {
          this.antivirusOptions = response.data;
        });
      },
      getAntivirusEditionOptions() {
        this.getDicts("caaesign_antivirus_edition").then(response => {
          this.antivirusEditionOptions = response.data;
        });
      },
      getIfDomainOptions() {
        this.getDicts("caaesign_yn").then(response => {
          this.ifDomainOptions = response.data;
        });
      },
      getSystemPatchOptions() {
        this.getDicts("caaesign_yn").then(response => {
          this.systemPatchOptions = response.data;
        });
      },
      getSweepDrugOptions() {
        this.getDicts("caaesign_sweep_drug").then(response => {
          this.sweepDrugOptions = response.data;
        });
      },
      getIfNetworkingOptions() {
        this.getDicts("caaesign_yn").then(response => {
          this.ifNetworkingOptions = response.data;
        });
      },
      getIfNpiOptions() {
        this.getDicts("caaesign_yn").then(response => {
          this.ifNpiOptions = response.data;
        });
      },
      attachidsBeforeUpload(file) {
        let isRightSize = file.size / 1024 / 1024 < 2
        if (!isRightSize) {
          this.$message.error('文件大小超过 2MB')
        }
        return isRightSize
      },
      // 文件上传成功处理
      uploadsucces(response, file, fileList) {
        if (response.data.name != undefined && response.data.name != "undefined") {
          this.upload.fileList.push({
            name: file.name,
            url: response.data.name
          });
          this.formData.attachids += response.data.name + ",";
        }
      },
      // 文件上传成功处理
      uploadsucces1(response, file, fileList) {
        // this.formData.entDatabaseServerItemsLists = response.data;
        response.data.forEach(item => {
          this.formData.entEquipmentItemsLists.push(item);
        })
      },
      handleChange(file, fileList) {
      },
      handleExceed(file, fileList) {
      },
      handleRemove(file, fileList) {
        this.$emit("delUploadImage", file.name);
        const index = this.upload.fileList.indexOf(file);
        this.upload.fileList.splice(index, 1);
        if (this.formData.attachids) {
          this.formData.attachids = this.formData.attachids.replace(file.url + ",", "");
        }
        this.upload.fileNameList.push(file.url);
        // delFileInfo(file.url);
      },
      handlePreview(file) {
        previewFileOos(file.url)
      },
      handlePreview1(file1) {
        axios({
          method: 'get',
          url: process.env.VUE_APP_BASE_API + "/system/fileInfo/downloadById?id=" + encodeURI('d1b197a010a912db025ef46a000a7b93') + "&delete=" + false,
          responseType: 'blob',
          // headers: {Authorization: "Bearer " + getZltAccessToken() ,token:"Bearer "+getAccessToken()},
          headers: getHeader(),
        }).then(res => {
          resolveBlob(res, "")
        })
      },
    }
  }

</script>
<style>
  .el-upload__tip {
    line-height: 1.2;
  }

  .del-handler {
    font-size: 20px;
    color: #ff4949;
  }

  .el-slider > /deep/ .el-slider__runway {
    margin: 15px 0;
  }

  .el-checkbox-group {
    font-size: inherit;
  }

  .el-color-picker {
    display: inherit;
  }

  .talbe-name-style {
    font-size: 16px;
    font-weight: bold;
    color: #1DB8FF;
    line-height: 50px;
  }

</style>
