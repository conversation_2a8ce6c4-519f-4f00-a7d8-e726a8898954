<template>
  <div class="ant-modal-content">
    <div class="ant-modal-body">
      <el-form ref="elForm" class="ant-form ant-form-horizontal" :model="formData" :rules="rules"
               size="medium" label-width="100px" :label-position="labelPosition">
        <div class="ant-card ant-card-bordered" id="staffCard">
          <div class="ant-card-body" id="printContent">
            <el-row :gutter="15">
              <span id="staffEvectionTitle" style="margin-bottom: 10px">{{formConfigData.title}}</span>
              <el-col :span="8" :xs="24" class="el-col-no-border">
                任務編碼:{{formData.serialno ? formData.serialno : '提交表單后自動生成'}}
              </el-col>
              <el-col :span="8" :xs="24" class="el-col-no-border"> 填單時間:{{formData.createTime}}
              </el-col>
              <el-col :span="8" :xs="24" class="el-col-no-border">
                填單人:{{formData.makerNo + ' / ' + formData.makerName}}
              </el-col>
              <el-col :span="24" :xs="24" class="talbe-name-style">
                承辦人基本信息
              </el-col>
              <el-col :span="6" :xs="24">
                <el-form-item label="承辦人工號" prop="applyEmpNo">
                  <el-input v-model="formData.applyEmpNo" placeholder="請輸入承辦人工號" :disabled='true' clearable
                            :style="{width: '100%'}"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6" :xs="24">
                <el-form-item label="承辦人" prop="applyEmpName">
                  <el-input v-model="formData.applyEmpName" placeholder="請輸入承辦人" readonly :disabled='true'
                            clearable :style="{width: '100%'}"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6" :xs="24">
                <el-form-item label="單位代碼" prop="applyDeptNo">
                  <el-input v-model="formData.applyDeptNo" placeholder="請輸入單位代碼" readonly :disabled='true'
                            clearable :style="{width: '100%'}"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6" :xs="24">
                <el-form-item label="費用代碼" prop="applyCostNo">
                  <el-input v-model="formData.applyCostNo" placeholder="請輸入費用代碼" readonly :disabled='true'
                            clearable :style="{width: '100%'}"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12" :xs="24">
                <el-form-item label="單位" prop="applyDeptName">
                  <el-input v-model="formData.applyDeptName" placeholder="請輸入單位" :disabled='true' clearable
                            :style="{width: '100%'}"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12" :xs="24">
                <el-form-item label="聯繫郵箱" prop="applyMail">
                  <el-input v-model="formData.applyMail" placeholder="請輸入聯繫郵箱" :disabled='true' clearable
                            :style="{width: '100%'}"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12" :xs="24">
                <el-form-item label="聯繫方式" prop="applyTel">
                  <el-input v-model="formData.applyTel" placeholder="請輸入聯繫方式" :disabled='true' clearable
                            :style="{width: '70%'}"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12" :xs="24">
                <el-form-item label-width="120px" label="廠區/區域/樓棟" prop="makerfactoryid">
                  <el-cascader v-model="formData.makerfactoryid" :options="makerfactoryidOptions"
                               :props="makerfactoryidProps" :style="{width: '100%'}" placeholder="請選擇所在廠區" clearable
                               :disabled='true'></el-cascader>
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24" class="talbe-name-style">
                申請信息
              </el-col>
              <div style="overflow-x: auto;width: 100%;">
                <el-scrollbar v-bind:class="formData.currentorder>=1 ? wid200:wid130">
                  <el-col :span="24" :xs="24" style="padding: 0px;">
                    <el-table v-if="isMobile" border stripe ref="entEquipmentItemsDragTableMobile" :data="formData.entEquipmentItemsLists" row-key="id">
                      <el-table-column label="產線聯網設備入網明細表" type="index" min-width="90%" :width="entEquipmentItemsDragTableMobileClientWidth">
                        <template slot-scope="scope">
                          <span>{{$t('common.serialNumber')}}:{{ scope.$index + 1 }}</span>
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.applyarea'" label="區域">
                            <el-input v-model.trim="scope.row.applyarea" :placeholder="$t('common.placeholderDefault') + '區域'" :disabled='true'></el-input>
                          </el-form-item>
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.applybuilding'" label="樓棟">
                            <el-input v-model.trim="scope.row.applybuilding" :placeholder="$t('common.placeholderDefault') + '樓棟'" :disabled='true'></el-input>
                          </el-form-item>
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.applyfloor'" label="樓層">
                            <el-input v-model.trim="scope.row.applyfloor" :placeholder="$t('common.placeholderDefault') + '樓層'" :disabled='true'></el-input>
                          </el-form-item>
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.applyline'" label="線別">
                            <el-input v-model.trim="scope.row.applyline" :placeholder="$t('common.placeholderDefault') + '線別'" :disabled='true'></el-input>
                          </el-form-item>
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.deviceVendors'" label="設備廠商">
                            <el-input v-model.trim="scope.row.deviceVendors" :placeholder="$t('common.placeholderDefault') + '設備廠商'" :disabled='true'></el-input>
                          </el-form-item>
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.deviceName'" label="設備名稱">
                            <el-input v-model.trim="scope.row.deviceName" :placeholder="$t('common.placeholderDefault') + '設備名稱'" :disabled='true'></el-input>
                          </el-form-item>
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.deviceNumber'" label="設備編號">
                            <el-input v-model.trim="scope.row.deviceNumber" :placeholder="$t('common.placeholderDefault') + '設備編號'" :disabled='true'></el-input>
                          </el-form-item>
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.operateSystem'" label="操作系統">
                            <el-input v-model.trim="scope.row.operateSystem" :placeholder="$t('common.placeholderDefault') + '操作系統'" :disabled='true'></el-input>
                          </el-form-item>
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.macAddress'" label="MAC地址">
                            <el-input v-model.trim="scope.row.macAddress" :placeholder="$t('common.placeholderDefault') + 'MAC地址'" :disabled='true'></el-input>
                          </el-form-item>
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.applyReason'" label="入網理由">
                            <el-input v-model.trim="scope.row.applyReason" :placeholder="$t('common.placeholderDefault') + '入網理由'" :disabled='true'></el-input>
                          </el-form-item>
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.closePort'" label="關閉445端口"  v-if="formData.currentorder>=1">
                            <el-select v-model.trim="scope.row.closePort" :placeholder="$t('common.placeholderDefault') + '關閉445端口'" :disabled='true'  >
                              <el-option v-for="(item, index) in closePortOptions" :key="index" :label="item.label" :value="item.value" :disabled="item.disabled"></el-option>
                            </el-select>
                          </el-form-item>
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.antivirus'" label="防毒軟體" v-if="formData.currentorder>=1">
                            <el-select v-model.trim="scope.row.antivirus" :placeholder="$t('common.placeholderDefault') + '防毒軟體'" :disabled='true'>
                              <el-option v-for="(item, index) in antivirusOptions" :key="index" :label="item.label" :value="item.value" :disabled="item.disabled"></el-option>
                            </el-select>
                          </el-form-item>
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.antivirusEdition'" label="防毒版本" v-if="formData.currentorder>=1">
                            <el-select v-model.trim="scope.row.antivirusEdition" :placeholder="$t('common.placeholderDefault') + '防毒版本'" :disabled='true'>
                              <el-option v-for="(item, index) in antivirusEditionOptions" :key="index" :label="item.label" :value="item.value" :disabled="item.disabled"></el-option>
                            </el-select>
                          </el-form-item>
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.virusDate'" label="病毒庫日期" v-if="formData.currentorder>=1">
                            <el-date-picker v-model.trim="scope.row.virusDate" :placeholder="$t('common.placeholderDefault') + '病毒庫日期'" :disabled='true'></el-date-picker>
                          </el-form-item>
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.ifDomain'" label="是否加域" v-if="formData.currentorder>=1">
                            <el-select v-model.trim="scope.row.ifDomain" :placeholder="$t('common.placeholderDefault') + '是否加域'" :disabled='true'>
                              <el-option v-for="(item, index) in ifDomainOptions" :key="index" :label="item.label" :value="item.value" :disabled="item.disabled"></el-option>
                            </el-select>
                          </el-form-item>
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.systemPatch'" label="系統補丁" v-if="formData.currentorder>=1">
                            <el-select v-model.trim="scope.row.systemPatch" :placeholder="$t('common.placeholderDefault') + '系統補丁'" :disabled='true'>
                              <el-option v-for="(item, index) in systemPatchOptions" :key="index" :label="item.label" :value="item.value" :disabled="item.disabled"></el-option>
                            </el-select>
                          </el-form-item>
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.sweepDrug'" label="掃毒結果" v-if="formData.currentorder>=1">
                            <el-select v-model.trim="scope.row.sweepDrug" :placeholder="$t('common.placeholderDefault') + '掃毒結果'" :disabled='true'>
                              <el-option v-for="(item, index) in sweepDrugOptions" :key="index" :label="item.label" :value="item.value" :disabled="item.disabled"></el-option>
                            </el-select>
                          </el-form-item>
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.ifNetworking'" label="是否聯網" v-if="formData.currentorder>=1">
                            <el-select v-model.trim="scope.row.ifNetworking" :placeholder="$t('common.placeholderDefault') + '是否聯網'" :disabled='true'>
                              <el-option v-for="(item, index) in ifNetworkingOptions" :key="index" :label="item.label" :value="item.value" :disabled="item.disabled"></el-option>
                            </el-select>
                          </el-form-item>
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.ifNpi'" label="是否NPI" v-if="formData.currentorder>=1">
                            <el-select v-model.trim="scope.row.ifNpi" :placeholder="$t('common.placeholderDefault') + '是否NPI'" :disabled='true'>
                              <el-option v-for="(item, index) in ifNpiOptions" :key="index" :label="item.label" :value="item.value" :disabled="item.disabled"></el-option>
                            </el-select>
                          </el-form-item>
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.ipAddress'" label="IP地址" v-if="formData.currentorder>=4">
                            <el-input v-model.trim="scope.row.ipAddress" :placeholder="$t('common.placeholderDefault') + 'IP地址'" :disabled='true'></el-input>
                          </el-form-item>
                          <el-button type="text" @click="handleDel_ent_equipment_items(scope.$index, scope.row)" class="del-handler" icon="el-icon-delete"></el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                    <el-table v-else border stripe ref="entEquipmentItemsDragTable" :data="formData.entEquipmentItemsLists" row-key="id" :max-height="tableHeight">
                      <el-table-column label="編號" type="index" min-width="5%"/>
                      <el-table-column label="區域" min-width="10%" prop="applyarea">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.applyarea'" label-width="0px">
                            {{scope.row.applyarea}}
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="樓棟" min-width="10%" prop="applybuilding">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.applybuilding'" label-width="0px">
                            {{scope.row.applybuilding}}
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="樓層" min-width="10%" prop="applyfloor">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.applyfloor'" label-width="0px">
                            {{scope.row.applyfloor}}
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="線別" min-width="10%" prop="applyline">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.applyline'" label-width="0px">
                            {{scope.row.applyline}}
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="設備廠商" min-width="10%" prop="deviceVendors">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.deviceVendors'" label-width="0px">
                            {{scope.row.deviceVendors}}
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="設備名稱" min-width="10%" prop="deviceName">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.deviceName'" label-width="0px">
                            {{scope.row.deviceName}}
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="設備編號" min-width="10%" prop="deviceNumber">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.deviceNumber'" label-width="0px">
                            {{scope.row.deviceNumber}}
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="操作系統" min-width="10%" prop="operateSystem">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.operateSystem'" label-width="0px">
                            {{scope.row.operateSystem}}
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="MAC地址" min-width="15%" prop="macAddress">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.macAddress'" label-width="0px">
                            {{scope.row.macAddress}}
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="入網理由" min-width="10%" prop="applyReason">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.applyReason'" label-width="0px">
                            {{scope.row.applyReason}}
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="關閉445端口" min-width="10%" prop="closePort" v-if="formData.currentorder==1" >
                        <template slot-scope="scope">
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.closePort'"
                                        label-width="0px" :rules="[{ required: true, message: '關閉445端口不能为空', trigger: 'blur'}]">
                            <el-select v-model.trim="scope.row.closePort" :placeholder="$t('common.placeholderDefault') + '關閉445端口'"  >
                              <el-option v-for="(item, index) in closePortOptions" :key="index" :label="item.label"
                                         :value="item.value" :disabled="item.disabled"></el-option>
                            </el-select>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="關閉445端口" min-width="10%" prop="closePort" v-else-if="formData.currentorder>1" >
                        <template slot-scope="scope">
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.closePort'" label-width="0px">
                            {{scope.row.closePort}}
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="防毒軟體" min-width="10%" prop="antivirus" v-if="formData.currentorder==1">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.antivirus'" label-width="0px" :rules="[{ required: true, message: '防毒軟體不能为空', trigger: 'blur'}]">
                            <el-select v-model.trim="scope.row.antivirus" :placeholder="$t('common.placeholderDefault') + '防毒軟體'">
                              <el-option v-for="(item, index) in antivirusOptions" :key="index" :label="item.label"
                                         :value="item.value" :disabled="item.disabled"></el-option>
                            </el-select>
                          </el-form-item>
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.antivirusOther'" v-if="scope.row.antivirus==='其他'"  label-width="0px" :rules="[{ required: true, message: '其他不能为空', trigger: 'blur'}]" >
                            <el-input v-model.trim="scope.row.antivirusOther"
                                      :placeholder="$t('common.placeholderDefault') + '防毒軟體其他'"></el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="防毒軟體" min-width="10%" prop="antivirus" v-else-if="formData.currentorder>1">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.antivirus'" label-width="0px">
                            {{scope.row.antivirus}}{{scope.row.antivirusOther}}
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="防毒版本" min-width="10%" prop="antivirusEdition" v-if="formData.currentorder==1">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.antivirusEdition'"
                                        label-width="0px" :rules="[{ required: true, message: '防毒版本不能为空', trigger: 'blur'}]">
                            <el-select v-model.trim="scope.row.antivirusEdition"
                                       :placeholder="$t('common.placeholderDefault') + '防毒版本'">
                              <el-option v-for="(item, index) in antivirusEditionOptions" :key="index"
                                         :label="item.label" :value="item.value" :disabled="item.disabled"></el-option>
                            </el-select>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="防毒版本" min-width="10%" prop="antivirusEdition" v-else-if="formData.currentorder>1">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.antivirusEdition'" label-width="0px">
                            {{scope.row.antivirusEdition}}
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="病毒庫日期" min-width="10%" prop="virusDate" v-if="formData.currentorder==1">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.virusDate'"
                                        label-width="0px" :rules="[{ required: true, message: '病毒庫日期不能为空', trigger: 'blur'}]">
                            <el-date-picker v-model.trim="scope.row.virusDate" :placeholder="$t('common.placeholderDefault') + '日期'">
                            </el-date-picker>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="病毒庫日期" min-width="10%" prop="virusDate" v-else-if="formData.currentorder>1">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.virusDate'" label-width="0px">
                            <el-date-picker v-model.trim="scope.row.virusDate"
                                            :placeholder="$t('common.placeholderDefault') + '病毒庫日期'" :disabled='true'>
                            </el-date-picker>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="是否加域" min-width="10%" prop="ifDomain" v-if="formData.currentorder==1">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.ifDomain'"
                                        label-width="0px" :rules="[{ required: true, message: '是否加域不能为空', trigger: 'blur'}]">
                            <el-select v-model.trim="scope.row.ifDomain"
                                       :placeholder="$t('common.placeholderDefault') + '是否加域'">
                              <el-option v-for="(item, index) in ifDomainOptions" :key="index" :label="item.label"
                                         :value="item.value" :disabled="item.disabled"></el-option>
                            </el-select>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="是否加域" min-width="10%" prop="ifDomain" v-else-if="formData.currentorder>1">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.ifDomain'" label-width="0px">
                            {{scope.row.ifDomain}}
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="系統補丁" min-width="10%" prop="systemPatch" v-if="formData.currentorder==1">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.systemPatch'"
                                        label-width="0px" :rules="[{ required: true, message: '系統補丁不能为空', trigger: 'blur'}]">
                            <el-select v-model.trim="scope.row.systemPatch"
                                       :placeholder="$t('common.placeholderDefault') + '系統補丁'">
                              <el-option v-for="(item, index) in systemPatchOptions" :key="index"
                                         :label="item.label" :value="item.value" :disabled="item.disabled"></el-option>
                            </el-select>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="系統補丁" min-width="10%" prop="systemPatch" v-else-if="formData.currentorder>1">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.systemPatch'" label-width="0px">
                            {{scope.row.systemPatch}}
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="掃毒結果" min-width="10%" prop="sweepDrug" v-if="formData.currentorder==1">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.sweepDrug'"
                                        label-width="0px" :rules="[{ required: true, message: '掃毒結果不能为空', trigger: 'blur'}]">
                            <el-select v-model.trim="scope.row.sweepDrug"
                                       :placeholder="$t('common.placeholderDefault') + '掃毒結果'">
                              <el-option v-for="(item, index) in sweepDrugOptions" :key="index" :label="item.label"
                                         :value="item.value" :disabled="item.disabled"></el-option>
                            </el-select>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="掃毒結果" min-width="10%" prop="sweepDrug" v-else-if="formData.currentorder>1">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.sweepDrug'" label-width="0px">
                            {{scope.row.sweepDrug}}
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="是否聯網" min-width="10%" prop="ifNetworking" v-if="formData.currentorder==1">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.ifNetworking'"
                                        label-width="0px" :rules="[{ required: true, message: '是否聯網不能为空', trigger: 'blur'}]">
                            <el-select v-model.trim="scope.row.ifNetworking"
                                       :placeholder="$t('common.placeholderDefault') + '是否聯網'">
                              <el-option v-for="(item, index) in ifNetworkingOptions" :key="index"
                                         :label="item.label" :value="item.value" :disabled="item.disabled"></el-option>
                            </el-select>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="是否聯網" min-width="10%" prop="ifNetworking" v-else-if="formData.currentorder>1">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.ifNetworking'" label-width="0px">
                            {{scope.row.ifNetworking}}
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="是否NPI" min-width="10%" prop="ifNpi" v-if="formData.currentorder==1">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.ifNpi'"
                                        label-width="0px" :rules="[{ required: true, message: '是否NPI不能为空', trigger: 'blur'}]">
                            <el-select v-model.trim="scope.row.ifNpi"
                                       :placeholder="$t('common.placeholderDefault') + '是否NPI'">
                              <el-option v-for="(item, index) in ifNpiOptions" :key="index" :label="item.label"
                                         :value="item.value" :disabled="item.disabled"></el-option>
                            </el-select>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="是否NPI" min-width="10%" prop="ifNpi" v-else-if="formData.currentorder>1">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.ifNpi'" label-width="0px">
                            {{scope.row.ifNpi}}
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="IP地址" min-width="10%" prop="ipAddress" v-if="formData.currentorder==4">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entEquipmentItemsLists.' + scope.$index + '.ipAddress'"
                                        label-width="0px" :rules="[{ required: true, message: 'IP地址不能为空', trigger: 'blur'}]">
                            <el-input v-model.trim="scope.row.ipAddress" :placeholder="$t('common.placeholderDefault') + 'IP地址'"></el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                    </el-table>
                  </el-col>
                </el-scrollbar>
              </div>
              <el-col :span="24" :xs="24">
                <el-form-item label="需求說明" prop="applyExplain">
                  <el-input v-model="formData.applyExplain" type="textarea" placeholder="請輸入需求說明"
                            :disabled='true' :autosize="{minRows: 4, maxRows: 4}" :style="{width: '100%'}"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24">
                <el-form-item label="附件" prop="attachids">
                  <el-upload :file-list="upload.fileList" :show-file-list="true" :on-preview="handlePreview"
                             :headers="upload.headers" :action="upload.url" list-type="text" :disabled='true'>
                    <el-button size="small" type="primary" icon="el-icon-upload" :disabled='true'>點擊上傳
                    </el-button>
                  </el-upload>
                </el-form-item>
              </el-col>

              <el-col :span="24" :xs="24">
                <el-form-item :label="$t('table.activity.approvalOpinions')">
                  <el-input v-model="formData.comment" type="textarea" rows="3"
                            :placeholder="$t('table.activity.inputApprovalOpinions')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24" class="print-hide-div">
                <div class="dialog-footer" align="center" style="padding-top: 5px;padding-bottom: 5px">
                  <el-button type="success" @click="handleTask(0)" :disabled="isDisabled">
                    {{$t('table.activity.pass')}}
                  </el-button>
                  <el-button type="danger" @click="handleTask(1)" :disabled="isDisabled">
                    {{$t('table.activity.rejection')}}
                  </el-button>
                  <el-button type="primary" v-print="'#printContent'" >打印</el-button>
                  <el-button @click="handleTrack">{{$t('table.activity.flowTracing')}}</el-button>
                  <el-button @click="returnForm">{{$t('common.return')}}</el-button>
                </div>
              </el-col>
            </el-row>
            <!-- 簽核線 -->
            <el-col :span="24" class="print-hide-div">
              <div class="dialog-footer" style="padding-top: 5px;padding-bottom: 5px">
                <div v-html="signPath"></div>
              </div>
            </el-col>
            <!-- 审核记录 -->
            <el-table border stripe :data="commentList">
              <el-table-column :label="$t('table.activity.taskId')" align="center" prop="id"/>
              <el-table-column :label="$t('table.activity.approvedBy')" align="center" prop="userId"
                               :show-overflow-tooltip="true"/>
              <el-table-column :label="$t('table.activity.approvalOpinions')" align="center"
                               prop="fullMessage" :show-overflow-tooltip="true"/>
              <el-table-column :label="$t('table.activity.operIp')" align="center" prop="operIp"
                               :show-overflow-tooltip="true"/>
              <el-table-column :label="$t('table.activity.auditStatus')" align="center" prop="status"
                               :formatter="statusFormat" :show-overflow-tooltip="true"/>
              <el-table-column :label="$t('table.activity.approvalTime')" align="center" prop="time"
                               width="180">
                <template slot-scope="scope">
                  <span>{{ parseTime(scope.row.time) }}</span>
                </template>
              </el-table-column>
            </el-table>
            <!-- 任务跟踪对话框 -->
            <el-dialog :title="$t('table.activity.taskMap')" :visible.sync="showImgDialog" width="760px">
              <img :src="imgUrl" style="padding-bottom: 60px;">
            </el-dialog>
          </div>
        </div>
      </el-form>
    </div>
  </div>
</template>
<script>
  import {
    getEntEquipmentNetwork,
    addEntEquipmentNetwork,
    editEntEquipmentNetwork,
    getSignPath,
    getSignConfigList,
  }
    from "@/api/caaesign/entEquipmentNetwork"
  import '@/assets/styles/design-build/design-add-view.scss'
  import {
    listTask,
    getTask,
    checkTask,
    taskComment
  }
    from "@/api/activiti/task"
  import {
    getAccessToken,
    getZltAccessToken
  }
    from "@/utils/auth";
  import {
    previewFile,
    getHeader, previewFileOos
  }
    from "@/utils/entfrm";
  import {
    listFileInfo,
    getFileInfo,
    delFileInfo,
    addFileInfo,
    editFileInfo,
    getByKey
  }
    from "@/api/system/fileInfo";

  export default {
    components: {},
    props: [],
    data() {
      return {
        formData: {
          applyEmpNo: undefined,
          applyEmpName: undefined,
          applyDeptNo: undefined,
          applyCostNo: undefined,
          applyDeptName: undefined,
          applyMail: undefined,
          applyTel: undefined,
          makerfactoryid: [""],
          applyExplain: undefined,
          attachids: "",
          bchargeno: undefined,
          itsjqrchargeno: undefined,
          itsjfschargeno: undefined,
          zxywqrchargeno: undefined,
          zxywshchargeno: undefined,
          sbrwchargeno: undefined,
          entEquipmentItemsLists: [],
        },
        formConfigData: {
          title: "產線聯網設備入網申請單",
          zxywqrchargeno: "資訊運維課級主管",
          zxywqrchargeno_required: true,
          bchargeno: "部級主管",
          bchargeno_required: true,
          zxywshchargeno: "資訊運維廠區主管",
          zxywshchargeno_required: true,
          sbrwchargeno: "設備入網作業",
          sbrwchargeno_required: true,
        },
        rules: {
          applyEmpNo: [{
            required: false,
            message: '請輸入承辦人工號',
            trigger: 'blur'
          }],
          applyEmpName: [{
            required: false,
            message: '請輸入承辦人',
            trigger: 'blur'
          }],
          applyDeptNo: [{
            required: false,
            message: '請輸入單位代碼',
            trigger: 'blur'
          }],
          applyCostNo: [{
            required: false,
            message: '請輸入費用代碼',
            trigger: 'blur'
          }],
          applyDeptName: [{
            required: false,
            message: '請輸入單位',
            trigger: 'blur'
          }],
          applyMail: [{
            required: false,
            message: '請輸入聯繫郵箱',
            trigger: 'blur'
          }],
          applyTel: [{
            required: false,
            message: '請輸入聯繫方式',
            trigger: 'blur'
          }],
          makerfactoryid: [{
            required: false,
            type: 'array',
            message: '請至少選擇一個所在廠區',
            trigger: 'change'
          }],
          applyExplain: [{
            required: false,
            message: '請輸入需求說明',
            trigger: 'blur'
          }],
          /*attachids: [],*/
          bchargeno: [{
            required: false,
            message: '部級主管不能為空',
            trigger: 'change'
          }],
          itsjqrchargeno: [{
            required: false,
            message: 'IT生技確認不能為空',
            trigger: 'change'
          }],
          itsjfschargeno: [{
            required: false,
            message: 'IT生技複審不能為空',
            trigger: 'change'
          }],
          zxywqrchargeno: [{
            required: false,
            message: '資訊運維確認不能為空',
            trigger: 'change'
          }],
          zxywshchargeno: [{
            required: false,
            message: '資訊運維審核不能為空',
            trigger: 'change'
          }],
          sbrwchargeno: [{
            required: false,
            message: '設備入網不能為空',
            trigger: 'change'
          }],
        },
        tableHeight: document.documentElement.scrollHeight - 245 + "px",
        // 审批意见数据
        commentList: [],
        //簽核路徑
        signPath: [],
        //任务图url
        imgUrl: '',
        isDisabled: false,
        // 是否显示任务图
        showImgDialog: false,
        auditStatus: [],
        // 文件上传参数
        upload: {
          // 是否显示弹出层
          open: false,
          fileList: [],
          fileNameList: [],
          data: {workflowKey: "dzqh_equipment_network"},
          // 弹出层标题
          title: "",
          multiple: false,
          // 是否禁用上传
          isUploading: false,
          // 设置上传的请求头部
          headers: getHeader(),
          // 上传的地址
          url: this.getAppBaseApi() + "/system/fileInfo/uploadWithOos",
        },
        makerfactoryidOptions: [],
        closePortOptions: [],
        antivirusOptions: [],
        antivirusEditionOptions: [],
        ifDomainOptions: [],
        systemPatchOptions: [],
        sweepDrugOptions: [],
        ifNetworkingOptions: [],
        ifNpiOptions: [],
        makerfactoryidProps: {
          "multiple": false,
          "expandTrigger": "hover"
        },
        entEquipmentItemsDragTableMobileClientWidth: 0,
        isMobile: false,
        labelPosition: 'left',
        showOther:false,
        wid130:'wid130',
        wid200:'wid200',
      }
    },
    computed: {},
    watch: {},
    created() {
      const id = this.$route.query.id;
      this.changeTagsView(this.$route.query);
      this.isMobile = this.isMobileFun()
      if (this.isMobile) {
        this.labelPosition = 'top'
      }
      getSignConfigList().then(response => {
        if (response.code === 0) {
          response.data.forEach(element => {
            const colKey = element.colKey.split('_')
            if (colKey.length > 1) {
              if (this.rules[colKey[0]] && colKey[1] === 'required') {
                this.rules[colKey[0]][0]['required'] = element.colValue === 'true'
                this.formConfigData[element.colKey] = element.colValue === 'true'
              }
              else {
                this.formConfigData[element.colKey] = element.colValue
              }
            }
            else {
              this.formConfigData[element.colKey] = element.colValue
            }
          });
        }
      })
      if (id != null && id != undefined) {
        getEntEquipmentNetwork(id).then(response => {
          this.formData = response.data;
          this.checkBoxParse()
          this.cascaderParse()
          taskComment(this.formData.processId).then(response => {
            this.commentList = response.data;
          });
          getSignPath(this.formData.processId).then(response => {
            this.signPath = response.data;
          });
          this.timeRangParse()
          if (this.formData.attachids) {
            let a = this.formData.attachids.split(',');
            if (a.length > 0) {
              a.forEach(item => {
                if (item) {
                  getByKey(item).then(response => {
                    this.upload.fileList.push({
                      name: response.data.orignalName,
                      url: response.data.name
                    });
                  })
                }
              })
            }
          }
        });
      }
      this.getMakerfactoryidOptions()
      this.getClosePortOptions()
      this.getAntivirusOptions()
      this.getAntivirusEditionOptions()
      this.getIfDomainOptions()
      this.getSystemPatchOptions()
      this.getSweepDrugOptions()
      this.getIfNetworkingOptions()
      this.getIfNpiOptions()
      this.getDicts("sign_status").then(response => {
        this.auditStatus = response.data;
      });
    },
    mounted() {
      this.$nextTick(function () {
        if (this.isMobile) {
          this.entEquipmentItemsDragTableMobileClientWidth = this.$refs.entEquipmentItemsDragTableMobile.$el
            .clientWidth
        }
      })
    },
    methods: {
      closeForm() {
        //关闭子页面
        if (this.$store.state.settings.tagsView) {
          this.$router.go(-1) // 返回
          this.$store.state.tagsView.visitedViews.splice(this.$store.state.tagsView.visitedViews.findIndex(
            item => item.path === this.$route.path), 1)
          this.$router.push(this.$store.state.tagsView.visitedViews[this.$store.state.tagsView.visitedViews
            .length - 1].path)
        }
        else {
          // parent.postMessage("closeCurrentTabMessage",'*');
          parent.postMessage("closeCurrentTabMessage", '*');
        }
      },
      returnForm() {
        //关闭子页面
        if (this.$store.state.settings.tagsView) {
          this.$router.go(-1) // 返回
          this.$store.state.tagsView.visitedViews.splice(this.$store.state.tagsView.visitedViews.findIndex(
              item => item.path === this.$route.path), 1)
          this.$router.push(this.$store.state.tagsView.visitedViews[this.$store.state.tagsView.visitedViews
              .length - 1].path)
        }
        else {
          parent.postMessage("returnCurrentTabMessage", '*');
        }
      },
      handleTrack() {
        this.imgUrl = process.env.VUE_APP_BASE_API + '/activiti/task/track/' + this.formData.processId,
          this.showImgDialog = true
      },
      statusFormat(row, column) {
        return this.selectDictLabel(this.auditStatus, row.status);
      },
      handleTask: function (pass) {
        this.isDisabled = true;
        this.$refs["elForm"].validate(valid => {
          if ((valid&&pass == 0) || (pass == 1 && this.formData.comment != null)) {
            this.formData.pass = pass
            if(pass == 0){
              const formData = JSON.parse(JSON.stringify(this.formData))
              formData.makerfactoryid = JSON.stringify(formData.makerfactoryid)
              editEntEquipmentNetwork(formData).then(response => {
                if (response.code === 0) {
                  checkTask(this.formData).then(response => {
                    if (response.code === 0) {
                      this.msgSuccess(this.$t('tips.operationSuccessful'));
                      this.closeForm();
                    }
                    else {
                      this.msgError(response.msg);
                    }
                  });
                }
                else {
                  this.msgError(response.msg);
                }
              });
            }
            if(pass == 1){
              checkTask(this.formData).then(response => {
                if (response.code === 0) {
                  this.msgSuccess(this.$t('tips.operationSuccessful'));
                  this.closeForm();
                }
                else {
                  this.msgError(response.msg);
                }
              });
            }
          } else {
            this.isDisabled = false;
            if(pass==1){
              this.msgInfo(this.$t('table.activity.inputApprovalOpinions'));
            }
          }
        });
      },
      getMakerfactoryidOptions() {
        this.getDicts("caaesign_factory").then(response => {
          this.makerfactoryidOptions = response.data;
          if (response.data.length > 0) {
            response.data.forEach(element => {
              this.getOptions(element);
            });
          }
        });
      },
      attachidsBeforeUpload(file) {
        let isRightSize = file.size / 1024 / 1024 < 2
        if (!isRightSize) {
          this.$message.error('文件大小超过 2MB')
        }
        return isRightSize
      },
      // 文件上传成功处理
      uploadsucces(response, file, fileList) {
        if (response.data.name != undefined && response.data.name != "undefined") {
          this.upload.fileList.push({
            name: file.name,
            url: response.data.name
          });
          this.formData.attachids += response.data.name + ",";
        }
      },
      handleChange(file, fileList) {
      },
      handleExceed(file, fileList) {
      },
      handleRemove(file, fileList) {
        this.$emit("delUploadImage", file.name);
        const index = this.upload.fileList.indexOf(file);
        this.upload.fileList.splice(index, 1);
        if (this.formData.attachids) {
          this.formData.attachids = this.formData.attachids.replace(file.url + ",", "");
        }
        this.upload.fileNameList.push(file.url);
        // delFileInfo(file.url);
      },
      handlePreview(file) {
        previewFileOos(file.url)
      },
      checkBoxParse() {
      },
      cascaderParse() {
        if (this.formData.makerfactoryid) {
          this.formData.makerfactoryid = JSON.parse(this.formData.makerfactoryid)
        }
      },
      timeRangParse() {
      },
      handleAdd_ent_equipment_items() {
        const cloumn = {
          applyarea: '',
          applybuilding: '',
          applyfloor: '',
          applyline: '',
          deviceVendors: '',
          deviceName: '',
          deviceNumber: '',
          operateSystem: '',
          macAddress: '',
          applyReason: '',
          closePort: '',
          antivirus: '',
          antivirusOther: '',
          antivirusEdition: '',
          virusDate: '',
          ifDomain: '',
          systemPatch: '',
          sweepDrug: '',
          ifNetworking: '',
          ifNpi: '',
          ipAddress: '',

        };
        this.formData.entEquipmentItemsLists.splice(this.formData.entEquipmentItemsLists.length, 0, cloumn);
        for (let index in this.formData.entEquipmentItemsLists) {
          this.formData.entEquipmentItemsLists[index].sort = parseInt(index) + 1;
        }
      },
      handleDel_ent_equipment_items(index, row) {
        let functionName = this.$t(
          'ent_equipment_network_8040b6a9d6905ebfb822b67dd57f1936.default.functionName');
        this.$confirm(this.$t('tips.deleteConfirm', ['產線聯網設備入網明細表', row.id]), this.$t('tips.warm'), {
          confirmButtonText: this.$t("common.confirmTrim"),
          cancelButtonText: this.$t("common.cancelTrim"),
          type: "warning",
        }).then(() => {
          this.formData.entEquipmentItemsLists.splice(index, 1);
          this.msgSuccess(this.$t("tips.deleteSuccess"));
        }).catch(function (err) {
          console.log(err);
        });
      },
      /*selectChanged(value,index){
        if(value==='其他') {
          this.showOther = true;

        }
        if(value==='OfficeScan'){
          this.showOther=false;
        }
      },*/
      getClosePortOptions() {
        this.getDicts("caaesign_yn").then(response => {
          this.closePortOptions = response.data;
        });
      },
      getAntivirusOptions() {
        this.getDicts("caaesign_antivirus_type").then(response => {
          this.antivirusOptions = response.data;
        });
      },
      getAntivirusEditionOptions() {
        this.getDicts("caaesign_antivirus_edition").then(response => {
          this.antivirusEditionOptions = response.data;
        });
      },
      getIfDomainOptions() {
        this.getDicts("caaesign_yn").then(response => {
          this.ifDomainOptions = response.data;
        });
      },
      getSystemPatchOptions() {
        this.getDicts("caaesign_yn").then(response => {
          this.systemPatchOptions = response.data;
        });
      },
      getSweepDrugOptions() {
        this.getDicts("caaesign_sweep_drug").then(response => {
          this.sweepDrugOptions = response.data;
        });
      },
      getIfNetworkingOptions() {
        this.getDicts("caaesign_yn").then(response => {
          this.ifNetworkingOptions = response.data;
        });
      },
      getIfNpiOptions() {
        this.getDicts("caaesign_yn").then(response => {
          this.ifNpiOptions = response.data;
        });
      },
    }
  }

</script>
<style scoped>
  .el-upload__tip {
    line-height: 1.2;
  }

  .del-handler {
    font-size: 20px;
    color: #ff4949;
  }

  .el-dialog {
    position: relative;
    margin: 0 auto 0px;
    background: #FFFFFF;
    border-radius: 2px;
    -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 50%;
    height: 60%;
  }

  .el-dialog__body {
    border-top: 1px solid #dcdfe6;
    border-bottom: 1px solid #dcdfe6;
    max-height: 85% !important;
    min-height: 70%;
    overflow-y: auto;
  }

  .el-slider > /deep/ .el-slider__runway {
    margin: 15px 0;
  }

  .el-checkbox-group {
    font-size: inherit;
  }

  .el-color-picker {
    display: inherit;
  }

  .talbe-name-style {
    font-size: 16px;
    font-weight: bold;
    color: #1DB8FF;
    line-height: 50px;
  }
  @media print{
    .print-hide-div{
      display:none;
    }
    .el-col:not(.el-col-no-border) {
      border: 1px solid #ccc;
      margin-top: -1px;
    }
    #printContent{
      box-sizing: border-box;
      width: 1000px;
    }
    .el-form-item {
      margin-top: 12px;
      margin-bottom: 12px;
    }
    .el-form-item__label{
      text-align: left;
    }
    .el-form-item{
      min-height:36px;
    }
    #serialno,#makerNo,#createTime{
      padding:10px 0;
    }
    *{
      box-sizing: border-box;
    }
    .el-table{
      border: 1px solid #dfe6ec;
    }
    html{
      background-color: #FFFFFF;
      margin: 0;
    }

    .el-table__header{
      table-layout: auto;
    }
    .table-max .el-table__body,.table-max .el-table__header{
      width:100% !important;
    }
    .table-max  col{
      width:calc(100% / 6)  !important;
    }
    .table-max .el-table__body .cell{
      width:100% !important;
    }


  }
  /*去除页眉页脚*/
  @page{
    size:  auto;
    margin: 3mm;
  }
  .table-style{
    text-align: center;
    border-collapse: collapse;
    border-spacing: 0;
    width: 100%;
  }
  .table-style td{
    border:1px solid #ccc;
    line-height: 30px;
  }
  .wid130{
    width: 130%;
  }
  .wid200{
    width: 250%;
  }
</style>
