<template>
  <div class="ant-modal-content" slot="content">
    <el-form ref="elForm" :model="formData"  size="medium"  :label-position="labelPosition" class="spHeight">
      <div class="form-info">
        <a style="text-decoration:none;">
          <div class="rating__text"></div>
          <span class="course-head">表單信息</span>
        </a>
      </div>
      <div class="profile-info-table">
        <div class="profile-info-block">
          <div>表單編號：</div>
          <div>{{formData.wfsslvpnprocess.serialno}}</div>
        </div>
      </div>
      <div class="space"></div>
      <div class="form-info" @click="isActive = !isActive">
        <a style="text-decoration:none;">
          <div class="rating__text"></div>
          <span class="course-head">承辦人信息</span>
          <img class="imgUrlArrowhead" :src="imgUrlArrowhead2" v-if="isActive==true">
          <img class="imgUrlArrowhead2" :src="imgUrlArrowhead2" v-if="isActive==false">
        </a>
      </div>
      <div class="ant-modal-body">
        <collapse>
          <div class="profile-info-table" v-show="isActive">
            <div class="content-small">
              <div class="content-medium-block">
                <div><span>工號：</span>{{formData.wfsslvpnprocess.dealno}}</div>
                <div><span>姓名：</span>{{formData.wfsslvpnprocess.dealname}}</div>
              </div>
              <div class="content-medium-block">
                <div><span>分機：</span>{{formData.wfsslvpnprocess.dealtel}}</div>
                <div><span>廠區：</span>{{formData.wfsslvpnprocess.dealfactoryid}}</div>
              </div>
              <div class="content-medium-block">
                <div><span>單位代碼：</span>{{formData.wfsslvpnprocess.dealdeptno}}</div>
                <div><span>使用區域：</span>{{formData.wfsslvpnprocess.applyarea}}/{{formData.wfsslvpnprocess.applybuilding}}</div>
              </div>
              <div class="content-medium-block">
                <div><span>申請日期：</span>{{parseTime(formData.wfsslvpnprocess.createtime,'{y}-{m}-{d}')}}</div>
                <div><span>安保區域：</span>{{formData.wfsslvpnprocess.securityarea}}</div>
              </div>
              <div class="content-medium-block">
                <div style="display: flex;justify-content: flex-start">
                  <span style="flex-shrink: 0">聯繫郵箱：</span>
                  <span>{{formData.wfsslvpnprocess.dealemail}}</span>
                </div>
              </div>
              <div class="content-medium-block">
                <div style="display: flex;justify-content: flex-start">
                  <span style="flex-shrink: 0">單位名稱：</span>
                  <span>{{formData.wfsslvpnprocess.dealdeptname}}</span>
                </div>
              </div>
            </div>
            <div class="content-medium">
              <div class="content-medium-block">
                <div><span>工號：</span>{{formData.wfsslvpnprocess.dealno}}</div>
                <div><span>姓名：</span>{{formData.wfsslvpnprocess.dealname}}</div>
                <div><span>所在廠區：</span>{{formData.wfsslvpnprocess.dealfactoryid}}</div>
              </div>
              <div class="content-medium-block">
                <div><span>聯繫分機：</span>{{formData.wfsslvpnprocess.dealtel}}</div>
                <div><span>單位代碼：</span>{{formData.wfsslvpnprocess.dealdeptno}}</div>
                <div><span>使用區域：</span>{{formData.wfsslvpnprocess.applyarea}}/{{formData.wfsslvpnprocess.applybuilding}}</div>
              </div>
              <div class="content-medium-block">
                <div><span>安保區域：</span>{{formData.wfsslvpnprocess.securityarea}}</div>
                <div style="flex: 2"><span>聯繫郵箱：</span>{{formData.wfsslvpnprocess.dealemail}}</div>
              </div>
              <div class="content-medium-block">
                <div><span>申請日期：</span>{{parseTime(formData.wfsslvpnprocess.createtime,'{y}-{m}-{d}')}}</div>
                <div style="flex: 2;display: flex;justify-content: flex-start">
                  <span style="flex-shrink: 0">單位名稱：</span>
                  <span>{{formData.wfsslvpnprocess.dealdeptname}}</span>
                </div>
              </div>
            </div>
          </div>
        </collapse>
      </div>
      <div class="space"></div>
      <div class="form-info" @click="isActive4 = !isActive4">
        <a style="text-decoration:none;">
          <div class="rating__text"></div>
          <span class="course-head">SSL VPN服務申請明細(共<span style="color: red;font-weight: bold;">{{formData.wfsslvpnprocess.itemsEntitys.length}}</span>筆)</span>
          <img class="imgUrlArrowhead" :src="imgUrlArrowhead2" v-if="isActive4==true">
          <img class="imgUrlArrowhead2" :src="imgUrlArrowhead2" v-if="isActive4==false">
        </a>
      </div>
      <div class="ant-modal-body">
        <collapse>
          <div class="profile-info-table" v-show="isActive4">
            <div v-for="(item, index) in formData.wfsslvpnprocess.itemsEntitys" :key="index">
              <div class="content-small">
                <div class="content-items-block content-items-title">
                  <div>第{{index+1}}位申請人信息</div>
                </div>
                <div class="content-items-block">
                  <div><span>工號</span></div>
                  <div>{{item.applyno}}</div>
                  <div><span>所在廠區</span></div>
                  <div>{{item.applyfactoryid}}</div>
                </div>
                <div class="content-items-block">
                  <div><span>姓名</span></div>
                  <div>{{item.applyname}}</div>
                  <div><span>費用代碼</span></div>
                  <div>{{item.applycostno}}</div>
                </div>
                <div class="content-items-block">
                  <div><span>資位</span></div>
                  <div>{{item.applyleveltype}}</div>
                  <div><span>單位代碼</span></div>
                  <div>{{item.applydeptno}}</div>
                </div>
                <div class="content-items-block">
                  <div><span>管理職</span></div>
                  <div>{{item.applymanager}}</div>
                  <div><span>法人代碼</span></div>
                  <div>{{item.applycompanycode}}</div>
                </div>
                <div class="content-items-block">
                  <div><span>聯繫分機</span></div>
                  <div>{{item.applyphone}}</div>
                  <div><span>法人名稱</span></div>
                  <div>{{item.applycompanyname}}</div>
                </div>
                <div class="content-items-block">
                  <div><span>用戶名</span></div>
                  <div>{{item.vpnname}}</div>
                  <div><span>手機號碼</span></div>
                  <div>{{item.vpnhostip}}</div>
                </div>
                <div class="content-items-block">
                  <div><span>申請人類型</span></div>
                  <div>{{item.vpnapplytype}}</div>
                  <div><span>申請類型</span></div>
                  <div>{{item.vpntype}}</div>
                </div>
                <div class="content-items-block">
                  <div><span>使用期限</span></div>
                  <div>{{item.vpnexpire}}</div>
                  <div><span>操作系統</span></div>
                  <div>{{item.vpnrequiretype}}</div>
                </div>
                <div class="content-items-block">
                  <div><span>使用日期</span></div>
                  <div style="flex: 4.7;justify-content:start;">
                    <span class="item-long">
                      {{parseTime(item.vpnstarttime,'{y}-{m}-{d}')}}至{{parseTime(item.vpnendtime,'{y}-{m}-{d}')}}
                    </span>
                  </div>
                </div>
                <div class="content-items-block">
                  <div><span>聯繫郵箱</span></div>
                  <div style="flex: 4.7;justify-content:start;">
                    <span class="item-long">{{item.applyemail}}</span>
                  </div>
                </div>
                <div class="content-items-block">
                  <div><span>單位名稱</span></div>
                  <div style="flex: 4.7;justify-content:start;">
                    <span class="item-long">{{item.applydeptname}}</span>
                  </div>
                </div>
                <div class="content-items-block">
                  <div><span>需求說明</span></div>
                  <div style="flex: 4.7;justify-content:start;">
                    <span class="item-long">{{item.describtion}}</span>
                  </div>
                </div>
              </div>
              <div class="content-medium">
                <div class="content-items-block content-items-title">
                  <div>第{{index+1}}位使用人信息</div>
                </div>
                <div class="content-items-block">
                  <div><span>工號</span></div>
                  <div>{{item.applyno}}</div>
                  <div><span>聯繫分機</span></div>
                  <div>{{item.applyphone}}</div>
                  <div><span>所在廠區</span></div>
                  <div>{{item.applyfactoryid}}</div>
                </div>
                <div class="content-items-block">
                  <div><span>姓名</span></div>
                  <div>{{item.applyname}}</div>
                  <div><span>單位代碼</span></div>
                  <div>{{item.applydeptno}}</div>
                  <div><span>費用代碼</span></div>
                  <div>{{item.applycostno}}</div>
                </div>
                <div class="content-items-block">
                  <div><span>資位</span></div>
                  <div>{{item.applyleveltype}}</div>
                  <div><span>法人代碼</span></div>
                  <div>{{item.applycompanycode}}</div>
                  <div><span>法人名稱</span></div>
                  <div>{{item.applycompanyname}}</div>
                </div>
                <div class="content-items-block">
                  <div><span>管理職</span></div>
                  <div>{{item.applymanager}}</div>
                  <div><span>用戶名</span></div>
                  <div>{{item.vpnname}}</div>
                  <div><span>手機號碼</span></div>
                  <div>{{item.vpnhostip}}</div>
                </div>
                <div class="content-items-block">
                  <div><span>申請人類型</span></div>
                  <div>{{item.vpnapplytype}}</div>
                  <div><span>申請類型</span></div>
                  <div>{{item.vpntype}}</div>
                  <div><span>使用期限</span></div>
                  <div>{{item.vpnexpire}}</div>
                </div>
                <div class="content-items-block">
                  <div><span>使用日期</span></div>
                  <div style="flex: 7.7;justify-content:start;">
                      <span class="item-long">
                        {{parseTime(item.vpnstarttime,'{y}-{m}-{d}')}}至{{parseTime(item.vpnendtime,'{y}-{m}-{d}')}}
                      </span>
                  </div>
                </div>
                <div class="content-items-block">
                  <div><span>聯繫郵箱</span></div>
                  <div style="flex: 7.7;justify-content:start;">
                    <span class="item-long">{{item.applyemail}}</span></div>
                </div>
                <div class="content-items-block">
                  <div><span>單位名稱</span></div>
                  <div style="flex: 7.7;justify-content:start;">
                    <span class="item-long">{{item.applydeptname}}</span></div>
                </div>
                <div class="content-items-block">
                  <div><span>需求說明</span></div>
                  <div style="flex: 7.7;justify-content:start;">
                    <span class="item-long">{{item.describtion}}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </collapse>
      </div>
      <div class="space" v-if="formData.wfsslvpnprocess.attachids!=''"></div>
      <div class="form-info" v-if="formData.wfsslvpnprocess.attachids!=''" @click="isActive7 = !isActive7" >
        <a style="text-decoration:none;">
          <div class="rating__text"></div>
          <span class="course-head">附件</span>
          <img class="imgUrlArrowhead" :src="imgUrlArrowhead2" v-if="isActive7==true">
          <img class="imgUrlArrowhead2" :src="imgUrlArrowhead2" v-if="isActive7==false">
        </a>
      </div>
      <div class="ant-modal-body" v-if="formData.wfsslvpnprocess.attachids!=''">
        <collapse>
          <div v-show="isActive7">
            <div class="file-style">
              <el-form-item label="" prop="attachids">
                <el-upload :file-list="upload.fileList" :show-file-list="true" :on-preview="handlePreview"
                           :headers="upload.headers" :action="upload.url" list-type="text" :disabled='true'>
                </el-upload>
              </el-form-item>
            </div>
          </div>
        </collapse>
      </div>
      <div class="space"></div>
      <div class="form-info" @click="isActive5 = !isActive5">
        <a style="text-decoration:none;">
          <div class="rating__text"></div>
          <span class="course-head">簽核路徑</span>
          <img class="imgUrlArrowhead" :src="imgUrlArrowhead2" v-if="isActive5==true">
          <img class="imgUrlArrowhead2" :src="imgUrlArrowhead2" v-if="isActive5==false">
        </a>
      </div>
      <div class="ant-modal-body">
        <collapse>
          <div v-show="isActive5">
            <div style="padding:16px;font-size: 14px;">
              <div v-html="formData.chargeNodeInfo"></div>
            </div>
          </div>
        </collapse>
      </div>
      <div class="space"></div>
      <div class="form-info" @click="isActive8 = !isActive8">
        <a style="text-decoration:none;">
          <span class="course-head">簽核記錄</span>
          <div class="rating__text"></div>
          <img class="imgUrlArrowhead" :src="imgUrlArrowhead2" v-if="isActive8==true">
          <img class="imgUrlArrowhead2" :src="imgUrlArrowhead2" v-if="isActive8==false">
        </a>
      </div>
      <div class="ant-modal-body">
        <collapse>
          <div v-show="isActive8">
            <div style="padding:0px;">
              <div class="table-responsive">
                <table class="table">
                  <thead>
                  <tr>
                    <th>序號</th>
                    <th>簽核時間</th>
                    <th>簽核節點</th>
                    <th>簽核主管</th>
                    <th>簽核意見</th>
                    <th>批註</th>
                  </tr>
                  </thead>
                  <tbody>
                  <tr v-for="(item, index) in formData.tQhChargelogs" :key="index">
                    <td>{{index+1}}</td>
                    <td>{{parseTime(item.createtime,'{y}-{m}-{d} {h}:{i}:{s}')}}</td>
                    <td>{{item.chargenode}}</td>
                    <td>{{item.chargename}}</td>
                    <td>{{item.ispass}}</td>
                    <td>{{item.decrib}}</td>
                  </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </collapse>
      </div>
      <div class="ant-modal-body" style="text-align: center;margin-top: 10px;">
        <el-form-item label="">
          <el-input v-model="formData.comment" type="textarea" rows="3" :style="{width: '90%'}"
                    :placeholder="$t('table.activity.inputApprovalOpinions')"></el-input>
        </el-form-item>
        <div class="dialog-footer" align="center" style="padding:10px;display: flex;justify-content:space-around;">
          <el-button @click="handleTask(1)" :disabled="isDisable" style="flex:1;background-color: #EC6464;color: white;border-radius: 10px;"  v-if="(this.$store.state.user.empNo)==currentAuditUser  && formData.nodeName != null && formData.nodeName != undefined && formData.nodeName!='申請人驗收確認'">
            {{$t('table.activity.rejection')}}
          </el-button>
          <el-button @click="skipTask" :disabled="isDisable" style="flex:1;background-color:#4084FF;color: white;border-radius: 10px;margin: 0 10px" v-if="this.$store.state.user.empNo==currentAuditUser  && formData.nodeName != null && formData.nodeName != undefined">跳過</el-button>
          <el-button @click="handleTask(0)" :disabled="isDisable" style="flex:1;background-color:#01D4CB;color: white;border-radius: 10px"  v-if="this.$store.state.user.empNo==currentAuditUser  && formData.nodeName != null && formData.nodeName != undefined">
            {{$t('table.activity.pass')}}
          </el-button>
        </div>
      </div>
    </el-form>
  </div>
</template>
<script>
import {getAccessToken} from '@/utils/auth';
import {completeTask, getByKey, sslvpnShowDetail} from "@/api/caaesign/wfcommonprocess";
import {auditComplete,getNodeInfo, skipTask} from "@/api/caaesign/common";
import collapse from "@/utils/collapse.js";
import imgUrlArrowhead from '@/assets/images/t02.png';
import imgUrlArrowhead2 from '@/assets/images/arrow.svg';
import '@/assets/styles/design-build/design-add-view.scss'
import {getHeader} from "@/utils/entfrm";
export default {
  components: {
    collapse
  },
  props: [],
  data() {
    return {
      imgUrlArrowhead:imgUrlArrowhead,
      imgUrlArrowhead2:imgUrlArrowhead2,
      formData: {
        chargeNodeInfo:undefined,
        fileName:undefined,
        tQhChargelogs:[{
          chargename:undefined,
          createtime:undefined,
          ispass:undefined,
          decrib:undefined,
          chargenode:undefined,
        }],
        wfsslvpnprocess:{
          serialno:undefined,
          itemsEntitys:[{

          }],
        }
      },
      rules:{
      },
      tableHeight: document.documentElement.scrollHeight - 245 + "px",
      // 审批意见数据
      commentList: [],
      //簽核路徑
      signPath: [],
      activeCount: -1,
      signPathActive: -1,
      //任务图url
      imgUrl: '',
      isDisable: false,
      isDisable2: true,
      currentAuditUser:undefined,
      // 是否显示任务图
      showImgDialog: false,
      // 文件上传参数
      upload: {
        // 是否显示弹出层
        open: false,
        fileList: [],
        fileNameList: [],
        // 弹出层标题
        title: "",
        multiple: false,
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: getHeader(),
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/fileInfo/upload"
      },
      demandNatureOptions: [],
      promotionDegreeOptions:[],
      hardwareResourcesOptions:[],
      dockingSystemOptions:[],
      isMobile: true,
      labelPosition: 'left',
      isActive: true,
      isActive2: true,
      isActive3: true,
      isActive4: true,
      isActive5: true,
      isActive6: true,
      isActive7: true,
      isActive8: true,
      isActive9: true,
    }
  },
  computed: {},
  watch: {},
  created() {
    const serialno = this.$route.query.serialno;
    this.changeTagsView(this.$route.query);
    this.isMobile = this.isMobileFun()
    if (serialno != null && serialno != undefined) {
      if (getAccessToken()) {
        sslvpnShowDetail(serialno).then(response => {
          this.formData = JSON.parse(response.data);
          if (this.formData.wfsslvpnprocess.attachids) {
            let a = this.formData.wfsslvpnprocess.attachids.split(',');
            if (a.length > 0) {
              // 使用 Promise.all 等待所有异步操作完成
              Promise.all(
                a.map(item => {
                  if (item) {
                    return getByKey(item).then(response => {
                      return {
                        name: response.data.name,
                        url: response.data.url,
                        id: response.data.id,
                        size: response.data.sizez,
                        type: response.data.type
                      };
                    });
                  }
                  return null;
                })
              ).then(results => {
                // 过滤掉 null 值（即无效的 item）
                results = results.filter(result => result !== null);
                // 将结果赋值给 upload.fileList
                this.upload.fileList = results;
              });
            }
          }
        });
        getNodeInfo(serialno).then(response => {
          this.currentAuditUser = JSON.parse(response.data).auditUser;
        });

      }else{
        this.$router.push( '/caaesign/wfonlineprocessApp/404App')
      }
    }
    ESignApp.onPageFinished()
  },
  methods: {
    closeForm() {
      //关闭子页面
      ESignApp.back()
    },
    returnNext(){
      auditComplete(this.formData.wfsslvpnprocess.serialno)
    },
    returnForm(){
      ESignApp.back()
    },
    handleTrack() {
      this.imgUrl = process.env.VUE_APP_BASE_API + '/activiti/task/track/' + this.formData.processId,
        this.showImgDialog = true
    },
    skipTask:function(){
      skipTask(this.formData.wfsslvpnprocess.serialno).then(response => {
        this.$message({
          message: this.$t('tips.operationSuccessful'),
          type: 'success',
          iconClass: ' ',
          center:true,
          offset: window.screen.height / 2,
          customClass: 'msgbox'
        });
        this.returnNext();
      });
    },
    handleTask: function(pass) {
      if(pass == 0){
        this.$refs["elForm"].validate(valid => {
          if (valid && this.isDisable2) {
            this.isDisable = true;
            this.formData.pass = pass
            completeTask({
              serialno: this.formData.wfsslvpnprocess.serialno,
              comment: this.formData.comment,
              status: pass
            }).then(response => {
              if (response.code === 0) {
                this.$message({
                  message: this.$t('tips.operationSuccessful'),
                  type: 'success',
                  iconClass: ' ',
                  center:true,
                  offset: window.screen.height / 2,
                  customClass: 'msgbox'
                });
                this.returnNext();
              } else {
                this.msgError(response.msg);
              }
            });
          }
        });
      }else if(pass == 1 && this.formData.comment != null){
        this.isDisable = true;
        this.formData.pass = pass
        this.formData.serialno=this.formData.wfsslvpnprocess.serialno;
        completeTask({
          serialno: this.formData.wfsslvpnprocess.serialno,
          comment: this.formData.comment,
          status: pass
        }).then(response => {
          if (response.code === 0) {
            this.$message({
              message: this.$t('tips.operationSuccessful'),
              type: 'success',
              iconClass: ' ',
              center:true,
              offset: window.screen.height / 2,
              customClass: 'msgbox'
            });
            this.returnNext();
          } else {
            this.msgError(response.msg);
          }
        });
      }else {
        this.isDisable = false;
        this.msgInfo(this.$t('table.activity.inputApprovalOpinions'));
      }
    },
    handlePreview(file) {
      if(file.type=='pdf'||file.type=='PDF'){
        ESignApp.previewPDF(process.env.VUE_APP_BASE_API +  '/caaesign/fileManager/downloadFile/' + file.id+"?name="+encodeURIComponent(file.name)+"&size="+file.size+"&extension="+file.type+"&encrypt=N");
      }else{
        ESignApp.previewOffice(process.env.VUE_APP_BASE_API +  '/caaesign/fileManager/downloadFile/' + file.id+"?name="+encodeURIComponent(file.name)+"&size="+file.size+"&extension="+file.type+"&encrypt=N");
      }
    },
    checkBoxParse() {},
    cascaderParse() {},
    decodeHtmlEntities(str) {
      const parser = new DOMParser();
      const doc = parser.parseFromString(str, 'text/html');
      return doc.body.textContent || doc.body.innerText || '';
    },
  }
}

</script>
<style scoped lang="scss">
.el-form-item {
  margin-top: 0px;
  margin-bottom: 0px;
}
.space{
  height: 15px;
  background-color: #F7F7F7;
}
.course-head {
  font-size: 15px;
  color: #1E2233;
  font-weight: bold;
}
.rating__text{
  font-weight:normal;
  width:4px;
  height:17px;
  background: #4B8AF8;
  border-radius: 4px;
  float:left;
  margin-top: 11px;
  margin-right: 10px;
}
.form-info{
  background-color:#EAEEFF;
  padding-left:15px;
  line-height: 38px;
}
.imgUrlArrowhead{
  float:right;
  margin-top: 13px;
  width: 16px;
  height: 16px;
  margin-right: 14px;
  transform: scaleX(-1) rotate(-90deg);
}
.imgUrlArrowhead2{
  float:right;
  margin-top: 13px;
  width: 16px;
  height: 16px;
  margin-right: 14px;
  transform: scaleX(-1) rotate(90deg);
}
.ant-modal-body {
  padding: 0px;
  font-size: 15px;
  word-wrap: break-word
}
.profile-info-table {
  font-size: 14px;
  color: #06142D;
  border-radius: 10px;
  background-color: #FFFFFF;
  font-family: Microsoft YaHei;

  .profile-info-block {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 11px 15px;
    border-bottom: 1px solid #F7F7F7;
    >div:nth-child(1){
      flex-basis: 35%;
      color: #57585A;
    }
    >div:nth-child(2){
      flex-basis: 65%;
      text-align: right;
    }
  }
  .profile-info-block__value a svg {
    width: 15px;
    height: 15px;
  }
  .content-medium-block{
    display: flex;
    align-items: center;
    padding: 11px 15px;
    border-bottom: 1px solid #F7F7F7;
    div{
      flex: 1;
      span:nth-child(1){
        color: #57585A;
      }
    }
  }
  .content-items-block{
    display: flex;
    align-items: stretch;
    justify-content: center;
    height: 45px;
    div{
      display: flex;
      justify-content: center;
      align-items: center;
      overflow-wrap: break-word;
      word-break: break-all;
      text-align: center;
    }
    >div:nth-child(1),>div:nth-child(3),>div:nth-child(5){
      flex: 1.3 0;
      background-color:#EAEEFF;
      span:not(.item-long){
        color: #57585A;
        padding: 0 5px;
      }
    }
    >div:first-of-type{
      margin-left: 2px;
    }
    >div:last-of-type{
      margin-right: 2px;
    }
    >div:nth-child(2),>div:nth-child(4),>div:nth-child(6){
      flex: 1.7 0;
    }

    .item-long{
      padding: 0 5px;
      text-align: left;
    }
  }
  .content-items-title{
    margin-top: 5px;
    margin-bottom: 5px;
  }
}

/*=====  End of profile  ======*/
.el-button--success{
  color: #fff;
  background-color: #1E80F9;
  border-color: #1E80F9;

}
.el-button--medium{
  border-radius: inherit;
  font-family: Microsoft YaHei;
  font-size: 14px;
  padding:15px 0px;
}

.el-button+.el-button {
  margin-left: 0;
  margin-top: 0px;
}

.el-button--success.is-disabled, .el-button--success.is-disabled:hover, .el-button--success.is-disabled:focus, .el-button--success.is-disabled:active {
  color: #fff;
  background-color: #B4D6FF;
  border-color: #B4D6FF;
}
::v-deep .el-form-item__error {
  color: #ff4949;
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
  position:absolute;
  top: 100%;
  right: 0;
}
/*-------------*/
.table-responsive {
  min-height: .01%;
  overflow-x: auto;
  .table {
    width: 100%;
    font-size: 14px;
    td,th {
      text-align: center;
      padding: 8px;
      border: 1px solid #ddd;
      white-space: nowrap
    }
    tbody>tr:nth-of-type(odd) {
      background-color: #f9f9f9
    }
  }
}

.file-style{
  padding:0px 16px 10px 16px;
}
/deep/.file-style .el-upload{
  display: none;
}
.text-area-style{
  display: flex;
  align-items: center;
}
.text-area-type /deep/.el-textarea__inner{
  border: 0px;
  padding: 0px;
  color: black;
  overflow-y: scroll;
}

.scroll-box{
  height: 100%;
  overflow-y:scroll;
  -webkit-overflow-scrolling: touch;
  overflow-x: hidden;
  overflow-wrap: anywhere;
  white-space: pre-wrap;
}
/* 针对 iOS 设备 */
@supports (-webkit-overflow-scrolling: touch) {
  .scroll-box {
    height: 100%;
    overflow-y:scroll;
    -webkit-overflow-scrolling: touch;
    overflow-x: hidden;
    overflow-wrap: anywhere;
    white-space: pre-wrap;
  }
}
.content-small,.content-medium{
  display: none;
}
/* 小屏幕設備 */
@media (max-width: 767px){
  .content-small{
    display: block;
  }

}
/* 中等屏幕設備 */
@media (min-width: 768px){
  .content-medium{
    display: block;
  }
}
</style>
