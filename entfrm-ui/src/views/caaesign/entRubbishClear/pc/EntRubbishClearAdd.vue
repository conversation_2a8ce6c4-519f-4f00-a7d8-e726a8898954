<template>
  <div class="ant-modal-content">
    <div class="ant-modal-body">
      <el-form ref="elForm" class="ant-form ant-form-horizontal" :model="formData" :rules="rules"
        size="medium" label-width="100px" :label-position="labelPosition">
        <div class="ant-card ant-card-bordered" id="staffCard">
          <div class="ant-card-body">
            <span id="staffEvectionTitle" style="margin-bottom: 10px">{{formConfigData.title}}</span>
            <el-col :span="8" :xs="24" class="el-col-no-border">
              任務編碼:{{formData.serialno?formData.serialno:'提交表單后自動生成'}}
            </el-col>
            <el-col :span="8" :xs="24" class="el-col-no-border"> 填單時間:{{formData.createTime}}
            </el-col>
            <el-col :span="8" :xs="24" class="el-col-no-border">
              填單人:{{formData.makerNo +' / '+ formData.makerName}}
            </el-col>
            <el-row :gutter="15">
              <el-col :span="9" :xs="24">
                <el-form-item label-width="120px" label="申請人工號：" prop="applyEmpNo">
                  <el-input v-model="formData.applyEmpNo" placeholder="請輸入申請人工號" clearable
                    :style="{width: '100%'}" @change='applyEmpNo_onchange'></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8" :xs="24">
                <el-form-item label-width="120px" label="申請人姓名：" prop="applyEmpName">
                  <el-input v-model="formData.applyEmpName" placeholder="請輸入申請人姓名" clearable
                    :style="{width: '100%'}"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="7" :xs="24">
                <el-form-item label="所屬廠區：" prop="makerfactoryid">
                  <el-select v-model="formData.makerfactoryid" placeholder="請選擇所屬廠區" clearable
                    :style="{width: '100%'}">
                    <el-option v-for="(item, index) in makerfactoryidOptions" :key="index" :label="item.label"
                      :value="item.value" :disabled="item.disabled"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="9" :xs="24">
                <el-form-item label="單位代碼：" prop="applyDeptNo">
                  <el-input v-model="formData.applyDeptNo" placeholder="請輸入單位代碼" clearable
                    :style="{width: '100%'}"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8" :xs="24">
                <el-form-item label="費用代碼：" prop="applyCostNo">
                  <el-input v-model="formData.applyCostNo" placeholder="請輸入費用代碼" clearable
                    :style="{width: '100%'}"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="7" :xs="24">
                <el-form-item label-width="70px" label="資     位：" prop="applyLevelType">
                  <el-input v-model="formData.applyLevelType" placeholder="請輸入資位資位" clearable
                    :style="{width: '100%'}"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="9" :xs="24">
                <el-form-item label="單位名稱：" prop="applyDeptNam">
                  <el-input v-model="formData.applyDeptNam" placeholder="請輸入單位名稱" clearable
                    :style="{width: '100%'}"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8" :xs="24">
                <el-form-item label="管理職：" prop="applyManager">
                  <el-input v-model="formData.applyManager" placeholder="請輸入管理職" clearable
                    :style="{width: '100%'}"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="7" :xs="24">
                <el-form-item label="分機/手機：" prop="applyTel">
                  <el-input v-model="formData.applyTel" placeholder="請輸入分機/手機" clearable
                    :style="{width: '100%'}"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="9" :xs="24">
                <el-form-item label="郵     箱：" prop="applyMail">
                  <el-input v-model="formData.applyMail" placeholder="請輸入郵箱郵箱" clearable
                    :style="{width: '100%'}"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8" :xs="24">
                <el-form-item label="法       人：" prop="applyLegal">
                  <el-input v-model="formData.applyLegal" placeholder="請輸入法人法人：" clearable
                    :style="{width: '100%'}"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="7" :xs="24">
                <el-form-item label="需求日期：" prop="clearDemandDate">
                  <el-date-picker v-model="formData.clearDemandDate" format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd" :style="{width: '100%'}" placeholder="请選擇需求日期" clearable>
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="9" :xs="24">
                <el-form-item label="清運地點：" prop="clearPlace">
                  <el-input v-model="formData.clearPlace" placeholder="請輸入清運地點" clearable
                    :style="{width: '100%'}"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8" :xs="24">
                <el-form-item label="清運物品：" prop="clearRubbish">
                  <el-input v-model="formData.clearRubbish" placeholder="請輸入清運物品" clearable
                    :style="{width: '100%'}"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="7" :xs="24">
                <el-form-item label="是否分撿：" prop="clearDivision">
                  <el-radio-group v-model="formData.clearDivision" size="medium">
                    <el-radio v-for="(item, index) in clearDivisionOptions" :key="index" :label="item.value"
                      :disabled="item.disabled">{{item.label}}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24">
                <el-form-item label="需求說明：" prop="demandDescription">
                  <el-input v-model="formData.demandDescription" type="textarea" placeholder="請輸入需求說明"
                    :autosize="{minRows: 4, maxRows: 4}" :style="{width: '100%'}"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24">
                <el-form-item label="附件：" prop="attachids">
                  <el-upload :file-list="upload.fileList" :show-file-list="true" :on-change="handleChange"
                    :on-exceed="handleExceed" :on-preview="handlePreview" :on-remove="handleRemove"
                    :on-success="uploadsucces" :before-upload="attachidsBeforeUpload"
                    :headers="upload.headers" :action="upload.url" list-type="text">
                    <el-button size="small" type="primary" icon="el-icon-upload">點擊上傳</el-button>
                  </el-upload>
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24">
                <label-form :label=formConfigData.rubbish_code_label labelWidth='120' labelPosition='left'
                  labelSize='14' :textarea=formConfigData.rubbish_code_text textSize='14'></label-form>
              </el-col>
              <el-col :span="24" :xs="24">
                <label-form :label=formConfigData.rubbish_memo_label labelWidth='80' labelPosition='left'
                  labelSize='14' :textarea=formConfigData.rubbish_memo_text textSize='14'></label-form>
              </el-col>
              <el-col :span="24" :xs="24">
                <label-form label='審核路徑及審核記錄' labelWidth='280' labelPosition='left' labelSize='14'
                  textSize='14'></label-form>
              </el-col>
              <el-col :span="24" :xs="24">
                <el-row :gutter="15">
                  <el-col :span="6" :xs="24">
                    <el-form-item prop="kchargeno" label-width="0">
                      <entfrm-sign-form-audit-hq :label="formConfigData.kchargeno"
                        :required='formConfigData.kchargeno_required' :alias="this.alias"
                        :applyFactoryId="this.formData.applyFactoryId"
                        :applyDeptNo="this.formData.applyDeptNo" :multipleSelect=true
                        :selectEmpProp="{empNo:this.formData.kchargeno,empName:this.formData.kchargename}"
                        :kchargeno="this.formData.kchargeno" :bchargeno="this.formData.bchargeno"
                        :cchargeno="this.formData.cchargeno" model-no="kchargeno" model-name="kchargename"
                        @onSignFormSelected="onSignFormSelected"></entfrm-sign-form-audit-hq>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6" :xs="24">
                    <el-form-item prop="bchargeno" label-width="0">
                      <entfrm-sign-form-audit-hq :label="formConfigData.bchargeno"
                        :required='formConfigData.bchargeno_required' :alias="this.alias"
                        :applyFactoryId="this.formData.applyFactoryId"
                        :applyDeptNo="this.formData.applyDeptNo" :multipleSelect=true
                        :selectEmpProp="{empNo:this.formData.bchargeno,empName:this.formData.bchargename}"
                        :kchargeno="this.formData.kchargeno" :bchargeno="this.formData.bchargeno"
                        :cchargeno="this.formData.cchargeno" model-no="bchargeno" model-name="bchargename"
                        @onSignFormSelected="onSignFormSelected"></entfrm-sign-form-audit-hq>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6" :xs="24">
                    <el-form-item prop="cchargeno" label-width="0">
                      <entfrm-sign-form-audit-hq :label="formConfigData.cchargeno"
                        :required='formConfigData.cchargeno_required' :alias="this.alias"
                        :applyFactoryId="this.formData.applyFactoryId"
                        :applyDeptNo="this.formData.applyDeptNo" :multipleSelect=true
                        :selectEmpProp="{empNo:this.formData.cchargeno,empName:this.formData.cchargename}"
                        :kchargeno="this.formData.kchargeno" :bchargeno="this.formData.bchargeno"
                        :cchargeno="this.formData.cchargeno" model-no="cchargeno" model-name="cchargename"
                        @onSignFormSelected="onSignFormSelected"></entfrm-sign-form-audit-hq>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6" :xs="24">
                    <el-form-item prop="hsgeneralno" label-width="0">
                      <entfrm-sign-form-add-hq :label="formConfigData.hsgeneralno"
                        :required='formConfigData.hsgeneralno_required'
                        :selectEmpProp="{empNo:this.formData.hsgeneralno,empName:this.formData.hsgeneralname}"
                        model-no="hsgeneralno" model-name="hsgeneralname"
                        @onSignFormSelected="onSignFormSelected"></entfrm-sign-form-add-hq>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-col>
              <el-col :span="24" :xs="24">
                <el-row :gutter="15">
                  <el-col :span="6" :xs="24">
                    <el-form-item prop="hsjingguanno" label-width="0">
                      <entfrm-sign-form-add-hq :label="formConfigData.hsjingguanno"
                        :required='formConfigData.hsjingguanno_required'
                        :selectEmpProp="{empNo:this.formData.hsjingguanno,empName:this.formData.hsjingguanname}"
                        model-no="hsjingguanno" model-name="hsjingguanname"
                        @onSignFormSelected="onSignFormSelected"></entfrm-sign-form-add-hq>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6" :xs="24">
                    <el-form-item prop="zchargeno" label-width="0">
                      <entfrm-sign-form-audit-hq :label="formConfigData.zchargeno"
                        :required='formConfigData.zchargeno_required' :alias="this.alias"
                        :applyFactoryId="this.formData.applyFactoryId"
                        :applyDeptNo="this.formData.applyDeptNo" :multipleSelect=true
                        :selectEmpProp="{empNo:this.formData.zchargeno,empName:this.formData.zchargename}"
                        :kchargeno="this.formData.kchargeno" :bchargeno="this.formData.bchargeno"
                        :cchargeno="this.formData.cchargeno" model-no="zchargeno" model-name="zchargename"
                        @onSignFormSelected="onSignFormSelected"></entfrm-sign-form-audit-hq>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6" :xs="24">
                    <el-form-item prop="generalquerenno" label-width="0">
                      <entfrm-sign-form-duty :label="formConfigData.generalquerenno"
                        :required='formConfigData.generalquerenno_required' duty-id="670" :alias="this.alias"
                        :applyFactoryId="this.formData.makerfactoryid" :isMobile=this.isMobile
                        :multipleSelect=true
                        :selectEmpProp="{empNo:this.formData.generalquerenno,empName:this.formData.generalquerenname}"
                        model-no="generalquerenno" model-name="generalquerenname"
                        @onSignFormSelected="onSignFormSelected"></entfrm-sign-form-duty>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-col>
              <el-col :span="24" :xs="24">
                <div class="dialog-footer" align="center" style="padding-top: 5px;padding-bottom: 5px">
                  <!--          <el-form-item size="large">-->
                  <el-button type="success" @click="handleSubmit" :disabled="isDisable">{{$t('table.submit')}}
                  </el-button>
                  <el-button type="primary" @click="submitForm" :disabled="isDisable">{{$t('common.save')}}
                  </el-button>
                  <el-button @click="closeForm">{{$t('common.cancel')}}</el-button>
                  <!--        </el-form-item>-->
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-form>
    </div>
  </div>
</template>
<script>
import {
  getEntRubbishClear,
  addEntRubbishClear,
  editEntRubbishClear,
  addEntRubbishClearAndStartProcess,
  editEntRubbishClearAndStartProcess,
  getSignConfigList,
  exportEntRubbishClearItems
}
from "@/api/caaesign/entRubbishClear"
import '@/assets/styles/design-build/design-add-view.scss'
import {
  getAccessToken,
  getZltAccessToken
}
from "@/utils/auth";
import {
  previewFileOos,
  getHeader
}
from "@/utils/entfrm";
import {
  listFileInfo,
  getFileInfo,
  delFileInfo,
  addFileInfo,
  editFileInfo,
  getByKey
}
from "@/api/system/fileInfo";
export default {
  components: {},
  props: [],
  data() {
    return {
      formData: {
        applyEmpNo: undefined,
        applyEmpName: undefined,
        makerfactoryid: undefined,
        applyDeptNo: undefined,
        applyCostNo: undefined,
        applyLevelType: undefined,
        applyDeptNam: undefined,
        applyManager: undefined,
        applyTel: undefined,
        applyMail: undefined,
        applyLegal: undefined,
        clearDemandDate: null,
        clearPlace: undefined,
        clearRubbish: undefined,
        clearDivision: undefined,
        demandDescription: undefined,
        attachids: "",
        cchargeno: undefined,
        hsgeneralno: undefined,
        hsjingguanno: undefined,
        zchargeno: undefined,
        generalquerenno: undefined,
        makerNo: this.$store.state.user.empNo,
        makerName: this.$store.state.user.name,
        dataSource: "pc",
      },
      formConfigData: {
        title: "垃圾清運服務申請單",
        rubbish_code_label: `本年度單價：`,
        rubbish_code_text: `1.施工垃圾：338元/車  （10T 清運車）；生活垃圾：155元/桶/月  （240L垃圾桶）
2.總務窗口：63357`,
        rubbish_memo_label: `備註`,
        rubbish_memo_text: `1.此單適用于廠房改造、設備拆裝、施工過程中產生的不可回收雜物，且不屬於工業危廢物
2.清運費用根據公司與環衛清運供應商簽訂之《垃圾清運協議》中約定費用為準
3.清運費用直接掛需求單位
4.核准主管為申請單位權限主管
5.清運過程中需由需求單位、庶務服務中心共同監管裝車`,
        kchargeno: "課級主管",
        kchargeno_required: false,
        bchargeno: "部級主管",
        bchargeno_required: false,
        cchargeno: "廠級主管",
        cchargeno_required: true,
        hsgeneralno: "會審總務窗口",
        hsgeneralno_required: true,
        hsjingguanno: "會審經管",
        hsjingguanno_required: true,
        zchargeno: "製造處級主管",
        zchargeno_required: true,
        generalquerenno: "總務窗口收單確認",
        generalquerenno_required: true,
      },
      tableHeight: document.documentElement.scrollHeight - 245 + "px",
      rules: {
        applyEmpNo: [{
          required: true,
          message: '請輸入申請人工號',
          trigger: 'blur'
        }],
        applyEmpName: [{
          required: true,
          message: '請輸入申請人姓名',
          trigger: 'blur'
        }],
        makerfactoryid: [{
          required: true,
          message: '請選擇所屬廠區',
          trigger: 'change'
        }],
        applyDeptNo: [{
          required: true,
          message: '請輸入單位代碼',
          trigger: 'blur'
        }],
        applyCostNo: [{
          required: true,
          message: '請輸入費用代碼',
          trigger: 'blur'
        }],
        applyLevelType: [{
          required: true,
          message: '請輸入資位資位',
          trigger: 'blur'
        }],
        applyDeptNam: [{
          required: true,
          message: '請輸入單位名稱',
          trigger: 'blur'
        }],
        applyManager: [],
        applyTel: [{
          required: true,
          message: '請輸入分機/手機',
          trigger: 'blur'
        }],
        applyMail: [{
          required: true,
          message: '請輸入郵箱郵箱',
          trigger: 'blur'
        }],
        applyLegal: [],
        clearDemandDate: [{
          required: true,
          message: '请選擇需求日期',
          trigger: 'change'
        }],
        clearPlace: [{
          required: true,
          message: '請輸入清運地點',
          trigger: 'blur'
        }],
        clearRubbish: [{
          required: true,
          message: '請輸入清運物品',
          trigger: 'blur'
        }],
        clearDivision: [],
        demandDescription: [{
          required: true,
          message: '請輸入需求說明',
          trigger: 'blur'
        }],
        attachids: [],
        kchargeno: [{
          required: false,
          message: '課級主管不能為空',
          trigger: 'change'
        }],
        bchargeno: [{
          required: false,
          message: '部級主管不能為空',
          trigger: 'change'
        }],
        cchargeno: [{
          required: true,
          message: '廠級主管不能為空',
          trigger: 'change'
        }],
        hsgeneralno: [{
          required: true,
          message: '會審總務窗口不能為空',
          trigger: 'change'
        }],
        hsjingguanno: [{
          required: true,
          message: '會審經管不能為空',
          trigger: 'change'
        }],
        zchargeno: [{
          required: true,
          message: '製造處級主管不能為空',
          trigger: 'change'
        }],
        generalquerenno: [{
          required: true,
          message: '總務窗口收單確認不能為空',
          trigger: 'change'
        }],
      },
      // 文件上传参数
      upload: {
        // 是否显示弹出层
        open: false,
        fileList: [],
        fileNameList: [],
        // 弹出层标题
        title: "",
        multiple: false,
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: this.getHeader(),
        // 上传的地址
        url: this.getAppBaseApi() + "/system/fileInfo/uploadWithOos"
      },
      makerfactoryidOptions: [],
      clearDivisionOptions: [],
      isMobile: false,
      isDisable: false,
      labelPosition: 'right',
      alias: "postgresql$_$pghrsign_ipebg_test$_$075d399c2dcaa20f94d53895254e58a1%_%design"
    }
  },
  computed: {},
  watch: {},
  created() {
    const id = this.$route.query.id;
    this.changeTagsView(this.$route.query);
    this.isMobile = this.isMobileFun()
    if (this.isMobile) {
      this.labelPosition = 'top'
    }
    getSignConfigList().then(response => {
      if (response.code === 0) {
        response.data.forEach(element => {
          const colKey = element.colKey.split('_')
          if (colKey.length > 1) {
            if (this.rules[colKey[0]] && colKey[1] === 'required') {
              this.rules[colKey[0]][0]['required'] = element.colValue === 'true'
              this.formConfigData[element.colKey] = element.colValue === 'true'
            }
            else {
              this.formConfigData[element.colKey] = element.colValue
            }
          }
          else {
            this.formConfigData[element.colKey] = element.colValue
          }
        });
      }
    })
    if (id != null && id != undefined) {
      getEntRubbishClear(id).then(response => {
        this.formData = response.data;
        this.checkBoxParse()
        this.cascaderParse()
        this.timeRangParse()
        if (this.formData.attachids) {
          let a = this.formData.attachids.split(',');
          if (a.length > 0) {
            a.forEach(item => {
              if (item) {
                getByKey(item).then(response => {
                  this.upload.fileList.push({
                    name: response.data.orignalName,
                    url: response.data.name
                  });
                })
              }
            })
          }
        }
      });
    }
    this.getMakerfactoryidOptions()
    this.getClearDivisionOptions()
  },
  mounted() {
    this.$nextTick(function() {
      if (this.isMobile) {
        this.entRubbishClearItemsDragTableMobileClientWidth = this.$refs
          .entRubbishClearItemsDragTableMobile.$el.clientWidth
      }
    })
  },
  methods: {
    submitForm() {
      this.$refs['elForm'].validate(valid => {
        if (valid) {
          this.isDisable = true;
          const formData = JSON.parse(JSON.stringify(this.formData))
          if (formData.id != undefined) {
            editEntRubbishClear(formData).then(response => {
              if (response.code === 0) {
                this.msgSuccess(this.$t('tips.updateSuccess'));
                this.closeForm();
              }
              else {
                this.msgError(response.msg);
              }
              this.isDisable = false;
            });
          }
          else {
            addEntRubbishClear(formData).then(response => {
              if (response.code === 0) {
                this.msgSuccess(this.$t('tips.createSuccess'));
                this.closeForm();
              }
              else {
                this.msgError(response.msg);
              }
              this.isDisable = false;
            });
          }
          /**
           * 提交的時候刪除被標記的需要刪除的附件
           */
          if (this.formData.attachids) {
            this.upload.fileNameList.forEach(item => {
              if (item) {
                delFileInfo(item);
              }
            })
          }
        }
      })
    },
    handleSubmit: function() {
      this.$refs["elForm"].validate(valid => {
        if (valid) {
          this.isDisable = true;
          const formData = JSON.parse(JSON.stringify(this.formData))
          if (formData.id != undefined) {
            editEntRubbishClearAndStartProcess(formData).then(response => {
              if (response.code === 0) {
                this.msgSuccess(this.$t('tips.updateSuccess'));
                this.closeForm();
              }
              else {
                this.msgError(response.msg);
              }
              this.isDisable = false;
            });
          }
          else {
            addEntRubbishClearAndStartProcess(formData).then(response => {
              if (response.code === 0) {
                this.msgSuccess(this.$t('tips.createSuccess'));
                this.closeForm();
              }
              else {
                this.msgError(response.msg);
              }
              this.isDisable = false;
            });
          }
          /**
           * 提交的時候刪除被標記的需要刪除的附件
           */
          if (this.formData.attachids) {
            this.upload.fileNameList.forEach(item => {
              if (item) {
                delFileInfo(item);
              }
            })
          }
        }
      })
    },
    resetForm() {
      this.$refs['elForm'].resetFields()
    },
    closeForm() {
      //关闭子页面
      if (this.$store.state.settings.tagsView) {
        this.$router.go(-1) // 返回
        this.$store.state.tagsView.visitedViews.splice(this.$store.state.tagsView.visitedViews.findIndex(
          item => item.path === this.$route.path), 1)
        this.$router.push(this.$store.state.tagsView.visitedViews[this.$store.state.tagsView.visitedViews
          .length - 1].path)
      }
      else {
        parent.postMessage("closeCurrentTabMessage", '*');
      }
    },
    getMakerfactoryidOptions() {
      this.getDicts("caaesign_factory").then(response => {
        this.makerfactoryidOptions = response.data;
      });
    },
    getClearDivisionOptions() {
      this.getDicts("clear_division").then(response => {
        this.clearDivisionOptions = response.data;
      });
    },
    attachidsBeforeUpload(file) {
      let isRightSize = file.size / 1024 / 1024 < 2
      if (!isRightSize) {
        this.$message.error('文件大小超过 2MB')
      }
      return isRightSize
    },
    // 文件上传成功处理
    uploadsucces(response, file, fileList) {
      if (response.data.name != undefined && response.data.name != "undefined") {
        this.upload.fileList.push({
          name: file.name,
          url: response.data.name
        });
        this.formData.attachids += response.data.name + ",";
      }
    },
    handleChange(file, fileList) {},
    handleExceed(file, fileList) {},
    handleRemove(file, fileList) {
      this.$emit("delUploadImage", file.name);
      const index = this.upload.fileList.indexOf(file);
      this.upload.fileList.splice(index, 1);
      if (this.formData.attachids) {
        this.formData.attachids = this.formData.attachids.replace(file.url + ",", "");
      }
      this.upload.fileNameList.push(file.url);
      // delFileInfo(file.url);
    },
    handlePreview(file) {
      previewFileOos(file.url)
    },
    checkBoxParse() {},
    cascaderParse() {},
    timeRangParse() {},
    applyEmpNo_onchange(data) {
      this.getInfoUserByEmpno(data).then(response => {
        if (response.code !== 0) {
          this.msgError(response.msg);
        }
        else {
          if (response.data != null) {
            this.formData.applyEmpName = response.data.empname
            this.formData.makerfactoryid = response.data.factoryid
            this.formData.applyDeptNo = response.data.deptno
            this.formData.applyCostNo = response.data.deptcostno
            this.formData.applyLevelType = response.data.leveltypename
            this.formData.applyDeptNam = response.data.deptname
            this.formData.applyManager = response.data.ismanager
            this.formData.applyMail = response.data.email
            this.formData.applyLegal = response.data.artificialperson
            this.formData.applyFactoryId = response.data.factoryid
          }
          else {
            this.formData.applyEmpName = ''
            this.formData.makerfactoryid = ''
            this.formData.applyDeptNo = ''
            this.formData.applyCostNo = ''
            this.formData.applyLevelType = ''
            this.formData.applyDeptNam = ''
            this.formData.applyManager = ''
            this.formData.applyMail = ''
            this.formData.applyLegal = ''
            this.formData.applyFactoryId = ''
          }
        }
      });
    },
    onSignFormSelected(selectEmp, modelNo, modelName) {
      this.$set(this.formData, modelNo, selectEmp.empNo)
      this.$set(this.formData, modelName, selectEmp.empName)
    },
  }
}

</script>
<style scoped>
.el-upload__tip {
  line-height: 1.2;
}

.del-handler {
  font-size: 20px;
  color: #ff4949;
}

.el-slider>/deep/.el-slider__runway {
  margin: 15px 0;
}

.el-checkbox-group {
  font-size: inherit;
}

.el-color-picker {
  display: inherit;
}

.talbe-name-style {
  font-size: 16px;
  font-weight: bold;
  color: #1DB8FF;
  line-height: 50px;
}

/deep/.el-input-number--medium {
  width: 100%;
}

</style>
