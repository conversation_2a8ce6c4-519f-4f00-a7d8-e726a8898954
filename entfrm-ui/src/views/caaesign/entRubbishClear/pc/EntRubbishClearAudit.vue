<template>
  <div class="ant-modal-content">
    <div class="ant-modal-body">
      <el-form ref="elForm" class="ant-form ant-form-horizontal" :model="formData" size="medium"
        label-width="100px" :label-position="labelPosition">
        <div class="ant-card ant-card-bordered" id="staffCard">
          <div class="ant-card-body">
            <el-row :gutter="15">
              <span id="staffEvectionTitle" style="margin-bottom: 10px">{{formConfigData.title}}</span>
              <el-col :span="8" :xs="24" class="el-col-no-border bar-to-bottom">
                任務編碼:{{formData.serialno?formData.serialno:'提交表單后自動生成'}}
              </el-col>
              <el-col :span="8" :xs="24" class="el-col-no-border bar-to-bottom"> 填單時間:{{formData.createTime}}
              </el-col>
              <el-col :span="8" :xs="24" class="el-col-no-border bar-to-bottom">
                填單人:{{formData.makerNo +' / '+ formData.makerName}}
              </el-col>
              <el-col :span="9" :xs="24">
                <el-form-item label-width="120px" label="申請人工號：" prop="applyEmpNo">
                  {{formData.applyEmpNo}}
                </el-form-item>
              </el-col>
              <el-col :span="8" :xs="24">
                <el-form-item label-width="120px" label="申請人姓名：" prop="applyEmpName">
                  {{formData.applyEmpName}}
                </el-form-item>
              </el-col>
              <el-col :span="7" :xs="24">
                <el-form-item label="所屬廠區：" prop="makerfactoryid">
                  {{formData.makerfactoryidValue}}
                </el-form-item>
              </el-col>
              <el-col :span="9" :xs="24">
                <el-form-item label="單位代碼：" prop="applyDeptNo">
                  {{formData.applyDeptNo}}
                </el-form-item>
              </el-col>
              <el-col :span="8" :xs="24">
                <el-form-item label="費用代碼：" prop="applyCostNo">
                  {{formData.applyCostNo}}
                </el-form-item>
              </el-col>
              <el-col :span="7" :xs="24">
                <el-form-item label-width="70px" label="資     位：" prop="applyLevelType">
                  {{formData.applyLevelType}}
                </el-form-item>
              </el-col>
              <el-col :span="9" :xs="24">
                <el-form-item label="單位名稱：" prop="applyDeptNam">
                  {{formData.applyDeptNam}}
                </el-form-item>
              </el-col>
              <el-col :span="8" :xs="24">
                <el-form-item label="管理職：" prop="applyManager">
                  {{formData.applyManager}}
                </el-form-item>
              </el-col>
              <el-col :span="7" :xs="24">
                <el-form-item label="分機/手機：" prop="applyTel">
                  {{formData.applyTel}}
                </el-form-item>
              </el-col>
              <el-col :span="9" :xs="24">
                <el-form-item label="郵     箱：" prop="applyMail">
                  {{formData.applyMail}}
                </el-form-item>
              </el-col>
              <el-col :span="8" :xs="24">
                <el-form-item label="法       人：" prop="applyLegal">
                  {{formData.applyLegal}}
                </el-form-item>
              </el-col>
              <el-col :span="7" :xs="24">
                <el-form-item label="需求日期：" prop="clearDemandDate">
                  {{formData.clearDemandDate}}
                </el-form-item>
              </el-col>
              <el-col :span="9" :xs="24">
                <el-form-item label="清運地點：" prop="clearPlace">
                  {{formData.clearPlace}}
                </el-form-item>
              </el-col>
              <el-col :span="8" :xs="24">
                <el-form-item label="清運物品：" prop="clearRubbish">
                  {{formData.clearRubbish}}
                </el-form-item>
              </el-col>
              <el-col :span="7" :xs="24">
                <el-form-item label="是否分撿：" prop="clearDivision">
                  {{formData.clearDivisionValue}}
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24">
                <el-form-item label="需求說明：" prop="demandDescription">
                  {{formData.demandDescription}}
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24">
                <el-form-item label="附件：" prop="attachids">
                  <el-upload :file-list="upload.fileList" :show-file-list="true" :on-preview="handlePreview"
                    :headers="upload.headers" :action="upload.url" list-type="text" :disabled='true'>
                    <el-button size="small" type="primary" icon="el-icon-upload" :disabled='true'>點擊上傳
                    </el-button>
                  </el-upload>
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24">
                <label-form :label=formConfigData.rubbish_code_label labelWidth='120' labelPosition='left'
                  labelSize='14' :textarea=formConfigData.rubbish_code_text textSize='14'></label-form>
              </el-col>
              <el-col :span="24" :xs="24">
                <label-form :label=formConfigData.rubbish_memo_label labelWidth='80' labelPosition='left'
                  labelSize='14' :textarea=formConfigData.rubbish_memo_text textSize='14'></label-form>
              </el-col>
              <el-col :span="24" :xs="24">
                <label-form label='審核路徑及審核記錄' labelWidth='280' labelPosition='left' labelSize='14'
                  textSize='14'></label-form>
              </el-col>
              <el-col :span="24" :xs="24">
                <el-form-item :label="$t('table.activity.approvalOpinions')">
                  <el-input v-model="formData.comment" type="textarea" rows="3"
                    :placeholder="$t('table.activity.inputApprovalOpinions')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24">
                <div class="dialog-footer" align="center" style="padding-top: 5px;padding-bottom: 5px">
                  <el-button type="success" @click="handleTask(0)" :disabled="isDisable">
                    {{$t('table.activity.pass')}}
                  </el-button>
                  <el-button type="danger" @click="handleTask(1)" :disabled="isDisable">
                    {{$t('table.activity.rejection')}}
                  </el-button>
                  <el-button @click="handleTrack">{{$t('table.activity.flowTracing')}}</el-button>
                  <el-button @click="closeForm">{{$t('common.close')}}</el-button>
                </div>
              </el-col>
              <!-- 簽核線 -->
              <el-col :span="24" class="print-hide-div">
                <div class="dialog-footer" style="padding-top: 5px;padding-bottom: 5px">
                  <div v-html="signPath"></div>
                </div>
              </el-col>
              <!-- 审核记录 -->
              <el-table border stripe :data="commentList">
                <el-table-column :label="$t('table.activity.taskId')" align="center" prop="id" />
                <el-table-column :label="$t('table.activity.approvedBy')" align="center" prop="userId"
                  :show-overflow-tooltip="true" />
                <el-table-column :label="$t('table.activity.approvalOpinions')" align="center"
                  prop="fullMessage" :show-overflow-tooltip="true" />
                <el-table-column :label="$t('table.activity.operIp')" align="center" prop="operIp"
                  :show-overflow-tooltip="true" />
                <el-table-column :label="$t('table.activity.auditStatus')" align="center" prop="status"
                  :formatter="statusFormat" :show-overflow-tooltip="true" />
                <el-table-column :label="$t('table.activity.approvalTime')" align="center" prop="time"
                  width="180">
                  <template slot-scope="scope">
                    <span>{{ parseTime(scope.row.time) }}</span>
                  </template>
                </el-table-column>
              </el-table>
              <!-- 任务跟踪对话框 -->
              <el-dialog :title="$t('table.activity.taskMap')" :visible.sync="showImgDialog" width="1000px">
                <img :src="imgUrl" style="padding-bottom: 60px;width:100%;">
              </el-dialog>
            </el-row>
          </div>
        </div>
      </el-form>
    </div>
  </div>
</template>
<script>
import {
  getEntRubbishClear,
  addEntRubbishClear,
  editEntRubbishClear,
  getSignPath,
  updateAndCheck,
  getSignConfigList,
  exportEntRubbishClearItems
}
from "@/api/caaesign/entRubbishClear"
import '@/assets/styles/design-build/design-add-view.scss'
import {
  listTask,
  getTask,
  checkTask,
  taskComment
}
from "@/api/activiti/task"
import {
  getAccessToken,
  getZltAccessToken
}
from "@/utils/auth";
import {
  previewFileOos,
  getHeader
}
from "@/utils/entfrm";
import {
  listFileInfo,
  getFileInfo,
  delFileInfo,
  addFileInfo,
  editFileInfo,
  getByKey
}
from "@/api/system/fileInfo";
export default {
  components: {},
  props: [],
  data() {
    return {
      formData: {
        applyEmpNo: undefined,
        applyEmpName: undefined,
        makerfactoryid: undefined,
        applyDeptNo: undefined,
        applyCostNo: undefined,
        applyLevelType: undefined,
        applyDeptNam: undefined,
        applyManager: undefined,
        applyTel: undefined,
        applyMail: undefined,
        applyLegal: undefined,
        clearDemandDate: null,
        clearPlace: undefined,
        clearRubbish: undefined,
        clearDivision: undefined,
        demandDescription: undefined,
        attachids: "",
        cchargeno: undefined,
        hsgeneralno: undefined,
        hsjingguanno: undefined,
        zchargeno: undefined,
        generalquerenno: undefined,
      },
      formConfigData: {
        title: "垃圾清運服務申請單",
        rubbish_code_label: `本年度單價：`,
        rubbish_code_text: `1.施工垃圾：338元/車  （10T 清運車）；生活垃圾：155元/桶/月  （240L垃圾桶）
2.總務窗口：63357`,
        rubbish_memo_label: `備註`,
        rubbish_memo_text: `1.此單適用于廠房改造、設備拆裝、施工過程中產生的不可回收雜物，且不屬於工業危廢物
2.清運費用根據公司與環衛清運供應商簽訂之《垃圾清運協議》中約定費用為準
3.清運費用直接掛需求單位
4.核准主管為申請單位權限主管
5.清運過程中需由需求單位、庶務服務中心共同監管裝車`,
        kchargeno: "課級主管",
        kchargeno_required: false,
        bchargeno: "部級主管",
        bchargeno_required: false,
        cchargeno: "廠級主管",
        cchargeno_required: true,
        hsgeneralno: "會審總務窗口",
        hsgeneralno_required: true,
        hsjingguanno: "會審經管",
        hsjingguanno_required: true,
        zchargeno: "製造處級主管",
        zchargeno_required: true,
        generalquerenno: "總務窗口收單確認",
        generalquerenno_required: true,
      },
      rules: {
        applyEmpNo: [{
          required: true,
          message: '請輸入申請人工號',
          trigger: 'blur'
        }],
        applyEmpName: [{
          required: true,
          message: '請輸入申請人姓名',
          trigger: 'blur'
        }],
        makerfactoryid: [{
          required: true,
          message: '請選擇所屬廠區',
          trigger: 'change'
        }],
        applyDeptNo: [{
          required: true,
          message: '請輸入單位代碼',
          trigger: 'blur'
        }],
        applyCostNo: [{
          required: true,
          message: '請輸入費用代碼',
          trigger: 'blur'
        }],
        applyLevelType: [{
          required: true,
          message: '請輸入資位資位',
          trigger: 'blur'
        }],
        applyDeptNam: [{
          required: true,
          message: '請輸入單位名稱',
          trigger: 'blur'
        }],
        applyManager: [],
        applyTel: [{
          required: true,
          message: '請輸入分機/手機',
          trigger: 'blur'
        }],
        applyMail: [{
          required: true,
          message: '請輸入郵箱郵箱',
          trigger: 'blur'
        }],
        applyLegal: [],
        clearDemandDate: [{
          required: true,
          message: '请選擇需求日期',
          trigger: 'change'
        }],
        clearPlace: [{
          required: true,
          message: '請輸入清運地點',
          trigger: 'blur'
        }],
        clearRubbish: [{
          required: true,
          message: '請輸入清運物品',
          trigger: 'blur'
        }],
        clearDivision: [],
        demandDescription: [{
          required: true,
          message: '請輸入需求說明',
          trigger: 'blur'
        }],
        attachids: [],
        kchargeno: [{
          required: false,
          message: '課級主管不能為空',
          trigger: 'change'
        }],
        bchargeno: [{
          required: false,
          message: '部級主管不能為空',
          trigger: 'change'
        }],
        cchargeno: [{
          required: true,
          message: '廠級主管不能為空',
          trigger: 'change'
        }],
        hsgeneralno: [{
          required: true,
          message: '會審總務窗口不能為空',
          trigger: 'change'
        }],
        hsjingguanno: [{
          required: true,
          message: '會審經管不能為空',
          trigger: 'change'
        }],
        zchargeno: [{
          required: true,
          message: '製造處級主管不能為空',
          trigger: 'change'
        }],
        generalquerenno: [{
          required: true,
          message: '總務窗口收單確認不能為空',
          trigger: 'change'
        }],
      },
      tableHeight: document.documentElement.scrollHeight - 245 + "px",
      // 审批意见数据
      commentList: [],
      //簽核路徑
      signPath: [],
      //任务图url
      imgUrl: '',
      isDisable: false,
      // 是否显示任务图
      showImgDialog: false,
      auditStatus: [],
      // 文件上传参数
      upload: {
        // 是否显示弹出层
        open: false,
        fileList: [],
        fileNameList: [],
        // 弹出层标题
        title: "",
        multiple: false,
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: this.getHeader(),
        // 上传的地址
        url: this.getAppBaseApi() + "/system/fileInfo/uploadWithOos"
      },
      makerfactoryidOptions: [],
      clearDivisionOptions: [],
      isMobile: false,
      labelPosition: 'left',
    }
  },
  computed: {},
  watch: {},
  created() {
    const id = this.$route.query.id;
    this.changeTagsView(this.$route.query);
    this.isMobile = this.isMobileFun()
    if (this.isMobile) {
      this.labelPosition = 'top'
    }
    getSignConfigList().then(response => {
      if (response.code === 0) {
        response.data.forEach(element => {
          const colKey = element.colKey.split('_')
          if (colKey.length > 1) {
            if (this.rules[colKey[0]] && colKey[1] === 'required') {
              this.rules[colKey[0]][0]['required'] = element.colValue === 'true'
              this.formConfigData[element.colKey] = element.colValue === 'true'
            }
            else {
              this.formConfigData[element.colKey] = element.colValue
            }
          }
          else {
            this.formConfigData[element.colKey] = element.colValue
          }
        });
      }
    })
    if (id != null && id != undefined) {
      getEntRubbishClear(id).then(response => {
        this.formData = response.data;
        this.checkBoxParse()
        this.cascaderParse()
        this.timeRangParse()
        taskComment(this.formData.processId).then(response => {
          this.commentList = response.data;
        });
        getSignPath(this.formData.processId).then(response => {
          this.signPath = response.data;
        });
        if (this.formData.attachids) {
          let a = this.formData.attachids.split(',');
          if (a.length > 0) {
            a.forEach(item => {
              if (item) {
                getByKey(item).then(response => {
                  this.upload.fileList.push({
                    name: response.data.orignalName,
                    url: response.data.name
                  });
                })
              }
            })
          }
        }
        this.getMakerfactoryidOptions()
        this.getClearDivisionOptions()
        this.getDicts("caaesign_factory").then(response => {
          if (this.formData.makerfactoryid) {
            this.formData.makerfactoryidValue = JSON.parse(JSON.stringify(this.formData
              .makerfactoryid))
            response.data.forEach(item => {
              if (this.formData.makerfactoryid) {
                this.formData.makerfactoryidValue = this.formData.makerfactoryidValue.replace(item
                  .value, item.label);
              }
            })
            this.$set(this.formData, this.formData.makerfactoryidValue, this.formData
              .makerfactoryidValue)
          }
        });
        this.getDicts("clear_division").then(response => {
          if (this.formData.clearDivision) {
            this.formData.clearDivisionValue = JSON.parse(JSON.stringify(this.formData.clearDivision))
            response.data.forEach(item => {
              if (this.formData.clearDivision) {
                this.formData.clearDivisionValue = this.formData.clearDivisionValue.replace(item
                  .value, item.label);
              }
            })
            this.$set(this.formData, this.formData.clearDivisionValue, this.formData
              .clearDivisionValue)
          }
        });
      });
    }
    this.getDicts("sign_status").then(response => {
      this.auditStatus = response.data;
    });
  },
  mounted() {
    this.$nextTick(function() {
      if (this.isMobile) {
        this.entRubbishClearItemsDragTableMobileClientWidth = this.$refs
          .entRubbishClearItemsDragTableMobile.$el.clientWidth
      }
    })
  },
  methods: {
    closeForm() {
      //关闭子页面
      if (this.$store.state.settings.tagsView) {
        this.$router.go(-1) // 返回
        this.$store.state.tagsView.visitedViews.splice(this.$store.state.tagsView.visitedViews.findIndex(
          item => item.path === this.$route.path), 1)
        this.$router.push(this.$store.state.tagsView.visitedViews[this.$store.state.tagsView.visitedViews
          .length - 1].path)
      }
      else {
        parent.postMessage("closeCurrentTabMessage", '*');
      }
    },
    handleTrack() {
      this.imgUrl = this.getAppBaseApi() + '/activiti/task/track/' + this.formData.processId,
        this.showImgDialog = true
    },
    statusFormat(row, column) {
      return this.selectDictLabel(this.auditStatus, row.status);
    },
    handleTask: function(pass) {
      this.$refs["elForm"].validate(valid => {
        this.isDisable = true;
        if (pass == 0 || (pass == 1 && this.formData.comment != null)) {
          this.formData.pass = pass
          checkTask(this.formData).then(response => {
            if (response.code === 0) {
              this.msgSuccess(this.$t('tips.operationSuccessful'));
              this.closeForm();
            }
            else {
              this.msgError(response.msg);
            }
            this.isDisable = false;
          });
        }
        else {
          this.isDisable = false;
          this.msgInfo(this.$t('table.activity.inputApprovalOpinions'));
        }
      });
    },
    getMakerfactoryidOptions() {
      this.getDicts("caaesign_factory").then(response => {
        this.makerfactoryidOptions = response.data;
      });
    },
    getClearDivisionOptions() {
      this.getDicts("clear_division").then(response => {
        this.clearDivisionOptions = response.data;
      });
    },
    attachidsBeforeUpload(file) {
      let isRightSize = file.size / 1024 / 1024 < 2
      if (!isRightSize) {
        this.$message.error('文件大小超过 2MB')
      }
      return isRightSize
    },
    // 文件上传成功处理
    uploadsucces(response, file, fileList) {
      if (response.data.name != undefined && response.data.name != "undefined") {
        this.upload.fileList.push({
          name: file.name,
          url: response.data.name
        });
        this.formData.attachids += response.data.name + ",";
      }
    },
    handleChange(file, fileList) {},
    handleExceed(file, fileList) {},
    handleRemove(file, fileList) {
      this.$emit("delUploadImage", file.name);
      const index = this.upload.fileList.indexOf(file);
      this.upload.fileList.splice(index, 1);
      if (this.formData.attachids) {
        this.formData.attachids = this.formData.attachids.replace(file.url + ",", "");
      }
      this.upload.fileNameList.push(file.url);
      // delFileInfo(file.url);
    },
    handlePreview(file) {
      previewFileOos(file.url)
    },
    checkBoxParse() {},
    cascaderParse() {},
    timeRangParse() {},
  }
}

</script>
<style scoped>
.el-upload__tip {
  line-height: 1.2;
}

.del-handler {
  font-size: 20px;
  color: #ff4949;
}

.el-dialog {
  position: relative;
  margin: 0 auto 0px;
  background: #FFFFFF;
  border-radius: 2px;
  -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 50%;
  height: 60%;
}

.el-dialog__body {
  border-top: 1px solid #dcdfe6;
  border-bottom: 1px solid #dcdfe6;
  max-height: 85% !important;
  min-height: 70%;
  overflow-y: auto;
}

.el-slider>/deep/.el-slider__runway {
  margin: 15px 0;
}

.el-checkbox-group {
  font-size: inherit;
}

.el-color-picker {
  display: inherit;
}

/deep/.el-input-number--medium {
  width: 100%;
}

.talbe-name-style {
  font-size: 16px;
  font-weight: bold;
  color: #1DB8FF;
  line-height: 50px;
}

/deep/.el-scrollbar__view table td {
  padding: 0;
}

.el-form-item {
  margin-top: 5px;
  margin-bottom: 5px;
}

.ant-card {
  color: black;
}

.el-table {
  color: black;
}

.el-form-item.edit-item {
  margin-top: 15px;
  margin-bottom: 22px;
}

</style>
