<template>
  <page-top-bar :title="formConfigData.title">
    <div class="mobile-content" slot="content">
      <div class="mobile-body">
        <el-form ref="elForm" :rules="rules" :show-message=false :hide-required-asterisk=true
          :model="formData" size="medium" label-width="100px" :label-position="labelPosition">
          <div class="mobile-card" id="staffCard">
            <div class="head-pannel">
              <el-col :span="8" :xs="24" class="el-col-no-border">
                任務編碼：{{formData.serialno?formData.serialno:'提交表單后自動生成'}}
              </el-col>
              <el-col :span="8" :xs="24" class="el-col-no-border"> 填單時間：{{formData.createTime}}
              </el-col>
              <el-col :span="8" :xs="24" class="el-col-no-border">
                填單人：{{formData.makerNo +' / '+ formData.makerName}}
              </el-col>
            </div>
            <el-row>
              <div class="body-main">
                <el-col :span="9" :xs="24">
                  <el-form-item label-width="120px" label="申請人工號：" prop="applyEmpNo">
                    {{formData.applyEmpNo}}
                  </el-form-item>
                </el-col>
                <el-col :span="8" :xs="24">
                  <el-form-item label-width="120px" label="申請人姓名：" prop="applyEmpName">
                    {{formData.applyEmpName}}
                  </el-form-item>
                </el-col>
                <el-col :span="7" :xs="24">
                  <el-form-item label="所屬廠區：" prop="makerfactoryid">
                    {{makerfactoryidValue}}
                  </el-form-item>
                </el-col>
                <el-col :span="9" :xs="24">
                  <el-form-item label="單位代碼：" prop="applyDeptNo">
                    {{formData.applyDeptNo}}
                  </el-form-item>
                </el-col>
                <el-col :span="8" :xs="24">
                  <el-form-item label="費用代碼：" prop="applyCostNo">
                    {{formData.applyCostNo}}
                  </el-form-item>
                </el-col>
                <el-col :span="7" :xs="24">
                  <el-form-item label-width="70px" label="資     位：" prop="applyLevelType">
                    {{formData.applyLevelType}}
                  </el-form-item>
                </el-col>
                <el-col :span="9" :xs="24">
                  <el-form-item label="單位名稱：" prop="applyDeptNam">
                    {{formData.applyDeptNam}}
                  </el-form-item>
                </el-col>
                <el-col :span="8" :xs="24">
                  <el-form-item label="管理職：" prop="applyManager">
                    {{formData.applyManager}}
                  </el-form-item>
                </el-col>
                <el-col :span="7" :xs="24">
                  <el-form-item label="分機/手機：" prop="applyTel">
                    {{formData.applyTel}}
                  </el-form-item>
                </el-col>
                <el-col :span="9" :xs="24">
                  <el-form-item label="郵     箱：" prop="applyMail">
                    {{formData.applyMail}}
                  </el-form-item>
                </el-col>
                <el-col :span="8" :xs="24">
                  <el-form-item label="法       人：" prop="applyLegal">
                    {{formData.applyLegal}}
                  </el-form-item>
                </el-col>
                <el-col :span="7" :xs="24">
                  <el-form-item label="需求日期：" prop="clearDemandDate">
                    {{formData.clearDemandDate}}
                  </el-form-item>
                </el-col>
                <el-col :span="9" :xs="24">
                  <el-form-item label="清運地點：" prop="clearPlace">
                    {{formData.clearPlace}}
                  </el-form-item>
                </el-col>
                <el-col :span="8" :xs="24">
                  <el-form-item label="清運物品：" prop="clearRubbish">
                    {{formData.clearRubbish}}
                  </el-form-item>
                </el-col>
                <el-col :span="7" :xs="24">
                  <el-form-item label="是否分撿：" prop="clearDivision">
                    {{clearDivisionValue}}
                  </el-form-item>
                </el-col>
                <el-col :span="24" :xs="24">
                  <el-form-item label="需求說明：" prop="demandDescription">
                    {{formData.demandDescription}}
                  </el-form-item>
                </el-col>
                <el-col :span="24" :xs="24">
                  <label-form :label=formConfigData.rubbish_code_label labelWidth='120' labelPosition='left'
                    labelSize='14' :textarea=formConfigData.rubbish_code_text textSize='14'></label-form>
                </el-col>
                <el-col :span="24" :xs="24">
                  <label-form :label=formConfigData.rubbish_memo_label labelWidth='80' labelPosition='left'
                    labelSize='14' :textarea=formConfigData.rubbish_memo_text textSize='14'></label-form>
                </el-col>
                <el-col :span="24" :xs="24">
                  <label-form label='審核路徑及審核記錄' labelWidth='280' labelPosition='left' labelSize='14'
                    textSize='14'></label-form>
                </el-col>
              </div>
              <div class="body-main">
                <el-col :span="24" :xs="24">
                  <el-form-item :label="$t('table.activity.approvalOpinions')">
                    <el-input v-model="formData.comment" type="textarea" rows="3" class="audit-content"
                      :placeholder="$t('table.activity.inputApprovalOpinions')"></el-input>
                  </el-form-item>
                </el-col>
                <div class="dialog-footer" align="center" style="padding-top: 5px;padding-bottom: 5px">
                  <el-button type="success" @click="handleTask(0)" :disabled="isDisabled">
                    {{$t('table.activity.pass')}}
                  </el-button>
                  <el-button type="danger" @click="handleTask(1)" :disabled="isDisabled">
                    {{$t('table.activity.rejection')}}
                  </el-button>
                  <el-button @click="closeForm">{{$t('common.close')}}</el-button>
                </div>
              </div>
              <!-- 簽核線 -->
              <div class="body-main">
                <el-col :span="24" class="print-hide-div">
                  <el-collapse v-model="signPathActive">
                    <el-collapse-item title="簽核線" :name="1">
                      <el-steps direction="vertical" :active="activeCount" process-status="success"
                        style="margin: 10px 5px 0 10px;">
                        <el-step :title="item" v-for="(item, index) in signPath" :key="index"
                          style="height:30px;"></el-step>
                      </el-steps>
                    </el-collapse-item>
                  </el-collapse>
                </el-col>
                <!-- 审核记录 -->
                <el-table :data="commentList">
                  <el-table-column type="expand">
                    <template slot-scope="props">
                      <el-form label-position="left" inline class="demo-table-expand">
                        <el-form-item :label="$t('table.activity.taskId')">
                          <span>{{ props.row.id }}</span>
                        </el-form-item>
                        <el-form-item :label="$t('table.activity.approvedBy')">
                          <span>{{ props.row.userId }}</span>
                        </el-form-item>
                        <el-form-item :label="$t('table.activity.approvalOpinions')">
                          <span>{{ props.row.fullMessage }}</span>
                        </el-form-item>
                        <el-form-item :label="$t('table.activity.operIp')">
                          <span>{{ props.row.operIp }}</span>
                        </el-form-item>
                        <el-form-item :label="$t('table.activity.auditStatus')">
                          <span>{{ statusFormat(props.row) }}</span>
                        </el-form-item>
                        <el-form-item :label="$t('table.activity.approvalTime')">
                          <span>{{ parseTime(props.row.time) }}</span>
                        </el-form-item>
                      </el-form>
                    </template>
                  </el-table-column>
                  <el-table-column :label="$t('table.activity.approvedBy')" align="center" min-width="35"
                    prop="userId">
                  </el-table-column>
                  <el-table-column :label="$t('table.activity.auditStatus')" align="center" min-width="30"
                    prop="status" :formatter="statusFormat">
                  </el-table-column>
                  <el-table-column :label="$t('table.activity.approvalTime')" align="center" min-width="45"
                    prop="time" :formatter="parseTime(commentList.time)">
                  </el-table-column>
                </el-table>
              </div>
              <!-- 任务跟踪对话框 -->
              <el-dialog :title="$t('table.activity.taskMap')" :visible.sync="showImgDialog" width="100vw"
                :modal-append-to-body="false">
                <img :src="imgUrl" style="padding-bottom: 60px;width:100%">
              </el-dialog>
            </el-row>
          </div>
        </el-form>
      </div>
    </div>
  </page-top-bar>
</template>
<script>
import {
  getEntRubbishClear,
  addEntRubbishClear,
  editEntRubbishClear,
  getSignPathApp,
  updateAndCheck,
  getSignConfigList
}
from "@/api/caaesign/entRubbishClear"
import '@/assets/styles/design-build/design-add-view.scss'
import {
  listTask,
  getTask,
  checkTask,
  taskComment
}
from "@/api/activiti/task"
import {
  getAccessToken,
  getZltAccessToken
}
from "@/utils/auth";
import {
  previewFile,
  getHeader
}
from "@/utils/entfrm";
import {
  getOptionsAndValues
}
from "@/api/system/dictData";
import {
  listFileInfo,
  getFileInfo,
  delFileInfo,
  addFileInfo,
  editFileInfo,
  getByKey
}
from "@/api/system/fileInfo";
export default {
  components: {},
  props: [],
  data() {
    return {
      formData: {
        applyEmpNo: undefined,
        applyEmpName: undefined,
        makerfactoryid: undefined,
        applyDeptNo: undefined,
        applyCostNo: undefined,
        applyLevelType: undefined,
        applyDeptNam: undefined,
        applyManager: undefined,
        applyTel: undefined,
        applyMail: undefined,
        applyLegal: undefined,
        clearDemandDate: null,
        clearPlace: undefined,
        clearRubbish: undefined,
        clearDivision: undefined,
        demandDescription: undefined,
        attachids: "",
        cchargeno: undefined,
        hsgeneralno: undefined,
        hsjingguanno: undefined,
        zchargeno: undefined,
        generalquerenno: undefined,
        dataSource: "app",
        currentorder: "",
        makerNo: this.$store.state.user.empNo,
        makerName: this.$store.state.user.name,
      },
      formConfigData: {
        title: "垃圾清運服務申請單",
        rubbish_code_label: `本年度單價：`,
        rubbish_code_text: `1.施工垃圾：338元/車  （10T 清運車）；生活垃圾：155元/桶/月  （240L垃圾桶）
2.總務窗口：63357`,
        rubbish_memo_label: `備註`,
        rubbish_memo_text: `1.此單適用于廠房改造、設備拆裝、施工過程中產生的不可回收雜物，且不屬於工業危廢物
2.清運費用根據公司與環衛清運供應商簽訂之《垃圾清運協議》中約定費用為準
3.清運費用直接掛需求單位
4.核准主管為申請單位權限主管
5.清運過程中需由需求單位、庶務服務中心共同監管裝車`,
        kchargeno: "課級主管",
        kchargeno_required: false,
        bchargeno: "部級主管",
        bchargeno_required: false,
        cchargeno: "廠級主管",
        cchargeno_required: true,
        hsgeneralno: "會審總務窗口",
        hsgeneralno_required: true,
        hsjingguanno: "會審經管",
        hsjingguanno_required: true,
        zchargeno: "製造處級主管",
        zchargeno_required: true,
        generalquerenno: "總務窗口收單確認",
        generalquerenno_required: true,
      },
      tableHeight: document.documentElement.scrollHeight - 245 + "px",
      rules: {},
      // 审批意见数据
      commentList: [],
      //簽核路徑
      signPath: [],
      activeCount: -1,
      signPathActive: -1,
      //任务图url
      imgUrl: '',
      isDisabled: false,
      // 是否显示任务图
      showImgDialog: false,
      auditStatus: [],
      // 文件上传参数
      upload: {
        // 是否显示弹出层
        open: false,
        fileList: [],
        fileNameList: [],
        // 弹出层标题
        title: "",
        multiple: false,
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: this.getHeader(),
        // 上传的地址
        url: this.getAppBaseApi() + "/system/fileInfo/uploadWithOos"
      },
      makerfactoryidOptions: [],
      makerfactoryidValue: null,
      clearDivisionOptions: [],
      clearDivisionValue: null,
      isMobile: true,
      labelPosition: 'left',
    }
  },
  computed: {},
  watch: {},
  created() {
    const id = this.$route.query.id;
    this.changeTagsView(this.$route.query);
    this.isMobile = true
    getSignConfigList().then(response => {
      if (response.code === 0) {
        response.data.forEach(element => {
          const colKey = element.colKey.split('_')
          if (colKey.length > 1) {
            if (this.rules[colKey[0]] && colKey[1] === 'required') {
              this.rules[colKey[0]][0]['required'] = element.colValue === 'true'
              this.formConfigData[element.colKey] = element.colValue === 'true'
            }
            else {
              this.formConfigData[element.colKey] = element.colValue
            }
          }
          else {
            this.formConfigData[element.colKey] = element.colValue
          }
        });
      }
    })
    if (id != null && id != undefined) {
      getEntRubbishClear(id).then(response => {
        this.formData = response.data;
        taskComment(this.formData.processId).then(response => {
          this.commentList = response.data;
        });
        getSignPathApp(this.formData.processId).then(response => {
          this.signPath = response.data.info;
          this.activeCount = response.data.activeCount;
        });
        if (this.formData.attachids) {
          let a = this.formData.attachids.split(',');
          if (a.length > 0) {
            a.forEach(item => {
              if (item) {
                getByKey(item).then(response => {
                  this.upload.fileList.push({
                    name: response.data.orignalName,
                    url: response.data.name
                  });
                })
              }
            })
          }
        }
        this.checkBoxParse()
        this.cascaderParse()
        this.timeRangParse()
        this.getMakerfactoryidOptions()
        this.getClearDivisionOptions()
        this.getDicts("clear_division").then(response => {
          if (this.formData.clearDivision && this.formData.clearDivision != "null") {
            this.clearDivisionValue = this.formData.clearDivision
            response.data.forEach(item => {
              if (this.formData.clearDivision && this.formData.clearDivision != "null") {
                this.clearDivisionValue = this.clearDivisionValue.replace(item.value, item.label);
              }
            })
          }
        });
      });
    }
    this.getDicts("sign_status").then(response => {
      this.auditStatus = response.data;
    });
  },
  mounted() {},
  methods: {
    closeForm() {
      //关闭子页面
      this.$router.go(-1) // 返回
    },
    handleTrack() {
      this.imgUrl = this.getAppBaseApi() + '/activiti/task/track/' + this.formData.processId,
        this.showImgDialog = true
    },
    statusFormat(row, column) {
      return this.selectDictLabel(this.auditStatus, row.status);
    },
    handleTask: function(pass) {
      this.isDisabled = true;
      this.$refs["elForm"].validate(valid => {
        if (pass == 0 || (pass == 1 && this.formData.comment != null)) {
          this.formData.pass = pass
          checkTask(this.formData).then(response => {
            if (response.code === 0) {
              this.$message({
                showClose: true,
                message: this.$t('tips.operationSuccessful'),
                type: "success",
                offset: 50
              });
              this.closeForm();
            }
            else {
              this.msgError(response.msg);
            }
          });
        }
        else {
          this.isDisabled = false;
          this.msgInfo(this.$t('table.activity.inputApprovalOpinions'));
        }
      });
    },
    getMakerfactoryidOptions() {
      this.getDicts("caaesign_factory").then(response => {
        this.makerfactoryidOptions = response.data;
        if (this.formData.makerfactoryid) {
          this.makerfactoryidValue = this.formData.makerfactoryid
          response.data.forEach(item => {
            this.makerfactoryidValue = this.makerfactoryidValue.replace(item.value, item.label);
          })
        }
      });
    },
    makerfactoryidMainShowPannel() {
      if (!this.makerfactoryidMainPicker) {
        let _self = this
        this.makerfactoryidMainPicker = this.$createPicker({
          title: '所屬廠區：',
          data: [this.makerfactoryidOptions],
          alias: {
            value: "value",
            text: "label"
          },
          swipeTime: 1000,
          onSelect: function(selectedVal, selectedIndex, selectedText) {
            _self.formData.makerfactoryid = selectedVal[0];
            _self.makerfactoryidValue = selectedText[0];
          },
        })
      }
      this.makerfactoryidMainPicker.show()
    },
    attachidsBeforeUpload(file) {
      let isRightSize = file.size / 1024 / 1024 < 2
      if (!isRightSize) {
        this.$message.error('文件大小超过 2MB')
      }
      return isRightSize
    },
    // 文件上传成功处理
    uploadsucces(response, file, fileList) {
      if (response.data.name != undefined && response.data.name != "undefined") {
        this.upload.fileList.push({
          name: file.name,
          url: response.data.name
        });
        this.formData.attachids += response.data.name + ",";
      }
    },
    handleChange(file, fileList) {},
    handleExceed(file, fileList) {},
    handleRemove(file, fileList) {
      this.$emit("delUploadImage", file.name);
      const index = this.upload.fileList.indexOf(file);
      this.upload.fileList.splice(index, 1);
      if (this.formData.attachids && this.formData.attachids != "null") {
        this.formData.attachids = this.formData.attachids.replace(file.url + ",", "");
      }
      this.upload.fileNameList.push(file.url);
      // delFileInfo(file.url);
    },
    handlePreview(file) {
      previewFile(file.url)
    },
    timeRangParse() {
      if (this.formData.clearDemandDate && this.formData.clearDemandDate != "null") {
        this.clearDemandDateValue = this.formData.clearDemandDate
      }
    },
    checkBoxParse() {},
    cascaderParse() {},
  }
}

</script>
<style rel="stylesheet/scss" lang="scss" scoped>
@import "~@/assets/styles/mobileSkin/mobileMixin.scss";

.el-upload__tip {
  line-height: 1.2;
}

.del-handler {
  font-size: 20px;
  color: #ff4949;
}

.el-dialog {
  position: relative;
  margin: 0 auto 0px;
  background: #FFFFFF;
  border-radius: 2px;
  -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 50%;
  height: 60%;
}

.el-dialog__body {
  border-top: 1px solid #dcdfe6;
  border-bottom: 1px solid #dcdfe6;
  max-height: 85% !important;
  min-height: 70%;
  overflow-y: auto;
}

.el-slider>/deep/.el-slider__runway {
  margin: 15px 0;
}

.el-checkbox-group {
  font-size: inherit;
}

.el-color-picker {
  display: inherit;
}

/deep/.demo-table-expand {
  font-size: 0;
}

/deep/.demo-table-expand label {
  width: 90px;
  color: #99a9bf;
}

/deep/.demo-table-expand/deep/.el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  width: 100%;
}

/deep/.el-input-number--medium {
  width: 100%;
}

.talbe-name-style {
  font-size: 16px;
  font-weight: bold;
  line-height: 50px;
}

/deep/.el-collapse-item__content {
  padding-bottom: 0;
}

/deep/.el-step__head.is-finish,
/deep/.el-step__title.is-finish {
  color: #C0C4CC;
  border-color: #C0C4CC;
}

/deep/.el-step__head.is-wait,
/deep/.el-step__title.is-wait {
  color: #000000;
  border-color: #000000;
}

/deep/.el-step__head.is-success,
/deep/.el-step__title.is-success {
  color: blue;
  border-color: blue;
}

.mobile-content {
  background: #EEEEEE;
}

.mobile-card {
  border: 0;
  background: transparent;
}

#staffCard {
  border: 0;
}

.mobile-body {
  padding: 0;
}

/deep/input,
/deep/textarea {
  border-color: transparent;
}

.head-pannel {
  background: #ffffff;
  padding: 15px;
  margin-top: 5px;
  color: #6B6B6B;
}

.head-pannel:after {
  clear: both;
  content: "";
  display: block;
}

.head-pannel>div {
  font-size: 14px;
  padding: 5px;
}

/deep/.el-row {
  margin-left: 0;
  margin-right: 0;
}

.body-main .el-form-item {
  margin-top: 15px;
  margin-bottom: 15px;
  border-color: transparent;
}

.el-table__expanded-cell .el-form-item {
  margin: 0;
}

.el-form-item>label {
  padding-left: 10px;
}

.body-main .el-col {
  border-bottom: 1px solid #EAEAEA;
}

.body-main {
  background: #ffffff;
  padding: 15px 20px;
  margin-top: 8px;
}

.body-main:after {
  clear: both;
  content: "";
  display: block;
}

.child-item {
  margin-top: 8px;
  background: #ffffff;
}

.child-item:after {
  clear: both;
  content: "";
  display: block;
}

.child-item /deep/.el-form-item {
  margin: 15px 10px;
  padding-bottom: 15px;
  border-color: transparent;
  border-bottom: 1px solid #EAEAEA;
}

.talbe-name-style {
  padding-left: 15px;
}

.sign-content {
  background: #ffffff;
  margin-top: 8px;
  color: #000000;
}

.sign-content:after {
  clear: both;
  content: "";
  display: block;
}

.sign-point>.el-form-item {
  padding: 5px 10px;
  margin-bottom: 0;
}

.btn-div {
  background: white;
  padding: 10px;
  margin: 10px 0;
  float: none;
}

.el-message-box__btns button:nth-child(2) {
  margin-left: 0;
}

.el-slider {
  /deep/.el-slider__bar {
    @include backgroundColor('themeMainColor');
  }
  
  /deep/.el-slider__button-wrapper {
    .el-slider__button {
      @include borderColor('themeMainColor');
    }
  }
}

.el-radio.is-checked {
  /deep/.el-radio__input.is-checked {
    .el-radio__inner {
      @include borderColor('themeMainColor');
      @include backgroundInfo('themeMainColor');
    }
  }
  
  /deep/.el-radio__input.is-checked+.el-radio__label {
    @include fontColor('themeMainColor');
  }
}

.el-checkbox.is-checked {
  /deep/.el-checkbox__input.is-checked {
    .el-checkbox__inner {
      @include borderColor('themeMainColor');
      @include backgroundInfo('themeMainColor');
    }
  }
  
  /deep/.el-checkbox__input.is-checked+.el-checkbox__label {
    @include fontColor('themeMainColor');
  }
}

.el-switch.is-checked {
  /deep/.el-switch__core {
    @include borderColor('themeMainColor');
    @include backgroundInfo('themeMainColor');
  }
}

.cube-popup {
  /deep/.cube-picker-confirm {
    @include fontColor('themeMainColor');
  }
}

.child-item {
  /deep/.el-button--primary {
    @include borderColor('themeMainColor');
    @include backgroundInfo('themeMainColor');
  }
  
  /deep/.del-handler {
    @include fontColor('themeMainColor');
  }
}

.dialog-footer {
  .el-button--success {
    @include backgroundInfo('themeMainColor');
    @include borderColor('themeMainColor');
  }
  
  .el-button--info {
    @include backgroundInfo('themeMainColor');
    @include borderColor('themeMainColor');
  }
}

/deep/.audit-content>textarea {
  border-color: #DCDFE6;
}

/deep/ .el-input.is-disabled .el-input__inner:disabled,
/deep/ .el-textarea.is-disabled .el-textarea__inner:disabled,
/deep/ .el-radio__input.is-disabled.is-checked .el-radio__inner::after,
/deep/ .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner::after {
  background-color: #ffffff;
  border-color: #ffffff;
  color: #C0C4CC;
}

</style>
