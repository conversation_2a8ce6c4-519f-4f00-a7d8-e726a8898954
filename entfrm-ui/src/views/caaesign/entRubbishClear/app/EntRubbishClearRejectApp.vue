<template>
  <page-top-bar :title="formConfigData.title">
    <div class="mobile-content" slot="content">
      <div class="mobile-body">
        <el-form ref="elForm" class="ant-form ant-form-horizontal" :model="formData" :rules="rules"
          size="medium" label-width="100px" :label-position="labelPosition">
          <div class="ant-card ant-card-bordered" id="staffCard">
            <div class="ant-card-body">
              <el-col :span="8" :xs="24" class="el-col-no-border">
                任務編碼：{{formData.serialno?formData.serialno:'提交表單后自動生成'}}
              </el-col>
              <el-col :span="8" :xs="24" class="el-col-no-border"> 填單時間：{{formData.createTime}}
              </el-col>
              <el-col :span="8" :xs="24" class="el-col-no-border">
                填單人：{{formData.makerNo +' / '+ formData.makerName}}
              </el-col>
              <el-row :gutter="15">
                <el-col :span="9" :xs="24">
                  <el-form-item label-width="120px" label="申請人工號：" prop="applyEmpNo">
                    <el-input v-model="formData.applyEmpNo" placeholder="請輸入申請人工號" clearable
                      :style="{width: '100%'}" @change='applyEmpNo_onchange'></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8" :xs="24">
                  <el-form-item label-width="120px" label="申請人姓名：" prop="applyEmpName">
                    <el-input v-model="formData.applyEmpName" placeholder="請輸入申請人姓名" clearable
                      :style="{width: '100%'}"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="7" :xs="24">
                  <el-form-item label="所屬廠區：" prop="makerfactoryid">
                    <el-input placeholder="請選擇所屬廠區" suffix-icon="el-icon-arrow-down" readonly
                      v-model="makerfactoryidValue" @focus="makerfactoryidMainShowPannel"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="9" :xs="24">
                  <el-form-item label="單位代碼：" prop="applyDeptNo">
                    <el-input v-model="formData.applyDeptNo" placeholder="請輸入單位代碼" clearable
                      :style="{width: '100%'}"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8" :xs="24">
                  <el-form-item label="費用代碼：" prop="applyCostNo">
                    <el-input v-model="formData.applyCostNo" placeholder="請輸入費用代碼" clearable
                      :style="{width: '100%'}"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="7" :xs="24">
                  <el-form-item label-width="70px" label="資     位：" prop="applyLevelType">
                    <el-input v-model="formData.applyLevelType" placeholder="請輸入資位資位" clearable
                      :style="{width: '100%'}"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="9" :xs="24">
                  <el-form-item label="單位名稱：" prop="applyDeptNam">
                    <el-input v-model="formData.applyDeptNam" placeholder="請輸入單位名稱" clearable
                      :style="{width: '100%'}"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8" :xs="24">
                  <el-form-item label="管理職：" prop="applyManager">
                    <el-input v-model="formData.applyManager" placeholder="請輸入管理職" clearable
                      :style="{width: '100%'}"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="7" :xs="24">
                  <el-form-item label="分機/手機：" prop="applyTel">
                    <el-input v-model="formData.applyTel" placeholder="請輸入分機/手機" clearable
                      :style="{width: '100%'}"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="9" :xs="24">
                  <el-form-item label="郵     箱：" prop="applyMail">
                    <el-input v-model="formData.applyMail" placeholder="請輸入郵箱郵箱" clearable
                      :style="{width: '100%'}"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8" :xs="24">
                  <el-form-item label="法       人：" prop="applyLegal">
                    <el-input v-model="formData.applyLegal" placeholder="請輸入法人法人：" clearable
                      :style="{width: '100%'}"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="7" :xs="24">
                  <el-form-item label="需求日期：" prop="clearDemandDate">
                    <el-input placeholder="请選擇需求日期" prefix-icon="el-icon-date" readonly
                      v-model="clearDemandDateValue" @focus="clearDemandDateShowDate"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="9" :xs="24">
                  <el-form-item label="清運地點：" prop="clearPlace">
                    <el-input v-model="formData.clearPlace" placeholder="請輸入清運地點" clearable
                      :style="{width: '100%'}"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8" :xs="24">
                  <el-form-item label="清運物品：" prop="clearRubbish">
                    <el-input v-model="formData.clearRubbish" placeholder="請輸入清運物品" clearable
                      :style="{width: '100%'}"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="7" :xs="24">
                  <el-form-item label="是否分撿：" prop="clearDivision">
                    <el-radio-group v-model="formData.clearDivision" size="medium">
                      <el-radio v-for="(item, index) in clearDivisionOptions" :key="index" :label="item.value"
                        :disabled="item.disabled">{{item.label}}</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="24" :xs="24">
                  <el-form-item label="需求說明：" prop="demandDescription">
                    <el-input v-model="formData.demandDescription" type="textarea" placeholder="請輸入需求說明"
                      :autosize="{minRows: 4, maxRows: 4}" :style="{width: '100%'}"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="24" :xs="24">
                  <label-form :label=formConfigData.rubbish_code_label labelWidth='120' labelPosition='left'
                    labelSize='14' :textarea=formConfigData.rubbish_code_text textSize='14'></label-form>
                </el-col>
                <el-col :span="24" :xs="24">
                  <label-form :label=formConfigData.rubbish_memo_label labelWidth='80' labelPosition='left'
                    labelSize='14' :textarea=formConfigData.rubbish_memo_text textSize='14'></label-form>
                </el-col>
                <el-col :span="24" :xs="24">
                  <label-form label='審核路徑及審核記錄' labelWidth='280' labelPosition='left' labelSize='14'
                    textSize='14'></label-form>
                </el-col>
                <el-col :span="6" :xs="24" class="sign-point">
                  <el-form-item prop="kchargeno" label-width="0">
                    <entfrm-sign-form-audit-hq :label="formConfigData.kchargeno"
                      :required='formConfigData.kchargeno_required' :alias="this.alias"
                      :applyFactoryId="this.formData.applyFactoryId" :applyDeptNo="this.formData.applyDeptNo"
                      :multipleSelect=true :isMobile=this.isMobile
                      :selectEmpProp="{empNo:this.formData.kchargeno,empName:this.formData.kchargename}"
                      :kchargeno="this.formData.kchargeno" :bchargeno="this.formData.bchargeno"
                      :cchargeno="this.formData.cchargeno" model-no="kchargeno" model-name="kchargename"
                      @onSignFormSelected="onSignFormSelected"></entfrm-sign-form-audit-hq>
                  </el-form-item>
                </el-col>
                <el-col :span="6" :xs="24" class="sign-point">
                  <el-form-item prop="bchargeno" label-width="0">
                    <entfrm-sign-form-audit-hq :label="formConfigData.bchargeno"
                      :required='formConfigData.bchargeno_required' :alias="this.alias"
                      :applyFactoryId="this.formData.applyFactoryId" :applyDeptNo="this.formData.applyDeptNo"
                      :multipleSelect=true :isMobile=this.isMobile
                      :selectEmpProp="{empNo:this.formData.bchargeno,empName:this.formData.bchargename}"
                      :kchargeno="this.formData.kchargeno" :bchargeno="this.formData.bchargeno"
                      :cchargeno="this.formData.cchargeno" model-no="bchargeno" model-name="bchargename"
                      @onSignFormSelected="onSignFormSelected"></entfrm-sign-form-audit-hq>
                  </el-form-item>
                </el-col>
                <el-col :span="6" :xs="24" class="sign-point">
                  <el-form-item prop="cchargeno" label-width="0">
                    <entfrm-sign-form-audit-hq :label="formConfigData.cchargeno"
                      :required='formConfigData.cchargeno_required' :alias="this.alias"
                      :applyFactoryId="this.formData.applyFactoryId" :applyDeptNo="this.formData.applyDeptNo"
                      :multipleSelect=true :isMobile=this.isMobile
                      :selectEmpProp="{empNo:this.formData.cchargeno,empName:this.formData.cchargename}"
                      :kchargeno="this.formData.kchargeno" :bchargeno="this.formData.bchargeno"
                      :cchargeno="this.formData.cchargeno" model-no="cchargeno" model-name="cchargename"
                      @onSignFormSelected="onSignFormSelected"></entfrm-sign-form-audit-hq>
                  </el-form-item>
                </el-col>
                <el-col :span="6" :xs="24" class="sign-point">
                  <el-form-item prop="hsgeneralno" label-width="0">
                    <entfrm-sign-form-add-hq :label="formConfigData.hsgeneralno"
                      :required='formConfigData.hsgeneralno_required'
                      :selectEmpProp="{empNo:this.formData.hsgeneralno,empName:this.formData.hsgeneralname}"
                      model-no="hsgeneralno" model-name="hsgeneralname"
                      @onSignFormSelected="onSignFormSelected" :isMobile=this.isMobile>
                    </entfrm-sign-form-add-hq>
                  </el-form-item>
                </el-col>
                <el-col :span="6" :xs="24" class="sign-point">
                  <el-form-item prop="hsjingguanno" label-width="0">
                    <entfrm-sign-form-add-hq :label="formConfigData.hsjingguanno"
                      :required='formConfigData.hsjingguanno_required'
                      :selectEmpProp="{empNo:this.formData.hsjingguanno,empName:this.formData.hsjingguanname}"
                      model-no="hsjingguanno" model-name="hsjingguanname"
                      @onSignFormSelected="onSignFormSelected" :isMobile=this.isMobile>
                    </entfrm-sign-form-add-hq>
                  </el-form-item>
                </el-col>
                <el-col :span="6" :xs="24" class="sign-point">
                  <el-form-item prop="zchargeno" label-width="0">
                    <entfrm-sign-form-audit-hq :label="formConfigData.zchargeno"
                      :required='formConfigData.zchargeno_required' :alias="this.alias"
                      :applyFactoryId="this.formData.applyFactoryId" :applyDeptNo="this.formData.applyDeptNo"
                      :multipleSelect=true :isMobile=this.isMobile
                      :selectEmpProp="{empNo:this.formData.zchargeno,empName:this.formData.zchargename}"
                      :kchargeno="this.formData.kchargeno" :bchargeno="this.formData.bchargeno"
                      :cchargeno="this.formData.cchargeno" model-no="zchargeno" model-name="zchargename"
                      @onSignFormSelected="onSignFormSelected"></entfrm-sign-form-audit-hq>
                  </el-form-item>
                </el-col>
                <el-col :span="6" :xs="24" class="sign-point">
                  <el-form-item prop="generalquerenno" label-width="0">
                    <entfrm-sign-form-duty :label="formConfigData.generalquerenno"
                      :required='formConfigData.generalquerenno_required' duty-id="670" :alias="this.alias"
                      :applyFactoryId="this.formData.makerfactoryid" :multipleSelect=true
                      :isMobile=this.isMobile
                      :selectEmpProp="{empNo:this.formData.generalquerenno,empName:this.formData.generalquerenname}"
                      model-no="generalquerenno" model-name="generalquerenname"
                      @onSignFormSelected="onSignFormSelected"></entfrm-sign-form-duty>
                  </el-form-item>
                </el-col>
                <el-col :span="24" :xs="24">
                  <div class="dialog-footer" align="center" style="padding-top: 5px;padding-bottom: 5px">
                    <el-button type="success" @click="handleSubmit()">
                      {{$t('table.activity.reSubmit')}}
                    </el-button>
                    <el-button type="primary" @click="submitForm">{{$t('common.save')}}</el-button>
                    <el-button @click="handleTask(9)">{{$t('table.activity.cancle')}}</el-button>
                    <el-button @click="closeForm">{{$t('common.close')}}</el-button>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>
        </el-form>
      </div>
    </div>
  </page-top-bar>
</template>
<script>
import {
  getEntRubbishClear,
  addEntRubbishClear,
  editEntRubbishClear,
  getSignPathApp,
  editEntRubbishClearAndResubmitProcess,
  getSignConfigList
}
from "@/api/caaesign/entRubbishClear"
import '@/assets/styles/design-build/design-add-view.scss'
import {
  listTask,
  getTask,
  checkTask,
  taskComment
}
from "@/api/activiti/task"
import {
  getAccessToken,
  getZltAccessToken
}
from "@/utils/auth";
import {
  previewFile,
  getHeader
}
from "@/utils/entfrm";
import {
  getOptionsAndValues
}
from "@/api/system/dictData";
import {
  listFileInfo,
  getFileInfo,
  delFileInfo,
  addFileInfo,
  editFileInfo,
  getByKey
}
from "@/api/system/fileInfo";
export default {
  components: {},
  props: [],
  data() {
    return {
      formData: {
        applyEmpNo: undefined,
        applyEmpName: undefined,
        makerfactoryid: undefined,
        applyDeptNo: undefined,
        applyCostNo: undefined,
        applyLevelType: undefined,
        applyDeptNam: undefined,
        applyManager: undefined,
        applyTel: undefined,
        applyMail: undefined,
        applyLegal: undefined,
        clearDemandDate: null,
        clearPlace: undefined,
        clearRubbish: undefined,
        clearDivision: undefined,
        demandDescription: undefined,
        attachids: "",
        cchargeno: undefined,
        hsgeneralno: undefined,
        hsjingguanno: undefined,
        zchargeno: undefined,
        generalquerenno: undefined,
        dataSource: "app",
        makerNo: this.$store.state.user.empNo,
        makerName: this.$store.state.user.name,
      },
      formConfigData: {
        title: "垃圾清運服務申請單",
        rubbish_code_label: `本年度單價：`,
        rubbish_code_text: `1.施工垃圾：338元/車  （10T 清運車）；生活垃圾：155元/桶/月  （240L垃圾桶）
2.總務窗口：63357`,
        rubbish_memo_label: `備註`,
        rubbish_memo_text: `1.此單適用于廠房改造、設備拆裝、施工過程中產生的不可回收雜物，且不屬於工業危廢物
2.清運費用根據公司與環衛清運供應商簽訂之《垃圾清運協議》中約定費用為準
3.清運費用直接掛需求單位
4.核准主管為申請單位權限主管
5.清運過程中需由需求單位、庶務服務中心共同監管裝車`,
        kchargeno: "課級主管",
        kchargeno_required: false,
        bchargeno: "部級主管",
        bchargeno_required: false,
        cchargeno: "廠級主管",
        cchargeno_required: true,
        hsgeneralno: "會審總務窗口",
        hsgeneralno_required: true,
        hsjingguanno: "會審經管",
        hsjingguanno_required: true,
        zchargeno: "製造處級主管",
        zchargeno_required: true,
        generalquerenno: "總務窗口收單確認",
        generalquerenno_required: true,
      },
      tableHeight: document.documentElement.scrollHeight - 245 + "px",
      rules: {
        applyEmpNo: [{
          required: true,
          message: '請輸入申請人工號',
          trigger: 'blur'
        }],
        applyEmpName: [{
          required: true,
          message: '請輸入申請人姓名',
          trigger: 'blur'
        }],
        makerfactoryid: [{
          required: true,
          message: '請選擇所屬廠區',
          trigger: 'change'
        }],
        applyDeptNo: [{
          required: true,
          message: '請輸入單位代碼',
          trigger: 'blur'
        }],
        applyCostNo: [{
          required: true,
          message: '請輸入費用代碼',
          trigger: 'blur'
        }],
        applyLevelType: [{
          required: true,
          message: '請輸入資位資位',
          trigger: 'blur'
        }],
        applyDeptNam: [{
          required: true,
          message: '請輸入單位名稱',
          trigger: 'blur'
        }],
        applyManager: [],
        applyTel: [{
          required: true,
          message: '請輸入分機/手機',
          trigger: 'blur'
        }],
        applyMail: [{
          required: true,
          message: '請輸入郵箱郵箱',
          trigger: 'blur'
        }],
        applyLegal: [],
        clearDemandDate: [{
          required: true,
          message: '请選擇需求日期',
          trigger: 'change'
        }],
        clearPlace: [{
          required: true,
          message: '請輸入清運地點',
          trigger: 'blur'
        }],
        clearRubbish: [{
          required: true,
          message: '請輸入清運物品',
          trigger: 'blur'
        }],
        clearDivision: [],
        demandDescription: [{
          required: true,
          message: '請輸入需求說明',
          trigger: 'blur'
        }],
        attachids: [],
        cchargeno: [{
          required: true,
          message: '廠級主管不能為空',
          trigger: 'change'
        }],
        hsgeneralno: [{
          required: true,
          message: '會審總務窗口不能為空',
          trigger: 'change'
        }],
        hsjingguanno: [{
          required: true,
          message: '會審經管不能為空',
          trigger: 'change'
        }],
        zchargeno: [{
          required: true,
          message: '製造處級主管不能為空',
          trigger: 'change'
        }],
        generalquerenno: [{
          required: true,
          message: '總務窗口收單確認不能為空',
          trigger: 'change'
        }],
      },
      // 文件上传参数
      upload: {
        // 是否显示弹出层
        open: false,
        fileList: [],
        fileNameList: [],
        // 弹出层标题
        title: "",
        multiple: false,
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: this.getHeader(),
        // 上传的地址
        url: this.getAppBaseApi() + "/system/fileInfo/uploadWithOos"
      },
      makerfactoryidOptions: [],
      makerfactoryidValue: null,
      clearDivisionOptions: [],
      clearDivisionValue: null,
      isMobile: true,
      labelPosition: 'right',
      clearDemandDateValue: undefined,
      clearDemandDateDateValue: [{
        is: 'cube-date-picker',
        title: '需求日期：',
        format: {
          year: 'YYYY',
          month: 'MM',
          date: 'DD'
        },
        value: new Date(),
        swipeTime: 1000,
        startColumn: 'year',
      }],
      alias: "postgresql$_$pghrsign_ipebg_test$_$075d399c2dcaa20f94d53895254e58a1%_%design"
    }
  },
  computed: {},
  watch: {},
  created() {
    const id = this.$route.query.id;
    this.changeTagsView(this.$route.query);
    this.isMobile = true
    if (this.isMobile) {
      this.labelPosition = 'left'
    }
    getSignConfigList().then(response => {
      if (response.code === 0) {
        response.data.forEach(element => {
          const colKey = element.colKey.split('_')
          if (colKey.length > 1) {
            if (this.rules[colKey[0]] && colKey[1] === 'required') {
              this.rules[colKey[0]][0]['required'] = element.colValue === 'true'
              this.formConfigData[element.colKey] = element.colValue === 'true'
            }
            else {
              this.formConfigData[element.colKey] = element.colValue
            }
          }
          else {
            this.formConfigData[element.colKey] = element.colValue
          }
        });
      }
    })
    if (id != null && id != undefined) {
      getEntRubbishClear(id).then(response => {
        this.formData = response.data;
        this.checkBoxParse()
        this.cascaderParse()
        this.timeRangParse()
        if (this.formData.attachids) {
          let a = this.formData.attachids.split(',');
          if (a.length > 0) {
            a.forEach(item => {
              if (item) {
                getByKey(item).then(response => {
                  this.upload.fileList.push({
                    name: response.data.orignalName,
                    url: response.data.name
                  });
                })
              }
            })
          }
        }
        this.getMakerfactoryidOptions()
        this.getClearDivisionOptions()
      });
    }
    if (id == null || id == undefined) {
      this.getMakerfactoryidOptions()
      this.getClearDivisionOptions()
    }
  },
  mounted() {},
  methods: {
    submitForm() {
      this.$refs['elForm'].validate(valid => {
        if (valid) {
          const formData = JSON.parse(JSON.stringify(this.formData))
          if (formData.id != undefined) {
            editEntRubbishClear(formData).then(response => {
              if (response.code === 0) {
                this.$message({
                  showClose: true,
                  message: this.$t('tips.updateSuccess'),
                  type: "success",
                  offset: 50
                });
                this.closeForm();
              }
              else {
                this.msgError(response.msg);
              }
            });
          }
          else {
            addEntRubbishClear(formData).then(response => {
              if (response.code === 0) {
                this.$message({
                  showClose: true,
                  message: this.$t('tips.createSuccess'),
                  type: "success",
                  offset: 50
                });
                this.closeForm();
              }
              else {
                this.msgError(response.msg);
              }
            });
          }
          /**
           * 提交的時候刪除被標記的需要刪除的附件
           */
          if (this.formData.attachids) {
            this.upload.fileNameList.forEach(item => {
              if (item) {
                delFileInfo(item);
              }
            })
          }
        }
      })
    },
    handleSubmit: function() {
      this.$refs["elForm"].validate(valid => {
        if (valid) {
          const formData = JSON.parse(JSON.stringify(this.formData))
          if (formData.id != undefined) {
            editEntRubbishClearAndStartProcess(formData).then(response => {
              if (response.code === 0) {
                this.$message({
                  showClose: true,
                  message: this.$t('tips.updateSuccess'),
                  type: "success",
                  offset: 50
                });
                this.closeForm();
              }
              else {
                this.msgError(response.msg);
              }
            });
          }
          else {
            addEntRubbishClearAndStartProcess(formData).then(response => {
              if (response.code === 0) {
                this.$message({
                  showClose: true,
                  message: this.$t('tips.createSuccess'),
                  type: "success",
                  offset: 50
                });
                this.closeForm();
              }
              else {
                this.msgError(response.msg);
              }
            });
          }
          /**
           * 提交的時候刪除被標記的需要刪除的附件
           */
          if (this.formData.attachids) {
            this.upload.fileNameList.forEach(item => {
              if (item) {
                delFileInfo(item);
              }
            })
          }
        }
      })
    },
    resetForm() {
      this.$refs['elForm'].resetFields()
    },
    closeForm() {
      //关闭子页面
      this.$router.go(-1) // 返回
    },
    getMakerfactoryidOptions() {
      this.getDicts("caaesign_factory").then(response => {
        this.makerfactoryidOptions = response.data;
        if (this.formData.makerfactoryid) {
          this.makerfactoryidValue = this.formData.makerfactoryid
          response.data.forEach(item => {
            this.makerfactoryidValue = this.makerfactoryidValue.replace(item.value, item.label);
          })
        }
      });
    },
    makerfactoryidMainShowPannel() {
      if (!this.makerfactoryidMainPicker) {
        let _self = this
        this.makerfactoryidMainPicker = this.$createPicker({
          title: '所屬廠區：',
          data: [this.makerfactoryidOptions],
          alias: {
            value: "value",
            text: "label"
          },
          swipeTime: 1000,
          onSelect: function(selectedVal, selectedIndex, selectedText) {
            _self.formData.makerfactoryid = selectedVal[0];
            _self.makerfactoryidValue = selectedText[0];
          },
        })
      }
      this.makerfactoryidMainPicker.show()
    },
    clearDemandDateShowDate() {
      this.clearDemandDateSegmentPicker.show()
    },
    getClearDivisionOptions() {
      this.getDicts("clear_division").then(response => {
        this.clearDivisionOptions = response.data;
      });
    },
    attachidsBeforeUpload(file) {
      let isRightSize = file.size / 1024 / 1024 < 2
      if (!isRightSize) {
        this.$message.error('文件大小超过 2MB')
      }
      return isRightSize
    },
    // 文件上传成功处理
    uploadsucces(response, file, fileList) {
      if (response.data.name != undefined && response.data.name != "undefined") {
        this.upload.fileList.push({
          name: file.name,
          url: response.data.name
        });
        this.formData.attachids += response.data.name + ",";
      }
    },
    handleChange(file, fileList) {},
    handleExceed(file, fileList) {},
    handleRemove(file, fileList) {
      this.$emit("delUploadImage", file.name);
      const index = this.upload.fileList.indexOf(file);
      this.upload.fileList.splice(index, 1);
      if (this.formData.attachids && this.formData.attachids != "null") {
        this.formData.attachids = this.formData.attachids.replace(file.url + ",", "");
      }
      this.upload.fileNameList.push(file.url);
      // delFileInfo(file.url);
    },
    handlePreview(file) {
      previewFile(file.url)
    },
    checkBoxParse() {},
    cascaderParse() {},
    timeRangParse() {},
    applyEmpNo_onchange(data) {
      this.getInfoUserByEmpno(data).then(response => {
        if (response.code !== 0) {
          this.msgError(response.msg);
        }
        else {
          if (response.data != null) {
            this.formData.applyEmpName = response.data.empname
            this.formData.makerfactoryid = response.data.factoryid
            for (var item of this.makerfactoryidOptions) {
              if (item.value == response.data.factoryid) {
                this.makerfactoryidValue = item.label
              }
            }
            this.formData.applyDeptNo = response.data.deptno
            this.formData.applyCostNo = response.data.deptcostno
            this.formData.applyLevelType = response.data.leveltypename
            this.formData.applyDeptNam = response.data.deptname
            this.formData.applyManager = response.data.ismanager
            this.formData.applyMail = response.data.email
            this.formData.applyLegal = response.data.artificialperson
            this.formData.applyFactoryId = response.data.factoryid
          }
          else {
            this.formData.applyEmpName = ''
            this.formData.makerfactoryid = ''
            this.formData.applyDeptNo = ''
            this.formData.applyCostNo = ''
            this.formData.applyLevelType = ''
            this.formData.applyDeptNam = ''
            this.formData.applyManager = ''
            this.formData.applyMail = ''
            this.formData.applyLegal = ''
            this.formData.applyFactoryId = ''
          }
        }
      });
    },
    onSignFormSelected(selectEmp, modelNo, modelName) {
      this.$set(this.formData, modelNo, selectEmp.empNo)
      this.$set(this.formData, modelName, selectEmp.empName)
    },
  }
}

</script>
<style rel="stylesheet/scss" lang="scss" scoped>
@import "~@/assets/styles/mobileSkin/mobileMixin.scss";

.el-upload__tip {
  line-height: 1.2;
}

/deep/.demo-table-expand {
  font-size: 0;
}

/deep/.demo-table-expand label {
  width: 90px;
  color: #99a9bf;
}

/deep/.demo-table-expand/deep/.el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  width: 100%;
}

/deep/.el-input-number--medium {
  width: 100%;
}

.del-handler {
  font-size: 20px;
  color: #ff4949;
}

.el-slider>/deep/.el-slider__runway {
  margin: 15px 0;
}

.el-checkbox-group {
  font-size: inherit;
}

.el-color-picker {
  display: inherit;
}

.talbe-name-style {
  font-size: 16px;
  font-weight: bold;
  line-height: 50px;
}

/deep/.el-input-number--medium {
  width: 100%;
}

/deep/.full-row {
  width: 100%;
}

.mobile-content {
  background: #EEEEEE;
}

.mobile-card {
  border: 0;
  background: transparent;
}

#staffCard {
  border: 0;
}

.mobile-body {
  padding: 0;
}

/deep/input,
/deep/textarea {
  border-color: transparent;
}

.head-pannel {
  background: #ffffff;
  padding: 15px;
  margin-top: 5px;
  color: #6B6B6B;
}

.head-pannel:after {
  clear: both;
  content: "";
  display: block;
}

.head-pannel>div {
  font-size: 14px;
  padding: 5px;
}

/deep/.el-row {
  margin-left: 0;
  margin-right: 0;
}

.body-main .el-form-item {
  margin-top: 15px;
  margin-bottom: 15px;
  border-color: transparent;
}

.el-table__expanded-cell .el-form-item {
  margin: 0;
}

.el-form-item>label {
  padding-left: 10px;
}

.body-main .el-col {
  border-bottom: 1px solid #EAEAEA;
}

.body-main {
  background: #ffffff;
  padding: 15px 20px;
  margin-top: 8px;
}

.body-main:after {
  clear: both;
  content: "";
  display: block;
}

.child-item {
  margin-top: 8px;
  background: #ffffff;
}

.child-item:after {
  clear: both;
  content: "";
  display: block;
}

.child-item /deep/.el-form-item {
  margin: 15px 10px;
  padding-bottom: 15px;
  border-color: transparent;
  border-bottom: 1px solid #EAEAEA;
}

.talbe-name-style {
  padding-left: 15px;
}

.sign-content {
  background: #ffffff;
  margin-top: 8px;
  color: #000000;
}

.sign-content:after {
  clear: both;
  content: "";
  display: block;
}

.sign-point>.el-form-item {
  padding: 5px 10px;
  margin-bottom: 0;
}

.btn-div {
  background: white;
  padding: 10px;
  margin: 10px 0;
  float: none;
}

.el-message-box__btns button:nth-child(2) {
  margin-left: 0;
}

.el-slider {
  /deep/.el-slider__bar {
    @include backgroundColor('themeMainColor');
  }
  
  /deep/.el-slider__button-wrapper {
    .el-slider__button {
      @include borderColor('themeMainColor');
    }
  }
}

.el-radio.is-checked {
  /deep/.el-radio__input.is-checked {
    .el-radio__inner {
      @include borderColor('themeMainColor');
      @include backgroundInfo('themeMainColor');
    }
  }
  
  /deep/.el-radio__input.is-checked+.el-radio__label {
    @include fontColor('themeMainColor');
  }
}

.el-checkbox.is-checked {
  /deep/.el-checkbox__input.is-checked {
    .el-checkbox__inner {
      @include borderColor('themeMainColor');
      @include backgroundInfo('themeMainColor');
    }
  }
  
  /deep/.el-checkbox__input.is-checked+.el-checkbox__label {
    @include fontColor('themeMainColor');
  }
}

.el-switch.is-checked {
  /deep/.el-switch__core {
    @include borderColor('themeMainColor');
    @include backgroundInfo('themeMainColor');
  }
}

.cube-popup {
  /deep/.cube-picker-confirm {
    @include fontColor('themeMainColor');
  }
}

.child-item {
  /deep/.el-button--primary {
    @include borderColor('themeMainColor');
    @include backgroundInfo('themeMainColor');
  }
  
  /deep/.del-handler {
    @include fontColor('themeMainColor');
  }
}

.dialog-footer {
  .el-button--success {
    @include backgroundInfo('themeMainColor');
    @include borderColor('themeMainColor');
  }
  
  .el-button--info {
    @include backgroundInfo('themeMainColor');
    @include borderColor('themeMainColor');
  }
}

</style>
