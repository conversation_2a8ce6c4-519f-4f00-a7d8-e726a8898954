<template>
    <page-top-bar title="太原周邊總處垃圾清運服務申請單">
        <div slot="content">
            <div class="tab-slide-container">
                <el-button
                        icon="el-icon-plus"
                        @click="handleAdd"
                        class="cube-btn-primary add-btn"
                >{{$t('table.add')}}</el-button>
                <cube-scroll :options="scrollOptions"
                             ref="scrollPannel"
                             :data="EntRubbishClearList"
                             @pulling-down="refreshPage"
                             @pulling-up="showNextPage"
                             style="height:calc(100vh - 80px)">
                    <el-form ref="form"  label-width="100px" label="left" style="padding:10px;">
                            <el-row  v-for="(item, index) in EntRubbishClearList" :key="index">
                                <el-col :span="24">
                                    <el-form-item :label="$t('ent_rubbish_clear_075d399c2dcaa20f94d53895254e58a1.label.serialno')">
                                       {{item.serialno}}
                                    </el-form-item>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        <el-form-item :label="$t('ent_rubbish_clear_075d399c2dcaa20f94d53895254e58a1.label.applyEmpNo')">
                                                    {{item.applyEmpNo}}
                                                </el-form-item>
                                                                                                                                                                                                                                                             <el-form-item :label="$t('ent_rubbish_clear_075d399c2dcaa20f94d53895254e58a1.label.applyEmpName')">
                                                    {{item.applyEmpName}}
                                                </el-form-item>
                                                                                                                                                                                                                                                             <el-form-item :label="$t('ent_rubbish_clear_075d399c2dcaa20f94d53895254e58a1.label.applyDeptNo')">
                                                    {{item.applyDeptNo}}
                                                </el-form-item>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 <el-form-item :label="$t('ent_rubbish_clear_075d399c2dcaa20f94d53895254e58a1.label.clearDemandDate')">
                                                    {{item.clearDemandDate}}
                                                </el-form-item>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             <el-form-item :label="$t('ent_rubbish_clear_075d399c2dcaa20f94d53895254e58a1.label.createTime')">
                                        {{item.createTime}}
                                    </el-form-item>
                                    <el-form-item :label="$t('ent_rubbish_clear_075d399c2dcaa20f94d53895254e58a1.label.signNode')">
                                        {{item.signNode}}
                                    </el-form-item>
                                    <el-form-item >
                                        <el-link type="primary" class="detail-btn" @click="showDetail(item.id)">{{$t('common.viewDetail')}} >></el-link>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                    </el-form>
                </cube-scroll>
            </div>
        </div>
    </page-top-bar>
</template>

<script>
    import { listEntRubbishClearApp } from "@/api/caaesign/entRubbishClear";
    export default {
        name: "EntRubbishClear",
        data() {
            return {
                EntRubbishClearList: [],
                queryParams: {
                    current: 1,
                    size: 5,
                    workStatus: 2,
                },
                scrollOptions: {
                    directionLockThreshold: 0,
                    pullDownRefresh: {
                        threshold: 60,
                        stop: 40,
                        txt: this.$t("common.refreshOk")
                    },
                    pullUpLoad: {
                        hreshold: 0,
                        txt: {more: this.$t("common.pullUpLoad"), noMore: this.$t("common.noMore")},
                        visible: true
                    },
                },
                                                                                                                                                                                                                                                                                                                                                                                                              workStatusOptions:[],
                                                                                                                                                                                                                                                                                                                                                                                              makerfactoryidOptions:[],
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     nextPageShow:true,
            };
        },
        created() {
            this.getList()
                                                                                                                                                                                                                                                                                                                                    this.getDicts("work_status").then(response => {
                            this.workStatusOptions = response.data;
                        });
                                                                                                                                                                                                                                                                                                                                            this.getDicts("caaesign_factory").then(response => {
                            this.makerfactoryidOptions = response.data;
                        });
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        },
        methods: {
            getList() {
                this.loading = true;
                listEntRubbishClearApp(this.queryParams).then(response => {
                    this.EntRubbishClearList = response.data;
                    this.total = response.total;
                    this.loading = false;
                });
            },
            refreshPage:function(){
                this.queryParams.current = 1
                listEntRubbishClearApp(this.queryParams).then(response => {
                    this.EntRubbishClearList = response.data;
                });
            },
            showNextPage:function(){
                if(this.nextPageShow){
                    this.nextPageShow = false
                    this.queryParams.current = this.queryParams.current + 1
                    listEntRubbishClearApp(this.queryParams).then(response => {
                        if(response.data!=null&&response.data.length>0){
                            this.EntRubbishClearList.push(...response.data)
                            this.nextPageShow = true
                        }else{
                            this.queryParams.current = this.queryParams.current - 1
                            this.$refs.scrollPannel.forceUpdate();
                            this.$refs.scrollPannel.refresh();
                            this.nextPageShow = true
                        }
                    });
                }
            },
            handleAdd:function(){
                this.$router.push('/caaesign/entRubbishClearApp/EntRubbishClearAddApp');
            },
            showDetail:function(id){
                this.$router.push({path:'/caaesign/entRubbishClearApp/EntRubbishClearDetailApp', query: { id: id } });
            },
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        }
    };
</script>
<style lang="scss" scoped>
    @import "~@/assets/styles/mobileSkin/mobileMixin.scss";
    /deep/.el-form-item {
        margin-bottom: 0;
    }
    /deep/.el-form .el-row{
        border-radius: 5px;
        box-shadow: 0px 0px 20px 0px #F6F6F6;
        color:#000000;
    }

    .tab-slide-container {
        background: #ffffff;

        .add-btn {
            @include borderColor('titleBgColor');
            @include fontColor('titleBgColor');
            background: #ffffff;
            margin-top: 2vw;
        }

        .detail-btn {
            /deep/ .el-link--inner {
                @include fontColor('titleBgColor');
            }
        }
    }
</style>
