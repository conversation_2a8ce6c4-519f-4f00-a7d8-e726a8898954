<template>
  <div class="ant-modal-content">
    <div class="ant-modal-body">
      <el-form ref="elForm" class="ant-form ant-form-horizontal" :model="formData" :rules="rules"
               size="medium" label-width="100px" :label-position="labelPosition">
        <div class="ant-card ant-card-bordered" id="staffCard">
          <div class="ant-card-body" id="printContent">
            <el-row :gutter="15">
              <span id="staffEvectionTitle" style="margin-bottom: 10px">數據庫服務申請單</span>
              <el-col :span="10" :xs="24" class="el-col-no-border" id="serialno">
                任務編碼:{{formData.serialno?formData.serialno:'提交表單后自動生成'}}
              </el-col>
              <el-col :span="8" :xs="24" class="el-col-no-border" id="createTime">
                填單時間:{{formData.createTime}}
              </el-col>
              <el-col :span="6" :xs="24" class="el-col-no-border" id="makerNo">
                填單人:{{formData.makerNo +' / '+ formData.makerName}}
              </el-col>
              <el-col :span="24" :xs="24" class="talbe-name-style">
                申請人基本信息
              </el-col>
              <el-col :span="6" :xs="24">
                <el-form-item label-width="110px" label="申請人工號" prop="applyEmpNo">
                  {{formData.applyEmpNo}}
                </el-form-item>
              </el-col>
              <el-col :span="6" :xs="24">
                <el-form-item label="申請人姓名" prop="applyEmpName">
                  {{formData.applyEmpName}}
                </el-form-item>
              </el-col>
              <el-col :span="6" :xs="24">
                <el-form-item label="聯繫電話" prop="applyTel">
                  {{formData.applyTel}}
                </el-form-item>
              </el-col>
              <el-col :span="6" :xs="24">
                <el-form-item label="廠區" prop="makerfactoryid">
                  {{formData.makerfactoryid}}
                </el-form-item>
              </el-col>
              <el-col :span="6" :xs="24">
                <el-form-item label-width="110px" label="部門代碼" prop="applyDeptNo">
                  {{formData.applyDeptNo}}
                </el-form-item>
              </el-col>
              <el-col :span="12" :xs="24">
                <el-form-item label="部門名稱" prop="applyDeptName">
                  {{formData.applyDeptName}}
                </el-form-item>
              </el-col>
              <el-col :span="6" :xs="24">
                <el-form-item label="申請日期" prop="applyDate">
                  {{formData.applyDate}}
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24">
                <el-form-item label-width="110px" label="需求類別" prop="demandType">
                  {{formData.demandType}}
                </el-form-item>
              </el-col>
              <el-col :span="12" :xs="24">
                <el-form-item label-width="110px" label="資料庫IP" prop="databaseIp">
                  {{formData.databaseIp}}
                </el-form-item>
              </el-col>
              <el-col :span="12" :xs="24">
                <el-form-item label="資料庫名稱" prop="databaseName">
                  {{formData.databaseName}}
                </el-form-item>
              </el-col>
              <!--<el-col :span="12" :xs="24">
                <el-form-item label-width="110px" label="重要級別" prop="importanceLevel">
                  {{formData.importanceLevel}}
                </el-form-item>
              </el-col>
              <el-col :span="12" :xs="24">
                <el-form-item label="機密等級" prop="confidentialLevel">
                  {{formData.confidentialLevel}}
                </el-form-item>
              </el-col>-->
              <el-col :span="12" :xs="24">
                <el-form-item label-width="110px" label="用戶單位" prop="userUnit">
                  {{formData.userUnit}}
                </el-form-item>
              </el-col>
              <el-col :span="6" :xs="24">
                <el-form-item label="法人" prop="legalPerson">
                  {{formData.legalPerson}}
                </el-form-item>
              </el-col>
              <el-col :span="6" :xs="24">
                <el-form-item label="費用代碼" prop="costNo">
                  {{formData.costNo}}
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24">
                <el-form-item label-width="110px" label="詳細需求說明" prop="demandIllstrate">
                  {{formData.demandIllstrate}}
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24" class="talbe-name-style">
                DB訪問明細信息
              </el-col>
              <div style="overflow-x: auto;width: 100%;">
                <el-scrollbar style="width:150%;">
                  <el-col :span="24" :xs="24" style="padding: 0px;">
                    <el-table v-if="isMobile" border stripe ref="entDatabaseServerItemsDragTableMobile"
                              :data="formData.entDatabaseServerItemsLists" row-key="id">
                      <el-table-column label="數據庫服務申請單明細表" type="index" min-width="90%"
                                       :width="entDatabaseServerItemsDragTableMobileClientWidth">
                        <template slot-scope="scope">
                          <span>{{$t('common.serialNumber')}}:{{ scope.$index + 1 }}</span>
                          <el-form-item :prop="'entDatabaseServerItemsLists.' + scope.$index + '.dbIp'"
                                        label="DB IP">
                            <el-input v-model.trim="scope.row.dbIp"
                                      :placeholder="$t('common.placeholderDefault') + 'DB IP'"
                                      :disabled='true'></el-input>
                          </el-form-item>
                          <el-form-item :prop="'entDatabaseServerItemsLists.' + scope.$index + '.dbUser'"
                                        label="DB User">
                            <el-input v-model.trim="scope.row.dbUser"
                                      :placeholder="$t('common.placeholderDefault') + 'DB User'" :disabled='true'>
                            </el-input>
                          </el-form-item>
                          <el-form-item :prop="'entDatabaseServerItemsLists.' + scope.$index + '.demandFactoryid'"
                                        label="需求廠區">
                            <el-select v-model.trim="scope.row.demandFactoryid"
                                       :placeholder="$t('common.placeholderDefault') + '需求廠區'" :disabled='true'>
                              <el-option v-for="(item, index) in demandFactoryidOptions" :key="index"
                                         :label="item.label" :value="item.value" :disabled="item.disabled"></el-option>
                            </el-select>
                          </el-form-item>
                          <el-form-item :prop="'entDatabaseServerItemsLists.' + scope.$index + '.applyType'"
                                        label="應用類型">
                            <el-select v-model.trim="scope.row.applyType"
                                       :placeholder="$t('common.placeholderDefault') + '應用類型'" :disabled='true'>
                              <el-option v-for="(item, index) in applyTypeOptions" :key="index" :label="item.label"
                                         :value="item.value" :disabled="item.disabled"></el-option>
                            </el-select>
                          </el-form-item>
                          <el-form-item :prop="'entDatabaseServerItemsLists.' + scope.$index + '.applyIp'"
                                        label="應用IP">
                            <el-input v-model.trim="scope.row.applyIp"
                                      :placeholder="$t('common.placeholderDefault') + '應用IP'"
                                      :disabled='true'></el-input>
                          </el-form-item>
                          <el-form-item :prop="'entDatabaseServerItemsLists.' + scope.$index + '.applyName'"
                                        label="應用名稱">
                            <el-input v-model.trim="scope.row.applyName"
                                      :placeholder="$t('common.placeholderDefault') + '應用名稱'"
                                      :disabled='true'></el-input>
                          </el-form-item>
                          <el-form-item :prop="'entDatabaseServerItemsLists.' + scope.$index + '.ifDdl'"
                                        label="是否開通DDL權限">
                            <el-select v-model.trim="scope.row.ifDdl"
                                       :placeholder="$t('common.placeholderDefault') + '是否開通DDL權限'" :disabled='true'>
                              <el-option v-for="(item, index) in ifDdlOptions" :key="index" :label="item.label"
                                         :value="item.value" :disabled="item.disabled"></el-option>
                            </el-select>
                          </el-form-item>
                          <el-form-item :prop="'entDatabaseServerItemsLists.' + scope.$index + '.accessDateStart'"
                                        label="訪問開始時間">
                            <el-date-picker v-model.trim="scope.row.accessDateStart"
                                            :placeholder="$t('common.placeholderDefault') + '訪問開始時間'" :disabled='true'>
                            </el-date-picker>
                          </el-form-item>
                          <el-form-item :prop="'entDatabaseServerItemsLists.' + scope.$index + '.accessDateEnd'"
                                        label="訪問結束時間">
                            <el-date-picker v-model.trim="scope.row.accessDateEnd"
                                            :placeholder="$t('common.placeholderDefault') + '訪問結束時間'" :disabled='true'>
                            </el-date-picker>
                          </el-form-item>
                          <el-form-item :prop="'entDatabaseServerItemsLists.' + scope.$index + '.responsNo'"
                                        label="負責人工號">
                            <el-input v-model.trim="scope.row.responsNo"
                                      :placeholder="$t('common.placeholderDefault') + '負責人工號'"
                                      :disabled='true'></el-input>
                          </el-form-item>
                          <el-form-item :prop="'entDatabaseServerItemsLists.' + scope.$index + '.responsName'"
                                        label="負責人姓名">
                            <el-input v-model.trim="scope.row.responsName"
                                      :placeholder="$t('common.placeholderDefault') + '負責人姓名'"
                                      :disabled='true'></el-input>
                          </el-form-item>
                          <el-form-item :prop="'entDatabaseServerItemsLists.' + scope.$index + '.responsDeptName'"
                                        label="部門">
                            <el-input v-model.trim="scope.row.responsDeptName"
                                      :placeholder="$t('common.placeholderDefault') + '部門'" :disabled='true'></el-input>
                          </el-form-item>
                          <el-button type="text"
                                     @click="handleDel_ent_database_server_items(scope.$index, scope.row)"
                                     class="del-handler" icon="el-icon-delete"></el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                    <el-table v-else border stripe ref="entDatabaseServerItemsDragTable"
                              :data="formData.entDatabaseServerItemsLists" row-key="id" :max-height="tableHeight">
                      <el-table-column label="編號" type="index" min-width="5%"/>
                      <el-table-column label="DB IP" min-width="10%" prop="dbIp">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entDatabaseServerItemsLists.' + scope.$index + '.dbIp'"
                                        label-width="0px">
                            <el-input v-model.trim="scope.row.dbIp"
                                      :placeholder="$t('common.placeholderDefault') + 'DB IP'"
                                      :disabled='true'></el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="DB User" min-width="10%" prop="dbUser">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entDatabaseServerItemsLists.' + scope.$index + '.dbUser'"
                                        label-width="0px">
                            <el-input v-model.trim="scope.row.dbUser"
                                      :placeholder="$t('common.placeholderDefault') + 'DB User'" :disabled='true'>
                            </el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="需求廠區" min-width="10%" prop="demandFactoryid">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entDatabaseServerItemsLists.' + scope.$index + '.demandFactoryid'"
                                        label-width="0px">
                            <el-select v-model.trim="scope.row.demandFactoryid"
                                       :placeholder="$t('common.placeholderDefault') + '需求廠區'" :disabled='true'>
                              <el-option v-for="(item, index) in demandFactoryidOptions" :key="index"
                                         :label="item.label" :value="item.value" :disabled="item.disabled"></el-option>
                            </el-select>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="應用類型" min-width="10%" prop="applyType">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entDatabaseServerItemsLists.' + scope.$index + '.applyType'"
                                        label-width="0px">
                            <el-select v-model.trim="scope.row.applyType"
                                       :placeholder="$t('common.placeholderDefault') + '應用類型'" :disabled='true'>
                              <el-option v-for="(item, index) in applyTypeOptions" :key="index" :label="item.label"
                                         :value="item.value" :disabled="item.disabled"></el-option>
                            </el-select>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="應用IP" min-width="10%" prop="applyIp">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entDatabaseServerItemsLists.' + scope.$index + '.applyIp'"
                                        label-width="0px">
                            <el-input v-model.trim="scope.row.applyIp"
                                      :placeholder="$t('common.placeholderDefault') + '應用IP'"
                                      :disabled='true'></el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="應用名稱" min-width="10%" prop="applyName">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entDatabaseServerItemsLists.' + scope.$index + '.applyName'"
                                        label-width="0px">
                            <el-input v-model.trim="scope.row.applyName"
                                      :placeholder="$t('common.placeholderDefault') + '應用名稱'"
                                      :disabled='true'></el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="是否開通DDL權限" min-width="10%" prop="ifDdl">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entDatabaseServerItemsLists.' + scope.$index + '.ifDdl'"
                                        label-width="0px">
                            <el-select v-model.trim="scope.row.ifDdl"
                                       :placeholder="$t('common.placeholderDefault') + '是否開通DDL權限'" :disabled='true'>
                              <el-option v-for="(item, index) in ifDdlOptions" :key="index" :label="item.label"
                                         :value="item.value" :disabled="item.disabled"></el-option>
                            </el-select>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="訪問開始時間" min-width="10%" prop="accessDateStart">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entDatabaseServerItemsLists.' + scope.$index + '.accessDateStart'"
                                        label-width="0px">
                            <el-date-picker v-model.trim="scope.row.accessDateStart"
                                            :placeholder="$t('common.placeholderDefault') + '訪問開始時間'" :disabled='true'>
                            </el-date-picker>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="訪問結束時間" min-width="10%" prop="accessDateEnd">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entDatabaseServerItemsLists.' + scope.$index + '.accessDateEnd'"
                                        label-width="0px">
                            <el-date-picker v-model.trim="scope.row.accessDateEnd"
                                            :placeholder="$t('common.placeholderDefault') + '訪問結束時間'" :disabled='true'>
                            </el-date-picker>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="負責人工號" min-width="10%" prop="responsNo">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entDatabaseServerItemsLists.' + scope.$index + '.responsNo'"
                                        label-width="0px">
                            <el-input v-model.trim="scope.row.responsNo"
                                      :placeholder="$t('common.placeholderDefault') + '負責人工號'"
                                      :disabled='true'></el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="負責人姓名" min-width="10%" prop="responsName">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entDatabaseServerItemsLists.' + scope.$index + '.responsName'"
                                        label-width="0px">
                            <el-input v-model.trim="scope.row.responsName"
                                      :placeholder="$t('common.placeholderDefault') + '負責人姓名'"
                                      :disabled='true'></el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="部門" min-width="10%" prop="responsDeptName">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entDatabaseServerItemsLists.' + scope.$index + '.responsDeptName'"
                                        label-width="0px">
                            <el-input v-model.trim="scope.row.responsDeptName"
                                      :placeholder="$t('common.placeholderDefault') + '部門'" :disabled='true'></el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                    </el-table>
                  </el-col>
                </el-scrollbar>
              </div>
              <el-col :span="24" :xs="24">
                <div class="dialog-footer" align="center" style="padding-top: 5px;padding-bottom: 5px">
                  <el-button type="danger" @click="closeForm">{{$t('common.close')}}</el-button>
                  <el-button type="primary" v-print="'#printContent'" >打印</el-button>
                  <el-button type="info" @click="handleTrack"
                             v-if="formData.workStatus == 2||formData.workStatus ==3||formData.workStatus ==4">
                    {{$t('table.activity.flowTracing')}}</el-button>
                </div>
              </el-col>
            </el-row>
            <!-- 簽核線 -->
            <el-col :span="24" class="print-hide-div">
              <div class="dialog-footer" style="padding-top: 5px;padding-bottom: 5px">
                <div v-html="signPath"></div>
              </div>
            </el-col>
            <!-- 审核记录 -->
            <el-table border stripe :data="commentList" class="table-max">
              <el-table-column :label="$t('table.activity.taskId')" align="center" prop="id"/>
              <el-table-column :label="$t('table.activity.approvedBy')" align="center" prop="userId"
                               :show-overflow-tooltip="true"/>
              <el-table-column :label="$t('table.activity.approvalOpinions')" align="center"
                               prop="fullMessage" :show-overflow-tooltip="true"/>
              <el-table-column :label="$t('table.activity.operIp')" align="center" prop="operIp"
                               :show-overflow-tooltip="true"/>
              <el-table-column :label="$t('table.activity.auditStatus')" align="center" prop="status"
                               :formatter="statusFormat" :show-overflow-tooltip="true"/>
              <el-table-column :label="$t('table.activity.approvalTime')" align="center" prop="time"
                               width="180">
                <template slot-scope="scope">
                  <span>{{ parseTime(scope.row.time) }}</span>
                </template>
              </el-table-column>
            </el-table>
            <!-- 任务跟踪对话框 -->
            <el-dialog :title="$t('table.activity.taskMap')" :visible.sync="showImgDialog" width="760px">
              <img :src="imgUrl" style="padding-bottom: 60px;">
            </el-dialog>
          </div>
        </div>
      </el-form>
    </div>
  </div>
</template>
<script>
  import {
    getEntDatabaseServer,
    addEntDatabaseServer,
    editEntDatabaseServer,
    getSignPath
  }
    from "@/api/caaesign/entDatabaseServer"
  import '@/assets/styles/design-build/design-add-view.scss'
  import {
    listTask,
    getTask,
    checkTask,
    taskComment
  }
    from "@/api/activiti/task"
  import {
    getAccessToken,
    getZltAccessToken
  }
    from "@/utils/auth";
  import {
    previewFile,
    getHeader
  }
    from "@/utils/entfrm";
  import {
    listFileInfo,
    getFileInfo,
    delFileInfo,
    addFileInfo,
    editFileInfo,
    getByKey
  }
    from "@/api/system/fileInfo";

  export default {
    components: {},
    props: [],
    data() {
      return {
        formData: {
          applyEmpNo: undefined,
          applyEmpName: undefined,
          applyTel: undefined,
          makerfactoryid: undefined,
          applyDeptNo: undefined,
          applyDeptName: undefined,
          applyDate: null,
          demandType: [],
          databaseIp: undefined,
          databaseName: undefined,
          importanceLevel: undefined,
          confidentialLevel: undefined,
          userUnit: undefined,
          legalPerson: undefined,
          costNo: undefined,
          demandIllstrate: undefined,
          entDatabaseServerItemsLists: [],
        },
        rules: {
          applyEmpNo: [{
            required: true,
            message: '請輸入申請人工號',
            trigger: 'blur'
          }],
          applyEmpName: [{
            required: true,
            message: '請輸入申請人姓名',
            trigger: 'blur'
          }],
          applyTel: [{
            required: true,
            message: '請輸入聯繫電話',
            trigger: 'blur'
          }],
          makerfactoryid: [{
            required: true,
            message: '請選擇廠區',
            trigger: 'change'
          }],
          applyDeptNo: [{
            required: true,
            message: '請輸入部門代碼',
            trigger: 'blur'
          }],
          applyDeptName: [{
            required: true,
            message: '請輸入部門名稱',
            trigger: 'blur'
          }],
          applyDate: [{
            required: true,
            message: '请選擇申請日期',
            trigger: 'change'
          }],
          demandType: [{
            required: true,
            type: 'array',
            message: '請至少選擇一個需求類別',
            trigger: 'change'
          }],
          databaseIp: [{
            required: true,
            message: '請輸入資料庫IP',
            trigger: 'blur'
          }],
          databaseName: [{
            required: true,
            message: '請輸入資料庫名稱',
            trigger: 'blur'
          }],
          importanceLevel: [{
            required: true,
            message: '重要級別不能為空',
            trigger: 'change'
          }],
          confidentialLevel: [{
            required: true,
            message: '機密等級不能為空',
            trigger: 'change'
          }],
          userUnit: [{
            required: true,
            message: '請輸入用戶單位',
            trigger: 'blur'
          }],
          legalPerson: [{
            required: true,
            message: '請輸入法人',
            trigger: 'blur'
          }],
          costNo: [{
            required: true,
            message: '請輸入費用代碼',
            trigger: 'blur'
          }],
          demandIllstrate: [{
            required: true,
            message: '請輸入詳細需求說明',
            trigger: 'blur'
          }],
        },
        tableHeight: document.documentElement.scrollHeight - 245 + "px",
        // 审批意见数据
        commentList: [],
        //簽核路徑
        signPath: [],
        //任务图url
        imgUrl: '',
        isDisabled: false,
        // 是否显示任务图
        showImgDialog: false,
        auditStatus: [],
        makerfactoryidOptions: [],
        demandTypeOptions: [{
          "createBy": "entfrm",
          "createTime": "2021-08-19 13:47:41",
          "updateBy": null,
          "updateTime": "2021-08-19 13:46:52",
          "remarks": null,
          "id": "0ee1720b8badf00cf211bee3866f9c19",
          "dictType": "caaesign_demand_type",
          "label": "DB用戶新增",
          "value": "1",
          "sort": 0,
          "endDate": null,
          "beginDate": null,
          "endSignDate": null,
          "beginSignDate": null
        }, {
          "createBy": "entfrm",
          "createTime": "2021-08-19 13:47:53",
          "updateBy": null,
          "updateTime": "2021-08-19 13:47:04",
          "remarks": null,
          "id": "7552ea3fdff33c9508fe4af42e13ce90",
          "dictType": "caaesign_demand_type",
          "label": "DB訪問權限更新",
          "value": "2",
          "sort": 1,
          "endDate": null,
          "beginDate": null,
          "endSignDate": null,
          "beginSignDate": null
        }, {
          "createBy": "entfrm",
          "createTime": "2021-08-19 13:48:11",
          "updateBy": null,
          "updateTime": "2021-08-19 13:47:22",
          "remarks": null,
          "id": "fd0a7445fff47a81376f3c972b3f4c9f",
          "dictType": "caaesign_demand_type",
          "label": "DB特殊需求",
          "value": "3",
          "sort": 2,
          "endDate": null,
          "beginDate": null,
          "endSignDate": null,
          "beginSignDate": null
        }],
        importanceLevelOptions: [{
          "createBy": "entfrm",
          "createTime": "2021-08-19 13:49:17",
          "updateBy": "entfrm",
          "updateTime": "2021-08-19 13:49:30",
          "remarks": null,
          "id": "2f12b39a2a0d2b72e146ef489b3ddd7d",
          "dictType": "caaesign_importance_level",
          "label": "A類",
          "value": "2",
          "sort": 0,
          "endDate": null,
          "beginDate": null,
          "endSignDate": null,
          "beginSignDate": null
        }, {
          "createBy": "entfrm",
          "createTime": "2021-08-19 13:49:25",
          "updateBy": "entfrm",
          "updateTime": "2021-08-19 13:49:47",
          "remarks": null,
          "id": "dacfb29975a4e41a169411c0895825e9",
          "dictType": "caaesign_importance_level",
          "label": "B類",
          "value": "1",
          "sort": 1,
          "endDate": null,
          "beginDate": null,
          "endSignDate": null,
          "beginSignDate": null
        }, {
          "createBy": "entfrm",
          "createTime": "2021-08-19 13:49:41",
          "updateBy": null,
          "updateTime": "2021-08-19 13:48:52",
          "remarks": null,
          "id": "b10b949f739f11450ee9c216bcbba2c9",
          "dictType": "caaesign_importance_level",
          "label": "C類",
          "value": "3",
          "sort": 2,
          "endDate": null,
          "beginDate": null,
          "endSignDate": null,
          "beginSignDate": null
        }],
        confidentialLevelOptions: [{
          "createBy": "entfrm",
          "createTime": "2021-08-19 13:51:02",
          "updateBy": null,
          "updateTime": "2021-08-19 13:50:13",
          "remarks": null,
          "id": "4c2a1bcd2e459d1bcf375153a3935b3b",
          "dictType": "caaesign_confidential_level",
          "label": "I級",
          "value": "1",
          "sort": 0,
          "endDate": null,
          "beginDate": null,
          "endSignDate": null,
          "beginSignDate": null
        }, {
          "createBy": "entfrm",
          "createTime": "2021-08-19 13:51:11",
          "updateBy": null,
          "updateTime": "2021-08-19 13:50:22",
          "remarks": null,
          "id": "71fe57ceb017df1c996ed2667782f2df",
          "dictType": "caaesign_confidential_level",
          "label": "II級",
          "value": "2",
          "sort": 1,
          "endDate": null,
          "beginDate": null,
          "endSignDate": null,
          "beginSignDate": null
        }, {
          "createBy": "entfrm",
          "createTime": "2021-08-19 13:51:19",
          "updateBy": "entfrm",
          "updateTime": "2021-08-19 13:51:25",
          "remarks": null,
          "id": "a573dc97e79ea3cfc65867d145766eb3",
          "dictType": "caaesign_confidential_level",
          "label": "III級",
          "value": "3",
          "sort": 2,
          "endDate": null,
          "beginDate": null,
          "endSignDate": null,
          "beginSignDate": null
        }],
        demandFactoryidOptions: [],
        applyTypeOptions: [],
        ifDdlOptions: [],
        entDatabaseServerItemsDragTableMobileClientWidth: 0,
        isMobile: false,
        labelPosition: 'left',
      }
    },
    computed: {},
    watch: {},
    created() {
      const id = this.$route.query.id;
      this.changeTagsView(this.$route.query);
      this.isMobile = this.isMobileFun()
      if (this.isMobile) {
        this.labelPosition = 'top'
      }
      if (id != null && id != undefined) {
        getEntDatabaseServer(id).then(response => {
          this.formData = response.data;
          taskComment(this.formData.processId).then(response => {
            this.commentList = response.data;
          });
          getSignPath(this.formData.processId).then(response => {
            this.signPath = response.data;
          });
          if (this.formData.attachids) {
            let a = this.formData.attachids.split(',');
            if (a.length > 0) {
              a.forEach(item => {
                if (item) {
                  getByKey(item).then(response => {
                    this.upload.fileList.push({
                      name: response.data.orignalName,
                      url: response.data.name
                    });
                  })
                }
              })
            }
          }
        });
      }
      this.getMakerfactoryidOptions()
      this.getDemandTypeOptions()
      this.getImportanceLevelOptions()
      this.getConfidentialLevelOptions()
      this.getDemandFactoryidOptions()
      this.getApplyTypeOptions()
      this.getIfDdlOptions()
      this.getDicts("sign_status").then(response => {
        this.auditStatus = response.data;
      });
      this.getDicts("caaesign_factory").then(response => {
        response.data.forEach(item => {
          this.formData.makerfactoryid = this.formData.makerfactoryid.replace(item.value, item.label);
        })
      });
      this.getDicts("caaesign_demand_type").then(response => {
        this.formData.demandType = JSON.parse(this.formData.demandType).join()
        response.data.forEach(item => {
          this.formData.demandType = this.formData.demandType.replace(item.value, item.label);
        })
      });
      this.getDicts("caaesign_importance_level").then(response => {
        response.data.forEach(item => {
          this.formData.importanceLevel = this.formData.importanceLevel.replace(item.value, item.label);
        })
      });
      this.getDicts("caaesign_confidential_level").then(response => {
        response.data.forEach(item => {
          this.formData.confidentialLevel = this.formData.confidentialLevel.replace(item.value, item
            .label);
        })
      });
    },
    mounted() {
      this.$nextTick(function () {
        if (this.isMobile) {
          this.entDatabaseServerItemsDragTableMobileClientWidth = this.$refs
            .entDatabaseServerItemsDragTableMobile.$el.clientWidth
        }
      })
    },
    methods: {
      closeForm() {
        //关闭子页面
        if (this.$store.state.settings.tagsView) {
          this.$router.go(-1) // 返回
          this.$store.state.tagsView.visitedViews.splice(this.$store.state.tagsView.visitedViews.findIndex(
            item => item.path === this.$route.path), 1)
          this.$router.push(this.$store.state.tagsView.visitedViews[this.$store.state.tagsView.visitedViews
            .length - 1].path)
        }
        else {
          // parent.postMessage("closeCurrentTabMessage",'*');
          parent.postMessage("closeCurrentTabMessage", '*');
        }
      },
      handleTrack() {
        this.imgUrl = process.env.VUE_APP_BASE_API + '/activiti/task/track/' + this.formData.processId,
          this.showImgDialog = true
      },
      statusFormat(row, column) {
        return this.selectDictLabel(this.auditStatus, row.status);
      },
      getMakerfactoryidOptions() {
        this.getDicts("caaesign_factory").then(response => {
          this.makerfactoryidOptions = response.data;
        });
      },
      getDemandTypeOptions() {
        this.getDicts("caaesign_demand_type").then(response => {
          this.demandTypeOptions = response.data;
        });
      },
      getImportanceLevelOptions() {
        this.getDicts("caaesign_importance_level").then(response => {
          this.importanceLevelOptions = response.data;
        });
      },
      getConfidentialLevelOptions() {
        this.getDicts("caaesign_confidential_level").then(response => {
          this.confidentialLevelOptions = response.data;
        });
      },
      checkBoxParse() {
        if (this.formData.demandType) {
          this.formData.demandType = JSON.parse(this.formData.demandType)
        }
      },
      cascaderParse() {
      },
      handleAdd_ent_database_server_items() {
        const cloumn = {
          remarks: '',
          makerdeptno: '',
          makerfactoryid: '',
          dbIp: '',
          dbUser: '',
          demandFactoryid: '',
          applyType: '',
          applyIp: '',
          applyName: '',
          ifDdl: '',
          accessDateStart: '',
          accessDateEnd: '',
          responsNo: '',
          responsName: '',
          responsDeptName: ''
        };
        this.formData.entDatabaseServerItemsLists.splice(this.formData.entDatabaseServerItemsLists.length, 0,
          cloumn);
        for (let index in this.formData.entDatabaseServerItemsLists) {
          this.formData.entDatabaseServerItemsLists[index].sort = parseInt(index) + 1;
        }
      },
      handleDel_ent_database_server_items(index, row) {
        let functionName = this.$t('ent_database_server_8040b6a9d6905ebfb822b67dd57f1936.default.functionName');
        this.$confirm(this.$t('tips.deleteConfirm', ['數據庫服務申請單明細表', row.id]), this.$t('tips.warm'), {
          confirmButtonText: this.$t("common.confirmTrim"),
          cancelButtonText: this.$t("common.cancelTrim"),
          type: "warning",
        }).then(() => {
          this.formData.entDatabaseServerItemsLists.splice(index, 1);
          this.msgSuccess(this.$t("tips.deleteSuccess"));
        }).catch(function (err) {
          console.log(err);
        });
      },
      getDemandFactoryidOptions() {
        this.getDicts("caaesign_factory").then(response => {
          this.demandFactoryidOptions = response.data;
        });
      },
      getApplyTypeOptions() {
        this.getDicts("caaesign_apply_type").then(response => {
          this.applyTypeOptions = response.data;
        });
      },
      getIfDdlOptions() {
        this.getDicts("caaesign_if_ddl").then(response => {
          this.ifDdlOptions = response.data;
        });
      },
    }
  }

</script>
<style scoped>
  @media print {
    .print-hide-div {
      display: none;
    }
  }

  .del-handler {
    font-size: 20px;
    color: #ff4949;
  }

  .el-dialog {
    position: relative;
    margin: 0 auto 0px;
    background: #FFFFFF;
    border-radius: 2px;
    -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 50%;
    height: 60%;
  }

  .el-dialog__body {
    border-top: 1px solid #dcdfe6;
    border-bottom: 1px solid #dcdfe6;
    max-height: 85% !important;
    min-height: 70%;
    overflow-y: auto;
  }
  .talbe-name-style{
    font-size: 16px;
    font-weight: bold;
    color: #1DB8FF;
    line-height: 50px;
  }
  @media print{
    .print-hide-div{
      display:none;
    }
    .el-col:not(.el-col-no-border) {
      border: 1px solid #ccc;
      margin-top: -1px;
    }
    #printContent{
      box-sizing: border-box;
      width: 1000px;
    }
    .el-form-item {
      margin-top: 12px;
      margin-bottom: 12px;
    }
    .el-form-item__label{
      text-align: left;
    }
    .el-form-item{
      min-height:36px;
    }
    #serialno,#makerNo,#createTime{
      padding:10px 0;
    }
    *{
      box-sizing: border-box;
    }
    .el-table{
      border: 1px solid #dfe6ec;
    }
    html{
      background-color: #FFFFFF;
      margin: 0;
    }

    .el-table__header{
      table-layout: auto;
    }
    .table-max .el-table__body,.table-max .el-table__header{
      width:100% !important;
    }
    .table-max  col{
      width:calc(100% / 6)  !important;
    }
    .table-max .el-table__body .cell{
      width:100% !important;
    }


  }
  /*去除页眉页脚*/
  @page{
    size:  auto;
    margin: 3mm;
  }
  .table-style{
    text-align: center;
    border-collapse: collapse;
    border-spacing: 0;
    width: 100%;
  }
  .table-style td{
    border:1px solid #ccc;
    line-height: 30px;
  }
</style>
