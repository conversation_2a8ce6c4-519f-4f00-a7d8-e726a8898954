<template>
    <div class="ant-modal-content" slot="content">
      <el-form ref="elForm" :model="formData" :rules="rules" size="medium"  :label-position="labelPosition" class="spHeight">
        <div class="form-info">
          <a style="text-decoration:none;">
            <div class="rating__text"></div>
            <span class="course-head">表單信息</span>
          </a>
        </div>
        <div class="profile-body-area">
          <div class="profile-body">
            <div class="profile-info-table">
              <div class="profile-info-block">
                <div>表單編號：</div>
                <div>{{formData.wfsystemprocess.serialno}}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="space"></div>
        <div class="form-info" @click="isActive = !isActive">
          <a style="text-decoration:none;">
            <div class="rating__text"></div>
            <span class="course-head">申請人信息</span>
            <img class="imgUrlArrowhead" :src="imgUrlArrowhead2" v-if="isActive==true">
            <img class="imgUrlArrowhead2" :src="imgUrlArrowhead2" v-if="isActive==false">
          </a>
        </div>
        <div class="ant-modal-body">
          <collapse>
              <div class="profile-info-table" v-show="isActive">
                <div class="content-small">
                  <div class="profile-info-block">
                    <div>姓名：</div>
                    <div>{{formData.wfsystemprocess.applyname}}</div>
                  </div>
                  <div class="profile-info-block">
                    <div>聯繫電話：</div>
                    <div>{{formData.wfsystemprocess.applytel}}</div>
                  </div>
                  <div class="profile-info-block">
                    <div>廠區：</div>
                    <div>{{formData.wfsystemprocess.applyfactoryname}}</div>
                  </div>
                  <div class="profile-info-block">
                    <div>單位名稱：</div>
                    <div>{{formData.wfsystemprocess.applydeptname}}</div>
                  </div>
                  <div class="profile-info-block">
                    <div>申請日期：</div>
                    <div>{{parseTime(formData.wfsystemprocess.applydate,'{y}-{m}-{d}')}}</div>
                  </div>
                </div>
                <div class="content-medium">
                  <div class="content-medium-block">
                    <div><span>工號：</span>{{formData.wfsystemprocess.applyno}}</div>
                    <div><span>姓名：</span>{{formData.wfsystemprocess.applyname}}</div>
                    <div><span>聯繫電話：</span>{{formData.wfsystemprocess.applytel}}</div>
                  </div>
                  <div class="content-medium-block">
                    <div><span>職責：</span>{{formData.wfsystemprocess.applypost}}</div>
                    <div><span>廠區：</span>{{formData.wfsystemprocess.applyfactoryname}}</div>
                    <div><span>申請日期：</span>{{parseTime(formData.wfsystemprocess.applydate,'{y}-{m}-{d}')}}</div>
                  </div>
                  <div class="content-medium-block">
                    <div><span>單位名稱：</span>{{formData.wfsystemprocess.applydeptname}}</div>
                  </div>
                </div>
              </div>
          </collapse>
        </div>
        <div class="space"></div>
        <div class="form-info" @click="isActive2 = !isActive2">
          <a style="text-decoration:none;">
            <div class="rating__text"></div>
            <span class="course-head">提出人信息</span>
            <img class="imgUrlArrowhead" :src="imgUrlArrowhead2" v-if="isActive2==true">
            <img class="imgUrlArrowhead2" :src="imgUrlArrowhead2" v-if="isActive2==false">
          </a>
        </div>
        <div class="ant-modal-body">
          <collapse>
            <div class="profile-info-table" v-show="isActive2">
              <div class="content-small">
                <div class="profile-info-block">
                  <div>工號：</div>
                  <div>{{formData.wfsystemprocess.dealno}}</div>
                </div>
                <div class="profile-info-block">
                  <div>姓名：</div>
                  <div>{{formData.wfsystemprocess.dealname}}</div>
                </div>
                <div class="profile-info-block">
                  <div>管理職：</div>
                  <div>{{formData.wfsystemprocess.dealpost}}</div>
                </div>
              </div>
              <div class="content-medium">
                <div class="content-medium-block">
                  <div><span>工號：</span>{{formData.wfsystemprocess.dealno}}</div>
                  <div><span>姓名：</span>{{formData.wfsystemprocess.dealname}}</div>
                  <div><span>管理職：</span>{{formData.wfsystemprocess.dealpost}}</div>
                </div>
              </div>
            </div>
          </collapse>
        </div>
        <div class="space"></div>
        <div class="form-info" @click="isActive3 = !isActive3">
          <a style="text-decoration:none;">
            <div class="rating__text"></div>
            <span class="course-head">申請內容</span>
            <img class="imgUrlArrowhead" :src="imgUrlArrowhead2" v-if="isActive3==true">
            <img class="imgUrlArrowhead2" :src="imgUrlArrowhead2" v-if="isActive3==false">
          </a>
        </div>
        <div class="ant-modal-body">
          <collapse>
            <div class="profile-info-table" v-show="isActive3">
              <div class="content-small">
                <div class="profile-info-block">
                  <div>系統類別：</div>
                  <div>{{formData.wfsystemprocess.systemCategoryname}}</div>
                </div>
                <div class="profile-info-block">
                  <div>系統名稱：</div>
                  <div>{{formData.wfsystemprocess.systemBelowname}}</div>
                </div>
                <div class="profile-info-block">
                  <div>專案類型：</div>
                  <div>{{formData.wfsystemprocess.projectTypename}}</div>
                </div>
                <div class="profile-info-block">
                  <div>需求描述：</div>
                  <div class="text-area-type">
<!--                    <el-input v-model="formData.wfsystemprocess.applydescription" type="textarea"
                              :autosize="{minRows: 3, maxRows: 3}" :style="{width: '90%'}" readonly>
                    </el-input>-->
                    <div  style="height: 10vmin;overflow: hidden;width:100%;text-align: left">
                      <div class="scroll-box" v-html="formData.wfsystemprocess.applydescription">
                      </div>
                    </div>
                  </div>
                </div>
                <div class="profile-info-block">
                  <div>用戶效益：</div>
                  <div>{{formData.wfsystemprocess.benefitAnalysis}}</div>
                </div>
                <div class="profile-info-block">
                  <div>效益說明：</div>
                  <div>{{formData.wfsystemprocess.benefitStatement}}</div>
                </div>
              </div>
              <div class="content-medium">
                <div class="content-medium-block">
                  <div><span>系統類別：</span>{{formData.wfsystemprocess.systemCategoryname}}</div>
                  <div><span>系統名稱：</span>{{formData.wfsystemprocess.systemBelowname}}</div>
                  <div> </div>
                </div>
                <div class="content-medium-block">
                  <div><span>專案類型：</span>{{formData.wfsystemprocess.projectTypename}}</div>
                </div>
                <div class="content-medium-block">
                  <div class="text-area-type text-area-style">
                    <span>需求描述：</span>
<!--                    <el-input v-model="formData.wfsystemprocess.applydescription" type="textarea"
                              :autosize="{minRows: 3, maxRows: 3}" :style="{width: '90%'}" readonly>
                    </el-input>-->
                    <div  style="height: 10vmin;overflow: hidden;width:90%;">
                      <div class="scroll-box" v-html="formData.wfsystemprocess.applydescription">
                      </div>
                    </div>
                  </div>
                </div>
                <div class="content-medium-block">
                  <div class="text-area-style">
                    <span>效益分析：</span>
                    <div>
                      <div>用戶效益：{{formData.wfsystemprocess.benefitAnalysis}}</div>
                      <div>效益說明：{{formData.wfsystemprocess.benefitStatement}}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </collapse>
        </div>
        <div class="space" v-if="formData.nodeName==formData.updateNodeName && formData.nodeName != null && formData.nodeName != undefined"></div>
        <div class="form-info" @click="isActive6 = !isActive6" v-if="formData.nodeName==formData.updateNodeName && formData.nodeName != null && formData.nodeName != undefined">
          <a style="text-decoration:none;">
            <div class="rating__text"></div>
            <span class="course-head">資訊評估信息</span>
            <img class="imgUrlArrowhead" :src="imgUrlArrowhead2" v-if="isActive6==true">
            <img class="imgUrlArrowhead2" :src="imgUrlArrowhead2" v-if="isActive6==false">
          </a>
        </div>
        <div class="ant-modal-body" v-if="formData.nodeName==formData.updateNodeName && formData.nodeName != null && formData.nodeName != undefined">
          <collapse>
            <div class="profile-info-table" v-show="isActive6">
              <div class="profile-info-block">
                <div><font style="color: red">*</font>需求性質：</div>
                <div>
                  <el-form-item prop="wfsystemprocess.demandNature" :rules="rules.wfsystemprocess_demandNature">
                    <el-select v-model="formData.wfsystemprocess.demandNature" placeholder="請選擇需求性質" clearable :style="{width: '80%'}"
                               @change="onChangeSelect('demandNature','demandNaturename','demandNatureOptions','')">
                      <el-option v-for="(item, index) in demandNatureOptions" :key="index" :label="item.label"
                                 :value="item.value" :disabled="item.disabled"></el-option>
                    </el-select>
                  </el-form-item>
                </div>
              </div>
              <div class="profile-info-block">
                <div><font style="color: red">*</font>推廣度：</div>
                <div>
                  <el-form-item prop="wfsystemprocess.promotionDegree" :rules="rules.wfsystemprocess_promotionDegree">
                    <el-select v-model="formData.wfsystemprocess.promotionDegree" placeholder="請選擇推廣度" clearable multiple :style="{width: '80%'}"
                               @change="onChangeSelect('promotionDegree','promotionDegreename','promotionDegreeOptions','multiple')">
                      <el-option v-for="(item, index) in promotionDegreeOptions" :key="index" :label="item.label"
                                 :value="item.value" :disabled="item.disabled"></el-option>
                    </el-select>
                    <el-input v-if="Array.isArray(formData.wfsystemprocess.promotionDegree) && formData.wfsystemprocess.promotionDegree.includes('OTHER')"
                              v-model="formData.wfsystemprocess.promotionDegreeother" :rules="rules.wfsystemprocess_promotionDegreeother" placeholder="請輸入其他" clearable :style="{width: '80%'}"></el-input>
                  </el-form-item>
                </div>
              </div>
              <div class="profile-info-block">
                <div>預計作業工期(天)：</div>
                <div>
                  <el-input v-model="formData.wfsystemprocess.activityDuration" placeholder="請輸入預計作業工期" clearable
                            :style="{width: '80%'}"></el-input>
                </div>
              </div>
              <div class="profile-info-block">
                <div>預計專案效益價值(RMB)：</div>
                <div>
                  <el-input v-model="formData.wfsystemprocess.projectBenefit" placeholder="請輸入預計作業工期" clearable
                            :style="{width: '80%'}"></el-input>
                </div>
              </div>
              <div class="profile-info-block">
                <div>預計運維效益價值(MB/年)：</div>
                <div>
                  <el-input v-model="formData.wfsystemprocess.operationBenefit" placeholder="請輸入預計作業工期" clearable
                            :style="{width: '80%'}"></el-input>
                </div>
              </div>
              <div class="profile-info-block">
                <div><font style="color: red">*</font>硬件資源：</div>
                <div>
                  <el-form-item prop="wfsystemprocess.hardwareResources" :rules="rules.wfsystemprocess_hardwareResources">
                    <el-select v-model="formData.wfsystemprocess.hardwareResources" placeholder="請選擇硬件資源" clearable :style="{width: '80%'}">
                      <el-option v-for="(item, index) in hardwareResourcesOptions" :key="index" :label="item.label"
                                 :value="item.value" :disabled="item.disabled"></el-option>
                    </el-select>
                  </el-form-item>
                </div>
              </div>
              <div class="profile-info-block">
                <div><font style="color: red">*</font>方案&費用：</div>
                <div>
                  <el-form-item prop="wfsystemprocess.planCost"  :rules="rules.wfsystemprocess_planCost">
                    <el-input v-model="formData.wfsystemprocess.planCost"  placeholder="請輸入方案&費用" clearable
                              :style="{width: '80%'}"></el-input>
                  </el-form-item>
                </div>
              </div>
              <div class="profile-info-block">
                <div><font style="color: red">*</font>對接系統：</div>
                <div>
                  <el-form-item prop="wfsystemprocess.dockingSystem" :rules="rules.wfsystemprocess_dockingSystem">
                    <el-select v-model="formData.wfsystemprocess.dockingSystem" placeholder="請選擇對接系統" clearable multiple :style="{width: '80%'}"
                               @change="onChangeSelect('dockingSystem','dockingSystemname','dockingSystemOptions','multiple')">
                      <el-option v-for="(item, index) in dockingSystemOptions" :key="index" :label="item.label"
                                 :value="item.value" :disabled="item.disabled"></el-option>
                    </el-select>
                  </el-form-item>
                </div>
              </div>
              <div class="profile-info-block">
                <div><font style="color: red">*</font>評估說明：</div>
                <div>
                  <el-form-item prop="wfsystemprocess.assessDescription"  :rules="rules.wfsystemprocess_assessDescription">
                    <el-input v-model="formData.wfsystemprocess.assessDescription" type="textarea" placeholder="請輸入評估說明"
                              :autosize="{minRows: 3, maxRows: 3}" ></el-input>
                  </el-form-item>
                </div>
              </div>
            </div>
          </collapse>
        </div>
        <div class="space" v-if="formData.nodeOrder>8"></div>
        <div class="form-info" @click="isActive9 = !isActive9" v-if="formData.nodeOrder>8">
          <a style="text-decoration:none;">
            <div class="rating__text"></div>
            <span class="course-head">資訊評估信息</span>
            <img class="imgUrlArrowhead" :src="imgUrlArrowhead2" v-if="isActive9==true">
            <img class="imgUrlArrowhead2" :src="imgUrlArrowhead2" v-if="isActive9==false">
          </a>
        </div>
        <div class="ant-modal-body" v-if="formData.nodeOrder>8">
          <collapse>
            <div class="profile-info-table" v-show="isActive9">
              <div class="content-small">
                <div class="profile-info-block">
                  <div>需求性質：</div>
                  <div>
                    {{formData.wfsystemprocess.demandNaturename}}
                  </div>
                </div>
                <div class="profile-info-block">
                  <div>推廣度：</div>
                  <div>
                    {{formData.wfsystemprocess.promotionDegreename}}
                  </div>
                </div>
                <div class="profile-info-block">
                  <div>預計作業工期(天)：</div>
                  <div>
                    {{formData.wfsystemprocess.activityDuration?formData.wfsystemprocess.activityDuration:'/'}}
                  </div>
                </div>
                <div class="profile-info-block">
                  <div>預計專案效益價值(RMB)：</div>
                  <div>
                    {{formData.wfsystemprocess.projectBenefit?formData.wfsystemprocess.projectBenefit:'/'}}
                  </div>
                </div>
                <div class="profile-info-block">
                  <div>預計運維效益價值(MB/年)：</div>
                  <div>
                    {{formData.wfsystemprocess.operationBenefit?formData.wfsystemprocess.operationBenefit:'/'}}
                  </div>
                </div>
                <div class="profile-info-block">
                  <div>硬件資源：</div>
                  <div>
                    {{formData.wfsystemprocess.hardwareResources === 'Y' ? '有' : formData.wfsystemprocess.hardwareResources === 'N' ? '無' : '/'}}
                  </div>
                </div>
                <div class="profile-info-block">
                  <div>方案&費用：</div>
                  <div>
                    {{formData.wfsystemprocess.planCost}}
                  </div>
                </div>
                <div class="profile-info-block">
                  <div>對接系統：</div>
                  <div>
                    {{formData.wfsystemprocess.dockingSystemname}}
                  </div>
                </div>
                <div class="profile-info-block">
                  <div>立項編號：</div>
                  <div>
                    {{formData.wfsystemprocess.projectnumber}}
                  </div>
                </div>
                <div class="profile-info-block">
                  <div>評估說明：</div>
                  <div>
                    {{formData.wfsystemprocess.assessDescription}}
                  </div>
                </div>
              </div>
              <div class="content-medium">
                <div class="content-medium-block">
                  <div><span>需求性質：</span>{{formData.wfsystemprocess.demandNaturename}}</div>
                  <div><span>立項編號：</span>{{formData.wfsystemprocess.projectnumber}}</div>
                  <div><span>預計作業工期：</span>{{formData.wfsystemprocess.activityDuration?formData.wfsystemprocess.activityDuration:'/'}}</div>
                </div>
                <div class="content-medium-block">
                  <div><span>推廣度：</span>{{formData.wfsystemprocess.promotionDegreename}}</div>
                </div>
                <div class="content-medium-block">
                  <div><span>對接系統：</span>{{formData.wfsystemprocess.dockingSystemname}}</div>
                </div>
                <div class="content-medium-block">
                  <div><span>硬件資源：</span>{{formData.wfsystemprocess.hardwareResources === 'Y' ? '有' : formData.wfsystemprocess.hardwareResources === 'N' ? '無' : '/'}}</div>
                  <div><span>方案&費用：</span>{{formData.wfsystemprocess.planCost}}</div>
                  <div></div>
                </div>
                <div class="content-medium-block">
                  <div><span>評估說明：</span>{{formData.wfsystemprocess.assessDescription}}</div>
                </div>
              </div>
            </div>
          </collapse>
        </div>
        <div class="space" v-if="formData.wfsystemprocess.attachids!=''"></div>
        <div class="form-info" v-if="formData.wfsystemprocess.attachids!=''" @click="isActive7 = !isActive7" >
          <a style="text-decoration:none;">
            <div class="rating__text"></div>
            <span class="course-head">附件</span>
            <img class="imgUrlArrowhead" :src="imgUrlArrowhead2" v-if="isActive7==true">
            <img class="imgUrlArrowhead2" :src="imgUrlArrowhead2" v-if="isActive7==false">
          </a>
        </div>
        <div class="ant-modal-body" v-if="formData.wfsystemprocess.attachids!=''">
          <collapse>
            <div v-show="isActive7">
              <div class="file-style">
                <el-form-item label="" prop="attachids">
                  <el-upload :file-list="upload.fileList" :show-file-list="true" :on-preview="handlePreview"
                             :headers="upload.headers" :action="upload.url" list-type="text" :disabled='true'>
                  </el-upload>
                </el-form-item>
              </div>
            </div>
          </collapse>
        </div>
        <div class="space"></div>
        <div class="form-info" @click="isActive5 = !isActive5">
          <a style="text-decoration:none;">
            <div class="rating__text"></div>
            <span class="course-head">簽核路徑</span>
            <img class="imgUrlArrowhead" :src="imgUrlArrowhead2" v-if="isActive5==true">
            <img class="imgUrlArrowhead2" :src="imgUrlArrowhead2" v-if="isActive5==false">
          </a>
        </div>
        <div class="ant-modal-body">
          <collapse>
            <div v-show="isActive5">
              <div style="padding:16px;font-size: 14px;">
                <div v-html="formData.chargeNodeInfo"></div>
              </div>
            </div>
          </collapse>
        </div>
        <div class="space"></div>
        <div class="form-info" @click="isActive8 = !isActive8">
          <a style="text-decoration:none;">
            <span class="course-head">簽核記錄</span>
            <div class="rating__text"></div>
            <img class="imgUrlArrowhead" :src="imgUrlArrowhead2" v-if="isActive8==true">
            <img class="imgUrlArrowhead2" :src="imgUrlArrowhead2" v-if="isActive8==false">
          </a>
        </div>
        <div class="ant-modal-body">
          <collapse>
            <div v-show="isActive8">
              <div style="padding:0px;">
              <div class="table-responsive">
                <table class="table">
                  <thead>
                    <tr>
                      <th>序號</th>
                      <th>簽核時間</th>
                      <th>簽核節點</th>
                      <th>簽核主管</th>
                      <th>簽核意見</th>
                      <th>批註</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="(item, index) in formData.tQhChargelogs" :key="index">
                      <td>{{index+1}}</td>
                      <td>{{parseTime(item.createtime,'{y}-{m}-{d} {h}:{i}:{s}')}}</td>
                      <td>{{item.chargenode}}</td>
                      <td>{{item.chargename}}</td>
                      <td>{{item.ispass}}</td>
                      <td>{{item.decrib}}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
              </div>
            </div>
          </collapse>
        </div>
        <div class="ant-modal-body" style="text-align: center;margin-top: 10px;">
          <el-form-item label="">
            <el-input v-model="formData.comment" type="textarea" rows="3" :style="{width: '90%'}"
                      :placeholder="$t('table.activity.inputApprovalOpinions')"></el-input>
          </el-form-item>
          <div class="dialog-footer" align="center" style="padding:10px;display: flex;justify-content:space-around;">
            <el-button @click="handleTask(1)" :disabled="isDisable" style="flex:1;background-color: #EC6464;color: white;border-radius: 10px;"  v-if="(this.$store.state.user.empNo)==currentAuditUser  && formData.nodeName != null && formData.nodeName != undefined">
              {{$t('table.activity.rejection')}}
            </el-button>
            <el-button @click="skipTask" :disabled="isDisable" style="flex:1;background-color:#4084FF;color: white;border-radius: 10px;margin: 0 10px" v-if="this.$store.state.user.empNo==currentAuditUser  && formData.nodeName != null && formData.nodeName != undefined">跳過</el-button>
            <el-button @click="handleTask(0)" :disabled="isDisable" style="flex:1;background-color:#01D4CB;color: white;border-radius: 10px"  v-if="this.$store.state.user.empNo==currentAuditUser  && formData.nodeName != null && formData.nodeName != undefined">
              {{$t('table.activity.pass')}}
            </el-button>
          </div>
        </div>
      </el-form>
    </div>
</template>
<script>
import {getAccessToken} from '@/utils/auth';
import {completeTask, downloadFile, getByKey, showDetail} from "@/api/caaesign/wfsystemprocess";
import {auditComplete,getNodeInfo, skipTask} from "@/api/caaesign/common";
import collapse from "@/utils/collapse.js";
import imgUrlArrowhead from '@/assets/images/t02.png';
import imgUrlArrowhead2 from '@/assets/images/arrow.svg';
import '@/assets/styles/design-build/design-add-view.scss'
import {getHeader} from "@/utils/entfrm";
export default {
  components: {
    collapse
  },
  props: [],
  data() {
    return {
      imgUrlArrowhead:imgUrlArrowhead,
      imgUrlArrowhead2:imgUrlArrowhead2,
      formData: {
        chargeNodeInfo:undefined,
        fileName:undefined,
        nodeName:undefined,
        nodeOrder:undefined,
        processId:undefined,
        viewPermissions:undefined,
        updateNodeName:undefined,
        tQhChargelogs:[{
          chargename:undefined,
          createtime:undefined,
          ispass:undefined,
          decrib:undefined,
          chargenode:undefined,
        }],
        wfsystemprocess:{
          serialno:undefined,
          applyno:undefined,
          applyname:undefined,
          applytel:undefined,
          applyfactoryname:undefined,
          applydeptname:undefined,
          applydate:undefined,
          dealname:undefined,
          dealpost:undefined,
          systemCategoryname:undefined,
          systemBelowname:undefined,
          projectTypename:undefined,
          applydescription:undefined,
          benefitAnalysis:undefined,
          benefitStatement:undefined,
          demandNature:undefined,
          demandNaturename:undefined,
          promotionDegree:undefined,
          promotionDegreename:undefined,
          promotionDegreeother:undefined,
          activityDuration:undefined,
          projectBenefit:undefined,
          operationBenefit:undefined,
          hardwareResources:undefined,
          planCost:undefined,
          assessDescription:undefined,
          dockingSystem:undefined,
          dockingSystemname:undefined,
          projectnumber:undefined,
        },
      },
      rules:{
        wfsystemprocess_demandNature: [{required: true, message: '請選擇需求性質', trigger: 'change'}],
        wfsystemprocess_promotionDegree: [{required: true, message: '請選擇推廣度', trigger: 'change'}],
        wfsystemprocess_dockingSystem: [{required: true, message: '請選擇對接系統', trigger: 'change'}],
        wfsystemprocess_promotionDegreeother: [{required: true, message: '請輸入', trigger: 'blur'}],
        wfsystemprocess_hardwareResources: [{required: true, message: '請選擇硬件資源', trigger: 'change'}],
        wfsystemprocess_planCost:[{required: true, message: "請輸入方案&費用", trigger: "blur" }],
        wfsystemprocess_assessDescription:[{required: true, message: "請輸入評估說明", trigger: "blur" }],
      },
      tableHeight: document.documentElement.scrollHeight - 245 + "px",
      // 审批意见数据
      commentList: [],
      //簽核路徑
      signPath: [],
      activeCount: -1,
      signPathActive: -1,
      //任务图url
      imgUrl: '',
      isDisable: false,
      isDisable2: true,
      currentAuditUser:undefined,
      // 是否显示任务图
      showImgDialog: false,
      // 文件上传参数
      upload: {
        // 是否显示弹出层
        open: false,
        fileList: [],
        fileNameList: [],
        // 弹出层标题
        title: "",
        multiple: false,
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: getHeader(),
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/fileInfo/upload"
      },
      demandNatureOptions: [],
      promotionDegreeOptions:[],
      hardwareResourcesOptions:[],
      dockingSystemOptions:[],
      isMobile: true,
      labelPosition: 'left',
      isActive: true,
      isActive2: true,
      isActive3: true,
      isActive4: true,
      isActive5: false,
      isActive6: true,
      isActive7: true,
      isActive8: true,
      isActive9: true,
    }
  },
  computed: {},
  watch: {},
  created() {
    const serialno = this.$route.query.serialno;
    this.changeTagsView(this.$route.query);
    this.isMobile = this.isMobileFun()
    if (serialno != null && serialno != undefined) {
      if (getAccessToken()) {
        showDetail(serialno).then(response => {
          this.formData = JSON.parse(response.data);
          this.formData.wfsystemprocess.applydescription =this.decodeHtmlEntities(this.formData.wfsystemprocess.applydescription);
          //this.formData.wfsystemprocess.applydescription = this.formData.wfsystemprocess.applydescription.replace(/\r\n/g,"<br>");
          if(this.formData.wfsystemprocess.assessDescription!= null && this.formData.wfsystemprocess.assessDescription != undefined){
            this.formData.wfsystemprocess.assessDescription=this.decodeHtmlEntities(this.formData.wfsystemprocess.assessDescription);
          }
          if (this.formData.wfsystemprocess.attachids) {
            let a = this.formData.wfsystemprocess.attachids.split(',');
            if (a.length > 0) {
              // 使用 Promise.all 等待所有异步操作完成
              Promise.all(
                a.map(item => {
                  if (item) {
                    return getByKey(item).then(response => {
                      return {
                        name: response.data.name,
                        url: response.data.url,
                        id: response.data.id,
                        size: response.data.sizez,
                        type: response.data.type
                      };
                    });
                  }
                  return null;
                })
              ).then(results => {
                // 过滤掉 null 值（即无效的 item）
                results = results.filter(result => result !== null);
                // 将结果赋值给 upload.fileList
                this.upload.fileList = results;
              });
            }
          }
        });
        getNodeInfo(serialno).then(response => {
          this.currentAuditUser = JSON.parse(response.data).auditUser;
        });

      }else{
        this.$router.push( '/caaesign/wfonlineprocessApp/404App')
      }
    }
    this.getDemandNatureOptions()
    this.getPromotionDegreeOptions()
    this.getHardwareResourcesOptions()
    this.getDockingSystemOptions()
    ESignApp.onPageFinished()
  },
  methods: {
    closeForm() {
      //关闭子页面
      ESignApp.back()
    },
    returnNext(){
      auditComplete(this.formData.wfsystemprocess.serialno)
    },
    returnForm(){
      ESignApp.back()
    },
    validate_promotionDegree(){
      if(this.formData.nodeName==this.formData.updateNodeName && this.formData.wfsystemprocess.promotionDegree.includes('OTHER')&&(this.formData.wfsystemprocess.promotionDegreeother==''||this.formData.wfsystemprocess.promotionDegreeother==undefined)){
        alert("請輸入其他！")
        this.isDisable2 = false;
      }else{
        this.isDisable2 = true;
      }
    },
    handleTrack() {
      this.imgUrl = process.env.VUE_APP_BASE_API + '/activiti/task/track/' + this.formData.processId,
        this.showImgDialog = true
    },
    skipTask:function(){
      skipTask(this.formData.wfsystemprocess.serialno).then(response => {
        this.$message({
          message: this.$t('tips.operationSuccessful'),
          type: 'success',
          iconClass: ' ',
          center:true,
          offset: window.screen.height / 2,
          customClass: 'msgbox'
        });
        this.returnNext();
      });
    },
    handleTask: function(pass) {
      if(pass == 0){
        this.$refs["elForm"].validate(valid => {
          this.validate_promotionDegree();
          if (valid && this.isDisable2) {
            this.isDisable = true;
            this.formData.pass = pass
            this.formData.serialno=this.formData.wfsystemprocess.serialno;
            if(this.formData.nodeName==this.formData.updateNodeName){
              this.formData.demandNature=this.formData.wfsystemprocess.demandNature
              this.formData.demandNaturename=this.formData.wfsystemprocess.demandNaturename
              this.formData.promotionDegree=this.formData.wfsystemprocess.promotionDegree.join(',')
              this.formData.promotionDegreename=this.formData.wfsystemprocess.promotionDegreename
              this.formData.promotionDegreeother=this.formData.wfsystemprocess.promotionDegreeother
              this.formData.activityDuration=this.formData.wfsystemprocess.activityDuration
              this.formData.projectBenefit=this.formData.wfsystemprocess.projectBenefit
              this.formData.operationBenefit=this.formData.wfsystemprocess.operationBenefit
              this.formData.hardwareResources=this.formData.wfsystemprocess.hardwareResources
              this.formData.planCost=this.formData.wfsystemprocess.planCost
              this.formData.dockingSystem=this.formData.wfsystemprocess.dockingSystem.join(',')
              this.formData.dockingSystemname=this.formData.wfsystemprocess.dockingSystemname
              this.formData.assessDescription=this.formData.wfsystemprocess.assessDescription
            }
            completeTask(this.formData).then(response => {
              if (response.code === 0) {
                this.$message({
                  message: this.$t('tips.operationSuccessful'),
                  type: 'success',
                  iconClass: ' ',
                  center:true,
                  offset: window.screen.height / 2,
                  customClass: 'msgbox'
                });
                this.returnNext();
              } else {
                this.msgError(response.msg);
              }
            });
          }
        });
      }else if(pass == 1 && this.formData.comment != null){
        this.isDisable = true;
        this.formData.pass = pass
        this.formData.serialno=this.formData.wfsystemprocess.serialno;
        completeTask(this.formData).then(response => {
          if (response.code === 0) {
            /*this.msgSuccess(this.$t('tips.operationSuccessful'));*/
            this.$message({
              message: this.$t('tips.operationSuccessful'),
              type: 'success',
              iconClass: ' ',
              center:true,
              offset: window.screen.height / 2,
              customClass: 'msgbox'
            });
            this.returnNext();
          } else {
            this.msgError(response.msg);
          }
        });
      }else {
        this.isDisable = false;
        this.msgInfo(this.$t('table.activity.inputApprovalOpinions'));
      }
    },
    attachidsBeforeUpload(file) {
      let isRightSize = file.size / 1024 / 1024 < 2
      if (!isRightSize) {
        this.$message.error('文件大小超过 2MB')
      }
      return isRightSize
    },
    // 文件上传成功处理
    uploadsucces(response, file, fileList) {
      if (response.data.name != undefined && response.data.name != "undefined") {
        this.upload.fileList.push({
          name: file.name,
          url: response.data.name
        });
        this.formData.attachids += response.data.name + ",";
      }
    },
    handleChange(file, fileList) {},
    handleExceed(file, fileList) {},
    handleRemove(file, fileList) {
      this.$emit("delUploadImage", file.name);
      const index = this.upload.fileList.indexOf(file);
      this.upload.fileList.splice(index, 1);
      if (this.formData.attachids) {
        this.formData.attachids = this.formData.attachids.replace(file.url + ",", "");
      }
      this.upload.fileNameList.push(file.url);
      // delFileInfo(file.url);
    },
    handlePreview(file) {
      /*downloadFile(file.id)*/
      if(file.type=='pdf'||file.type=='PDF'){
        ESignApp.previewPDF(process.env.VUE_APP_BASE_API +  '/caaesign/fileManager/downloadFile/' + file.id+"?name="+encodeURIComponent(file.name)+"&size="+file.size+"&extension="+file.type+"&encrypt=N");
      }else{
        ESignApp.previewOffice(process.env.VUE_APP_BASE_API +  '/caaesign/fileManager/downloadFile/' + file.id+"?name="+encodeURIComponent(file.name)+"&size="+file.size+"&extension="+file.type+"&encrypt=N");
      }
    },
    checkBoxParse() {},
    cascaderParse() {},
    getDemandNatureOptions(){
      this.getDicts("SYS_DEMAND_NATURE").then(response => {
        this.demandNatureOptions = response.data;
      });
    },
    getPromotionDegreeOptions(){
      this.getDicts("SYS_PROMOTION_DEGREE").then(response => {
        this.promotionDegreeOptions = response.data;
      });
    },
    getHardwareResourcesOptions(){
      this.getDicts("SYS_HARDWARE_RESOURCES").then(response => {
        this.hardwareResourcesOptions = response.data;
      });
    },
    getDockingSystemOptions(){
      this.getDicts("dict_dockingSystem").then(response => {
        this.dockingSystemOptions = response.data;
      });
    },
    decodeHtmlEntities(str) {
      const parser = new DOMParser();
      const doc = parser.parseFromString(str, 'text/html');
      return doc.body.textContent || doc.body.innerText || '';
    },
    onChangeSelect(type,typeName,optionsName,multiple){
      var value=this.formData.wfsystemprocess[type];
      var label='';
      if(multiple!=''){
        label=this.selectTypeFormats(value,optionsName)
      }else{
        label=this.selectTypeFormat(value,optionsName)
      }
      this.formData.wfsystemprocess[typeName]=label;
/*      this.formData.wfsystemprocess[type]=value*/
    },
    selectTypeFormat(value,optionsName){
      return this.selectDictLabel(this[optionsName], value)
    },
    selectTypeFormats(values,optionsName){
      return this.selectDictLabelss(this[optionsName], values)
    },
  }
}

</script>
<style scoped lang="scss">
.el-form-item {
  margin-top: 0px;
  margin-bottom: 0px;
}
.space{
  height: 15px;
  background-color: #F7F7F7;
}
.course-head {
  font-size: 15px;
  color: #1E2233;
  font-weight: bold;
}
.rating__text{
  font-weight:normal;
  width:4px;
  height:17px;
  background: #4B8AF8;
  border-radius: 4px;
  float:left;
  margin-top: 11px;
  margin-right: 10px;
}
.form-info{
  background-color:#EAEEFF;
  padding-left:15px;
  line-height: 38px;
}
.imgUrlArrowhead{
  float:right;
  margin-top: 13px;
  width: 16px;
  height: 16px;
  margin-right: 14px;
  transform: scaleX(-1) rotate(-90deg);
}
.imgUrlArrowhead2{
  float:right;
  margin-top: 13px;
  width: 16px;
  height: 16px;
  margin-right: 14px;
  transform: scaleX(-1) rotate(90deg);
}
.ant-modal-body {
  padding: 0px;
  font-size: 15px;
  word-wrap: break-word
}
.profile-info-table {
  font-size: 14px;
  color: #06142D;
  border-radius: 10px;
  background-color: #FFFFFF;
  font-family: Microsoft YaHei;

  .profile-info-block {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 11px 15px;
    border-bottom: 1px solid #F7F7F7;
    >div:nth-child(1){
      flex-basis: 35%;
      color: #57585A;
    }
    >div:nth-child(2){
      flex-basis: 65%;
      text-align: right;
    }
  }
  .profile-info-block__value a svg {
    width: 15px;
    height: 15px;
  }
  .content-medium-block{
    display: flex;
    align-items: center;
    padding: 11px 15px;
    border-bottom: 1px solid #F7F7F7;
    div{
      flex: 1;
      span{
        color: #57585A;
      }
    }
  }
}

/*=====  End of profile  ======*/
.el-button--success{
  color: #fff;
  background-color: #1E80F9;
  border-color: #1E80F9;

}
.el-button--medium{
  border-radius: inherit;
  font-family: Microsoft YaHei;
  font-size: 14px;
  padding:15px 0px;
}

.el-button+.el-button {
  margin-left: 0;
  margin-top: 0px;
}

.el-button--success.is-disabled, .el-button--success.is-disabled:hover, .el-button--success.is-disabled:focus, .el-button--success.is-disabled:active {
  color: #fff;
  background-color: #B4D6FF;
  border-color: #B4D6FF;
}
::v-deep .el-form-item__error {
  color: #ff4949;
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
  position:absolute;
  top: 100%;
  right: 0;
}
/*-------------*/
.table-responsive {
  min-height: .01%;
  overflow-x: auto;
  .table {
    width: 100%;
    font-size: 14px;
    td,th {
      text-align: center;
      padding: 8px;
      border: 1px solid #ddd;
      white-space: nowrap
    }
    tbody>tr:nth-of-type(odd) {
      background-color: #f9f9f9
    }
  }
}

.file-style{
  padding:0px 16px 10px 16px;
}
/deep/.file-style .el-upload{
  display: none;
}
.text-area-style{
  display: flex;
  align-items: center;
}
.text-area-type /deep/.el-textarea__inner{
  border: 0px;
  padding: 0px;
  color: black;
  overflow-y: scroll;
}
.scroll-box{
  height: 100%;
  overflow-y:scroll;
  -webkit-overflow-scrolling: touch;
  overflow-x: hidden;
  overflow-wrap: anywhere;
  white-space: pre-wrap;
}
/* 针对 iOS 设备 */
@supports (-webkit-overflow-scrolling: touch) {
  .scroll-box {
    height: 100%;
    overflow-y:scroll;
    -webkit-overflow-scrolling: touch;
    overflow-x: hidden;
    overflow-wrap: anywhere;
    white-space: pre-wrap;
  }
}
.content-small,.content-medium{
  display: none;
}
/* 小屏幕設備 */
@media (max-width: 767px){
  .content-small{
    display: block;
  }

}
/* 中等屏幕設備 */
@media (min-width: 768px) {
  .content-medium{
    display: block;
  }
}
</style>
<style>
.msgbox {
  min-width: 120px !important;
  background-color: #D7D7D7;
}
.el-message--success {
  background-color: #D7D7D7;
  border: 0px;
}

.el-message .el-icon-success {
  font-size: 14px;
  border-color: #D7D7D7;
}

.el-message--success .el-message__content {
  font-size: 14px;
  color:black !important;
}
</style>
