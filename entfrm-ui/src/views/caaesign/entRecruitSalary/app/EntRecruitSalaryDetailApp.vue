<template>
  <page-top-bar :title="formConfigData.title">
    <div class="mobile-content" slot="content">
      <div class="mobile-body">
        <el-form ref="elForm" :model="formData" size="medium" label-width="100px"
          :label-position="labelPosition">
          <div class="mobile-card" id="staffCard">
            <div class="head-pannel">
              <el-col :span="8" :xs="24" class="el-col-no-border">
                任務編碼：{{formData.serialno?formData.serialno:'提交表單后自動生成'}}
              </el-col>
              <el-col :span="8" :xs="24" class="el-col-no-border"> 填單時間：{{formData.createTime}}
              </el-col>
              <el-col :span="8" :xs="24" class="el-col-no-border">
                填單人：{{formData.makerNo +' / '+ formData.makerName}}
              </el-col>
            </div>
            <el-row :gutter="15">
              <div class="body-main">
                <el-col :span="24" :xs="24">
                  <el-form-item label-width="70px" label="流水碼" prop="recruitSerialNo">
                    {{formData.recruitSerialNo}}
                  </el-form-item>
                </el-col>
                <el-col :span="12" :xs="24">
                  <el-form-item label-width="1px" label="" prop="recruitFiletype">
                    {{formData.recruitFiletype}}
                  </el-form-item>
                </el-col>
                <el-col :span="12" :xs="24">
                  <el-form-item label-width="1px" label="" prop="recruitUrgent">
                    {{formData.recruitUrgent}}
                  </el-form-item>
                </el-col>
                <el-col :span="24" :xs="24" undefined>
                  <el-collapse v-model="entRecruitSalaryItemsMainActiveNames">
                    <el-collapse-item title="" name="1">
                      <div>
                        <el-collapse v-model="entRecruitSalaryItemsActiveNames">
                          <el-collapse-item v-for="(item, index) in formData.entRecruitSalaryItemsLists"
                            :key="index" :title="'編號：'+(index+1)" :name="index">
                            <el-form-item label="工號">
                              {{item.recruitEmpNo}}
                            </el-form-item>
                            <el-form-item label="姓名">
                              {{item.recruitEmpNam}}
                            </el-form-item>
                            <el-form-item label="廠區">
                              {{item.recruitEmpPlant}}
                            </el-form-item>
                            <el-form-item label="機能總處級/製造總處級">
                              {{item.recruitEmpZongchu}}
                            </el-form-item>
                            <el-form-item label="機能處級/製造處級">
                              {{item.recruitEmpChu}}
                            </el-form-item>
                            <el-form-item label="次機能處級/次製造處級">
                              {{item.recruitEmpJineng}}
                            </el-form-item>
                            <el-form-item label="廠級/ 中心級">
                              {{item.recruitEmpChang}}
                            </el-form-item>
                            <el-form-item label="部級">
                              {{item.recruitEmpBuji}}
                            </el-form-item>
                            <el-form-item label="課級">
                              {{item.recruitEmpKeji}}
                            </el-form-item>
                            <el-form-item label="入集團日期">
                              {{item.recruitGroupDate}}
                            </el-form-item>
                            <el-form-item label="學歷">
                              {{item.recruitEmpEducation}}
                            </el-form-item>
                            <el-form-item label="試用資位">
                              {{item.recruitTrialPosition}}
                            </el-form-item>
                            <el-form-item label="試用職務">
                              {{item.recruitTrialJob}}
                            </el-form-item>
                            <el-form-item label="試用標準薪資a">
                              {{item.recruitTrialSalary}}
                            </el-form-item>
                            <el-form-item label="試用專案補項">
                              {{item.recruitTrialBuxiang}}
                            </el-form-item>
                            <el-form-item label="資位">
                              {{item.recruitStaffPosition}}
                            </el-form-item>
                            <el-form-item label="職務">
                              {{item.recruitStaffJob}}
                            </el-form-item>
                            <el-form-item label="試用專案補項起止日期">
                              {{recruitTrialTimeperiodEntRecruitSalaryItemsFormat(item.recruitTrialTimeperiod)}}
                            </el-form-item>
                            <el-form-item label="調整 額度 b">
                              {{item.recruitStaffQuota}}
                            </el-form-item>
                            <el-form-item label="標準薪資=a+b">
                              {{item.recruitStaffSalary}}
                            </el-form-item>
                            <el-form-item label="考核等級">
                              {{item.recruitStaffAppraisal}}
                            </el-form-item>
                            <el-form-item label="級距範圍">
                              {{item.recruitStaffRange}}
                            </el-form-item>
                            <el-form-item label="專案補項">
                              {{item.recruitStaffBuxiang}}
                            </el-form-item>
                            <el-form-item label="專案補項起止日期">
                              {{recruitStaffTimeperiodEntRecruitSalaryItemsFormat(item.recruitStaffTimeperiod)}}
                            </el-form-item>
                            <el-form-item label="生效日期">
                              {{item.recruitEffectiveDate}}
                            </el-form-item>
                          </el-collapse-item>
                        </el-collapse>
                      </div>
                    </el-collapse-item>
                  </el-collapse>
                </el-col>
              </div>
              <el-col :span="24" :xs="24" class="print-hide-div btn-div">
                <div class="dialog-footer " align="center" style="padding-top: 5px;padding-bottom: 5px">
                  <el-button type="danger" @click="closeForm">{{$t('common.close')}}</el-button>
                  <el-button type="info" @click="handleTrack"
                    v-if="formData.workStatus == 2||formData.workStatus ==3||formData.workStatus ==4">
                    {{$t('table.activity.flowTracing')}}</el-button>
                </div>
              </el-col>
              <!-- 簽核線 -->
              <div class="body-main">
                <el-col :span="24" class="print-hide-div">
                  <el-collapse v-model="signPathActive">
                    <el-collapse-item title="簽核線" :name="1">
                      <el-steps direction="vertical" :active="activeCount" process-status="success"
                        style="margin: 10px 5px 0 10px;">
                        <el-step :title="item" v-for="(item, index) in signPath" :key="index"
                          style="height:30px;"></el-step>
                      </el-steps>
                    </el-collapse-item>
                  </el-collapse>
                </el-col>
                <!-- 审核记录 -->
                <el-table :data="commentList">
                  <el-table-column type="expand">
                    <template slot-scope="props">
                      <el-form label-position="left" inline class="demo-table-expand">
                        <el-form-item :label="$t('table.activity.taskId')">
                          <span>{{ props.row.id }}</span>
                        </el-form-item>
                        <el-form-item :label="$t('table.activity.approvedBy')">
                          <span>{{ props.row.userId }}</span>
                        </el-form-item>
                        <el-form-item :label="$t('table.activity.approvalOpinions')">
                          <span>{{ props.row.fullMessage }}</span>
                        </el-form-item>
                        <el-form-item :label="$t('table.activity.operIp')">
                          <span>{{ props.row.operIp }}</span>
                        </el-form-item>
                        <el-form-item :label="$t('table.activity.auditStatus')">
                          <span>{{ statusFormat(props.row) }}</span>
                        </el-form-item>
                        <el-form-item :label="$t('table.activity.approvalTime')">
                          <span>{{ parseTime(props.row.time) }}</span>
                        </el-form-item>
                      </el-form>
                    </template>
                  </el-table-column>
                  <el-table-column :label="$t('table.activity.approvedBy')" align="center" min-width="35"
                    prop="userId">
                  </el-table-column>
                  <el-table-column :label="$t('table.activity.auditStatus')" align="center" prop="status"
                    min-width="30" :formatter="statusFormat">
                  </el-table-column>
                  <el-table-column :label="$t('table.activity.approvalTime')" align="center" prop="time"
                    min-width="45" :formatter="parseTime(commentList.time)">
                  </el-table-column>
                </el-table>
              </div>
              <!-- 任务跟踪对话框 -->
              <el-dialog :title="$t('table.activity.taskMap')" :visible.sync="showImgDialog" width="100vw"
                :modal-append-to-body="false">
                <img :src="imgUrl" style="padding-bottom: 60px;width:100%">
              </el-dialog>
            </el-row>
          </div>
        </el-form>
      </div>
    </div>
  </page-top-bar>
</template>
<script>
import {
  getEntRecruitSalary,
  addEntRecruitSalary,
  editEntRecruitSalary,
  getSignPathApp,
  updateAndCheck,
  getSignConfigList
}
from "@/api/caaesign/entRecruitSalary"
import '@/assets/styles/design-build/design-add-view.scss'
import {
  listTask,
  getTask,
  checkTask,
  taskComment
}
from "@/api/activiti/task"
import {
  getAccessToken,
  getZltAccessToken
}
from "@/utils/auth";
import {
  previewFile,
  getHeader
}
from "@/utils/entfrm";
import {
  getOptionsAndValues
}
from "@/api/system/dictData";
import {
  listFileInfo,
  getFileInfo,
  delFileInfo,
  addFileInfo,
  editFileInfo,
  getByKey
}
from "@/api/system/fileInfo";
export default {
  components: {},
  props: [],
  data() {
    return {
      formData: {
        recruitSerialNo: undefined,
        recruitFiletype: undefined,
        recruitUrgent: undefined,
        cbchargeno: undefined,
        shenhechargeno: undefined,
        hezhunchargeno: undefined,
        entRecruitSalaryItemsLists: [],
      },
      formConfigData: {
        title: "iPEG試用期滿薪資異動匯總審批表",
        cbchargeno: "承辦",
        cbchargeno_required: true,
        shenhechargeno: "審核",
        shenhechargeno_required: true,
        hezhunchargeno: "核准",
        hezhunchargeno_required: true,
      },
      tableHeight: document.documentElement.scrollHeight - 245 + "px",
      // 审批意见数据
      commentList: [],
      //簽核路徑
      signPath: [],
      activeCount: -1,
      signPathActive: -1,
      //任务图url
      imgUrl: '',
      isDisabled: false,
      // 是否显示任务图
      showImgDialog: false,
      auditStatus: [],
      recruitFiletypeOptions: [],
      recruitFiletypeValue: null,
      recruitUrgentOptions: [],
      recruitUrgentValue: null,
      entRecruitSalaryItemsActiveNames: ["1"],
      entRecruitSalaryItemsMainActiveNames: [""],
      isMobile: true,
      labelPosition: 'left',
    }
  },
  computed: {},
  watch: {},
  created() {
    const id = this.$route.query.id;
    this.changeTagsView(this.$route.query);
    this.isMobile = true
    getSignConfigList().then(response => {
      if (response.code === 0) {
        response.data.forEach(element => {
          const colKey = element.colKey.split('_')
          if (colKey.length > 1) {
            if (this.rules[colKey[0]] && colKey[1] === 'required') {
              this.rules[colKey[0]][0]['required'] = element.colValue === 'true'
              this.formConfigData[element.colKey] = element.colValue === 'true'
            }
            else {
              this.formConfigData[element.colKey] = element.colValue
            }
          }
          else {
            this.formConfigData[element.colKey] = element.colValue
          }
        });
      }
    })
    if (id != null && id != undefined) {
      getEntRecruitSalary(id).then(response => {
        this.formData = response.data;
        taskComment(this.formData.processId).then(response => {
          this.commentList = response.data;
        });
        getSignPathApp(this.formData.processId).then(response => {
          this.signPath = response.data.info;
          this.activeCount = response.data.activeCount;
        });
        if (this.formData.attachids) {
          let a = this.formData.attachids.split(',');
          if (a.length > 0) {
            a.forEach(item => {
              if (item) {
                getByKey(item).then(response => {
                  this.upload.fileList.push({
                    name: response.data.orignalName,
                    url: response.data.name
                  });
                })
              }
            })
          }
        }
        this.getDicts("recruit_filetype").then(response => {
          response.data.forEach(item => {
            if (this.formData.recruitFiletype && this.formData.recruitFiletype != "null") {
              this.formData.recruitFiletype = this.formData.recruitFiletype.replace(item.value,
                item.label);
            }
          })
        });
        this.getDicts("recruit_urgent").then(response => {
          response.data.forEach(item => {
            if (this.formData.recruitUrgent && this.formData.recruitUrgent != "null") {
              this.formData.recruitUrgent = this.formData.recruitUrgent.replace(item.value, item
                .label);
            }
          })
        });
      });
    }
    this.getDicts("sign_status").then(response => {
      this.auditStatus = response.data;
    });
  },
  methods: {
    closeForm() {
      //关闭子页面
      this.$router.go(-1) // 返回
    },
    handleTrack() {
      this.imgUrl = this.getAppBaseApi() + '/activiti/task/track/' + this.formData.processId,
        this.showImgDialog = true
    },
    statusFormat(row, column) {
      return this.selectDictLabel(this.auditStatus, row.status);
    },
    checkBoxParse() {},
    cascaderParse() {},
    handleAdd_ent_recruit_salary_items() {
      const cloumn = {
        recruitEmpNo: '',
        recruitEmpNam: '',
        recruitEmpPlant: '',
        recruitEmpZongchu: '',
        recruitEmpChu: '',
        recruitEmpJineng: '',
        recruitEmpChang: '',
        recruitEmpBuji: '',
        recruitEmpKeji: '',
        recruitGroupDate: '',
        recruitEmpEducation: '',
        recruitTrialPosition: '',
        recruitTrialJob: '',
        recruitTrialSalary: '',
        recruitTrialBuxiang: '',
        recruitStaffPosition: '',
        recruitStaffJob: '',
        recruitTrialTimeperiod: [],
        recruitStaffQuota: '',
        recruitStaffSalary: '',
        recruitStaffAppraisal: '',
        recruitStaffRange: '',
        recruitStaffBuxiang: '',
        recruitStaffTimeperiod: [],
        recruitEffectiveDate: ''
      };
      this.formData.entRecruitSalaryItemsLists.splice(this.formData.entRecruitSalaryItemsLists.length, 0,
        cloumn);
      for (let index in this.formData.entRecruitSalaryItemsLists) {
        this.formData.entRecruitSalaryItemsLists[index].sort = parseInt(index) + 1;
      }
    },
    handleDel_ent_recruit_salary_items(index, row) {
      let functionName = this.$t('ent_recruit_salary_075d399c2dcaa20f94d53895254e58a1.default.functionName');
      this.$confirm(this.$t('tips.deleteConfirm', ['iPEG試用期滿薪資異動匯總審批表從表', row.sort]), this.$t('tips.warm'), {
        confirmButtonText: this.$t("common.confirmTrim"),
        cancelButtonText: this.$t("common.cancelTrim"),
        type: "warning",
      }).then(() => {
        this.formData.entRecruitSalaryItemsLists.splice(index, 1);
        this.$message({
          showClose: true,
          message: this.$t("tips.deleteSuccess"),
          type: "success",
          offset: 50
        });
      }).catch(function(err) {
        console.log(err);
      });
    },
    EntRecruitSalaryItems_recruitEmpNo_onchange(item) {
      this.getInfoUserByEmpno(item.recruitEmpNo).then(responseInfo => {
        if (responseInfo.code !== 0) {
          this.msgError(responseInfo.msg);
        }
        else {
          if (responseInfo.data != null) {
            item.recruitEmpNam = responseInfo.data.empname
            item.recruitEmpPlant = responseInfo.data.factoryid
          }
          else {
            item.recruitEmpNam = ''
            item.recruitEmpPlant = ''
          }
        }
      });
    },
    recruitTrialTimeperiodEntRecruitSalaryItemsFormat(value) {
      let returnInfo = ""
      if (value && value != "null") {
        if (typeof value == "string") {
          returnInfo = JSON.parse(value).join("  至  ")
        }
        else {
          returnInfo = value.join("  至  ")
        }
      }
      return returnInfo
    },
    recruitStaffTimeperiodEntRecruitSalaryItemsFormat(value) {
      let returnInfo = ""
      if (value && value != "null") {
        if (typeof value == "string") {
          returnInfo = JSON.parse(value).join("  至  ")
        }
        else {
          returnInfo = value.join("  至  ")
        }
      }
      return returnInfo
    },
  }
}

</script>
<style rel="stylesheet/scss" lang="scss" scoped>
@import "~@/assets/styles/mobileSkin/mobileMixin.scss";

.del-handler {
  font-size: 20px;
  color: #ff4949;
}

.el-dialog {
  position: relative;
  margin: 0 auto 0px;
  background: #FFFFFF;
  border-radius: 2px;
  -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 50%;
  height: 60%;
}

.el-dialog__body {
  border-top: 1px solid #dcdfe6;
  border-bottom: 1px solid #dcdfe6;
  max-height: 85% !important;
  min-height: 70%;
  overflow-y: auto;
}

.el-slider>/deep/.el-slider__runway {
  margin: 15px 0;
}

.el-checkbox-group {
  font-size: inherit;
}

.el-color-picker {
  display: inherit;
}

/deep/.demo-table-expand {
  font-size: 0;
}

/deep/.demo-table-expand label {
  width: 90px;
  color: #99a9bf;
}

/deep/.demo-table-expand/deep/.el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  width: 100%;
}

/deep/.el-input-number--medium {
  width: 100%;
}

.talbe-name-style {
  font-size: 16px;
  font-weight: bold;
  line-height: 50px;
}

/deep/.el-collapse-item__content {
  padding-bottom: 0;
}

/deep/.el-step__head.is-finish,
/deep/.el-step__title.is-finish {
  color: #C0C4CC;
  border-color: #C0C4CC;
}

/deep/.el-step__head.is-wait,
/deep/.el-step__title.is-wait {
  color: #000000;
  border-color: #000000;
}

/deep/.el-step__head.is-success,
/deep/.el-step__title.is-success {
  color: blue;
  border-color: blue;
}

.mobile-content {
  background: #EEEEEE;
}

.mobile-card {
  border: 0;
  background: transparent;
}

#staffCard {
  border: 0;
}

.mobile-body {
  padding: 0;
}

/deep/input,
/deep/textarea {
  border-color: transparent;
}

.head-pannel {
  background: #ffffff;
  padding: 15px;
  margin-top: 5px;
  color: #6B6B6B;
}

.head-pannel:after {
  clear: both;
  content: "";
  display: block;
}

.head-pannel>div {
  font-size: 14px;
  padding: 5px;
}

/deep/.el-row {
  margin-left: 0;
  margin-right: 0;
}

.body-main .el-form-item {
  margin-top: 15px;
  margin-bottom: 15px;
  border-color: transparent;
}

.el-table__expanded-cell .el-form-item {
  margin: 0;
}

.el-form-item>label {
  padding-left: 10px;
}

.body-main .el-col {
  border-bottom: 1px solid #EAEAEA;
}

.body-main {
  background: #ffffff;
  padding: 15px 20px;
  margin-top: 8px;
}

.body-main:after {
  clear: both;
  content: "";
  display: block;
}

.child-item {
  margin-top: 8px;
  background: #ffffff;
}

.child-item:after {
  clear: both;
  content: "";
  display: block;
}

.child-item /deep/.el-form-item {
  margin: 15px 10px;
  padding-bottom: 15px;
  border-color: transparent;
  border-bottom: 1px solid #EAEAEA;
}

.talbe-name-style {
  padding-left: 15px;
}

.sign-content {
  background: #ffffff;
  margin-top: 8px;
  color: #000000;
}

.sign-content:after {
  clear: both;
  content: "";
  display: block;
}

.sign-point>.el-form-item {
  padding: 5px 10px;
  margin-bottom: 0;
}

.btn-div {
  background: white;
  padding: 10px;
  margin: 10px 0;
  float: none;
}

.el-message-box__btns button:nth-child(2) {
  margin-left: 0;
}

.el-slider {
  /deep/.el-slider__bar {
    @include backgroundColor('themeMainColor');
  }
  
  /deep/.el-slider__button-wrapper {
    .el-slider__button {
      @include borderColor('themeMainColor');
    }
  }
}

.el-radio.is-checked {
  /deep/.el-radio__input.is-checked {
    .el-radio__inner {
      @include borderColor('themeMainColor');
      @include backgroundInfo('themeMainColor');
    }
  }
  
  /deep/.el-radio__input.is-checked+.el-radio__label {
    @include fontColor('themeMainColor');
  }
}

.el-checkbox.is-checked {
  /deep/.el-checkbox__input.is-checked {
    .el-checkbox__inner {
      @include borderColor('themeMainColor');
      @include backgroundInfo('themeMainColor');
    }
  }
  
  /deep/.el-checkbox__input.is-checked+.el-checkbox__label {
    @include fontColor('themeMainColor');
  }
}

.el-switch.is-checked {
  /deep/.el-switch__core {
    @include borderColor('themeMainColor');
    @include backgroundInfo('themeMainColor');
  }
}

.cube-popup {
  /deep/.cube-picker-confirm {
    @include fontColor('themeMainColor');
  }
}

.child-item {
  /deep/.el-button--primary {
    @include borderColor('themeMainColor');
    @include backgroundInfo('themeMainColor');
  }
  
  /deep/.del-handler {
    @include fontColor('themeMainColor');
  }
}

.dialog-footer {
  .el-button--success {
    @include backgroundInfo('themeMainColor');
    @include borderColor('themeMainColor');
  }
  
  .el-button--info {
    @include backgroundInfo('themeMainColor');
    @include borderColor('themeMainColor');
  }
}

</style>
