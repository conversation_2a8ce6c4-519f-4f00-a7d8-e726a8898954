<template>
  <page-top-bar :title="formConfigData.title">
    <div class="mobile-content" slot="content">
      <div class="mobile-body">
        <el-form ref="elForm" :hide-required-asterisk=true :show-message=false :model="formData"
          :rules="rules" size="medium" label-width="100px" :label-position="labelPosition">
          <div class="mobile-card" id="staffCard">
            <div>
              <div class="head-pannel">
                <el-col :span="8" :xs="24" class="el-col-no-border">
                  任務編碼：{{formData.serialno?formData.serialno:'提交表單后自動生成'}}
                </el-col>
                <el-col :span="8" :xs="24" class="el-col-no-border"> 填單時間：{{formData.createTime}}
                </el-col>
                <el-col :span="8" :xs="24" class="el-col-no-border">
                  填單人：{{formData.makerNo +' / '+ formData.makerName}}
                </el-col>
              </div>
              <el-row>
                <div class="body-main">
                  <el-col :span="24" :xs="24">
                    <el-form-item label-width="70px" label="流水碼" prop="recruitSerialNo">
                      <el-input v-model="formData.recruitSerialNo" placeholder="XX單位[行政]-20XX-XXX" clearable
                        :style="{width: '100%'}"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" :xs="24">
                    <el-form-item label-width="1px" label="" prop="recruitFiletype">
                      <el-radio-group v-model="formData.recruitFiletype" size="medium">
                        <el-radio v-for="(item, index) in recruitFiletypeOptions" :key="index"
                          :label="item.value" :disabled="item.disabled">{{item.label}}</el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" :xs="24">
                    <el-form-item label-width="1px" label="" prop="recruitUrgent">
                      <el-radio-group v-model="formData.recruitUrgent" size="medium">
                        <el-radio v-for="(item, index) in recruitUrgentOptions" :key="index"
                          :label="item.value" :disabled="item.disabled">{{item.label}}</el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                </div>
                <div class="child-item">
                  <el-col :span="24" :xs="24" class="talbe-name-style">
                  </el-col>
                  <el-scrollbar style="width:100%;" class="child-table-entRecruitSalaryItems">
                    <el-col :span="24" :xs="24" style="padding: 0px;width:300%">
                      <el-table border stripe ref="entRecruitSalaryItemsDragTableMobile"
                        :data="formData.entRecruitSalaryItemsLists" row-key="id">
                        <el-table-column label="iPEG試用期滿薪資異動匯總審批表從表" min-width="90%"
                          :width="entRecruitSalaryItemsDragTableMobileClientWidth">
                          <template slot-scope="scope">
                            <el-form-item
                              :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitEmpNo'"
                              :rules="rules.entRecruitSalaryItems_recruitEmpNo" label="工號">
                              <el-input v-model.trim="scope.row.recruitEmpNo"
                                :placeholder="$t('common.placeholderDefault') + '工號'"
                                @change='EntRecruitSalaryItems_recruitEmpNo_onchange(scope.row)'
                                class="full-row"></el-input>
                            </el-form-item>
                            <el-form-item
                              :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitEmpNam'"
                              :rules="rules.entRecruitSalaryItems_recruitEmpNam" label="姓名">
                              <el-input v-model.trim="scope.row.recruitEmpNam"
                                :placeholder="$t('common.placeholderDefault') + '姓名'" class="full-row">
                              </el-input>
                            </el-form-item>
                            <el-form-item
                              :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitEmpPlant'"
                              :rules="rules.entRecruitSalaryItems_recruitEmpPlant" label="廠區">
                              <el-input v-model.trim="scope.row.recruitEmpPlant"
                                :placeholder="$t('common.placeholderDefault') + '廠區'" class="full-row">
                              </el-input>
                            </el-form-item>
                            <el-form-item
                              :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitEmpZongchu'"
                              :rules="rules.entRecruitSalaryItems_recruitEmpZongchu" label="機能總處級/製造總處級">
                              <el-input v-model.trim="scope.row.recruitEmpZongchu"
                                :placeholder="$t('common.placeholderDefault') + '機能總處級/製造總處級'"
                                class="full-row"></el-input>
                            </el-form-item>
                            <el-form-item
                              :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitEmpChu'"
                              :rules="rules.entRecruitSalaryItems_recruitEmpChu" label="機能處級/製造處級">
                              <el-input v-model.trim="scope.row.recruitEmpChu"
                                :placeholder="$t('common.placeholderDefault') + '機能處級/製造處級'" class="full-row">
                              </el-input>
                            </el-form-item>
                            <el-form-item
                              :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitEmpJineng'"
                              :rules="rules.entRecruitSalaryItems_recruitEmpJineng" label="次機能處級/次製造處級">
                              <el-input v-model.trim="scope.row.recruitEmpJineng"
                                :placeholder="$t('common.placeholderDefault') + '次機能處級/次製造處級'"
                                class="full-row"></el-input>
                            </el-form-item>
                            <el-form-item
                              :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitEmpChang'"
                              :rules="rules.entRecruitSalaryItems_recruitEmpChang" label="廠級/ 中心級">
                              <el-input v-model.trim="scope.row.recruitEmpChang"
                                :placeholder="$t('common.placeholderDefault') + '廠級/ 中心級'" class="full-row">
                              </el-input>
                            </el-form-item>
                            <el-form-item
                              :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitEmpBuji'"
                              :rules="rules.entRecruitSalaryItems_recruitEmpBuji" label="部級">
                              <el-input v-model.trim="scope.row.recruitEmpBuji"
                                :placeholder="$t('common.placeholderDefault') + '部級'" class="full-row">
                              </el-input>
                            </el-form-item>
                            <el-form-item
                              :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitEmpKeji'"
                              :rules="rules.entRecruitSalaryItems_recruitEmpKeji" label="課級">
                              <el-input v-model.trim="scope.row.recruitEmpKeji"
                                :placeholder="$t('common.placeholderDefault') + '課級'" class="full-row">
                              </el-input>
                            </el-form-item>
                            <el-form-item
                              :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitGroupDate'"
                              :rules="rules.entRecruitSalaryItems_recruitGroupDate" label="入集團日期">
                              <el-input :placeholder="$t('common.placeholderDefault') + '入集團日期'"
                                class="full-row" prefix-icon="el-icon-date" readonly
                                v-model.trim="scope.row.recruitGroupDate"
                                @focus="recruitGroupDateEntRecruitSalaryItemsShowDate(scope.$index, scope.row)">
                              </el-input>
                            </el-form-item>
                            <el-form-item
                              :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitEmpEducation'"
                              :rules="rules.entRecruitSalaryItems_recruitEmpEducation" label="學歷">
                              <el-input v-model.trim="scope.row.recruitEmpEducation"
                                :placeholder="$t('common.placeholderDefault') + '學歷'" class="full-row">
                              </el-input>
                            </el-form-item>
                            <el-form-item
                              :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitTrialPosition'"
                              :rules="rules.entRecruitSalaryItems_recruitTrialPosition" label="試用資位">
                              <el-input v-model.trim="scope.row.recruitTrialPosition"
                                :placeholder="$t('common.placeholderDefault') + '試用資位'" class="full-row">
                              </el-input>
                            </el-form-item>
                            <el-form-item
                              :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitTrialJob'"
                              :rules="rules.entRecruitSalaryItems_recruitTrialJob" label="試用職務">
                              <el-input v-model.trim="scope.row.recruitTrialJob"
                                :placeholder="$t('common.placeholderDefault') + '試用職務'" class="full-row">
                              </el-input>
                            </el-form-item>
                            <el-form-item
                              :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitTrialSalary'"
                              :rules="rules.entRecruitSalaryItems_recruitTrialSalary" label="試用標準薪資a">
                              <el-input v-model.trim="scope.row.recruitTrialSalary"
                                :placeholder="$t('common.placeholderDefault') + '試用標準薪資a'" class="full-row">
                              </el-input>
                            </el-form-item>
                            <el-form-item
                              :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitTrialBuxiang'"
                              :rules="rules.entRecruitSalaryItems_recruitTrialBuxiang" label="試用專案補項">
                              <el-input v-model.trim="scope.row.recruitTrialBuxiang"
                                :placeholder="$t('common.placeholderDefault') + '試用專案補項'" class="full-row">
                              </el-input>
                            </el-form-item>
                            <el-form-item
                              :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitStaffPosition'"
                              :rules="rules.entRecruitSalaryItems_recruitStaffPosition" label="資位">
                              <el-input v-model.trim="scope.row.recruitStaffPosition"
                                :placeholder="$t('common.placeholderDefault') + '資位'" class="full-row">
                              </el-input>
                            </el-form-item>
                            <el-form-item
                              :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitStaffJob'"
                              :rules="rules.entRecruitSalaryItems_recruitStaffJob" label="職務">
                              <el-input v-model.trim="scope.row.recruitStaffJob"
                                :placeholder="$t('common.placeholderDefault') + '職務'" class="full-row">
                              </el-input>
                            </el-form-item>
                            <el-form-item
                              :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitTrialTimeperiod'"
                              :rules="rules.entRecruitSalaryItems_recruitTrialTimeperiod" label="試用專案補項起止日期">
                              <el-input :placeholder="$t('common.placeholderDefault') + '試用專案補項起止日期'"
                                class="full-row" prefix-icon="el-icon-date" readonly
                                v-model.trim="scope.row.recruitTrialTimeperiodEntRecruitSalaryItemsValue"
                                @focus="recruitTrialTimeperiodEntRecruitSalaryItemsShowDate(scope.$index, scope.row)">
                              </el-input>
                            </el-form-item>
                            <el-form-item
                              :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitStaffQuota'"
                              :rules="rules.entRecruitSalaryItems_recruitStaffQuota" label="調整 額度 b">
                              <el-input v-model.trim="scope.row.recruitStaffQuota"
                                :placeholder="$t('common.placeholderDefault') + '調整 額度 b'" class="full-row">
                              </el-input>
                            </el-form-item>
                            <el-form-item
                              :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitStaffSalary'"
                              :rules="rules.entRecruitSalaryItems_recruitStaffSalary" label="標準薪資=a+b">
                              <el-input v-model.trim="scope.row.recruitStaffSalary"
                                :placeholder="$t('common.placeholderDefault') + '標準薪資=a+b'" class="full-row">
                              </el-input>
                            </el-form-item>
                            <el-form-item
                              :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitStaffAppraisal'"
                              :rules="rules.entRecruitSalaryItems_recruitStaffAppraisal" label="考核等級">
                              <el-input v-model.trim="scope.row.recruitStaffAppraisal"
                                :placeholder="$t('common.placeholderDefault') + '考核等級'" class="full-row">
                              </el-input>
                            </el-form-item>
                            <el-form-item
                              :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitStaffRange'"
                              :rules="rules.entRecruitSalaryItems_recruitStaffRange" label="級距範圍">
                              <el-input v-model.trim="scope.row.recruitStaffRange"
                                :placeholder="$t('common.placeholderDefault') + '級距範圍'" class="full-row">
                              </el-input>
                            </el-form-item>
                            <el-form-item
                              :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitStaffBuxiang'"
                              :rules="rules.entRecruitSalaryItems_recruitStaffBuxiang" label="專案補項">
                              <el-input v-model.trim="scope.row.recruitStaffBuxiang"
                                :placeholder="$t('common.placeholderDefault') + '專案補項'" class="full-row">
                              </el-input>
                            </el-form-item>
                            <el-form-item
                              :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitStaffTimeperiod'"
                              :rules="rules.entRecruitSalaryItems_recruitStaffTimeperiod" label="專案補項起止日期">
                              <el-input :placeholder="$t('common.placeholderDefault') + '專案補項起止日期'"
                                class="full-row" prefix-icon="el-icon-date" readonly
                                v-model.trim="scope.row.recruitStaffTimeperiodEntRecruitSalaryItemsValue"
                                @focus="recruitStaffTimeperiodEntRecruitSalaryItemsShowDate(scope.$index, scope.row)">
                              </el-input>
                            </el-form-item>
                            <el-form-item
                              :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitEffectiveDate'"
                              :rules="rules.entRecruitSalaryItems_recruitEffectiveDate" label="生效日期">
                              <el-input :placeholder="$t('common.placeholderDefault') + '生效日期'"
                                class="full-row" prefix-icon="el-icon-date" readonly
                                v-model.trim="scope.row.recruitEffectiveDate"
                                @focus="recruitEffectiveDateEntRecruitSalaryItemsShowDate(scope.$index, scope.row)">
                              </el-input>
                            </el-form-item>
                            <el-button type="text"
                              @click="handleDel_ent_recruit_salary_items(scope.$index, scope.row)"
                              class="del-handler" icon="el-icon-delete"></el-button>
                          </template>
                        </el-table-column>
                      </el-table>
                    </el-col>
                  </el-scrollbar>
                  <el-col :span="24" :xs="24" style="padding:10px 5px">
                    <el-button type="primary" icon="el-icon-plus" size="mini"
                      @click="handleAdd_ent_recruit_salary_items">
                      {{$t('ent_recruit_salary_075d399c2dcaa20f94d53895254e58a1.default.add')}}</el-button>
                  </el-col>
                </div>
                <div class="sign-content">
                  <el-col :span="8" :xs="24" class="sign-point">
                    <el-form-item prop="cbchargeno" label-width="0">
                      <entfrm-sign-form-add-hq :label="formConfigData.cbchargeno"
                        :required='formConfigData.cbchargeno_required'
                        :selectEmpProp="{empNo:this.formData.cbchargeno,empName:this.formData.cbchargename}"
                        model-no="cbchargeno" model-name="cbchargename"
                        @onSignFormSelected="onSignFormSelected" :isMobile=this.isMobile>
                      </entfrm-sign-form-add-hq>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" :xs="24" class="sign-point">
                    <el-form-item prop="shenhechargeno" label-width="0">
                      <entfrm-sign-form-add-hq :label="formConfigData.shenhechargeno"
                        :required='formConfigData.shenhechargeno_required'
                        :selectEmpProp="{empNo:this.formData.shenhechargeno,empName:this.formData.shenhechargename}"
                        model-no="shenhechargeno" model-name="shenhechargename"
                        @onSignFormSelected="onSignFormSelected" :isMobile=this.isMobile>
                      </entfrm-sign-form-add-hq>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" :xs="24" class="sign-point">
                    <el-form-item prop="hezhunchargeno" label-width="0">
                      <entfrm-sign-form-add-hq :label="formConfigData.hezhunchargeno"
                        :required='formConfigData.hezhunchargeno_required'
                        :selectEmpProp="{empNo:this.formData.hezhunchargeno,empName:this.formData.hezhunchargename}"
                        model-no="hezhunchargeno" model-name="hezhunchargename"
                        @onSignFormSelected="onSignFormSelected" :isMobile=this.isMobile>
                      </entfrm-sign-form-add-hq>
                    </el-form-item>
                  </el-col>
                </div>
                <el-col :span="24" :xs="24" class="btn-div">
                  <div class="dialog-footer" align="center" style="padding-top: 5px;padding-bottom: 5px">
                    <!--          <el-form-item size="large">-->
                    <el-button type="success" @click="handleSubmit">{{$t('table.submit')}}</el-button>
                    <!-- <el-button type="primary" @click="submitForm">{{$t('common.save')}}</el-button>-->
                    <el-button @click="closeForm">{{$t('common.cancel')}}</el-button>
                    <!--        </el-form-item>-->
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>
        </el-form>
      </div>
    </div>
  </page-top-bar>
</template>
<script>
import {
  getEntRecruitSalary,
  addEntRecruitSalary,
  editEntRecruitSalary,
  addEntRecruitSalaryAndStartProcess,
  editEntRecruitSalaryAndStartProcess,
  getSignConfigList
}
from "@/api/caaesign/entRecruitSalary"
import '@/assets/styles/design-build/design-add-view.scss'
import {
  getAccessToken,
  getZltAccessToken
}
from "@/utils/auth";
import {
  previewFile,
  getHeader
}
from "@/utils/entfrm";
import {
  getOptionsAndValues
}
from "@/api/system/dictData";
import {
  listFileInfo,
  getFileInfo,
  delFileInfo,
  addFileInfo,
  editFileInfo,
  getByKey
}
from "@/api/system/fileInfo";
export default {
  components: {},
  props: [],
  data() {
    return {
      formData: {
        recruitSerialNo: undefined,
        recruitFiletype: undefined,
        recruitUrgent: undefined,
        cbchargeno: undefined,
        shenhechargeno: undefined,
        hezhunchargeno: undefined,
        entRecruitSalaryItemsLists: [],
        entRecruitSalaryItemsActiveNames: [],
        makerNo: this.$store.state.user.empNo,
        makerName: this.$store.state.user.name,
        dataSource: "app",
      },
      formConfigData: {
        title: "iPEG試用期滿薪資異動匯總審批表",
        cbchargeno: "承辦",
        cbchargeno_required: true,
        shenhechargeno: "審核",
        shenhechargeno_required: true,
        hezhunchargeno: "核准",
        hezhunchargeno_required: true,
      },
      tableHeight: document.documentElement.scrollHeight - 245 + "px",
      rules: {
        recruitSerialNo: [{
          required: true,
          message: 'XX單位[行政]-20XX-XXX',
          trigger: 'blur'
        }],
        recruitFiletype: [{
          required: true,
          message: '不能為空',
          trigger: 'change'
        }],
        recruitUrgent: [{
          required: true,
          message: '不能為空',
          trigger: 'change'
        }],
        entRecruitSalaryItems_recruitEmpNo: [{
          required: true,
          message: '工號不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitEmpNam: [{
          required: true,
          message: '姓名不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitEmpPlant: [{
          required: true,
          message: '廠區不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitEmpZongchu: [{
          required: true,
          message: '機能總處級/製造總處級不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitEmpChu: [{
          required: true,
          message: '機能處級/製造處級不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitEmpJineng: [{
          required: true,
          message: '次機能處級/次製造處級不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitEmpChang: [{
          required: true,
          message: '廠級/ 中心級不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitEmpBuji: [{
          required: true,
          message: '部級不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitEmpKeji: [{
          required: true,
          message: '課級不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitGroupDate: [{
          required: true,
          message: '入集團日期不能為空',
          trigger: 'change'
        }],
        entRecruitSalaryItems_recruitEmpEducation: [{
          required: true,
          message: '學歷不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitTrialPosition: [{
          required: true,
          message: '試用資位不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitTrialJob: [{
          required: true,
          message: '試用職務不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitTrialSalary: [{
          required: true,
          message: '試用標準薪資a不能為空',
          trigger: 'blur'
        }, {
          pattern: /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/,
          message: '請輸入數字！',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitTrialBuxiang: [{
          required: true,
          message: '試用專案補項不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitStaffPosition: [{
          required: true,
          message: '資位不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitStaffJob: [{
          required: true,
          message: '職務不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitStaffQuota: [{
          required: true,
          message: '調整 額度 b不能為空',
          trigger: 'blur'
        }, {
          pattern: /^(([0-9]+)|([0-9]+\.[0-9]{1,2}))$/,
          message: '最多保留倆位小數，請檢查輸入信息！',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitStaffSalary: [{
          required: true,
          message: '標準薪資=a+b不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitStaffAppraisal: [{
          required: true,
          message: '考核等級不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitStaffRange: [{
          required: true,
          message: '級距範圍不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitStaffBuxiang: [{
          required: true,
          message: '專案補項不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitEffectiveDate: [{
          required: true,
          message: '生效日期不能為空',
          trigger: 'change'
        }],
        cbchargeno: [{
          required: true,
          message: '承辦不能為空',
          trigger: 'change'
        }],
        shenhechargeno: [{
          required: true,
          message: '審核不能為空',
          trigger: 'change'
        }],
        hezhunchargeno: [{
          required: true,
          message: '核准不能為空',
          trigger: 'change'
        }],
      },
      recruitFiletypeOptions: [],
      recruitFiletypeValue: null,
      recruitUrgentOptions: [],
      recruitUrgentValue: null,
      entRecruitSalaryItemsDragTableMobileClientWidth: 0,
      isMobile: true,
      labelPosition: 'right',
      alias: "postgresql$_$pghrsign_ipebg_test$_$075d399c2dcaa20f94d53895254e58a1%_%design"
    }
  },
  computed: {},
  watch: {},
  created() {
    const id = this.$route.query.id;
    this.changeTagsView(this.$route.query);
    this.isMobile = true
    if (this.isMobile) {
      this.labelPosition = 'left'
    }
    getSignConfigList().then(response => {
      if (response.code === 0) {
        response.data.forEach(element => {
          const colKey = element.colKey.split('_')
          if (colKey.length > 1) {
            if (this.rules[colKey[0]] && colKey[1] === 'required') {
              this.rules[colKey[0]][0]['required'] = element.colValue === 'true'
              this.formConfigData[element.colKey] = element.colValue === 'true'
            }
            else {
              this.formConfigData[element.colKey] = element.colValue
            }
          }
          else {
            this.formConfigData[element.colKey] = element.colValue
          }
        });
      }
    })
    if (id != null && id != undefined) {
      getEntRecruitSalary(id).then(response => {
        this.formData = response.data;
        this.checkBoxParse()
        this.cascaderParse()
        this.timeRangParse()
        if (this.formData.attachids) {
          let a = this.formData.attachids.split(',');
          if (a.length > 0) {
            a.forEach(item => {
              if (item) {
                getByKey(item).then(response => {
                  this.upload.fileList.push({
                    name: response.data.orignalName,
                    url: response.data.name
                  });
                })
              }
            })
          }
        }
        this.getRecruitFiletypeOptions()
        this.getRecruitUrgentOptions()
      });
    }
    if (id == null || id == undefined) {
      this.getRecruitFiletypeOptions()
      this.getRecruitUrgentOptions()
    }
  },
  mounted() {
    this.recruitGroupDateEntRecruitSalaryItemsSegmentPicker = this.$createSegmentPicker({
      data: [{
        is: 'cube-date-picker',
        title: '入集團日期',
        value: new Date(),
        swipeTime: 1000,
        startColumn: 'year',
      }],
      onSelect: (selectedDates, selectedVals, selectedTexts) => {
        let value = ""
        selectedTexts.forEach(item => {
          value = item.join("-")
        })
        this.formData.entRecruitSalaryItemsLists[this.entRecruitSalaryItems].recruitGroupDate = value
      }
    });
    this.recruitTrialTimeperiodEntRecruitSalaryItemsSegmentPicker = this.$createSegmentPicker({
      data: [{
        is: 'cube-date-picker',
        title: '開始時間',
        value: new Date(),
        swipeTime: 1000,
        startColumn: 'year',
        format: {
          year: 'YYYY',
          month: 'MM',
          date: 'DD'
        },
      }, {
        is: 'cube-date-picker',
        title: '結束時間',
        value: new Date(),
        swipeTime: 1000,
        startColumn: 'year',
        format: {
          year: 'YYYY',
          month: 'MM',
          date: 'DD'
        },
      }],
      onSelect: (selectedDates, selectedVals, selectedTexts) => {
        let value = new Array();
        selectedTexts.forEach(item => {
          value.push(item.join("-"))
        })
        this.formData.entRecruitSalaryItemsLists[this.entRecruitSalaryItems].recruitTrialTimeperiod =
          value;
        this.$set(this.formData.entRecruitSalaryItemsLists[this.entRecruitSalaryItems],
          "recruitTrialTimeperiodEntRecruitSalaryItemsValue", value.join("  至  "))
      },
    });
    this.recruitStaffTimeperiodEntRecruitSalaryItemsSegmentPicker = this.$createSegmentPicker({
      data: [{
        is: 'cube-date-picker',
        title: '開始時間',
        value: new Date(),
        swipeTime: 1000,
        startColumn: 'year',
        format: {
          year: 'YYYY',
          month: 'MM',
          date: 'DD'
        },
      }, {
        is: 'cube-date-picker',
        title: '結束時間',
        value: new Date(),
        swipeTime: 1000,
        startColumn: 'year',
        format: {
          year: 'YYYY',
          month: 'MM',
          date: 'DD'
        },
      }],
      onSelect: (selectedDates, selectedVals, selectedTexts) => {
        let value = new Array();
        selectedTexts.forEach(item => {
          value.push(item.join("-"))
        })
        this.formData.entRecruitSalaryItemsLists[this.entRecruitSalaryItems].recruitStaffTimeperiod =
          value;
        this.$set(this.formData.entRecruitSalaryItemsLists[this.entRecruitSalaryItems],
          "recruitStaffTimeperiodEntRecruitSalaryItemsValue", value.join("  至  "))
      },
    });
    this.recruitEffectiveDateEntRecruitSalaryItemsSegmentPicker = this.$createSegmentPicker({
      data: [{
        is: 'cube-date-picker',
        title: '生效日期',
        value: new Date(),
        swipeTime: 1000,
        startColumn: 'year',
      }],
      onSelect: (selectedDates, selectedVals, selectedTexts) => {
        let value = ""
        selectedTexts.forEach(item => {
          value = item.join("-")
        })
        this.formData.entRecruitSalaryItemsLists[this.entRecruitSalaryItems].recruitEffectiveDate =
          value
      }
    });
  },
  methods: {
    submitForm() {
      this.$refs['elForm'].validate(valid => {
        if (valid) {
          const formData = JSON.parse(JSON.stringify(this.formData))
          for (let index in formData.entRecruitSalaryItemsLists) {
            if (typeof formData.entRecruitSalaryItemsLists[index].recruitTrialTimeperiod == "object") {
              formData.entRecruitSalaryItemsLists[index].recruitTrialTimeperiod = JSON.stringify(formData
                .entRecruitSalaryItemsLists[index].recruitTrialTimeperiod)
            }
            if (typeof formData.entRecruitSalaryItemsLists[index].recruitStaffTimeperiod == "object") {
              formData.entRecruitSalaryItemsLists[index].recruitStaffTimeperiod = JSON.stringify(formData
                .entRecruitSalaryItemsLists[index].recruitStaffTimeperiod)
            }
          }
          if (formData.id != undefined) {
            editEntRecruitSalary(formData).then(response => {
              if (response.code === 0) {
                this.$message({
                  showClose: true,
                  message: this.$t('tips.updateSuccess'),
                  type: "success",
                  offset: 50
                });
                this.closeForm();
              }
              else {
                this.msgError(response.msg);
              }
            });
          }
          else {
            addEntRecruitSalary(formData).then(response => {
              if (response.code === 0) {
                this.$message({
                  showClose: true,
                  message: this.$t('tips.createSuccess'),
                  type: "success",
                  offset: 50
                });
                this.closeForm();
              }
              else {
                this.msgError(response.msg);
              }
            });
          }
          /**
           * 提交的時候刪除被標記的需要刪除的附件
           */
          if (this.formData.attachids) {
            this.upload.fileNameList.forEach(item => {
              if (item) {
                delFileInfo(item);
              }
            })
          }
        }
      })
    },
    handleSubmit: function() {
      this.$refs["elForm"].validate(valid => {
        if (valid) {
          const formData = JSON.parse(JSON.stringify(this.formData))
          for (let index in formData.entRecruitSalaryItemsLists) {
            if (typeof formData.entRecruitSalaryItemsLists[index].recruitTrialTimeperiod == "object") {
              formData.entRecruitSalaryItemsLists[index].recruitTrialTimeperiod = JSON.stringify(
                formData.entRecruitSalaryItemsLists[index].recruitTrialTimeperiod)
            }
            if (typeof formData.entRecruitSalaryItemsLists[index].recruitStaffTimeperiod == "object") {
              formData.entRecruitSalaryItemsLists[index].recruitStaffTimeperiod = JSON.stringify(
                formData.entRecruitSalaryItemsLists[index].recruitStaffTimeperiod)
            }
          }
          if (formData.id != undefined) {
            editEntRecruitSalaryAndStartProcess(formData).then(response => {
              if (response.code === 0) {
                this.$message({
                  showClose: true,
                  message: this.$t('tips.updateSuccess'),
                  type: "success",
                  offset: 50
                });
                this.closeForm();
              }
              else {
                this.msgError(response.msg);
              }
            });
          }
          else {
            addEntRecruitSalaryAndStartProcess(formData).then(response => {
              if (response.code === 0) {
                this.$message({
                  showClose: true,
                  message: this.$t('tips.createSuccess'),
                  type: "success",
                  offset: 50
                });
                this.closeForm();
              }
              else {
                this.msgError(response.msg);
              }
            });
          }
          /**
           * 提交的時候刪除被標記的需要刪除的附件
           */
          if (this.formData.attachids) {
            this.upload.fileNameList.forEach(item => {
              if (item) {
                delFileInfo(item);
              }
            })
          }
        }
      })
    },
    resetForm() {
      this.$refs['elForm'].resetFields()
    },
    closeForm() {
      //关闭子页面
      this.$router.go(-1) // 返回
    },
    getRecruitFiletypeOptions() {
      this.getDicts("recruit_filetype").then(response => {
        this.recruitFiletypeOptions = response.data;
      });
    },
    getRecruitUrgentOptions() {
      this.getDicts("recruit_urgent").then(response => {
        this.recruitUrgentOptions = response.data;
      });
    },
    checkBoxParse() {},
    cascaderParse() {},
    timeRangParse() {
      for (let index in this.formData.entRecruitSalaryItemsLists) {
        if (this.formData.entRecruitSalaryItemsLists[index].recruitTrialTimeperiod) {
          this.formData.entRecruitSalaryItemsLists[index].recruitTrialTimeperiod = JSON.parse(this.formData
            .entRecruitSalaryItemsLists[index].recruitTrialTimeperiod)
        }
        if (this.formData.entRecruitSalaryItemsLists[index].recruitStaffTimeperiod) {
          this.formData.entRecruitSalaryItemsLists[index].recruitStaffTimeperiod = JSON.parse(this.formData
            .entRecruitSalaryItemsLists[index].recruitStaffTimeperiod)
        }
      }
    },
    onSignFormSelected(selectEmp, modelNo, modelName) {
      this.$set(this.formData, modelNo, selectEmp.empNo)
      this.$set(this.formData, modelName, selectEmp.empName)
    },
    handleAdd_ent_recruit_salary_items() {
      const cloumn = {
        recruitEmpNo: '',
        recruitEmpNam: '',
        recruitEmpPlant: '',
        recruitEmpZongchu: '',
        recruitEmpChu: '',
        recruitEmpJineng: '',
        recruitEmpChang: '',
        recruitEmpBuji: '',
        recruitEmpKeji: '',
        recruitGroupDate: '',
        recruitEmpEducation: '',
        recruitTrialPosition: '',
        recruitTrialJob: '',
        recruitTrialSalary: '',
        recruitTrialBuxiang: '',
        recruitStaffPosition: '',
        recruitStaffJob: '',
        recruitTrialTimeperiod: [],
        recruitStaffQuota: '',
        recruitStaffSalary: '',
        recruitStaffAppraisal: '',
        recruitStaffRange: '',
        recruitStaffBuxiang: '',
        recruitStaffTimeperiod: [],
        recruitEffectiveDate: ''
      };
      this.formData.entRecruitSalaryItemsLists.splice(this.formData.entRecruitSalaryItemsLists.length, 0,
        cloumn);
      for (let index in this.formData.entRecruitSalaryItemsLists) {
        this.formData.entRecruitSalaryItemsLists[index].sort = parseInt(index) + 1;
      }
    },
    handleDel_ent_recruit_salary_items(index, row) {
      let functionName = this.$t('ent_recruit_salary_075d399c2dcaa20f94d53895254e58a1.default.functionName');
      this.$confirm(this.$t('tips.deleteConfirm', ['iPEG試用期滿薪資異動匯總審批表從表', row.sort]), this.$t('tips.warm'), {
        confirmButtonText: this.$t("common.confirmTrim"),
        cancelButtonText: this.$t("common.cancelTrim"),
        type: "warning",
      }).then(() => {
        this.formData.entRecruitSalaryItemsLists.splice(index, 1);
        this.$message({
          showClose: true,
          message: this.$t("tips.deleteSuccess"),
          type: "success",
          offset: 50
        });
      }).catch(function(err) {
        console.log(err);
      });
    },
    EntRecruitSalaryItems_recruitEmpNo_onchange(item) {
      this.getInfoUserByEmpno(item.recruitEmpNo).then(responseInfo => {
        if (responseInfo.code !== 0) {
          this.msgError(responseInfo.msg);
        }
        else {
          if (responseInfo.data != null) {
            item.recruitEmpNam = responseInfo.data.empname
            item.recruitEmpPlant = responseInfo.data.factoryid
          }
          else {
            item.recruitEmpNam = ''
            item.recruitEmpPlant = ''
          }
        }
      });
    },
    recruitGroupDateEntRecruitSalaryItemsShowDate(index, row) {
      this.entRecruitSalaryItems = index
      this.recruitGroupDateEntRecruitSalaryItemsSegmentPicker.show()
    },
    recruitTrialTimeperiodEntRecruitSalaryItemsShowDate(index, row) {
      this.entRecruitSalaryItems = index
      this.recruitTrialTimeperiodEntRecruitSalaryItemsSegmentPicker.show()
    },
    recruitStaffTimeperiodEntRecruitSalaryItemsShowDate(index, row) {
      this.entRecruitSalaryItems = index
      this.recruitStaffTimeperiodEntRecruitSalaryItemsSegmentPicker.show()
    },
    recruitEffectiveDateEntRecruitSalaryItemsShowDate(index, row) {
      this.entRecruitSalaryItems = index
      this.recruitEffectiveDateEntRecruitSalaryItemsSegmentPicker.show()
    },
  }
}

</script>
<style rel="stylesheet/scss" lang="scss" scoped>
@import "~@/assets/styles/mobileSkin/mobileMixin.scss";

/deep/.demo-table-expand {
  font-size: 0;
}

/deep/.demo-table-expand label {
  width: 90px;
  color: #99a9bf;
}

/deep/.demo-table-expand/deep/.el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  width: 100%;
}

/deep/.el-input-number--medium {
  width: 100%;
}

.del-handler {
  font-size: 20px;
  color: #ff4949;
}

.el-slider>/deep/.el-slider__runway {
  margin: 15px 0;
}

.el-checkbox-group {
  font-size: inherit;
}

.el-color-picker {
  display: inherit;
}

.talbe-name-style {
  font-size: 16px;
  font-weight: bold;
  line-height: 50px;
}

/deep/.el-input-number--medium {
  width: 100%;
}

/deep/.full-row {
  width: 100%;
}

.mobile-content {
  background: #EEEEEE;
}

.mobile-card {
  border: 0;
  background: transparent;
}

#staffCard {
  border: 0;
}

.mobile-body {
  padding: 0;
}

/deep/input,
/deep/textarea {
  border-color: transparent;
}

.head-pannel {
  background: #ffffff;
  padding: 15px;
  margin-top: 5px;
  color: #6B6B6B;
}

.head-pannel:after {
  clear: both;
  content: "";
  display: block;
}

.head-pannel>div {
  font-size: 14px;
  padding: 5px;
}

/deep/.el-row {
  margin-left: 0;
  margin-right: 0;
}

.body-main .el-form-item {
  margin-top: 15px;
  margin-bottom: 15px;
  border-color: transparent;
}

.el-table__expanded-cell .el-form-item {
  margin: 0;
}

.el-form-item>label {
  padding-left: 10px;
}

.body-main .el-col {
  border-bottom: 1px solid #EAEAEA;
}

.body-main {
  background: #ffffff;
  padding: 15px 20px;
  margin-top: 8px;
}

.body-main:after {
  clear: both;
  content: "";
  display: block;
}

.child-item {
  margin-top: 8px;
  background: #ffffff;
}

.child-item:after {
  clear: both;
  content: "";
  display: block;
}

.child-item /deep/.el-form-item {
  margin: 15px 10px;
  padding-bottom: 15px;
  border-color: transparent;
  border-bottom: 1px solid #EAEAEA;
}

.talbe-name-style {
  padding-left: 15px;
}

.sign-content {
  background: #ffffff;
  margin-top: 8px;
  color: #000000;
}

.sign-content:after {
  clear: both;
  content: "";
  display: block;
}

.sign-point>.el-form-item {
  padding: 5px 10px;
  margin-bottom: 0;
}

.btn-div {
  background: white;
  padding: 10px;
  margin: 10px 0;
  float: none;
}

.el-message-box__btns button:nth-child(2) {
  margin-left: 0;
}

.el-slider {
  /deep/.el-slider__bar {
    @include backgroundColor('themeMainColor');
  }
  
  /deep/.el-slider__button-wrapper {
    .el-slider__button {
      @include borderColor('themeMainColor');
    }
  }
}

.el-radio.is-checked {
  /deep/.el-radio__input.is-checked {
    .el-radio__inner {
      @include borderColor('themeMainColor');
      @include backgroundInfo('themeMainColor');
    }
  }
  
  /deep/.el-radio__input.is-checked+.el-radio__label {
    @include fontColor('themeMainColor');
  }
}

.el-checkbox.is-checked {
  /deep/.el-checkbox__input.is-checked {
    .el-checkbox__inner {
      @include borderColor('themeMainColor');
      @include backgroundInfo('themeMainColor');
    }
  }
  
  /deep/.el-checkbox__input.is-checked+.el-checkbox__label {
    @include fontColor('themeMainColor');
  }
}

.el-switch.is-checked {
  /deep/.el-switch__core {
    @include borderColor('themeMainColor');
    @include backgroundInfo('themeMainColor');
  }
}

.cube-popup {
  /deep/.cube-picker-confirm {
    @include fontColor('themeMainColor');
  }
}

.child-item {
  /deep/.el-button--primary {
    @include borderColor('themeMainColor');
    @include backgroundInfo('themeMainColor');
  }
  
  /deep/.del-handler {
    @include fontColor('themeMainColor');
  }
}

.dialog-footer {
  .el-button--success {
    @include backgroundInfo('themeMainColor');
    @include borderColor('themeMainColor');
  }
  
  .el-button--info {
    @include backgroundInfo('themeMainColor');
    @include borderColor('themeMainColor');
  }
}

</style>
