<template>
  <div class="ant-modal-content">
    <div class="ant-modal-body">
      <el-form ref="elForm" class="ant-form ant-form-horizontal" :model="formData" :rules="rules"
        size="medium" label-width="100px" :label-position="labelPosition">
        <div class="ant-card ant-card-bordered" id="staffCard">
          <div class="ant-card-body">
            <div class="page-header">
              <img  style="height: 1cm;width: 100%" :src="imgHederUrl" alt=""/>
            </div>
            <span id="staffEvectionTitle" style="margin-bottom: 10px">{{formConfigData.title}}</span>
<!--            <div class="under-title">-->
<!--              <div class="under-title-top-left">-->
<!--              </div>-->
<!--            </div>-->
            <el-row :gutter="15">
              <el-col :span="12" :xs="24" class="el-col-no-border">
                <el-form-item label-width="1px" label="" prop="recruitFiletype" class="top-left-radio">
                  <el-radio-group v-model="formData.recruitFiletype" size="medium">
                    <el-radio v-for="(item, index) in recruitFiletypeOptions" :key="index" :label="item.value"
                              :disabled="item.disabled">{{item.label}}</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label-width="1px" label="" prop="recruitUrgent" class="top-left-radio">
                  <el-radio-group v-model="formData.recruitUrgent" size="medium">
                    <el-radio v-for="(item, index) in recruitUrgentOptions" :key="index" :label="item.value"
                              :disabled="item.disabled">{{item.label}}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12" :xs="24" class="el-col-no-border">
                <el-form-item label-width="1px" label="" prop="recruitFiletype" class="top-right-radio">
                  流水碼：XX單位[行政]-20XX-XXX
                </el-form-item>
                <el-form-item label-width="1px" label="" prop="recruitFiletype" class="top-right-radio">
                  日期：[行政]xxxx-xxx
                </el-form-item>
              </el-col>
              <el-scrollbar style="width:100%;" class="child-table-entRecruitSalaryItems">
                <el-col :span="24" :xs="24" style="padding: 0px;width:300%">
                  <el-table border stripe ref="entRecruitSalaryItemsDragTable"
                            :header-cell-style="headClass"
                            :cell-style="rowClass"
                    :data="formData.entRecruitSalaryItemsLists" row-key="id" :max-height="tableHeight">
                    <el-table-column label="序" type="index" width="40px" />
                    <el-table-column label="基本信息" type="index">
                      <el-table-column label="工號" min-width="10%" prop="recruitEmpNo">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitEmpNo'"
                                        :rules="rules.entRecruitSalaryItems_recruitEmpNo" label-width="0px">
                            <el-input v-model.trim="scope.row.recruitEmpNo"
                                      :placeholder="$t('common.placeholderDefault') + '工號'"
                                      @change='EntRecruitSalaryItems_recruitEmpNo_onchange(scope.row)'></el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="姓名" min-width="10%" prop="recruitEmpNam">
                        <template slot-scope="scope">
                          <el-form-item :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitEmpNam'"
                                        :rules="rules.entRecruitSalaryItems_recruitEmpNam" label-width="0px">
                            <el-input v-model.trim="scope.row.recruitEmpNam"
                                      :placeholder="$t('common.placeholderDefault') + '姓名'"></el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="廠區" min-width="10%" prop="recruitEmpPlant">
                        <template slot-scope="scope">
                          <el-form-item
                            :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitEmpPlant'"
                            :rules="rules.entRecruitSalaryItems_recruitEmpPlant" label-width="0px">
                            <el-input v-model.trim="scope.row.recruitEmpPlant"
                                      :placeholder="$t('common.placeholderDefault') + '廠區'"></el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="組織信息" type="index">
                        <el-table-column label="機能總處級/製造總處級" min-width="10%" prop="recruitEmpZongchu">
                          <template slot-scope="scope">
                            <el-form-item
                              :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitEmpZongchu'"
                              :rules="rules.entRecruitSalaryItems_recruitEmpZongchu" label-width="0px">
                              <el-input v-model.trim="scope.row.recruitEmpZongchu"
                                        :placeholder="$t('common.placeholderDefault') + '機能總處級/製造總處級'"></el-input>
                            </el-form-item>
                          </template>
                        </el-table-column>
                        <el-table-column label="機能處級/製造處級" min-width="10%" prop="recruitEmpChu">
                          <template slot-scope="scope">
                            <el-form-item :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitEmpChu'"
                                          :rules="rules.entRecruitSalaryItems_recruitEmpChu" label-width="0px">
                              <el-input v-model.trim="scope.row.recruitEmpChu"
                                        :placeholder="$t('common.placeholderDefault') + '機能處級/製造處級'"></el-input>
                            </el-form-item>
                          </template>
                        </el-table-column>
                        <el-table-column label="次機能處級/次製造處級" min-width="10%" prop="recruitEmpJineng">
                          <template slot-scope="scope">
                            <el-form-item
                              :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitEmpJineng'"
                              :rules="rules.entRecruitSalaryItems_recruitEmpJineng" label-width="0px">
                              <el-input v-model.trim="scope.row.recruitEmpJineng"
                                        :placeholder="$t('common.placeholderDefault') + '次機能處級/次製造處級'"></el-input>
                            </el-form-item>
                          </template>
                        </el-table-column>
                        <el-table-column label="廠級/ 中心級" min-width="10%" prop="recruitEmpChang">
                          <template slot-scope="scope">
                            <el-form-item
                              :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitEmpChang'"
                              :rules="rules.entRecruitSalaryItems_recruitEmpChang" label-width="0px">
                              <el-input v-model.trim="scope.row.recruitEmpChang"
                                        :placeholder="$t('common.placeholderDefault') + '廠級/ 中心級'"></el-input>
                            </el-form-item>
                          </template>
                        </el-table-column>
                        <el-table-column label="部級" min-width="10%" prop="recruitEmpBuji">
                          <template slot-scope="scope">
                            <el-form-item :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitEmpBuji'"
                                          :rules="rules.entRecruitSalaryItems_recruitEmpBuji" label-width="0px">
                              <el-input v-model.trim="scope.row.recruitEmpBuji"
                                        :placeholder="$t('common.placeholderDefault') + '部級'"></el-input>
                            </el-form-item>
                          </template>
                        </el-table-column>
                        <el-table-column label="課級" min-width="10%" prop="recruitEmpKeji">
                          <template slot-scope="scope">
                            <el-form-item :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitEmpKeji'"
                                          :rules="rules.entRecruitSalaryItems_recruitEmpKeji" label-width="0px">
                              <el-input v-model.trim="scope.row.recruitEmpKeji"
                                        :placeholder="$t('common.placeholderDefault') + '課級'"></el-input>
                            </el-form-item>
                          </template>
                        </el-table-column>
                      </el-table-column>
                      <el-table-column label="入集團日期" min-width="10%" prop="recruitGroupDate">
                        <template slot-scope="scope">
                          <el-form-item
                            :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitGroupDate'"
                            :rules="rules.entRecruitSalaryItems_recruitGroupDate" label-width="0px">
                            <el-date-picker v-model.trim="scope.row.recruitGroupDate" value-format="yyyy-MM-dd"
                                            :placeholder="$t('common.placeholderDefault') + '入集團日期'"></el-date-picker>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="學歷" min-width="10%" prop="recruitEmpEducation">
                        <template slot-scope="scope">
                          <el-form-item
                            :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitEmpEducation'"
                            :rules="rules.entRecruitSalaryItems_recruitEmpEducation" label-width="0px">
                            <el-input v-model.trim="scope.row.recruitEmpEducation"
                                      :placeholder="$t('common.placeholderDefault') + '學歷'"></el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                    </el-table-column>
                    <el-table-column label="試用期內" type="index">

                      <el-table-column label="試用資位" min-width="10%" prop="recruitTrialPosition">
                        <template slot-scope="scope">
                          <el-form-item
                            :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitTrialPosition'"
                            :rules="rules.entRecruitSalaryItems_recruitTrialPosition" label-width="0px">
                            <el-input v-model.trim="scope.row.recruitTrialPosition"
                                      :placeholder="$t('common.placeholderDefault') + '試用資位'"></el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="試用職務" min-width="10%" prop="recruitTrialJob">
                        <template slot-scope="scope">
                          <el-form-item
                            :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitTrialJob'"
                            :rules="rules.entRecruitSalaryItems_recruitTrialJob" label-width="0px">
                            <el-input v-model.trim="scope.row.recruitTrialJob"
                                      :placeholder="$t('common.placeholderDefault') + '試用職務'"></el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="試用標準薪資a" min-width="10%" prop="recruitTrialSalary">
                        <template slot-scope="scope">
                          <el-form-item
                            :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitTrialSalary'"
                            :rules="rules.entRecruitSalaryItems_recruitTrialSalary" label-width="0px">
                            <el-input v-model.trim="scope.row.recruitTrialSalary"
                                      :placeholder="$t('common.placeholderDefault') + '試用標準薪資a'"></el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="試用專案補項" min-width="10%" prop="recruitTrialBuxiang">
                        <template slot-scope="scope">
                          <el-form-item
                            :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitTrialBuxiang'"
                            :rules="rules.entRecruitSalaryItems_recruitTrialBuxiang" label-width="0px">
                            <el-input v-model.trim="scope.row.recruitTrialBuxiang"
                                      :placeholder="$t('common.placeholderDefault') + '試用專案補項'"></el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="資位" min-width="10%" prop="recruitStaffPosition">
                        <template slot-scope="scope">
                          <el-form-item
                            :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitStaffPosition'"
                            :rules="rules.entRecruitSalaryItems_recruitStaffPosition" label-width="0px">
                            <el-input v-model.trim="scope.row.recruitStaffPosition"
                                      :placeholder="$t('common.placeholderDefault') + '資位'"></el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                    </el-table-column>
                    <el-table-column label="試用期滿" type="index">
                      <el-table-column label="職務" min-width="10%" prop="recruitStaffJob">
                        <template slot-scope="scope">
                          <el-form-item
                            :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitStaffJob'"
                            :rules="rules.entRecruitSalaryItems_recruitStaffJob" label-width="0px">
                            <el-input v-model.trim="scope.row.recruitStaffJob"
                                      :placeholder="$t('common.placeholderDefault') + '職務'"></el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="試用專案補項起止日期" min-width="10%" prop="recruitTrialTimeperiod">
                        <template slot-scope="scope">
                          <el-form-item
                            :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitTrialTimeperiod'"
                            :rules="rules.entRecruitSalaryItems_recruitTrialTimeperiod" label-width="0px">
                            <el-date-picker v-model.trim="scope.row.recruitTrialTimeperiod"
                                            value-format="yyyy-MM" type="daterange" style="width:100%"
                                            :placeholder="$t('common.placeholderDefault') + '試用專案補項起止日期'"
                                            start-placeholder="開始日期" end-placeholder="結束日期" range-separator="至">
                            </el-date-picker>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="調整 額度 b" min-width="10%" prop="recruitStaffQuota">
                        <template slot-scope="scope">
                          <el-form-item
                            :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitStaffQuota'"
                            :rules="rules.entRecruitSalaryItems_recruitStaffQuota" label-width="0px">
                            <el-input v-model.trim="scope.row.recruitStaffQuota"
                                      :placeholder="$t('common.placeholderDefault') + '調整 額度 b'"></el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="標準薪資=a+b" min-width="10%" prop="recruitStaffSalary">
                        <template slot-scope="scope">
                          <el-form-item
                            :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitStaffSalary'"
                            :rules="rules.entRecruitSalaryItems_recruitStaffSalary" label-width="0px">
                            <el-input v-model.trim="scope.row.recruitStaffSalary"
                                      :placeholder="$t('common.placeholderDefault') + '標準薪資=a+b'"></el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="標準薪資級距對應范圍" type="index">
                        <el-table-column label="考核等級" min-width="10%" prop="recruitStaffAppraisal">
                          <template slot-scope="scope">
                            <el-form-item
                              :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitStaffAppraisal'"
                              :rules="rules.entRecruitSalaryItems_recruitStaffAppraisal" label-width="0px">
                              <el-input v-model.trim="scope.row.recruitStaffAppraisal"
                                        :placeholder="$t('common.placeholderDefault') + '考核等級'"></el-input>
                            </el-form-item>
                          </template>
                        </el-table-column>
                        <el-table-column label="級距範圍" min-width="10%" prop="recruitStaffRange">
                          <template slot-scope="scope">
                            <el-form-item
                              :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitStaffRange'"
                              :rules="rules.entRecruitSalaryItems_recruitStaffRange" label-width="0px">
                              <el-input v-model.trim="scope.row.recruitStaffRange"
                                        :placeholder="$t('common.placeholderDefault') + '級距範圍'"></el-input>
                            </el-form-item>
                          </template>
                        </el-table-column><el-table-column label="專案補項" min-width="10%" prop="recruitStaffBuxiang">
                        <template slot-scope="scope">
                          <el-form-item
                            :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitStaffBuxiang'"
                            :rules="rules.entRecruitSalaryItems_recruitStaffBuxiang" label-width="0px">
                            <el-input v-model.trim="scope.row.recruitStaffBuxiang"
                                      :placeholder="$t('common.placeholderDefault') + '專案補項'"></el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                        <el-table-column label="專案補項起止日期" min-width="10%" prop="recruitStaffTimeperiod">
                          <template slot-scope="scope">
                            <el-form-item
                              :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitStaffTimeperiod'"
                              :rules="rules.entRecruitSalaryItems_recruitStaffTimeperiod" label-width="0px">
                              <el-date-picker v-model.trim="scope.row.recruitStaffTimeperiod"
                                              value-format="yyyy-MM" type="daterange" style="width:100%"
                                              :placeholder="$t('common.placeholderDefault') + '專案補項起止日期'"
                                              start-placeholder="開始日期" end-placeholder="結束日期" range-separator="至">
                              </el-date-picker>
                            </el-form-item>
                          </template>
                        </el-table-column>
                      </el-table-column>
                    </el-table-column>

                    <el-table-column label="生效日期" min-width="10%" prop="recruitEffectiveDate">
                      <template slot-scope="scope">
                        <el-form-item
                          :prop="'entRecruitSalaryItemsLists.' + scope.$index + '.recruitEffectiveDate'"
                          :rules="rules.entRecruitSalaryItems_recruitEffectiveDate" label-width="0px">
                          <el-date-picker v-model.trim="scope.row.recruitEffectiveDate"
                                          value-format="yyyy-MM-dd" :placeholder="$t('common.placeholderDefault') + '生效日期'">
                          </el-date-picker>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column align="center" :label="$t('table.operation')" width="80">
                      <template slot-scope="scope">
                        <el-button type="text"
                          @click="handleDelEntRecruitSalaryItems(scope.$index, scope.row)" class="del-handler"
                          icon="el-icon-delete"></el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-col>
              </el-scrollbar>
              <el-col :span="24" :xs="24" style="padding-top: 5px;padding-bottom: 5px">
                <el-button type="primary" icon="el-icon-plus" size="mini"
                  @click="handleAddEntRecruitSalaryItems">{{$t('common.add')}}</el-button>
              </el-col>
              <el-col :span="3" :xs="24" style="padding-top: 5px;padding-bottom: 5px;height:45px;">
                <el-button type="text" icon="el-icon-download" size="mini"
                  @click="handlePreviewEntRecruitSalaryItems">下載模版</el-button>
              </el-col>
              <el-col :span="21" :xs="24" style="padding-top: 5px;padding-bottom: 5px">
                <el-upload :file-list="uploadEntRecruitSalaryItems.fileList" :show-file-list="false"
                  :on-change="handleChangeEntRecruitSalaryItems"
                  :on-exceed="handleExceedEntRecruitSalaryItems"
                  :on-preview="handlePreviewEntRecruitSalaryItems"
                  :on-remove="handleRemoveEntRecruitSalaryItems"
                  :on-success="uploadsuccesEntRecruitSalaryItems"
                  :headers="uploadEntRecruitSalaryItems.headers" :action="uploadEntRecruitSalaryItems.url"
                  :before-upload="beforeUploadEntRecruitSalaryItems" accept=".xls,.xlsx" list-type="text">
                  <el-button size="small" type="primary" icon="el-icon-upload2">{{ $t('table.import') }}
                  </el-button>
                </el-upload>
              </el-col>
              <el-col :span="24" :xs="24">
                <img  style="height: 4cm;width: 100%" :src="imgQianheliuchengtuUrl" alt=""/>
              </el-col>
              <el-col :span="24" :xs="24">
                <el-row :gutter="15">
                  <el-col :span="8" :xs="24">
                    <el-form-item prop="cbchargeno" label-width="0">
                      <entfrm-sign-form-add-hq :label="formConfigData.cbchargeno"
                        :required='formConfigData.cbchargeno_required'
                        :selectEmpProp="{empNo:this.formData.cbchargeno,empName:this.formData.cbchargename}"
                        model-no="cbchargeno" model-name="cbchargename"
                        @onSignFormSelected="onSignFormSelected"></entfrm-sign-form-add-hq>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" :xs="24">
                    <el-form-item prop="shenhechargeno" label-width="0">
                      <entfrm-sign-form-add-hq :label="formConfigData.shenhechargeno"
                        :required='formConfigData.shenhechargeno_required'
                        :selectEmpProp="{empNo:this.formData.shenhechargeno,empName:this.formData.shenhechargename}"
                        model-no="shenhechargeno" model-name="shenhechargename"
                        @onSignFormSelected="onSignFormSelected"></entfrm-sign-form-add-hq>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" :xs="24">
                    <el-form-item prop="hezhunchargeno" label-width="0">
                      <entfrm-sign-form-add-hq :label="formConfigData.hezhunchargeno"
                        :required='formConfigData.hezhunchargeno_required'
                        :selectEmpProp="{empNo:this.formData.hezhunchargeno,empName:this.formData.hezhunchargename}"
                        model-no="hezhunchargeno" model-name="hezhunchargename"
                        @onSignFormSelected="onSignFormSelected"></entfrm-sign-form-add-hq>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-col>
              <el-col :span="24" :xs="24">
                <div class="dialog-footer" align="center" style="padding-top: 5px;padding-bottom: 5px">
                  <!--          <el-form-item size="large">-->
                  <el-button type="success" @click="handleSubmit" :disabled="isDisable">{{$t('table.submit')}}
                  </el-button>
                  <el-button type="primary" @click="submitForm" :disabled="isDisable">{{$t('common.save')}}
                  </el-button>
                  <el-button @click="closeForm">{{$t('common.cancel')}}</el-button>
                  <!--        </el-form-item>-->
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-form>
    </div>
  </div>
</template>
<script>
import {
  getEntRecruitSalary,
  addEntRecruitSalary,
  editEntRecruitSalary,
  addEntRecruitSalaryAndStartProcess,
  editEntRecruitSalaryAndStartProcess,
  getSignConfigList,
  exportEntRecruitSalaryItems
}
from "@/api/caaesign/entRecruitSalary"
import '@/assets/styles/design-build/design-add-view.scss'
import {
  getAccessToken,
  getZltAccessToken
}
from "@/utils/auth";
import {
  previewFile,
  getHeader
}
from "@/utils/entfrm";
import {
  listFileInfo,
  getFileInfo,
  delFileInfo,
  addFileInfo,
  editFileInfo,
  getByKey
}
from "@/api/system/fileInfo";
import imgHeder from '@/assets/images/A3.jpg'
import imgQianheliuchengtu from '@/assets/images/qianheliuchengtu.png'
export default {
  components: {},
  props: [],
  data() {
    return {
      imgHederUrl: imgHeder,
      imgQianheliuchengtuUrl: imgQianheliuchengtu,
      formData: {
        recruitSerialNo: undefined,
        recruitFiletype: undefined,
        recruitUrgent: undefined,
        entRecruitSalaryItemsLists: [],
        cbchargeno: undefined,
        shenhechargeno: undefined,
        hezhunchargeno: undefined,
        makerNo: this.$store.state.user.empNo,
        makerName: this.$store.state.user.name,
        dataSource: "pc",
      },
      formConfigData: {
        title: "iPEG試用期滿薪資異動匯總審批表",
        cbchargeno: "承辦",
        cbchargeno_required: true,
        shenhechargeno: "審核",
        shenhechargeno_required: true,
        hezhunchargeno: "核准",
        hezhunchargeno_required: true,
      },
      tableHeight: document.documentElement.scrollHeight - 245 + "px",
      rules: {
        recruitSerialNo: [{
          required: true,
          message: 'XX單位[行政]-20XX-XXX',
          trigger: 'blur'
        }],
        recruitFiletype: [{
          required: true,
          message: '不能為空',
          trigger: 'change'
        }],
        recruitUrgent: [{
          required: true,
          message: '不能為空',
          trigger: 'change'
        }],
        entRecruitSalaryItems_recruitEmpNo: [{
          required: true,
          message: '工號不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitEmpNam: [{
          required: true,
          message: '姓名不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitEmpPlant: [{
          required: true,
          message: '廠區不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitEmpZongchu: [{
          required: true,
          message: '機能總處級/製造總處級不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitEmpChu: [{
          required: true,
          message: '機能處級/製造處級不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitEmpJineng: [{
          required: true,
          message: '次機能處級/次製造處級不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitEmpChang: [{
          required: true,
          message: '廠級/ 中心級不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitEmpBuji: [{
          required: true,
          message: '部級不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitEmpKeji: [{
          required: true,
          message: '課級不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitGroupDate: [{
          required: true,
          message: '入集團日期不能為空',
          trigger: 'change'
        }],
        entRecruitSalaryItems_recruitEmpEducation: [{
          required: true,
          message: '學歷不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitTrialPosition: [{
          required: true,
          message: '試用資位不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitTrialJob: [{
          required: true,
          message: '試用職務不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitTrialSalary: [{
          required: true,
          message: '試用標準薪資a不能為空',
          trigger: 'blur'
        }, {
          pattern: /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/,
          message: '請輸入數字！',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitTrialBuxiang: [{
          required: true,
          message: '試用專案補項不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitStaffPosition: [{
          required: true,
          message: '資位不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitStaffJob: [{
          required: true,
          message: '職務不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitStaffQuota: [{
          required: true,
          message: '調整 額度 b不能為空',
          trigger: 'blur'
        }, {
          pattern: /^(([0-9]+)|([0-9]+\.[0-9]{1,2}))$/,
          message: '最多保留倆位小數，請檢查輸入信息！',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitStaffSalary: [{
          required: true,
          message: '標準薪資=a+b不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitStaffAppraisal: [{
          required: true,
          message: '考核等級不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitStaffRange: [{
          required: true,
          message: '級距範圍不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitStaffBuxiang: [{
          required: true,
          message: '專案補項不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitEffectiveDate: [{
          required: true,
          message: '生效日期不能為空',
          trigger: 'change'
        }],
        cbchargeno: [{
          required: true,
          message: '承辦不能為空',
          trigger: 'change'
        }],
        shenhechargeno: [{
          required: true,
          message: '審核不能為空',
          trigger: 'change'
        }],
        hezhunchargeno: [{
          required: true,
          message: '核准不能為空',
          trigger: 'change'
        }],
      },
      // 文件上传参数
      uploadEntRecruitSalaryItems: {
        // 是否显示弹出层
        open: false,
        fileList: [],
        fileNameList: [],
        // 弹出层标题
        title: "",
        multiple: false,
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: this.getHeader(),
        // 上传的地址
        url: this.getAppBaseApi() + "/caaesign/entRecruitSalary/importEntRecruitSalaryItems"
      },
      recruitFiletypeOptions: [],
      recruitUrgentOptions: [],
      entRecruitSalaryItemsDragTableMobileClientWidth: 0,
      isMobile: false,
      isDisable: false,
      labelPosition: 'right',
      alias: "postgresql$_$pghrsign_ipebg_test$_$075d399c2dcaa20f94d53895254e58a1%_%design"
    }
  },
  computed: {},
  watch: {},
  created() {
    const id = this.$route.query.id;
    this.changeTagsView(this.$route.query);
    this.isMobile = this.isMobileFun()
    if (this.isMobile) {
      this.labelPosition = 'top'
    }
    getSignConfigList().then(response => {
      if (response.code === 0) {
        response.data.forEach(element => {
          const colKey = element.colKey.split('_')
          if (colKey.length > 1) {
            if (this.rules[colKey[0]] && colKey[1] === 'required') {
              this.rules[colKey[0]][0]['required'] = element.colValue === 'true'
              this.formConfigData[element.colKey] = element.colValue === 'true'
            }
            else {
              this.formConfigData[element.colKey] = element.colValue
            }
          }
          else {
            this.formConfigData[element.colKey] = element.colValue
          }
        });
      }
    })
    if (id != null && id != undefined) {
      getEntRecruitSalary(id).then(response => {
        this.formData = response.data;
        this.checkBoxParse()
        this.cascaderParse()
        this.timeRangParse()
        if (this.formData.attachids) {
          let a = this.formData.attachids.split(',');
          if (a.length > 0) {
            a.forEach(item => {
              if (item) {
                getByKey(item).then(response => {
                  this.upload.fileList.push({
                    name: response.data.orignalName,
                    url: response.data.name
                  });
                })
              }
            })
          }
        }
      });
    }
    this.getRecruitFiletypeOptions()
    this.getRecruitUrgentOptions()
  },
  mounted() {
    this.$nextTick(function() {
      if (this.isMobile) {
        this.entRecruitSalaryItemsDragTableMobileClientWidth = this.$refs
          .entRecruitSalaryItemsDragTableMobile.$el.clientWidth
      }
    })
  },
  methods: {
    submitForm() {
      this.$refs['elForm'].validate(valid => {
        if (valid) {
          this.isDisable = true;
          const formData = JSON.parse(JSON.stringify(this.formData))
          for (let index in formData.entRecruitSalaryItemsLists) {
            formData.entRecruitSalaryItemsLists[index].recruitTrialTimeperiod = JSON.stringify(formData
              .entRecruitSalaryItemsLists[index].recruitTrialTimeperiod)
            formData.entRecruitSalaryItemsLists[index].recruitStaffTimeperiod = JSON.stringify(formData
              .entRecruitSalaryItemsLists[index].recruitStaffTimeperiod)
          }
          if (formData.id != undefined) {
            editEntRecruitSalary(formData).then(response => {
              if (response.code === 0) {
                this.msgSuccess(this.$t('tips.updateSuccess'));
                this.closeForm();
              }
              else {
                this.msgError(response.msg);
              }
              this.isDisable = false;
            });
          }
          else {
            addEntRecruitSalary(formData).then(response => {
              if (response.code === 0) {
                this.msgSuccess(this.$t('tips.createSuccess'));
                this.closeForm();
              }
              else {
                this.msgError(response.msg);
              }
              this.isDisable = false;
            });
          }
          /**
           * 提交的時候刪除被標記的需要刪除的附件
           */
          if (this.formData.attachids) {
            this.upload.fileNameList.forEach(item => {
              if (item) {
                delFileInfo(item);
              }
            })
          }
        }
      })
    },
    handleSubmit: function() {
      this.$refs["elForm"].validate(valid => {
        if (valid) {
          this.isDisable = true;
          const formData = JSON.parse(JSON.stringify(this.formData))
          for (let index in formData.entRecruitSalaryItemsLists) {
            formData.entRecruitSalaryItemsLists[index].recruitTrialTimeperiod = JSON.stringify(formData
              .entRecruitSalaryItemsLists[index].recruitTrialTimeperiod)
            formData.entRecruitSalaryItemsLists[index].recruitStaffTimeperiod = JSON.stringify(formData
              .entRecruitSalaryItemsLists[index].recruitStaffTimeperiod)
          }
          if (formData.id != undefined) {
            editEntRecruitSalaryAndStartProcess(formData).then(response => {
              if (response.code === 0) {
                this.msgSuccess(this.$t('tips.updateSuccess'));
                this.closeForm();
              }
              else {
                this.msgError(response.msg);
              }
              this.isDisable = false;
            });
          }
          else {
            addEntRecruitSalaryAndStartProcess(formData).then(response => {
              if (response.code === 0) {
                this.msgSuccess(this.$t('tips.createSuccess'));
                this.closeForm();
              }
              else {
                this.msgError(response.msg);
              }
              this.isDisable = false;
            });
          }
          /**
           * 提交的時候刪除被標記的需要刪除的附件
           */
          if (this.formData.attachids) {
            this.upload.fileNameList.forEach(item => {
              if (item) {
                delFileInfo(item);
              }
            })
          }
        }
      })
    },
    resetForm() {
      this.$refs['elForm'].resetFields()
    },
    closeForm() {
      //关闭子页面
      if (this.$store.state.settings.tagsView) {
        this.$router.go(-1) // 返回
        this.$store.state.tagsView.visitedViews.splice(this.$store.state.tagsView.visitedViews.findIndex(
          item => item.path === this.$route.path), 1)
        this.$router.push(this.$store.state.tagsView.visitedViews[this.$store.state.tagsView.visitedViews
          .length - 1].path)
      }
      else {
        parent.postMessage("closeCurrentTabMessage", '*');
      }
    },
    getRecruitFiletypeOptions() {
      this.getDicts("recruit_filetype").then(response => {
        this.recruitFiletypeOptions = response.data;
      });
    },
    getRecruitUrgentOptions() {
      this.getDicts("recruit_urgent").then(response => {
        this.recruitUrgentOptions = response.data;
      });
    },
    checkBoxParse() {},
    cascaderParse() {},
    timeRangParse() {
      for (let index in this.formData.entRecruitSalaryItemsLists) {
        if (this.formData.entRecruitSalaryItemsLists[index].recruitTrialTimeperiod) {
          this.formData.entRecruitSalaryItemsLists[index].recruitTrialTimeperiod = JSON.parse(this.formData
            .entRecruitSalaryItemsLists[index].recruitTrialTimeperiod)
        }
        if (this.formData.entRecruitSalaryItemsLists[index].recruitStaffTimeperiod) {
          this.formData.entRecruitSalaryItemsLists[index].recruitStaffTimeperiod = JSON.parse(this.formData
            .entRecruitSalaryItemsLists[index].recruitStaffTimeperiod)
        }
      }
    },
    onSignFormSelected(selectEmp, modelNo, modelName) {
      this.$set(this.formData, modelNo, selectEmp.empNo)
      this.$set(this.formData, modelName, selectEmp.empName)
    },
    handleAddEntRecruitSalaryItems() {
      const cloumn = {
        recruitEmpNo: '',
        recruitEmpNam: '',
        recruitEmpPlant: '',
        recruitEmpZongchu: '',
        recruitEmpChu: '',
        recruitEmpJineng: '',
        recruitEmpChang: '',
        recruitEmpBuji: '',
        recruitEmpKeji: '',
        recruitGroupDate: '',
        recruitEmpEducation: '',
        recruitTrialPosition: '',
        recruitTrialJob: '',
        recruitTrialSalary: '',
        recruitTrialBuxiang: '',
        recruitStaffPosition: '',
        recruitStaffJob: '',
        recruitTrialTimeperiod: [],
        recruitStaffQuota: '',
        recruitStaffSalary: '',
        recruitStaffAppraisal: '',
        recruitStaffRange: '',
        recruitStaffBuxiang: '',
        recruitStaffTimeperiod: [],
        recruitEffectiveDate: ''
      };
      this.formData.entRecruitSalaryItemsLists.splice(this.formData.entRecruitSalaryItemsLists.length, 0,
        cloumn);
      for (let index in this.formData.entRecruitSalaryItemsLists) {
        this.formData.entRecruitSalaryItemsLists[index].sort = parseInt(index) + 1;
      }
    },
    handleDelEntRecruitSalaryItems(index, row) {
      let functionName = this.$t('ent_recruit_salary_075d399c2dcaa20f94d53895254e58a1.default.functionName');
      this.$confirm(this.$t('tips.deleteConfirm', ['iPEG試用期滿薪資異動匯總審批表從表', row.id]), this.$t('tips.warm'), {
        confirmButtonText: this.$t("common.confirmTrim"),
        cancelButtonText: this.$t("common.cancelTrim"),
        type: "warning",
      }).then(() => {
        this.formData.entRecruitSalaryItemsLists.splice(index, 1);
        this.msgSuccess(this.$t("tips.deleteSuccess"));
      }).catch(function(err) {
        console.log(err);
      });
    },
    // 文件上传成功处理
    uploadsuccesEntRecruitSalaryItems(response, file, fileList) {
      if (response.code === 0) {
        this.msgSuccess(this.$t('tips.importSuccess'));
        this.formData.entRecruitSalaryItemsLists = this.formData.entRecruitSalaryItemsLists.concat(response
          .data);
        response.data.forEach(item => {
          this.EntRecruitSalaryItems_recruitEmpNo_onchange(item)
        })
      }
      else {
        this.msgError(response.msg);
      }
    },
    handleChangeEntRecruitSalaryItems(file, fileList) {},
    handleExceedEntRecruitSalaryItems(file, fileList) {},
    handleRemoveEntRecruitSalaryItems(file, fileList) {
      this.$emit("delUploadImage", file.name);
      const index = this.upload.fileList.indexOf(file);
      this.upload.fileList.splice(index, 1);
      this.formData.attachids = this.formData.attachids.replace(file.url + ",", "");
      this.upload.fileNameList.push(file.url);
      // delFileInfo(file.url);
    },
    handlePreviewEntRecruitSalaryItems(file) {
      const queryParams = this.queryParams;
      exportEntRecruitSalaryItems(queryParams).then(response => {
        this.download(response.data);
      })
    },
    EntRecruitSalaryItems_recruitEmpNo_onchange(item) {
      this.getInfoUserByEmpno(item.recruitEmpNo).then(responseInfo => {
        if (responseInfo.code !== 0) {
          this.msgError(responseInfo.msg);
        }
        else {
          if (responseInfo.data != null) {
            item.recruitEmpNam = responseInfo.data.empname
            item.recruitEmpPlant = responseInfo.data.factoryid
          }
          else {
            item.recruitEmpNam = ''
            item.recruitEmpPlant = ''
          }
        }
      });
    },
    entRecruitSalaryItems_getSummaries(param) {
      const {
        columns,
        data
      } = param;
      const sums = [];
      const scales = [];
      const childAttributesMap = {};
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = this.$t('common.sumText');
          return;
        }
        if (column.property && childAttributesMap[column.property] && childAttributesMap[column.property]
          .sumMasterColumn) {
          scales[index] = 0;
          if (childAttributesMap[column.property].decimalScale) {
            scales[index] = childAttributesMap[column.property].decimalScale
          }
          const values = data.map(item => Number(item[column.property]));
          if (!values.every(value => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                var x = prev + curr
                return x
              }
              else {
                return prev
              }
            }, 0);
            sums[index] = sums[index].toFixed(scales[index])
            this.formData[childAttributesMap[column.property].sumMasterColumn] = sums[index]
          }
        }
      });
      return sums;
    },
    beforeUploadEntRecruitSalaryItems(file) {
      let isRightSize = file.size / 1024 / 1024 < 2
      if (!isRightSize) {
        this.$message.error('文件大小超过 2MB')
      }
      let isAccept = new RegExp('.xls,.xlsx'.replaceAll(',', '|')).test(file.name)
      if (!isAccept) {
        this.$message.error('应该选择.xls,.xlsx类型的文件')
      }
      return isRightSize && isAccept
    },
    headClass(){
      return "text-align:center"
    },
    rowClass(){
      return "text-align:center"
    }
  }
}

</script>
<style scoped>
.del-handler {
  font-size: 20px;
  color: #ff4949;
}

.el-slider>/deep/.el-slider__runway {
  margin: 15px 0;
}

.el-checkbox-group {
  font-size: inherit;
}

.el-color-picker {
  display: inherit;
}

.talbe-name-style {
  font-size: 16px;
  font-weight: bold;
  color: #1DB8FF;
  line-height: 50px;
}

/deep/.el-input-number--medium {
  width: 100%;
}

.under-title {
  width: 100%;
}
.under-title-top-left {
  width: 50%;
  float: left;
}
.under-title-top-right {
  width: 100%;
  text-align: right;
  float: left;
}
.ant-modal-content .el-form-item {
  margin: 0;
}
.top-left-radio {
  line-height: 30px;
}
.top-right-radio {
  line-height: 30px;
  text-align: right;
}
</style>
