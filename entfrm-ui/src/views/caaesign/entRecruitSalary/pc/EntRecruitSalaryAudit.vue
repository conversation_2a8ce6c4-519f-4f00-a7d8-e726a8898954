<template>
  <div class="ant-modal-content">
    <div class="ant-modal-body">
      <el-form ref="elForm" class="ant-form ant-form-horizontal" :model="formData" size="medium"
        label-width="100px" :label-position="labelPosition">
        <div class="ant-card ant-card-bordered" id="staffCard">
          <div class="ant-card-body">
            <el-row :gutter="15">
              <span id="staffEvectionTitle" style="margin-bottom: 10px">{{formConfigData.title}}</span>
              <el-col :span="8" :xs="24" class="el-col-no-border bar-to-bottom">
                任務編碼:{{formData.serialno?formData.serialno:'提交表單后自動生成'}}
              </el-col>
              <el-col :span="8" :xs="24" class="el-col-no-border bar-to-bottom"> 填單時間:{{formData.createTime}}
              </el-col>
              <el-col :span="8" :xs="24" class="el-col-no-border bar-to-bottom">
                填單人:{{formData.makerNo +' / '+ formData.makerName}}
              </el-col>
              <el-col :span="24" :xs="24">
                <el-form-item label-width="70px" label="流水碼" prop="recruitSerialNo">
                  {{formData.recruitSerialNo}}
                </el-form-item>
              </el-col>
              <el-col :span="12" :xs="24">
                <el-form-item label-width="1px" label="" prop="recruitFiletype">
                  {{formData.recruitFiletypeValue}}
                </el-form-item>
              </el-col>
              <el-col :span="12" :xs="24">
                <el-form-item label-width="1px" label="" prop="recruitUrgent">
                  {{formData.recruitUrgentValue}}
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24" class="talbe-name-style">
              </el-col>
              <el-scrollbar style="width:100%;" class="child-table-entRecruitSalaryItems">
                <el-col :span="24" :xs="24" style="padding: 0px;width:300%">
                  <el-table border stripe ref="entRecruitSalaryItemsDragTable"
                    :data="formData.entRecruitSalaryItemsLists" row-key="id" :max-height="tableHeight">
                    <el-table-column label="編號" type="index" width="50px" />
                    <el-table-column label="工號" min-width="10%" prop="recruitEmpNo">
                      <template slot-scope="scope">
                        <el-form-item label-width="0px">
                          {{scope.row.recruitEmpNo}}
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column label="姓名" min-width="10%" prop="recruitEmpNam">
                      <template slot-scope="scope">
                        <el-form-item label-width="0px">
                          {{scope.row.recruitEmpNam}}
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column label="廠區" min-width="10%" prop="recruitEmpPlant">
                      <template slot-scope="scope">
                        <el-form-item label-width="0px">
                          {{scope.row.recruitEmpPlant}}
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column label="機能總處級/製造總處級" min-width="10%" prop="recruitEmpZongchu">
                      <template slot-scope="scope">
                        <el-form-item label-width="0px">
                          {{scope.row.recruitEmpZongchu}}
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column label="機能處級/製造處級" min-width="10%" prop="recruitEmpChu">
                      <template slot-scope="scope">
                        <el-form-item label-width="0px">
                          {{scope.row.recruitEmpChu}}
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column label="次機能處級/次製造處級" min-width="10%" prop="recruitEmpJineng">
                      <template slot-scope="scope">
                        <el-form-item label-width="0px">
                          {{scope.row.recruitEmpJineng}}
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column label="廠級/ 中心級" min-width="10%" prop="recruitEmpChang">
                      <template slot-scope="scope">
                        <el-form-item label-width="0px">
                          {{scope.row.recruitEmpChang}}
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column label="部級" min-width="10%" prop="recruitEmpBuji">
                      <template slot-scope="scope">
                        <el-form-item label-width="0px">
                          {{scope.row.recruitEmpBuji}}
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column label="課級" min-width="10%" prop="recruitEmpKeji">
                      <template slot-scope="scope">
                        <el-form-item label-width="0px">
                          {{scope.row.recruitEmpKeji}}
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column label="入集團日期" min-width="10%" prop="recruitGroupDate">
                      <template slot-scope="scope">
                        <el-form-item label-width="0px">
                          {{scope.row.recruitGroupDate}}
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column label="學歷" min-width="10%" prop="recruitEmpEducation">
                      <template slot-scope="scope">
                        <el-form-item label-width="0px">
                          {{scope.row.recruitEmpEducation}}
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column label="試用資位" min-width="10%" prop="recruitTrialPosition">
                      <template slot-scope="scope">
                        <el-form-item label-width="0px">
                          {{scope.row.recruitTrialPosition}}
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column label="試用職務" min-width="10%" prop="recruitTrialJob">
                      <template slot-scope="scope">
                        <el-form-item label-width="0px">
                          {{scope.row.recruitTrialJob}}
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column label="試用標準薪資a" min-width="10%" prop="recruitTrialSalary">
                      <template slot-scope="scope">
                        <el-form-item label-width="0px">
                          {{scope.row.recruitTrialSalary}}
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column label="試用專案補項" min-width="10%" prop="recruitTrialBuxiang">
                      <template slot-scope="scope">
                        <el-form-item label-width="0px">
                          {{scope.row.recruitTrialBuxiang}}
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column label="資位" min-width="10%" prop="recruitStaffPosition">
                      <template slot-scope="scope">
                        <el-form-item label-width="0px">
                          {{scope.row.recruitStaffPosition}}
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column label="職務" min-width="10%" prop="recruitStaffJob">
                      <template slot-scope="scope">
                        <el-form-item label-width="0px">
                          {{scope.row.recruitStaffJob}}
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column label="試用專案補項起止日期" min-width="10%" prop="recruitTrialTimeperiod">
                      <template slot-scope="scope">
                        <el-form-item label-width="0px">
                          {{recruitTrialTimeperiodEntRecruitSalaryItemsFormat(scope.row.recruitTrialTimeperiod)}}
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column label="調整 額度 b" min-width="10%" prop="recruitStaffQuota">
                      <template slot-scope="scope">
                        <el-form-item label-width="0px">
                          {{scope.row.recruitStaffQuota}}
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column label="標準薪資=a+b" min-width="10%" prop="recruitStaffSalary">
                      <template slot-scope="scope">
                        <el-form-item label-width="0px">
                          {{scope.row.recruitStaffSalary}}
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column label="考核等級" min-width="10%" prop="recruitStaffAppraisal">
                      <template slot-scope="scope">
                        <el-form-item label-width="0px">
                          {{scope.row.recruitStaffAppraisal}}
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column label="級距範圍" min-width="10%" prop="recruitStaffRange">
                      <template slot-scope="scope">
                        <el-form-item label-width="0px">
                          {{scope.row.recruitStaffRange}}
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column label="專案補項" min-width="10%" prop="recruitStaffBuxiang">
                      <template slot-scope="scope">
                        <el-form-item label-width="0px">
                          {{scope.row.recruitStaffBuxiang}}
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column label="專案補項起止日期" min-width="10%" prop="recruitStaffTimeperiod">
                      <template slot-scope="scope">
                        <el-form-item label-width="0px">
                          {{recruitStaffTimeperiodEntRecruitSalaryItemsFormat(scope.row.recruitStaffTimeperiod)}}
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column label="生效日期" min-width="10%" prop="recruitEffectiveDate">
                      <template slot-scope="scope">
                        <el-form-item label-width="0px">
                          {{scope.row.recruitEffectiveDate}}
                        </el-form-item>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-col>
              </el-scrollbar>
              <el-col :span="24" :xs="24">
                <el-form-item :label="$t('table.activity.approvalOpinions')">
                  <el-input v-model="formData.comment" type="textarea" rows="3"
                    :placeholder="$t('table.activity.inputApprovalOpinions')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24">
                <div class="dialog-footer" align="center" style="padding-top: 5px;padding-bottom: 5px">
                  <el-button type="success" @click="handleTask(0)" :disabled="isDisable">
                    {{$t('table.activity.pass')}}
                  </el-button>
                  <el-button type="danger" @click="handleTask(1)" :disabled="isDisable">
                    {{$t('table.activity.rejection')}}
                  </el-button>
                  <el-button @click="handleTrack">{{$t('table.activity.flowTracing')}}</el-button>
                  <el-button @click="closeForm">{{$t('common.close')}}</el-button>
                </div>
              </el-col>
              <!-- 簽核線 -->
              <el-col :span="24" class="print-hide-div">
                <div class="dialog-footer" style="padding-top: 5px;padding-bottom: 5px">
                  <div v-html="signPath"></div>
                </div>
              </el-col>
              <!-- 审核记录 -->
              <el-table border stripe :data="commentList">
                <el-table-column :label="$t('table.activity.taskId')" align="center" prop="id" />
                <el-table-column :label="$t('table.activity.approvedBy')" align="center" prop="userId"
                  :show-overflow-tooltip="true" />
                <el-table-column :label="$t('table.activity.approvalOpinions')" align="center"
                  prop="fullMessage" :show-overflow-tooltip="true" />
                <el-table-column :label="$t('table.activity.operIp')" align="center" prop="operIp"
                  :show-overflow-tooltip="true" />
                <el-table-column :label="$t('table.activity.auditStatus')" align="center" prop="status"
                  :formatter="statusFormat" :show-overflow-tooltip="true" />
                <el-table-column :label="$t('table.activity.approvalTime')" align="center" prop="time"
                  width="180">
                  <template slot-scope="scope">
                    <span>{{ parseTime(scope.row.time) }}</span>
                  </template>
                </el-table-column>
              </el-table>
              <!-- 任务跟踪对话框 -->
              <el-dialog :title="$t('table.activity.taskMap')" :visible.sync="showImgDialog" width="1000px">
                <img :src="imgUrl" style="padding-bottom: 60px;width:100%;">
              </el-dialog>
            </el-row>
          </div>
        </div>
      </el-form>
    </div>
  </div>
</template>
<script>
import {
  getEntRecruitSalary,
  addEntRecruitSalary,
  editEntRecruitSalary,
  getSignPath,
  updateAndCheck,
  getSignConfigList,
  exportEntRecruitSalaryItems
}
from "@/api/caaesign/entRecruitSalary"
import '@/assets/styles/design-build/design-add-view.scss'
import {
  listTask,
  getTask,
  checkTask,
  taskComment
}
from "@/api/activiti/task"
import {
  getAccessToken,
  getZltAccessToken
}
from "@/utils/auth";
import {
  previewFile,
  getHeader
}
from "@/utils/entfrm";
import {
  listFileInfo,
  getFileInfo,
  delFileInfo,
  addFileInfo,
  editFileInfo,
  getByKey
}
from "@/api/system/fileInfo";
export default {
  components: {},
  props: [],
  data() {
    return {
      formData: {
        recruitSerialNo: undefined,
        recruitFiletype: undefined,
        recruitUrgent: undefined,
        entRecruitSalaryItemsLists: [],
        cbchargeno: undefined,
        shenhechargeno: undefined,
        hezhunchargeno: undefined,
      },
      formConfigData: {
        title: "iPEG試用期滿薪資異動匯總審批表",
        cbchargeno: "承辦",
        cbchargeno_required: true,
        shenhechargeno: "審核",
        shenhechargeno_required: true,
        hezhunchargeno: "核准",
        hezhunchargeno_required: true,
      },
      rules: {
        recruitSerialNo: [{
          required: true,
          message: 'XX單位[行政]-20XX-XXX',
          trigger: 'blur'
        }],
        recruitFiletype: [{
          required: true,
          message: '不能為空',
          trigger: 'change'
        }],
        recruitUrgent: [{
          required: true,
          message: '不能為空',
          trigger: 'change'
        }],
        entRecruitSalaryItems_recruitEmpNo: [{
          required: true,
          message: '工號不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitEmpNam: [{
          required: true,
          message: '姓名不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitEmpPlant: [{
          required: true,
          message: '廠區不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitEmpZongchu: [{
          required: true,
          message: '機能總處級/製造總處級不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitEmpChu: [{
          required: true,
          message: '機能處級/製造處級不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitEmpJineng: [{
          required: true,
          message: '次機能處級/次製造處級不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitEmpChang: [{
          required: true,
          message: '廠級/ 中心級不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitEmpBuji: [{
          required: true,
          message: '部級不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitEmpKeji: [{
          required: true,
          message: '課級不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitGroupDate: [{
          required: true,
          message: '入集團日期不能為空',
          trigger: 'change'
        }],
        entRecruitSalaryItems_recruitEmpEducation: [{
          required: true,
          message: '學歷不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitTrialPosition: [{
          required: true,
          message: '試用資位不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitTrialJob: [{
          required: true,
          message: '試用職務不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitTrialSalary: [{
          required: true,
          message: '試用標準薪資a不能為空',
          trigger: 'blur'
        }, {
          pattern: /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/,
          message: '請輸入數字！',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitTrialBuxiang: [{
          required: true,
          message: '試用專案補項不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitStaffPosition: [{
          required: true,
          message: '資位不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitStaffJob: [{
          required: true,
          message: '職務不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitStaffQuota: [{
          required: true,
          message: '調整 額度 b不能為空',
          trigger: 'blur'
        }, {
          pattern: /^(([0-9]+)|([0-9]+\.[0-9]{1,2}))$/,
          message: '最多保留倆位小數，請檢查輸入信息！',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitStaffSalary: [{
          required: true,
          message: '標準薪資=a+b不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitStaffAppraisal: [{
          required: true,
          message: '考核等級不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitStaffRange: [{
          required: true,
          message: '級距範圍不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitStaffBuxiang: [{
          required: true,
          message: '專案補項不能為空',
          trigger: 'blur'
        }],
        entRecruitSalaryItems_recruitEffectiveDate: [{
          required: true,
          message: '生效日期不能為空',
          trigger: 'change'
        }],
        cbchargeno: [{
          required: true,
          message: '承辦不能為空',
          trigger: 'change'
        }],
        shenhechargeno: [{
          required: true,
          message: '審核不能為空',
          trigger: 'change'
        }],
        hezhunchargeno: [{
          required: true,
          message: '核准不能為空',
          trigger: 'change'
        }],
      },
      tableHeight: document.documentElement.scrollHeight - 245 + "px",
      // 审批意见数据
      commentList: [],
      //簽核路徑
      signPath: [],
      //任务图url
      imgUrl: '',
      isDisable: false,
      // 是否显示任务图
      showImgDialog: false,
      auditStatus: [],
      // 文件上传参数
      uploadEntRecruitSalaryItems: {
        // 是否显示弹出层
        open: false,
        fileList: [],
        fileNameList: [],
        // 弹出层标题
        title: "",
        multiple: false,
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: this.getHeader(),
        // 上传的地址
        url: this.getAppBaseApi() + "/caaesign/entRecruitSalary/importEntRecruitSalaryItems"
      },
      recruitFiletypeOptions: [],
      recruitUrgentOptions: [],
      entRecruitSalaryItemsDragTableMobileClientWidth: 0,
      isMobile: false,
      labelPosition: 'left',
    }
  },
  computed: {},
  watch: {},
  created() {
    const id = this.$route.query.id;
    this.changeTagsView(this.$route.query);
    this.isMobile = this.isMobileFun()
    if (this.isMobile) {
      this.labelPosition = 'top'
    }
    getSignConfigList().then(response => {
      if (response.code === 0) {
        response.data.forEach(element => {
          const colKey = element.colKey.split('_')
          if (colKey.length > 1) {
            if (this.rules[colKey[0]] && colKey[1] === 'required') {
              this.rules[colKey[0]][0]['required'] = element.colValue === 'true'
              this.formConfigData[element.colKey] = element.colValue === 'true'
            }
            else {
              this.formConfigData[element.colKey] = element.colValue
            }
          }
          else {
            this.formConfigData[element.colKey] = element.colValue
          }
        });
      }
    })
    if (id != null && id != undefined) {
      getEntRecruitSalary(id).then(response => {
        this.formData = response.data;
        this.checkBoxParse()
        this.cascaderParse()
        this.timeRangParse()
        if (!this.formData.entRecruitSalaryItemsLists) {
          this.formData.entRecruitSalaryItemsLists = []
        }
        taskComment(this.formData.processId).then(response => {
          this.commentList = response.data;
        });
        getSignPath(this.formData.processId).then(response => {
          this.signPath = response.data;
        });
        if (this.formData.attachids) {
          let a = this.formData.attachids.split(',');
          if (a.length > 0) {
            a.forEach(item => {
              if (item) {
                getByKey(item).then(response => {
                  this.upload.fileList.push({
                    name: response.data.orignalName,
                    url: response.data.name
                  });
                })
              }
            })
          }
        }
        this.getRecruitFiletypeOptions()
        this.getRecruitUrgentOptions()
        this.getDicts("recruit_filetype").then(response => {
          if (this.formData.recruitFiletype) {
            this.formData.recruitFiletypeValue = JSON.parse(JSON.stringify(this.formData
              .recruitFiletype))
            response.data.forEach(item => {
              if (this.formData.recruitFiletype) {
                this.formData.recruitFiletypeValue = this.formData.recruitFiletypeValue.replace(
                  item.value, item.label);
              }
            })
            this.$set(this.formData, this.formData.recruitFiletypeValue, this.formData
              .recruitFiletypeValue)
          }
        });
        this.getDicts("recruit_urgent").then(response => {
          if (this.formData.recruitUrgent) {
            this.formData.recruitUrgentValue = JSON.parse(JSON.stringify(this.formData.recruitUrgent))
            response.data.forEach(item => {
              if (this.formData.recruitUrgent) {
                this.formData.recruitUrgentValue = this.formData.recruitUrgentValue.replace(item
                  .value, item.label);
              }
            })
            this.$set(this.formData, this.formData.recruitUrgentValue, this.formData
              .recruitUrgentValue)
          }
        });
      });
    }
    this.getDicts("sign_status").then(response => {
      this.auditStatus = response.data;
    });
  },
  mounted() {
    this.$nextTick(function() {
      if (this.isMobile) {
        this.entRecruitSalaryItemsDragTableMobileClientWidth = this.$refs
          .entRecruitSalaryItemsDragTableMobile.$el.clientWidth
      }
    })
  },
  methods: {
    closeForm() {
      //关闭子页面
      if (this.$store.state.settings.tagsView) {
        this.$router.go(-1) // 返回
        this.$store.state.tagsView.visitedViews.splice(this.$store.state.tagsView.visitedViews.findIndex(
          item => item.path === this.$route.path), 1)
        this.$router.push(this.$store.state.tagsView.visitedViews[this.$store.state.tagsView.visitedViews
          .length - 1].path)
      }
      else {
        parent.postMessage("closeCurrentTabMessage", '*');
      }
    },
    handleTrack() {
      this.imgUrl = this.getAppBaseApi() + '/activiti/task/track/' + this.formData.processId,
        this.showImgDialog = true
    },
    statusFormat(row, column) {
      return this.selectDictLabel(this.auditStatus, row.status);
    },
    handleTask: function(pass) {
      this.$refs["elForm"].validate(valid => {
        this.isDisable = true;
        if (pass == 0 || (pass == 1 && this.formData.comment != null)) {
          this.formData.pass = pass
          checkTask(this.formData).then(response => {
            if (response.code === 0) {
              this.msgSuccess(this.$t('tips.operationSuccessful'));
              this.closeForm();
            }
            else {
              this.msgError(response.msg);
            }
            this.isDisable = false;
          });
        }
        else {
          this.isDisable = false;
          this.msgInfo(this.$t('table.activity.inputApprovalOpinions'));
        }
      });
    },
    getRecruitFiletypeOptions() {
      this.getDicts("recruit_filetype").then(response => {
        this.recruitFiletypeOptions = response.data;
      });
    },
    getRecruitUrgentOptions() {
      this.getDicts("recruit_urgent").then(response => {
        this.recruitUrgentOptions = response.data;
      });
    },
    checkBoxParse() {},
    cascaderParse() {},
    timeRangParse() {
      for (let index in this.formData.entRecruitSalaryItemsLists) {
        if (this.formData.entRecruitSalaryItemsLists[index].recruitTrialTimeperiod) {
          this.formData.entRecruitSalaryItemsLists[index].recruitTrialTimeperiod = JSON.parse(this.formData
            .entRecruitSalaryItemsLists[index].recruitTrialTimeperiod)
        }
        if (this.formData.entRecruitSalaryItemsLists[index].recruitStaffTimeperiod) {
          this.formData.entRecruitSalaryItemsLists[index].recruitStaffTimeperiod = JSON.parse(this.formData
            .entRecruitSalaryItemsLists[index].recruitStaffTimeperiod)
        }
      }
    },
    handleAddEntRecruitSalaryItems() {
      const cloumn = {
        recruitEmpNo: '',
        recruitEmpNam: '',
        recruitEmpPlant: '',
        recruitEmpZongchu: '',
        recruitEmpChu: '',
        recruitEmpJineng: '',
        recruitEmpChang: '',
        recruitEmpBuji: '',
        recruitEmpKeji: '',
        recruitGroupDate: '',
        recruitEmpEducation: '',
        recruitTrialPosition: '',
        recruitTrialJob: '',
        recruitTrialSalary: '',
        recruitTrialBuxiang: '',
        recruitStaffPosition: '',
        recruitStaffJob: '',
        recruitTrialTimeperiod: [],
        recruitStaffQuota: '',
        recruitStaffSalary: '',
        recruitStaffAppraisal: '',
        recruitStaffRange: '',
        recruitStaffBuxiang: '',
        recruitStaffTimeperiod: [],
        recruitEffectiveDate: ''
      };
      this.formData.entRecruitSalaryItemsLists.splice(this.formData.entRecruitSalaryItemsLists.length, 0,
        cloumn);
      for (let index in this.formData.entRecruitSalaryItemsLists) {
        this.formData.entRecruitSalaryItemsLists[index].sort = parseInt(index) + 1;
      }
    },
    handleDelEntRecruitSalaryItems(index, row) {
      let functionName = this.$t('ent_recruit_salary_075d399c2dcaa20f94d53895254e58a1.default.functionName');
      this.$confirm(this.$t('tips.deleteConfirm', ['iPEG試用期滿薪資異動匯總審批表從表', row.id]), this.$t('tips.warm'), {
        confirmButtonText: this.$t("common.confirmTrim"),
        cancelButtonText: this.$t("common.cancelTrim"),
        type: "warning",
      }).then(() => {
        this.formData.entRecruitSalaryItemsLists.splice(index, 1);
        this.msgSuccess(this.$t("tips.deleteSuccess"));
      }).catch(function(err) {
        console.log(err);
      });
    },
    // 文件上传成功处理
    uploadsuccesEntRecruitSalaryItems(response, file, fileList) {
      if (response.code === 0) {
        this.msgSuccess(this.$t('tips.importSuccess'));
        this.formData.entRecruitSalaryItemsLists = this.formData.entRecruitSalaryItemsLists.concat(response
          .data);
        response.data.forEach(item => {
          this.EntRecruitSalaryItems_recruitEmpNo_onchange(item)
        })
      }
      else {
        this.msgError(response.msg);
      }
    },
    handleChangeEntRecruitSalaryItems(file, fileList) {},
    handleExceedEntRecruitSalaryItems(file, fileList) {},
    handleRemoveEntRecruitSalaryItems(file, fileList) {
      this.$emit("delUploadImage", file.name);
      const index = this.upload.fileList.indexOf(file);
      this.upload.fileList.splice(index, 1);
      this.formData.attachids = this.formData.attachids.replace(file.url + ",", "");
      this.upload.fileNameList.push(file.url);
      // delFileInfo(file.url);
    },
    handlePreviewEntRecruitSalaryItems(file) {
      const queryParams = this.queryParams;
      exportEntRecruitSalaryItems(queryParams).then(response => {
        this.download(response.data);
      })
    },
    EntRecruitSalaryItems_recruitEmpNo_onchange(item) {
      this.getInfoUserByEmpno(item.recruitEmpNo).then(responseInfo => {
        if (responseInfo.code !== 0) {
          this.msgError(responseInfo.msg);
        }
        else {
          if (responseInfo.data != null) {
            item.recruitEmpNam = responseInfo.data.empname
            item.recruitEmpPlant = responseInfo.data.factoryid
          }
          else {
            item.recruitEmpNam = ''
            item.recruitEmpPlant = ''
          }
        }
      });
    },
    entRecruitSalaryItems_getSummaries(param) {
      const {
        columns,
        data
      } = param;
      const sums = [];
      const scales = [];
      const childAttributesMap = {};
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = this.$t('common.sumText');
          return;
        }
        if (column.property && childAttributesMap[column.property] && childAttributesMap[column.property]
          .sumMasterColumn) {
          scales[index] = 0;
          if (childAttributesMap[column.property].decimalScale) {
            scales[index] = childAttributesMap[column.property].decimalScale
          }
          const values = data.map(item => Number(item[column.property]));
          if (!values.every(value => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                var x = prev + curr
                return x
              }
              else {
                return prev
              }
            }, 0);
            sums[index] = sums[index].toFixed(scales[index])
            this.formData[childAttributesMap[column.property].sumMasterColumn] = sums[index]
          }
        }
      });
      return sums;
    },
    beforeUploadEntRecruitSalaryItems(file) {
      let isRightSize = file.size / 1024 / 1024 < 2
      if (!isRightSize) {
        this.$message.error('文件大小超过 2MB')
      }
      let isAccept = new RegExp('.xls,.xlsx'.replaceAll(',', '|')).test(file.name)
      if (!isAccept) {
        this.$message.error('应该选择.xls,.xlsx类型的文件')
      }
      return isRightSize && isAccept
    },
    recruitTrialTimeperiodEntRecruitSalaryItemsFormat(rowColumn) {
      let returnInfo = ""
      if (rowColumn) {
        returnInfo = JSON.parse(JSON.stringify(rowColumn)).join("  至  ")
      }
      return returnInfo
    },
    recruitStaffTimeperiodEntRecruitSalaryItemsFormat(rowColumn) {
      let returnInfo = ""
      if (rowColumn) {
        returnInfo = JSON.parse(JSON.stringify(rowColumn)).join("  至  ")
      }
      return returnInfo
    },
  }
}

</script>
<style scoped>
.del-handler {
  font-size: 20px;
  color: #ff4949;
}

.el-dialog {
  position: relative;
  margin: 0 auto 0px;
  background: #FFFFFF;
  border-radius: 2px;
  -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 50%;
  height: 60%;
}

.el-dialog__body {
  border-top: 1px solid #dcdfe6;
  border-bottom: 1px solid #dcdfe6;
  max-height: 85% !important;
  min-height: 70%;
  overflow-y: auto;
}

.el-slider>/deep/.el-slider__runway {
  margin: 15px 0;
}

.el-checkbox-group {
  font-size: inherit;
}

.el-color-picker {
  display: inherit;
}

/deep/.el-input-number--medium {
  width: 100%;
}

.talbe-name-style {
  font-size: 16px;
  font-weight: bold;
  color: #1DB8FF;
  line-height: 50px;
}

/deep/.el-scrollbar__view table td {
  padding: 0;
}

.el-form-item {
  margin-top: 5px;
  margin-bottom: 5px;
}

.ant-card {
  color: black;
}

.el-table {
  color: black;
}

.el-form-item.edit-item {
  margin-top: 15px;
  margin-bottom: 22px;
}

</style>
