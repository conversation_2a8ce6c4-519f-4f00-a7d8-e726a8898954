<template>
  <page-top-bar :title="formConfigData.title">
    <div class="mobile-content" slot="content">
      <div class="mobile-body">
        <el-form ref="elForm" :model="formData" size="medium" label-width="100px"
          :label-position="labelPosition">
          <div class="mobile-card" id="staffCard">
            <div class="head-pannel">
              <el-col :span="8" :xs="24" class="el-col-no-border">
                任務編碼：{{formData.serialno?formData.serialno:'提交表單后自動生成'}}
              </el-col>
              <el-col :span="8" :xs="24" class="el-col-no-border"> 填單時間：{{formData.createTime}}
              </el-col>
              <el-col :span="8" :xs="24" class="el-col-no-border">
                填單人：{{formData.makerNo +' / '+ formData.makerName}}
              </el-col>
            </div>
            <el-row :gutter="15">
              <div class="body-main">
                <el-col :span="12" :xs="24">
                  <el-form-item label-width="1px" label="" prop="groupUrgent">
                    {{formData.groupUrgent}}
                  </el-form-item>
                </el-col>
                <el-col :span="12" :xs="24">
                  <el-form-item label-width="1px" label="" prop="groupFiletype">
                    {{formData.groupFiletype}}
                  </el-form-item>
                </el-col>
                <el-col :span="12" :xs="24">
                  <el-form-item label="呈                  核：" prop="groupApproval">
                    {{formData.groupApproval}}
                  </el-form-item>
                </el-col>
                <el-col :span="12" :xs="24">
                  <el-form-item label=" 發文單位：" prop="groupDept">
                    {{formData.groupDept}}
                  </el-form-item>
                </el-col>
                <el-col :span="12" :xs="24">
                  <el-form-item label="會審單位：" prop="groupAudit">
                    {{formData.groupAudit}}
                  </el-form-item>
                </el-col>
                <el-col :span="12" :xs="24">
                  <el-form-item label="發文字號：" prop="groupIssue">
                    {{formData.groupIssue}}
                  </el-form-item>
                </el-col>
                <el-col :span="12" :xs="24">
                  <el-form-item label="受文單位：" prop="groupAddres">
                    {{formData.groupAddres}}
                  </el-form-item>
                </el-col>
                <el-col :span="12" :xs="24">
                  <el-form-item label="發文日期：" prop="groupDate">
                    {{formData.groupDate}}
                  </el-form-item>
                </el-col>
                <el-col :span="24" :xs="24">
                  <el-form-item label="主   旨：" prop="groupNote">
                    {{formData.groupNote}}
                  </el-form-item>
                </el-col>
                <el-col :span="24" :xs="24">
                  <label-form :label=formConfigData.beijingshuoming_label labelWidth='120'
                    labelPosition='left' labelSize='14' textSize='14'></label-form>
                </el-col>
                <el-col :span="24" :xs="24">
                  <el-form-item label-width="1px" label="" prop="groupDemand">
                    {{formData.groupDemand}}
                  </el-form-item>
                </el-col>
                <el-col :span="24" :xs="24">
                  <label-form :label=formConfigData.shixiang_label labelWidth='120' labelPosition='left'
                    labelSize='14' textSize='14'></label-form>
                </el-col>
                <el-col :span="24" :xs="24">
                  <el-form-item label-width="1px" label="" prop="groupMatters">
                    {{formData.groupMatters}}
                  </el-form-item>
                </el-col>
                <el-col :span="24" :xs="24">
                  <label-form :label=formConfigData.fujian_label labelWidth='120' labelPosition='left'
                    labelSize='14' textSize='14'></label-form>
                </el-col>
                <el-col :span="24" :xs="24">
                  <label-form :label=formConfigData.groupnode_label labelWidth='110' labelPosition='left'
                    labelSize='14' :textarea=formConfigData.groupnode_text textSize='14'></label-form>
                </el-col>
              </div>
              <el-col :span="24" :xs="24" class="print-hide-div btn-div">
                <div class="dialog-footer " align="center" style="padding-top: 5px;padding-bottom: 5px">
                  <el-button type="danger" @click="closeForm">{{$t('common.close')}}</el-button>
                  <el-button type="info" @click="handleTrack"
                    v-if="formData.workStatus == 2||formData.workStatus ==3||formData.workStatus ==4">
                    {{$t('table.activity.flowTracing')}}</el-button>
                </div>
              </el-col>
              <!-- 簽核線 -->
              <div class="body-main">
                <el-col :span="24" class="print-hide-div">
                  <el-collapse v-model="signPathActive">
                    <el-collapse-item title="簽核線" :name="1">
                      <el-steps direction="vertical" :active="activeCount" process-status="success"
                        style="margin: 10px 5px 0 10px;">
                        <el-step :title="item" v-for="(item, index) in signPath" :key="index"
                          style="height:30px;"></el-step>
                      </el-steps>
                    </el-collapse-item>
                  </el-collapse>
                </el-col>
                <!-- 审核记录 -->
                <el-table :data="commentList">
                  <el-table-column type="expand">
                    <template slot-scope="props">
                      <el-form label-position="left" inline class="demo-table-expand">
                        <el-form-item :label="$t('table.activity.taskId')">
                          <span>{{ props.row.id }}</span>
                        </el-form-item>
                        <el-form-item :label="$t('table.activity.approvedBy')">
                          <span>{{ props.row.userId }}</span>
                        </el-form-item>
                        <el-form-item :label="$t('table.activity.approvalOpinions')">
                          <span>{{ props.row.fullMessage }}</span>
                        </el-form-item>
                        <el-form-item :label="$t('table.activity.operIp')">
                          <span>{{ props.row.operIp }}</span>
                        </el-form-item>
                        <el-form-item :label="$t('table.activity.auditStatus')">
                          <span>{{ statusFormat(props.row) }}</span>
                        </el-form-item>
                        <el-form-item :label="$t('table.activity.approvalTime')">
                          <span>{{ parseTime(props.row.time) }}</span>
                        </el-form-item>
                      </el-form>
                    </template>
                  </el-table-column>
                  <el-table-column :label="$t('table.activity.approvedBy')" align="center" min-width="35"
                    prop="userId">
                  </el-table-column>
                  <el-table-column :label="$t('table.activity.auditStatus')" align="center" prop="status"
                    min-width="30" :formatter="statusFormat">
                  </el-table-column>
                  <el-table-column :label="$t('table.activity.approvalTime')" align="center" prop="time"
                    min-width="45" :formatter="parseTime(commentList.time)">
                  </el-table-column>
                </el-table>
              </div>
              <!-- 任务跟踪对话框 -->
              <el-dialog :title="$t('table.activity.taskMap')" :visible.sync="showImgDialog" width="100vw"
                :modal-append-to-body="false">
                <img :src="imgUrl" style="padding-bottom: 60px;width:100%">
              </el-dialog>
            </el-row>
          </div>
        </el-form>
      </div>
    </div>
  </page-top-bar>
</template>
<script>
import {
  getEntGroupLiaison,
  addEntGroupLiaison,
  editEntGroupLiaison,
  getSignPathApp,
  updateAndCheck,
  getSignConfigList
}
from "@/api/caaesign/entGroupLiaison"
import '@/assets/styles/design-build/design-add-view.scss'
import {
  listTask,
  getTask,
  checkTask,
  taskComment
}
from "@/api/activiti/task"
import {
  getAccessToken,
  getZltAccessToken
}
from "@/utils/auth";
import {
  previewFile,
  getHeader
}
from "@/utils/entfrm";
import {
  getOptionsAndValues
}
from "@/api/system/dictData";
import {
  listFileInfo,
  getFileInfo,
  delFileInfo,
  addFileInfo,
  editFileInfo,
  getByKey
}
from "@/api/system/fileInfo";
export default {
  components: {},
  props: [],
  data() {
    return {
      formData: {
        groupUrgent: undefined,
        groupFiletype: undefined,
        groupApproval: undefined,
        groupDept: undefined,
        groupAudit: undefined,
        groupIssue: undefined,
        groupAddres: undefined,
        groupDate: null,
        groupNote: undefined,
        groupDemand: undefined,
        groupMatters: undefined,
        attachids: "",
        cbchargeno: undefined,
        shenhechargeno: undefined,
        huishenchargeno: undefined,
        hezhunchargeno: undefined,
      },
      formConfigData: {
        title: "iPEBG事業群聯絡單",
        beijingshuoming_label: `一、背景說明：`,
        beijingshuoming_text: ``,
        shixiang_label: `二、申請事項：`,
        shixiang_text: ``,
        fujian_label: `三、相關附件：`,
        fujian_text: ``,
        groupnode_label: `表單固定編碼：`,
        groupnode_text: `行政-人資Ⅶ-行政-003-2024A      `,
        cbchargeno: "承辦",
        cbchargeno_required: true,
        shenhechargeno: "審核",
        shenhechargeno_required: true,
        huishenchargeno: "會審",
        huishenchargeno_required: true,
        hezhunchargeno: "核准",
        hezhunchargeno_required: true,
      },
      tableHeight: document.documentElement.scrollHeight - 245 + "px",
      // 审批意见数据
      commentList: [],
      //簽核路徑
      signPath: [],
      activeCount: -1,
      signPathActive: -1,
      //任务图url
      imgUrl: '',
      isDisabled: false,
      // 是否显示任务图
      showImgDialog: false,
      auditStatus: [],
      // 文件上传参数
      upload: {
        // 是否显示弹出层
        open: false,
        fileList: [],
        fileNameList: [],
        // 弹出层标题
        title: "",
        multiple: false,
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: this.getHeader(),
        // 上传的地址
        url: this.getAppBaseApi() + "/system/fileInfo/upload"
      },
      groupUrgentOptions: [],
      groupUrgentValue: null,
      groupFiletypeOptions: [],
      groupFiletypeValue: null,
      isMobile: true,
      labelPosition: 'left',
    }
  },
  computed: {},
  watch: {},
  created() {
    const id = this.$route.query.id;
    this.changeTagsView(this.$route.query);
    this.isMobile = true
    getSignConfigList().then(response => {
      if (response.code === 0) {
        response.data.forEach(element => {
          const colKey = element.colKey.split('_')
          if (colKey.length > 1) {
            if (this.rules[colKey[0]] && colKey[1] === 'required') {
              this.rules[colKey[0]][0]['required'] = element.colValue === 'true'
              this.formConfigData[element.colKey] = element.colValue === 'true'
            }
            else {
              this.formConfigData[element.colKey] = element.colValue
            }
          }
          else {
            this.formConfigData[element.colKey] = element.colValue
          }
        });
      }
    })
    if (id != null && id != undefined) {
      getEntGroupLiaison(id).then(response => {
        this.formData = response.data;
        taskComment(this.formData.processId).then(response => {
          this.commentList = response.data;
        });
        getSignPathApp(this.formData.processId).then(response => {
          this.signPath = response.data.info;
          this.activeCount = response.data.activeCount;
        });
        if (this.formData.attachids) {
          let a = this.formData.attachids.split(',');
          if (a.length > 0) {
            a.forEach(item => {
              if (item) {
                getByKey(item).then(response => {
                  this.upload.fileList.push({
                    name: response.data.orignalName,
                    url: response.data.name
                  });
                })
              }
            })
          }
        }
        this.getDicts("group_urgent").then(response => {
          response.data.forEach(item => {
            if (this.formData.groupUrgent && this.formData.groupUrgent != "null") {
              this.formData.groupUrgent = this.formData.groupUrgent.replace(item.value, item
                .label);
            }
          })
        });
        this.getDicts("group_filetype").then(response => {
          response.data.forEach(item => {
            if (this.formData.groupFiletype && this.formData.groupFiletype != "null") {
              this.formData.groupFiletype = this.formData.groupFiletype.replace(item.value, item
                .label);
            }
          })
        });
      });
    }
    this.getDicts("sign_status").then(response => {
      this.auditStatus = response.data;
    });
  },
  methods: {
    closeForm() {
      //关闭子页面
      this.$router.go(-1) // 返回
    },
    handleTrack() {
      this.imgUrl = this.getAppBaseApi() + '/activiti/task/track/' + this.formData.processId,
        this.showImgDialog = true
    },
    statusFormat(row, column) {
      return this.selectDictLabel(this.auditStatus, row.status);
    },
    attachidsBeforeUpload(file) {
      let isRightSize = file.size / 1024 / 1024 < 2
      if (!isRightSize) {
        this.$message.error('文件大小超过 2MB')
      }
      return isRightSize
    },
    // 文件上传成功处理
    uploadsucces(response, file, fileList) {
      if (response.data.name != undefined && response.data.name != "undefined") {
        this.upload.fileList.push({
          name: file.name,
          url: response.data.name
        });
        this.formData.attachids += response.data.name + ",";
      }
    },
    handleChange(file, fileList) {},
    handleExceed(file, fileList) {},
    handleRemove(file, fileList) {
      this.$emit("delUploadImage", file.name);
      const index = this.upload.fileList.indexOf(file);
      this.upload.fileList.splice(index, 1);
      if (this.formData.attachids && this.formData.attachids != "null") {
        this.formData.attachids = this.formData.attachids.replace(file.url + ",", "");
      }
      this.upload.fileNameList.push(file.url);
      // delFileInfo(file.url);
    },
    handlePreview(file) {
      previewFile(file.url)
    },
    checkBoxParse() {},
    cascaderParse() {},
  }
}

</script>
<style rel="stylesheet/scss" lang="scss" scoped>
@import "~@/assets/styles/mobileSkin/mobileMixin.scss";

.el-upload__tip {
  line-height: 1.2;
}

.del-handler {
  font-size: 20px;
  color: #ff4949;
}

.el-dialog {
  position: relative;
  margin: 0 auto 0px;
  background: #FFFFFF;
  border-radius: 2px;
  -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 50%;
  height: 60%;
}

.el-dialog__body {
  border-top: 1px solid #dcdfe6;
  border-bottom: 1px solid #dcdfe6;
  max-height: 85% !important;
  min-height: 70%;
  overflow-y: auto;
}

.el-slider>/deep/.el-slider__runway {
  margin: 15px 0;
}

.el-checkbox-group {
  font-size: inherit;
}

.el-color-picker {
  display: inherit;
}

/deep/.demo-table-expand {
  font-size: 0;
}

/deep/.demo-table-expand label {
  width: 90px;
  color: #99a9bf;
}

/deep/.demo-table-expand/deep/.el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  width: 100%;
}

/deep/.el-input-number--medium {
  width: 100%;
}

.talbe-name-style {
  font-size: 16px;
  font-weight: bold;
  line-height: 50px;
}

/deep/.el-collapse-item__content {
  padding-bottom: 0;
}

/deep/.el-step__head.is-finish,
/deep/.el-step__title.is-finish {
  color: #C0C4CC;
  border-color: #C0C4CC;
}

/deep/.el-step__head.is-wait,
/deep/.el-step__title.is-wait {
  color: #000000;
  border-color: #000000;
}

/deep/.el-step__head.is-success,
/deep/.el-step__title.is-success {
  color: blue;
  border-color: blue;
}

.mobile-content {
  background: #EEEEEE;
}

.mobile-card {
  border: 0;
  background: transparent;
}

#staffCard {
  border: 0;
}

.mobile-body {
  padding: 0;
}

/deep/input,
/deep/textarea {
  border-color: transparent;
}

.head-pannel {
  background: #ffffff;
  padding: 15px;
  margin-top: 5px;
  color: #6B6B6B;
}

.head-pannel:after {
  clear: both;
  content: "";
  display: block;
}

.head-pannel>div {
  font-size: 14px;
  padding: 5px;
}

/deep/.el-row {
  margin-left: 0;
  margin-right: 0;
}

.body-main .el-form-item {
  margin-top: 15px;
  margin-bottom: 15px;
  border-color: transparent;
}

.el-table__expanded-cell .el-form-item {
  margin: 0;
}

.el-form-item>label {
  padding-left: 10px;
}

.body-main .el-col {
  border-bottom: 1px solid #EAEAEA;
}

.body-main {
  background: #ffffff;
  padding: 15px 20px;
  margin-top: 8px;
}

.body-main:after {
  clear: both;
  content: "";
  display: block;
}

.child-item {
  margin-top: 8px;
  background: #ffffff;
}

.child-item:after {
  clear: both;
  content: "";
  display: block;
}

.child-item /deep/.el-form-item {
  margin: 15px 10px;
  padding-bottom: 15px;
  border-color: transparent;
  border-bottom: 1px solid #EAEAEA;
}

.talbe-name-style {
  padding-left: 15px;
}

.sign-content {
  background: #ffffff;
  margin-top: 8px;
  color: #000000;
}

.sign-content:after {
  clear: both;
  content: "";
  display: block;
}

.sign-point>.el-form-item {
  padding: 5px 10px;
  margin-bottom: 0;
}

.btn-div {
  background: white;
  padding: 10px;
  margin: 10px 0;
  float: none;
}

.el-message-box__btns button:nth-child(2) {
  margin-left: 0;
}

.el-slider {
  /deep/.el-slider__bar {
    @include backgroundColor('themeMainColor');
  }
  
  /deep/.el-slider__button-wrapper {
    .el-slider__button {
      @include borderColor('themeMainColor');
    }
  }
}

.el-radio.is-checked {
  /deep/.el-radio__input.is-checked {
    .el-radio__inner {
      @include borderColor('themeMainColor');
      @include backgroundInfo('themeMainColor');
    }
  }
  
  /deep/.el-radio__input.is-checked+.el-radio__label {
    @include fontColor('themeMainColor');
  }
}

.el-checkbox.is-checked {
  /deep/.el-checkbox__input.is-checked {
    .el-checkbox__inner {
      @include borderColor('themeMainColor');
      @include backgroundInfo('themeMainColor');
    }
  }
  
  /deep/.el-checkbox__input.is-checked+.el-checkbox__label {
    @include fontColor('themeMainColor');
  }
}

.el-switch.is-checked {
  /deep/.el-switch__core {
    @include borderColor('themeMainColor');
    @include backgroundInfo('themeMainColor');
  }
}

.cube-popup {
  /deep/.cube-picker-confirm {
    @include fontColor('themeMainColor');
  }
}

.child-item {
  /deep/.el-button--primary {
    @include borderColor('themeMainColor');
    @include backgroundInfo('themeMainColor');
  }
  
  /deep/.del-handler {
    @include fontColor('themeMainColor');
  }
}

.dialog-footer {
  .el-button--success {
    @include backgroundInfo('themeMainColor');
    @include borderColor('themeMainColor');
  }
  
  .el-button--info {
    @include backgroundInfo('themeMainColor');
    @include borderColor('themeMainColor');
  }
}

</style>
