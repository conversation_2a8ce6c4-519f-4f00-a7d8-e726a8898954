<template>
  <page-top-bar :title="formConfigData.title">
    <div class="mobile-content" slot="content">
      <div class="mobile-body">
        <el-form ref="elForm" class="ant-form ant-form-horizontal" :model="formData" :rules="rules"
          size="medium" label-width="100px" :label-position="labelPosition">
          <div class="ant-card ant-card-bordered" id="staffCard">
            <div class="ant-card-body">
              <el-col :span="8" :xs="24" class="el-col-no-border">
                任務編碼：{{formData.serialno?formData.serialno:'提交表單后自動生成'}}
              </el-col>
              <el-col :span="8" :xs="24" class="el-col-no-border"> 填單時間：{{formData.createTime}}
              </el-col>
              <el-col :span="8" :xs="24" class="el-col-no-border">
                填單人：{{formData.makerNo +' / '+ formData.makerName}}
              </el-col>
              <el-row :gutter="15">
                <el-col :span="12" :xs="24">
                  <el-form-item label-width="1px" label="" prop="groupUrgent">
                    <el-radio-group v-model="formData.groupUrgent" size="medium">
                      <el-radio v-for="(item, index) in groupUrgentOptions" :key="index" :label="item.value"
                        :disabled="item.disabled">{{item.label}}</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="12" :xs="24">
                  <el-form-item label-width="1px" label="" prop="groupFiletype">
                    <el-radio-group v-model="formData.groupFiletype" size="medium">
                      <el-radio v-for="(item, index) in groupFiletypeOptions" :key="index" :label="item.value"
                        :disabled="item.disabled">{{item.label}}</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="12" :xs="24">
                  <el-form-item label="呈                  核：" prop="groupApproval">
                    <el-input v-model="formData.groupApproval" placeholder="請輸入呈核呈核" clearable
                      :style="{width: '100%'}"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12" :xs="24">
                  <el-form-item label=" 發文單位：" prop="groupDept">
                    <el-input v-model="formData.groupDept" placeholder="請輸入 發文單位" clearable
                      :style="{width: '100%'}"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12" :xs="24">
                  <el-form-item label="會審單位：" prop="groupAudit">
                    <el-input v-model="formData.groupAudit" placeholder="請輸入會審單位" clearable
                      :style="{width: '100%'}"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12" :xs="24">
                  <el-form-item label="發文字號：" prop="groupIssue">
                    <el-input v-model="formData.groupIssue" placeholder="請輸入發文字號" clearable
                      :style="{width: '100%'}"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12" :xs="24">
                  <el-form-item label="受文單位：" prop="groupAddres">
                    <el-input v-model="formData.groupAddres" placeholder="請輸入受文單位" clearable
                      :style="{width: '100%'}"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12" :xs="24">
                  <el-form-item label="發文日期：" prop="groupDate">
                    <el-input placeholder="请選擇發文日期" prefix-icon="el-icon-date" readonly
                      v-model="groupDateValue" @focus="groupDateShowDate"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="24" :xs="24">
                  <el-form-item label="主   旨：" prop="groupNote">
                    <el-input v-model="formData.groupNote" placeholder="請輸入主旨" clearable
                      :style="{width: '100%'}"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="24" :xs="24">
                  <label-form :label=formConfigData.beijingshuoming_label labelWidth='120'
                    labelPosition='left' labelSize='14' textSize='14'></label-form>
                </el-col>
                <el-col :span="24" :xs="24">
                  <el-form-item label-width="1px" label="" prop="groupDemand">
                    <el-input v-model="formData.groupDemand" type="textarea" placeholder="請輸入背景說明"
                      :autosize="{minRows: 4, maxRows: 4}" :style="{width: '100%'}"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="24" :xs="24">
                  <label-form :label=formConfigData.shixiang_label labelWidth='120' labelPosition='left'
                    labelSize='14' textSize='14'></label-form>
                </el-col>
                <el-col :span="24" :xs="24">
                  <el-form-item label-width="1px" label="" prop="groupMatters">
                    <el-input v-model="formData.groupMatters" type="textarea" placeholder="請輸入申請事項"
                      :autosize="{minRows: 4, maxRows: 4}" :style="{width: '100%'}"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="24" :xs="24">
                  <label-form :label=formConfigData.fujian_label labelWidth='120' labelPosition='left'
                    labelSize='14' textSize='14'></label-form>
                </el-col>
                <el-col :span="24" :xs="24">
                  <label-form :label=formConfigData.groupnode_label labelWidth='110' labelPosition='left'
                    labelSize='14' :textarea=formConfigData.groupnode_text textSize='14'></label-form>
                </el-col>
                <el-col :span="6" :xs="24" class="sign-point">
                  <el-form-item prop="cbchargeno" label-width="0">
                    <entfrm-sign-form-add-hq :label="formConfigData.cbchargeno"
                      :required='formConfigData.cbchargeno_required'
                      :selectEmpProp="{empNo:this.formData.cbchargeno,empName:this.formData.cbchargename}"
                      model-no="cbchargeno" model-name="cbchargename" @onSignFormSelected="onSignFormSelected"
                      :isMobile=this.isMobile></entfrm-sign-form-add-hq>
                  </el-form-item>
                </el-col>
                <el-col :span="6" :xs="24" class="sign-point">
                  <el-form-item prop="shenhechargeno" label-width="0">
                    <entfrm-sign-form-add-hq :label="formConfigData.shenhechargeno"
                      :required='formConfigData.shenhechargeno_required'
                      :selectEmpProp="{empNo:this.formData.shenhechargeno,empName:this.formData.shenhechargename}"
                      model-no="shenhechargeno" model-name="shenhechargename"
                      @onSignFormSelected="onSignFormSelected" :isMobile=this.isMobile>
                    </entfrm-sign-form-add-hq>
                  </el-form-item>
                </el-col>
                <el-col :span="6" :xs="24" class="sign-point">
                  <el-form-item prop="huishenchargeno" label-width="0">
                    <entfrm-sign-form-add-hq :label="formConfigData.huishenchargeno"
                      :required='formConfigData.huishenchargeno_required'
                      :selectEmpProp="{empNo:this.formData.huishenchargeno,empName:this.formData.huishenchargename}"
                      model-no="huishenchargeno" model-name="huishenchargename"
                      @onSignFormSelected="onSignFormSelected" :isMobile=this.isMobile>
                    </entfrm-sign-form-add-hq>
                  </el-form-item>
                </el-col>
                <el-col :span="6" :xs="24" class="sign-point">
                  <el-form-item prop="hezhunchargeno" label-width="0">
                    <entfrm-sign-form-add-hq :label="formConfigData.hezhunchargeno"
                      :required='formConfigData.hezhunchargeno_required'
                      :selectEmpProp="{empNo:this.formData.hezhunchargeno,empName:this.formData.hezhunchargename}"
                      model-no="hezhunchargeno" model-name="hezhunchargename"
                      @onSignFormSelected="onSignFormSelected" :isMobile=this.isMobile>
                    </entfrm-sign-form-add-hq>
                  </el-form-item>
                </el-col>
                <el-col :span="24" :xs="24">
                  <div class="dialog-footer" align="center" style="padding-top: 5px;padding-bottom: 5px">
                    <el-button type="success" @click="handleSubmit()">
                      {{$t('table.activity.reSubmit')}}
                    </el-button>
                    <el-button type="primary" @click="submitForm">{{$t('common.save')}}</el-button>
                    <el-button @click="handleTask(9)">{{$t('table.activity.cancle')}}</el-button>
                    <el-button @click="closeForm">{{$t('common.close')}}</el-button>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>
        </el-form>
      </div>
    </div>
  </page-top-bar>
</template>
<script>
import {
  getEntGroupLiaison,
  addEntGroupLiaison,
  editEntGroupLiaison,
  getSignPathApp,
  editEntGroupLiaisonAndResubmitProcess,
  getSignConfigList
}
from "@/api/caaesign/entGroupLiaison"
import '@/assets/styles/design-build/design-add-view.scss'
import {
  listTask,
  getTask,
  checkTask,
  taskComment
}
from "@/api/activiti/task"
import {
  getAccessToken,
  getZltAccessToken
}
from "@/utils/auth";
import {
  previewFile,
  getHeader
}
from "@/utils/entfrm";
import {
  getOptionsAndValues
}
from "@/api/system/dictData";
import {
  listFileInfo,
  getFileInfo,
  delFileInfo,
  addFileInfo,
  editFileInfo,
  getByKey
}
from "@/api/system/fileInfo";
export default {
  components: {},
  props: [],
  data() {
    return {
      formData: {
        groupUrgent: undefined,
        groupFiletype: undefined,
        groupApproval: undefined,
        groupDept: undefined,
        groupAudit: undefined,
        groupIssue: undefined,
        groupAddres: undefined,
        groupDate: null,
        groupNote: undefined,
        groupDemand: undefined,
        groupMatters: undefined,
        attachids: "",
        cbchargeno: undefined,
        shenhechargeno: undefined,
        huishenchargeno: undefined,
        hezhunchargeno: undefined,
        dataSource: "app",
        makerNo: this.$store.state.user.empNo,
        makerName: this.$store.state.user.name,
      },
      formConfigData: {
        title: "iPEBG事業群聯絡單",
        beijingshuoming_label: `一、背景說明：`,
        beijingshuoming_text: ``,
        shixiang_label: `二、申請事項：`,
        shixiang_text: ``,
        fujian_label: `三、相關附件：`,
        fujian_text: ``,
        groupnode_label: `表單固定編碼：`,
        groupnode_text: `行政-人資Ⅶ-行政-003-2024A      `,
        cbchargeno: "承辦",
        cbchargeno_required: true,
        shenhechargeno: "審核",
        shenhechargeno_required: true,
        huishenchargeno: "會審",
        huishenchargeno_required: true,
        hezhunchargeno: "核准",
        hezhunchargeno_required: true,
      },
      tableHeight: document.documentElement.scrollHeight - 245 + "px",
      rules: {
        groupUrgent: [{
          required: true,
          message: '不能為空',
          trigger: 'change'
        }],
        groupFiletype: [{
          required: true,
          message: '不能為空',
          trigger: 'change'
        }],
        groupApproval: [{
          required: true,
          message: '請輸入呈核呈核',
          trigger: 'blur'
        }],
        groupDept: [{
          required: true,
          message: '請輸入 發文單位',
          trigger: 'blur'
        }],
        groupAudit: [{
          required: true,
          message: '請輸入會審單位',
          trigger: 'blur'
        }],
        groupIssue: [{
          required: true,
          message: '請輸入發文字號',
          trigger: 'blur'
        }],
        groupAddres: [{
          required: true,
          message: '請輸入受文單位',
          trigger: 'blur'
        }],
        groupDate: [{
          required: true,
          message: '请選擇發文日期',
          trigger: 'change'
        }],
        groupNote: [{
          required: true,
          message: '請輸入主旨',
          trigger: 'blur'
        }],
        groupDemand: [{
          required: true,
          message: '請輸入背景說明',
          trigger: 'blur'
        }],
        groupMatters: [{
          required: true,
          message: '請輸入申請事項',
          trigger: 'blur'
        }],
        attachids: [],
        cbchargeno: [{
          required: true,
          message: '承辦不能為空',
          trigger: 'change'
        }],
        shenhechargeno: [{
          required: true,
          message: '審核不能為空',
          trigger: 'change'
        }],
        huishenchargeno: [{
          required: true,
          message: '會審不能為空',
          trigger: 'change'
        }],
        hezhunchargeno: [{
          required: true,
          message: '核准不能為空',
          trigger: 'change'
        }],
      },
      // 文件上传参数
      upload: {
        // 是否显示弹出层
        open: false,
        fileList: [],
        fileNameList: [],
        // 弹出层标题
        title: "",
        multiple: false,
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: this.getHeader(),
        // 上传的地址
        url: this.getAppBaseApi() + "/system/fileInfo/upload"
      },
      groupUrgentOptions: [],
      groupUrgentValue: null,
      groupFiletypeOptions: [],
      groupFiletypeValue: null,
      isMobile: true,
      labelPosition: 'right',
      groupDateValue: undefined,
      groupDateDateValue: [{
        is: 'cube-date-picker',
        title: '發文日期：',
        format: {
          year: 'YYYY',
          month: 'MM',
          date: 'DD'
        },
        value: new Date(),
        swipeTime: 1000,
        startColumn: 'year',
      }],
      alias: "postgresql$_$pghrsign_ipebg_test$_$075d399c2dcaa20f94d53895254e58a1%_%design"
    }
  },
  computed: {},
  watch: {},
  created() {
    const id = this.$route.query.id;
    this.changeTagsView(this.$route.query);
    this.isMobile = true
    if (this.isMobile) {
      this.labelPosition = 'left'
    }
    getSignConfigList().then(response => {
      if (response.code === 0) {
        response.data.forEach(element => {
          const colKey = element.colKey.split('_')
          if (colKey.length > 1) {
            if (this.rules[colKey[0]] && colKey[1] === 'required') {
              this.rules[colKey[0]][0]['required'] = element.colValue === 'true'
              this.formConfigData[element.colKey] = element.colValue === 'true'
            }
            else {
              this.formConfigData[element.colKey] = element.colValue
            }
          }
          else {
            this.formConfigData[element.colKey] = element.colValue
          }
        });
      }
    })
    if (id != null && id != undefined) {
      getEntGroupLiaison(id).then(response => {
        this.formData = response.data;
        this.checkBoxParse()
        this.cascaderParse()
        this.timeRangParse()
        if (this.formData.attachids) {
          let a = this.formData.attachids.split(',');
          if (a.length > 0) {
            a.forEach(item => {
              if (item) {
                getByKey(item).then(response => {
                  this.upload.fileList.push({
                    name: response.data.orignalName,
                    url: response.data.name
                  });
                })
              }
            })
          }
        }
        this.getGroupUrgentOptions()
        this.getGroupFiletypeOptions()
      });
    }
    if (id == null || id == undefined) {
      this.getGroupUrgentOptions()
      this.getGroupFiletypeOptions()
    }
  },
  mounted() {},
  methods: {
    submitForm() {
      this.$refs['elForm'].validate(valid => {
        if (valid) {
          const formData = JSON.parse(JSON.stringify(this.formData))
          if (formData.id != undefined) {
            editEntGroupLiaison(formData).then(response => {
              if (response.code === 0) {
                this.$message({
                  showClose: true,
                  message: this.$t('tips.updateSuccess'),
                  type: "success",
                  offset: 50
                });
                this.closeForm();
              }
              else {
                this.msgError(response.msg);
              }
            });
          }
          else {
            addEntGroupLiaison(formData).then(response => {
              if (response.code === 0) {
                this.$message({
                  showClose: true,
                  message: this.$t('tips.createSuccess'),
                  type: "success",
                  offset: 50
                });
                this.closeForm();
              }
              else {
                this.msgError(response.msg);
              }
            });
          }
          /**
           * 提交的時候刪除被標記的需要刪除的附件
           */
          if (this.formData.attachids) {
            this.upload.fileNameList.forEach(item => {
              if (item) {
                delFileInfo(item);
              }
            })
          }
        }
      })
    },
    handleSubmit: function() {
      this.$refs["elForm"].validate(valid => {
        if (valid) {
          const formData = JSON.parse(JSON.stringify(this.formData))
          if (formData.id != undefined) {
            editEntGroupLiaisonAndStartProcess(formData).then(response => {
              if (response.code === 0) {
                this.$message({
                  showClose: true,
                  message: this.$t('tips.updateSuccess'),
                  type: "success",
                  offset: 50
                });
                this.closeForm();
              }
              else {
                this.msgError(response.msg);
              }
            });
          }
          else {
            addEntGroupLiaisonAndStartProcess(formData).then(response => {
              if (response.code === 0) {
                this.$message({
                  showClose: true,
                  message: this.$t('tips.createSuccess'),
                  type: "success",
                  offset: 50
                });
                this.closeForm();
              }
              else {
                this.msgError(response.msg);
              }
            });
          }
          /**
           * 提交的時候刪除被標記的需要刪除的附件
           */
          if (this.formData.attachids) {
            this.upload.fileNameList.forEach(item => {
              if (item) {
                delFileInfo(item);
              }
            })
          }
        }
      })
    },
    resetForm() {
      this.$refs['elForm'].resetFields()
    },
    closeForm() {
      //关闭子页面
      this.$router.go(-1) // 返回
    },
    getGroupUrgentOptions() {
      this.getDicts("group_urgent").then(response => {
        this.groupUrgentOptions = response.data;
      });
    },
    getGroupFiletypeOptions() {
      this.getDicts("group_filetype").then(response => {
        this.groupFiletypeOptions = response.data;
      });
    },
    groupDateShowDate() {
      this.groupDateSegmentPicker.show()
    },
    attachidsBeforeUpload(file) {
      let isRightSize = file.size / 1024 / 1024 < 2
      if (!isRightSize) {
        this.$message.error('文件大小超过 2MB')
      }
      return isRightSize
    },
    // 文件上传成功处理
    uploadsucces(response, file, fileList) {
      if (response.data.name != undefined && response.data.name != "undefined") {
        this.upload.fileList.push({
          name: file.name,
          url: response.data.name
        });
        this.formData.attachids += response.data.name + ",";
      }
    },
    handleChange(file, fileList) {},
    handleExceed(file, fileList) {},
    handleRemove(file, fileList) {
      this.$emit("delUploadImage", file.name);
      const index = this.upload.fileList.indexOf(file);
      this.upload.fileList.splice(index, 1);
      if (this.formData.attachids && this.formData.attachids != "null") {
        this.formData.attachids = this.formData.attachids.replace(file.url + ",", "");
      }
      this.upload.fileNameList.push(file.url);
      // delFileInfo(file.url);
    },
    handlePreview(file) {
      previewFile(file.url)
    },
    checkBoxParse() {},
    cascaderParse() {},
    timeRangParse() {},
    onSignFormSelected(selectEmp, modelNo, modelName) {
      this.$set(this.formData, modelNo, selectEmp.empNo)
      this.$set(this.formData, modelName, selectEmp.empName)
    },
  }
}

</script>
<style rel="stylesheet/scss" lang="scss" scoped>
@import "~@/assets/styles/mobileSkin/mobileMixin.scss";

.el-upload__tip {
  line-height: 1.2;
}

/deep/.demo-table-expand {
  font-size: 0;
}

/deep/.demo-table-expand label {
  width: 90px;
  color: #99a9bf;
}

/deep/.demo-table-expand/deep/.el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  width: 100%;
}

/deep/.el-input-number--medium {
  width: 100%;
}

.del-handler {
  font-size: 20px;
  color: #ff4949;
}

.el-slider>/deep/.el-slider__runway {
  margin: 15px 0;
}

.el-checkbox-group {
  font-size: inherit;
}

.el-color-picker {
  display: inherit;
}

.talbe-name-style {
  font-size: 16px;
  font-weight: bold;
  line-height: 50px;
}

/deep/.el-input-number--medium {
  width: 100%;
}

/deep/.full-row {
  width: 100%;
}

.mobile-content {
  background: #EEEEEE;
}

.mobile-card {
  border: 0;
  background: transparent;
}

#staffCard {
  border: 0;
}

.mobile-body {
  padding: 0;
}

/deep/input,
/deep/textarea {
  border-color: transparent;
}

.head-pannel {
  background: #ffffff;
  padding: 15px;
  margin-top: 5px;
  color: #6B6B6B;
}

.head-pannel:after {
  clear: both;
  content: "";
  display: block;
}

.head-pannel>div {
  font-size: 14px;
  padding: 5px;
}

/deep/.el-row {
  margin-left: 0;
  margin-right: 0;
}

.body-main .el-form-item {
  margin-top: 15px;
  margin-bottom: 15px;
  border-color: transparent;
}

.el-table__expanded-cell .el-form-item {
  margin: 0;
}

.el-form-item>label {
  padding-left: 10px;
}

.body-main .el-col {
  border-bottom: 1px solid #EAEAEA;
}

.body-main {
  background: #ffffff;
  padding: 15px 20px;
  margin-top: 8px;
}

.body-main:after {
  clear: both;
  content: "";
  display: block;
}

.child-item {
  margin-top: 8px;
  background: #ffffff;
}

.child-item:after {
  clear: both;
  content: "";
  display: block;
}

.child-item /deep/.el-form-item {
  margin: 15px 10px;
  padding-bottom: 15px;
  border-color: transparent;
  border-bottom: 1px solid #EAEAEA;
}

.talbe-name-style {
  padding-left: 15px;
}

.sign-content {
  background: #ffffff;
  margin-top: 8px;
  color: #000000;
}

.sign-content:after {
  clear: both;
  content: "";
  display: block;
}

.sign-point>.el-form-item {
  padding: 5px 10px;
  margin-bottom: 0;
}

.btn-div {
  background: white;
  padding: 10px;
  margin: 10px 0;
  float: none;
}

.el-message-box__btns button:nth-child(2) {
  margin-left: 0;
}

.el-slider {
  /deep/.el-slider__bar {
    @include backgroundColor('themeMainColor');
  }
  
  /deep/.el-slider__button-wrapper {
    .el-slider__button {
      @include borderColor('themeMainColor');
    }
  }
}

.el-radio.is-checked {
  /deep/.el-radio__input.is-checked {
    .el-radio__inner {
      @include borderColor('themeMainColor');
      @include backgroundInfo('themeMainColor');
    }
  }
  
  /deep/.el-radio__input.is-checked+.el-radio__label {
    @include fontColor('themeMainColor');
  }
}

.el-checkbox.is-checked {
  /deep/.el-checkbox__input.is-checked {
    .el-checkbox__inner {
      @include borderColor('themeMainColor');
      @include backgroundInfo('themeMainColor');
    }
  }
  
  /deep/.el-checkbox__input.is-checked+.el-checkbox__label {
    @include fontColor('themeMainColor');
  }
}

.el-switch.is-checked {
  /deep/.el-switch__core {
    @include borderColor('themeMainColor');
    @include backgroundInfo('themeMainColor');
  }
}

.cube-popup {
  /deep/.cube-picker-confirm {
    @include fontColor('themeMainColor');
  }
}

.child-item {
  /deep/.el-button--primary {
    @include borderColor('themeMainColor');
    @include backgroundInfo('themeMainColor');
  }
  
  /deep/.del-handler {
    @include fontColor('themeMainColor');
  }
}

.dialog-footer {
  .el-button--success {
    @include backgroundInfo('themeMainColor');
    @include borderColor('themeMainColor');
  }
  
  .el-button--info {
    @include backgroundInfo('themeMainColor');
    @include borderColor('themeMainColor');
  }
}

</style>
