<template>
  <div class="ant-modal-content">
    <div class="ant-modal-body">
      <el-form ref="elForm" class="ant-form ant-form-horizontal" :model="formData" :rules="rules"
        size="medium" label-width="100px" :label-position="labelPosition">
        <div class="ant-card ant-card-bordered" id="staffCard">
          <div class="ant-card-body">
            <div class="page-header">
              <img  style="height: 1cm;width: 100%" :src="imgHederUrl" alt=""/>
            </div>
            <div class="titleDiv">
              <span id="staffEvectionTitle">
                iPEBG事業群<br />
                <span class="titleUnderline">聯絡單</span><br />
              </span>
              <div class="titleRight">
                <el-form-item label-width="1px" label="" prop="groupUrgent">
                  <el-radio-group v-model="formData.groupUrgent" size="medium">
                    <el-radio v-for="(item, index) in groupUrgentOptions" :key="index" :label="item.value"
                              :disabled="item.disabled">{{item.label}}</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label-width="1px" label="" prop="groupFiletype">
                  <el-radio-group v-model="formData.groupFiletype" size="medium">
                    <el-radio v-for="(item, index) in groupFiletypeOptions" :key="index" :label="item.value"
                              :disabled="item.disabled">{{item.label}}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </div>
            </div>
            <el-row :gutter="15">
              <el-col :span="12" :xs="24">
                <el-form-item label="呈核:" prop="groupApproval">
                  <el-input v-model="formData.groupApproval" placeholder="請輸入呈核呈核" clearable
                    :style="{width: '100%'}"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12" :xs="24">
                <el-form-item label=" 發文單位：" prop="groupDept">
                  <el-input v-model="formData.groupDept" placeholder="請輸入 發文單位" clearable
                    :style="{width: '100%'}"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12" :xs="24">
                <el-form-item label="會審單位：" prop="groupAudit">
                  <el-input v-model="formData.groupAudit" placeholder="請輸入會審單位" clearable
                    :style="{width: '100%'}"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12" :xs="24">
                <el-form-item label="發文字號：" prop="groupIssue">
                  <el-input v-model="formData.groupIssue" placeholder="請輸入發文字號" clearable
                    :style="{width: '100%'}"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12" :xs="24">
                <el-form-item label="受文單位：" prop="groupAddres">
                  <el-input v-model="formData.groupAddres" placeholder="請輸入受文單位" clearable
                    :style="{width: '100%'}"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12" :xs="24">
                <el-form-item label="發文日期：" prop="groupDate">
                  <el-date-picker v-model="formData.groupDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                    :style="{width: '100%'}" placeholder="请選擇發文日期" clearable></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24">
                <el-form-item prop="groupNote">
                  <template v-slot:label>
                   <span class="mainFont ">主旨：</span>
                  </template>
                  <el-input v-model="formData.groupNote" placeholder="請輸入主旨" clearable
                    :style="{width: '100%'}"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24">
                <label-form :label=formConfigData.beijingshuoming_label labelWidth='120' labelPosition='left'
                  labelSize='14' textSize='14'></label-form>
              </el-col>
              <el-col :span="24" :xs="24">
                <el-form-item label-width="1px" label="" prop="groupDemand">
                  <el-input v-model="formData.groupDemand" type="textarea" placeholder="請輸入背景說明"
                    :autosize="{minRows: 4, maxRows: 4}" :style="{width: '100%'}"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24">
                <label-form :label=formConfigData.shixiang_label labelWidth='120' labelPosition='left'
                  labelSize='14' textSize='14'></label-form>
              </el-col>
              <el-col :span="24" :xs="24">
                <el-form-item label-width="1px" label="" prop="groupMatters">
                  <el-input v-model="formData.groupMatters" type="textarea" placeholder="請輸入申請事項"
                    :autosize="{minRows: 4, maxRows: 4}" :style="{width: '100%'}"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24">
                <label-form :label=formConfigData.fujian_label labelWidth='120' labelPosition='left'
                  labelSize='14' textSize='14'></label-form>
              </el-col>
              <el-col :span="24" :xs="24">
                <el-form-item label-width="1px" label="" prop="attachids">
                  <el-upload :file-list="upload.fileList" :show-file-list="true" :on-change="handleChange"
                    :on-exceed="handleExceed" :on-preview="handlePreview" :on-remove="handleRemove"
                    :on-success="uploadsucces" :before-upload="attachidsBeforeUpload"
                    :headers="upload.headers" :action="upload.url" list-type="text">
                    <el-button size="small" type="primary" icon="el-icon-upload">點擊上傳</el-button>
                  </el-upload>
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24">
                <img  style="height: 4cm;width: 100%" :src="imgQianheliuchengtuUrl" alt=""/>
              </el-col>
              <el-col :span="24" :xs="24">
                <label-form :label=formConfigData.groupnode_label labelWidth='110' labelPosition='left'
                  labelSize='14' :textarea=formConfigData.groupnode_text textSize='14'></label-form>
              </el-col>
              <el-col :span="24" :xs="24">
                <el-row :gutter="15">
                  <el-col :span="6" :xs="24">
                    <el-form-item prop="cbchargeno" label-width="0">
                      <entfrm-sign-form-add-hq :label="formConfigData.cbchargeno"
                        :required='formConfigData.cbchargeno_required'
                        :selectEmpProp="{empNo:this.formData.cbchargeno,empName:this.formData.cbchargename}"
                        model-no="cbchargeno" model-name="cbchargename"
                        @onSignFormSelected="onSignFormSelected"></entfrm-sign-form-add-hq>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6" :xs="24">
                    <el-form-item prop="shenhechargeno" label-width="0">
                      <entfrm-sign-form-add-hq :label="formConfigData.shenhechargeno"
                        :required='formConfigData.shenhechargeno_required'
                        :selectEmpProp="{empNo:this.formData.shenhechargeno,empName:this.formData.shenhechargename}"
                        model-no="shenhechargeno" model-name="shenhechargename"
                        @onSignFormSelected="onSignFormSelected"></entfrm-sign-form-add-hq>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6" :xs="24">
                    <el-form-item prop="huishenchargeno" label-width="0">
                      <entfrm-sign-form-add-hq :label="formConfigData.huishenchargeno"
                        :required='formConfigData.huishenchargeno_required'
                        :selectEmpProp="{empNo:this.formData.huishenchargeno,empName:this.formData.huishenchargename}"
                        model-no="huishenchargeno" model-name="huishenchargename"
                        @onSignFormSelected="onSignFormSelected"></entfrm-sign-form-add-hq>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6" :xs="24">
                    <el-form-item prop="hezhunchargeno" label-width="0">
                      <entfrm-sign-form-add-hq :label="formConfigData.hezhunchargeno"
                        :required='formConfigData.hezhunchargeno_required'
                        :selectEmpProp="{empNo:this.formData.hezhunchargeno,empName:this.formData.hezhunchargename}"
                        model-no="hezhunchargeno" model-name="hezhunchargename"
                        @onSignFormSelected="onSignFormSelected"></entfrm-sign-form-add-hq>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-col>
              <el-col :span="24" :xs="24">
                <div class="dialog-footer" align="center" style="padding-top: 5px;padding-bottom: 5px">
                  <!--          <el-form-item size="large">-->
                  <el-button type="success" @click="handleSubmit" :disabled="isDisable">{{$t('table.submit')}}
                  </el-button>
                  <el-button type="primary" @click="submitForm" :disabled="isDisable">{{$t('common.save')}}
                  </el-button>
                  <el-button @click="closeForm">{{$t('common.cancel')}}</el-button>
                  <!--        </el-form-item>-->
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-form>
    </div>
  </div>
</template>
<script>
import {
  getEntGroupLiaison,
  addEntGroupLiaison,
  editEntGroupLiaison,
  addEntGroupLiaisonAndStartProcess,
  editEntGroupLiaisonAndStartProcess,
  getSignConfigList
}
from "@/api/caaesign/entGroupLiaison"
import '@/assets/styles/design-build/design-add-view.scss'
import {
  getAccessToken,
  getZltAccessToken
}
from "@/utils/auth";
import {
  previewFile,
  getHeader
}
from "@/utils/entfrm";
import {
  listFileInfo,
  getFileInfo,
  delFileInfo,
  addFileInfo,
  editFileInfo,
  getByKey
}
from "@/api/system/fileInfo";
import imgHeder from '@/assets/images/A3.jpg'
import imgQianheliuchengtu from '@/assets/images/qianheliuchengtu.png'
export default {
  components: {},
  props: [],
  data() {
    return {
      imgHederUrl: imgHeder,
      imgQianheliuchengtuUrl: imgQianheliuchengtu,
      formData: {
        groupUrgent: undefined,
        groupFiletype: undefined,
        groupApproval: undefined,
        groupDept: undefined,
        groupAudit: undefined,
        groupIssue: undefined,
        groupAddres: undefined,
        groupDate: null,
        groupNote: undefined,
        groupDemand: undefined,
        groupMatters: undefined,
        attachids: "",
        cbchargeno: undefined,
        shenhechargeno: undefined,
        huishenchargeno: undefined,
        hezhunchargeno: undefined,
        makerNo: this.$store.state.user.empNo,
        makerName: this.$store.state.user.name,
        dataSource: "pc",
      },
      formConfigData: {
        title: "iPEBG事業群聯絡單",
        beijingshuoming_label: `一、背景說明：`,
        beijingshuoming_text: ``,
        shixiang_label: `二、申請事項：`,
        shixiang_text: ``,
        fujian_label: `三、相關附件：`,
        fujian_text: ``,
        groupnode_label: `表單固定編碼：`,
        groupnode_text: `行政-人資Ⅶ-行政-003-2024A      `,
        cbchargeno: "承辦",
        cbchargeno_required: true,
        shenhechargeno: "審核",
        shenhechargeno_required: true,
        huishenchargeno: "會審",
        huishenchargeno_required: true,
        hezhunchargeno: "核准",
        hezhunchargeno_required: true,
      },
      tableHeight: document.documentElement.scrollHeight - 245 + "px",
      rules: {
        groupUrgent: [{
          required: true,
          message: '不能為空',
          trigger: 'change'
        }],
        groupFiletype: [{
          required: true,
          message: '不能為空',
          trigger: 'change'
        }],
        groupApproval: [{
          required: true,
          message: '請輸入呈核呈核',
          trigger: 'blur'
        }],
        groupDept: [{
          required: true,
          message: '請輸入 發文單位',
          trigger: 'blur'
        }],
        groupAudit: [{
          required: true,
          message: '請輸入會審單位',
          trigger: 'blur'
        }],
        groupIssue: [{
          required: true,
          message: '請輸入發文字號',
          trigger: 'blur'
        }],
        groupAddres: [{
          required: true,
          message: '請輸入受文單位',
          trigger: 'blur'
        }],
        groupDate: [{
          required: true,
          message: '请選擇發文日期',
          trigger: 'change'
        }],
        groupNote: [{
          required: true,
          message: '請輸入主旨',
          trigger: 'blur'
        }],
        groupDemand: [{
          required: true,
          message: '請輸入背景說明',
          trigger: 'blur'
        }],
        groupMatters: [{
          required: true,
          message: '請輸入申請事項',
          trigger: 'blur'
        }],
        attachids: [],
        cbchargeno: [{
          required: true,
          message: '承辦不能為空',
          trigger: 'change'
        }],
        shenhechargeno: [{
          required: true,
          message: '審核不能為空',
          trigger: 'change'
        }],
        huishenchargeno: [{
          required: true,
          message: '會審不能為空',
          trigger: 'change'
        }],
        hezhunchargeno: [{
          required: true,
          message: '核准不能為空',
          trigger: 'change'
        }],
      },
      // 文件上传参数
      upload: {
        // 是否显示弹出层
        open: false,
        fileList: [],
        fileNameList: [],
        // 弹出层标题
        title: "",
        multiple: false,
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: this.getHeader(),
        // 上传的地址
        url: this.getAppBaseApi() + "/system/fileInfo/upload"
      },
      groupUrgentOptions: [],
      groupFiletypeOptions: [],
      isMobile: false,
      isDisable: false,
      labelPosition: 'right',
      alias: "postgresql$_$pghrsign_ipebg_test$_$075d399c2dcaa20f94d53895254e58a1%_%design"
    }
  },
  computed: {},
  watch: {},
  created() {
    const id = this.$route.query.id;
    this.changeTagsView(this.$route.query);
    this.isMobile = this.isMobileFun()
    if (this.isMobile) {
      this.labelPosition = 'top'
    }
    getSignConfigList().then(response => {
      if (response.code === 0) {
        response.data.forEach(element => {
          const colKey = element.colKey.split('_')
          if (colKey.length > 1) {
            if (this.rules[colKey[0]] && colKey[1] === 'required') {
              this.rules[colKey[0]][0]['required'] = element.colValue === 'true'
              this.formConfigData[element.colKey] = element.colValue === 'true'
            }
            else {
              this.formConfigData[element.colKey] = element.colValue
            }
          }
          else {
            this.formConfigData[element.colKey] = element.colValue
          }
        });
      }
    })
    if (id != null && id != undefined) {
      getEntGroupLiaison(id).then(response => {
        this.formData = response.data;
        this.checkBoxParse()
        this.cascaderParse()
        this.timeRangParse()
        if (this.formData.attachids) {
          let a = this.formData.attachids.split(',');
          if (a.length > 0) {
            a.forEach(item => {
              if (item) {
                getByKey(item).then(response => {
                  this.upload.fileList.push({
                    name: response.data.orignalName,
                    url: response.data.name
                  });
                })
              }
            })
          }
        }
      });
    }
    this.getGroupUrgentOptions()
    this.getGroupFiletypeOptions()
  },
  mounted() {
    this.$nextTick(function() {
      if (this.isMobile) {}
    })
  },
  methods: {
    submitForm() {
      this.$refs['elForm'].validate(valid => {
        if (valid) {
          this.isDisable = true;
          const formData = JSON.parse(JSON.stringify(this.formData))
          if (formData.id != undefined) {
            editEntGroupLiaison(formData).then(response => {
              if (response.code === 0) {
                this.msgSuccess(this.$t('tips.updateSuccess'));
                this.closeForm();
              }
              else {
                this.msgError(response.msg);
              }
              this.isDisable = false;
            });
          }
          else {
            addEntGroupLiaison(formData).then(response => {
              if (response.code === 0) {
                this.msgSuccess(this.$t('tips.createSuccess'));
                this.closeForm();
              }
              else {
                this.msgError(response.msg);
              }
              this.isDisable = false;
            });
          }
          /**
           * 提交的時候刪除被標記的需要刪除的附件
           */
          if (this.formData.attachids) {
            this.upload.fileNameList.forEach(item => {
              if (item) {
                delFileInfo(item);
              }
            })
          }
        }
      })
    },
    handleSubmit: function() {
      this.$refs["elForm"].validate(valid => {
        if (valid) {
          this.isDisable = true;
          const formData = JSON.parse(JSON.stringify(this.formData))
          if (formData.id != undefined) {
            editEntGroupLiaisonAndStartProcess(formData).then(response => {
              if (response.code === 0) {
                this.msgSuccess(this.$t('tips.updateSuccess'));
                this.closeForm();
              }
              else {
                this.msgError(response.msg);
              }
              this.isDisable = false;
            });
          }
          else {
            addEntGroupLiaisonAndStartProcess(formData).then(response => {
              if (response.code === 0) {
                this.msgSuccess(this.$t('tips.createSuccess'));
                this.closeForm();
              }
              else {
                this.msgError(response.msg);
              }
              this.isDisable = false;
            });
          }
          /**
           * 提交的時候刪除被標記的需要刪除的附件
           */
          if (this.formData.attachids) {
            this.upload.fileNameList.forEach(item => {
              if (item) {
                delFileInfo(item);
              }
            })
          }
        }
      })
    },
    resetForm() {
      this.$refs['elForm'].resetFields()
    },
    closeForm() {
      //关闭子页面
      if (this.$store.state.settings.tagsView) {
        this.$router.go(-1) // 返回
        this.$store.state.tagsView.visitedViews.splice(this.$store.state.tagsView.visitedViews.findIndex(
          item => item.path === this.$route.path), 1)
        this.$router.push(this.$store.state.tagsView.visitedViews[this.$store.state.tagsView.visitedViews
          .length - 1].path)
      }
      else {
        parent.postMessage("closeCurrentTabMessage", '*');
      }
    },
    getGroupUrgentOptions() {
      this.getDicts("group_urgent").then(response => {
        this.groupUrgentOptions = response.data;
      });
    },
    getGroupFiletypeOptions() {
      this.getDicts("group_filetype").then(response => {
        this.groupFiletypeOptions = response.data;
      });
    },
    attachidsBeforeUpload(file) {
      let isRightSize = file.size / 1024 / 1024 < 2
      if (!isRightSize) {
        this.$message.error('文件大小超过 2MB')
      }
      return isRightSize
    },
    // 文件上传成功处理
    uploadsucces(response, file, fileList) {
      if (response.data.name != undefined && response.data.name != "undefined") {
        this.upload.fileList.push({
          name: file.name,
          url: response.data.name
        });
        this.formData.attachids += response.data.name + ",";
      }
    },
    handleChange(file, fileList) {},
    handleExceed(file, fileList) {},
    handleRemove(file, fileList) {
      this.$emit("delUploadImage", file.name);
      const index = this.upload.fileList.indexOf(file);
      this.upload.fileList.splice(index, 1);
      if (this.formData.attachids) {
        this.formData.attachids = this.formData.attachids.replace(file.url + ",", "");
      }
      this.upload.fileNameList.push(file.url);
      // delFileInfo(file.url);
    },
    handlePreview(file) {
      previewFile(file.url)
    },
    checkBoxParse() {},
    cascaderParse() {},
    timeRangParse() {},
    onSignFormSelected(selectEmp, modelNo, modelName) {
      this.$set(this.formData, modelNo, selectEmp.empNo)
      this.$set(this.formData, modelName, selectEmp.empName)
    },
  }
}

</script>
<style scoped>
.el-upload__tip {
  line-height: 1.2;
}

.del-handler {
  font-size: 20px;
  color: #ff4949;
}

.el-slider>/deep/.el-slider__runway {
  margin: 15px 0;
}

.el-checkbox-group {
  font-size: inherit;
}

.el-color-picker {
  display: inherit;
}

.talbe-name-style {
  font-size: 16px;
  font-weight: bold;
  color: #1DB8FF;
  line-height: 50px;
}

/deep/.el-input-number--medium {
  width: 100%;
}

#staffEvectionTitle {
  margin-bottom: 10px;
}
.titleUnderline {
  border-bottom: 2px solid;
  font-weight: 700;
  color: #000;
  font-size: 24px;
}
.ant-modal-content .el-col:not(.el-col-no-border) {
     border: 0 solid #ccc;
     margin-top: 0;
}
/deep/input,
/deep/textarea {
  border-left-color: transparent;
  border-right-color: transparent;
  border-top-color: transparent;
  border-bottom-color: black;
  border-radius: 0;
}
.titleDiv {
  height: 90px;
  position: relative;
}
.titleRight{
  position: absolute;
  right: 7.5px;
  top: 0;
  border: 1px solid black;
  padding-left: 2px;
  padding-right: 2px;
}
.titleRight .el-form-item{
  margin-top: 5px;
  margin-bottom: 5px;
}
/deep/ .el-form-item__label{
  text-align: justify;
  text-align-last: justify;
}
.mainFont {
  font-size: 20px;
  font-weight: 600;
  color: #000;
}
</style>
