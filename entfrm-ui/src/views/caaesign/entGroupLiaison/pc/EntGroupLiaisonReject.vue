<template>
  <div class="ant-modal-content">
    <div class="ant-modal-body">
      <el-form ref="elForm" class="ant-form ant-form-horizontal" :model="formData" :rules="rules"
        size="medium" label-width="100px" :label-position="labelPosition">
        <div class="ant-card ant-card-bordered" id="staffCard">
          <div class="ant-card-body">
            <el-row :gutter="15">
              <span id="staffEvectionTitle" style="margin-bottom: 10px">{{formConfigData.title}}</span>
              <el-col :span="8" :xs="24" class="el-col-no-border bar-to-bottom">
                任務編碼:{{formData.serialno?formData.serialno:'提交表單后自動生成'}}
              </el-col>
              <el-col :span="8" :xs="24" class="el-col-no-border bar-to-bottom"> 填單時間:{{formData.createTime}}
              </el-col>
              <el-col :span="8" :xs="24" class="el-col-no-border bar-to-bottom">
                填單人:{{formData.makerNo +' / '+ formData.makerName}}
              </el-col>
              <el-col :span="12" :xs="24">
                <el-form-item label-width="1px" label="" prop="groupUrgent">
                  <el-radio-group v-model="formData.groupUrgent" size="medium">
                    <el-radio v-for="(item, index) in groupUrgentOptions" :key="index" :label="item.value"
                      :disabled="item.disabled">{{item.label}}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12" :xs="24">
                <el-form-item label-width="1px" label="" prop="groupFiletype">
                  <el-radio-group v-model="formData.groupFiletype" size="medium">
                    <el-radio v-for="(item, index) in groupFiletypeOptions" :key="index" :label="item.value"
                      :disabled="item.disabled">{{item.label}}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12" :xs="24">
                <el-form-item label="呈                  核：" prop="groupApproval">
                  <el-input v-model="formData.groupApproval" placeholder="請輸入呈核呈核" clearable
                    :style="{width: '100%'}"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12" :xs="24">
                <el-form-item label=" 發文單位：" prop="groupDept">
                  <el-input v-model="formData.groupDept" placeholder="請輸入 發文單位" clearable
                    :style="{width: '100%'}"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12" :xs="24">
                <el-form-item label="會審單位：" prop="groupAudit">
                  <el-input v-model="formData.groupAudit" placeholder="請輸入會審單位" clearable
                    :style="{width: '100%'}"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12" :xs="24">
                <el-form-item label="發文字號：" prop="groupIssue">
                  <el-input v-model="formData.groupIssue" placeholder="請輸入發文字號" clearable
                    :style="{width: '100%'}"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12" :xs="24">
                <el-form-item label="受文單位：" prop="groupAddres">
                  <el-input v-model="formData.groupAddres" placeholder="請輸入受文單位" clearable
                    :style="{width: '100%'}"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12" :xs="24">
                <el-form-item label="發文日期：" prop="groupDate">
                  <el-date-picker v-model="formData.groupDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                    :style="{width: '100%'}" placeholder="请選擇發文日期" clearable></el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24">
                <el-form-item label="主   旨：" prop="groupNote">
                  <el-input v-model="formData.groupNote" placeholder="請輸入主旨" clearable
                    :style="{width: '100%'}"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24">
                <label-form :label=formConfigData.beijingshuoming_label labelWidth='120' labelPosition='left'
                  labelSize='14' textSize='14'></label-form>
              </el-col>
              <el-col :span="24" :xs="24">
                <el-form-item label-width="1px" label="" prop="groupDemand">
                  <el-input v-model="formData.groupDemand" type="textarea" placeholder="請輸入背景說明"
                    :autosize="{minRows: 4, maxRows: 4}" :style="{width: '100%'}"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24">
                <label-form :label=formConfigData.shixiang_label labelWidth='120' labelPosition='left'
                  labelSize='14' textSize='14'></label-form>
              </el-col>
              <el-col :span="24" :xs="24">
                <el-form-item label-width="1px" label="" prop="groupMatters">
                  <el-input v-model="formData.groupMatters" type="textarea" placeholder="請輸入申請事項"
                    :autosize="{minRows: 4, maxRows: 4}" :style="{width: '100%'}"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24">
                <label-form :label=formConfigData.fujian_label labelWidth='120' labelPosition='left'
                  labelSize='14' textSize='14'></label-form>
              </el-col>
              <el-col :span="24" :xs="24">
                <el-form-item label-width="1px" label="" prop="attachids">
                  <el-upload :file-list="upload.fileList" :show-file-list="true" :on-change="handleChange"
                    :on-exceed="handleExceed" :on-preview="handlePreview" :on-remove="handleRemove"
                    :on-success="uploadsucces" :before-upload="attachidsBeforeUpload"
                    :headers="upload.headers" :action="upload.url" list-type="text">
                    <el-button size="small" type="primary" icon="el-icon-upload">點擊上傳</el-button>
                  </el-upload>
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24">
                <label-form :label=formConfigData.groupnode_label labelWidth='110' labelPosition='left'
                  labelSize='14' :textarea=formConfigData.groupnode_text textSize='14'></label-form>
              </el-col>
              <el-col :span="24" :xs="24">
                <el-row :gutter="15">
                  <el-col :span="6" :xs="24">
                    <el-form-item prop="cbchargeno" label-width="0">
                      <entfrm-sign-form-add-hq :label="formConfigData.cbchargeno"
                        :required='formConfigData.cbchargeno_required'
                        :selectEmpProp="{empNo:this.formData.cbchargeno,empName:this.formData.cbchargename}"
                        model-no="cbchargeno" model-name="cbchargename"
                        @onSignFormSelected="onSignFormSelected"></entfrm-sign-form-add-hq>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6" :xs="24">
                    <el-form-item prop="shenhechargeno" label-width="0">
                      <entfrm-sign-form-add-hq :label="formConfigData.shenhechargeno"
                        :required='formConfigData.shenhechargeno_required'
                        :selectEmpProp="{empNo:this.formData.shenhechargeno,empName:this.formData.shenhechargename}"
                        model-no="shenhechargeno" model-name="shenhechargename"
                        @onSignFormSelected="onSignFormSelected"></entfrm-sign-form-add-hq>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6" :xs="24">
                    <el-form-item prop="huishenchargeno" label-width="0">
                      <entfrm-sign-form-add-hq :label="formConfigData.huishenchargeno"
                        :required='formConfigData.huishenchargeno_required'
                        :selectEmpProp="{empNo:this.formData.huishenchargeno,empName:this.formData.huishenchargename}"
                        model-no="huishenchargeno" model-name="huishenchargename"
                        @onSignFormSelected="onSignFormSelected"></entfrm-sign-form-add-hq>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6" :xs="24">
                    <el-form-item prop="hezhunchargeno" label-width="0">
                      <entfrm-sign-form-add-hq :label="formConfigData.hezhunchargeno"
                        :required='formConfigData.hezhunchargeno_required'
                        :selectEmpProp="{empNo:this.formData.hezhunchargeno,empName:this.formData.hezhunchargename}"
                        model-no="hezhunchargeno" model-name="hezhunchargename"
                        @onSignFormSelected="onSignFormSelected"></entfrm-sign-form-add-hq>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-col>
              <el-col :span="24" :xs="24">
                <div class="dialog-footer" align="center" style="padding-top: 5px;padding-bottom: 5px">
                  <el-button type="success" @click="handleSubmit()">
                    {{$t('table.activity.reSubmit')}}
                  </el-button>
                  <el-button type="primary" @click="submitForm" :disabled="isDisable">{{$t('common.save')}}
                  </el-button>
                  <el-button @click="handleTask(9)" :disabled="isDisable">{{$t('table.activity.cancle')}}
                  </el-button>
                  <el-button @click="closeForm">{{$t('common.close')}}</el-button>
                </div>
              </el-col>
              <!-- 簽核線 -->
              <el-col :span="24" class="print-hide-div">
                <div class="dialog-footer" style="padding-top: 5px;padding-bottom: 5px">
                  <div v-html="signPath"></div>
                </div>
              </el-col>
              <!-- 审核记录 -->
              <el-table border stripe :data="commentList" class="print-hide-div">
                <el-table-column :label="$t('table.activity.taskId')" align="center" prop="id" />
                <el-table-column :label="$t('table.activity.approvedBy')" align="center" prop="userId"
                  :show-overflow-tooltip="true" />
                <el-table-column :label="$t('table.activity.approvalOpinions')" align="center"
                  prop="fullMessage" :show-overflow-tooltip="true" />
                <el-table-column :label="$t('table.activity.operIp')" align="center" prop="operIp"
                  :show-overflow-tooltip="true" />
                <el-table-column :label="$t('table.activity.auditStatus')" align="center" prop="status"
                  :formatter="statusFormat" :show-overflow-tooltip="true" />
                <el-table-column :label="$t('table.activity.approvalTime')" align="center" prop="time"
                  width="180">
                  <template slot-scope="scope">
                    <span>{{ parseTime(scope.row.time) }}</span>
                  </template>
                </el-table-column>
              </el-table>
              <!-- 任务跟踪对话框 -->
              <el-dialog :title="$t('table.activity.taskMap')" :visible.sync="showImgDialog" width="1000px">
                <img :src="imgUrl" style="padding-bottom: 60px;width:100%;">
              </el-dialog>
            </el-row>
          </div>
        </div>
      </el-form>
    </div>
  </div>
</template>
<script>
import {
  getEntGroupLiaison,
  addEntGroupLiaison,
  editEntGroupLiaison,
  getSignPath,
  editEntGroupLiaisonAndResubmitProcess,
  getSignConfigList
}
from "@/api/caaesign/entGroupLiaison"
import '@/assets/styles/design-build/design-add-view.scss'
import {
  listTask,
  getTask,
  checkTask,
  taskComment
}
from "@/api/activiti/task"
import {
  getAccessToken,
  getZltAccessToken
}
from "@/utils/auth";
import {
  previewFile,
  getHeader
}
from "@/utils/entfrm";
import {
  listFileInfo,
  getFileInfo,
  delFileInfo,
  addFileInfo,
  editFileInfo,
  getByKey
}
from "@/api/system/fileInfo";
export default {
  components: {},
  props: [],
  data() {
    return {
      formData: {
        groupUrgent: undefined,
        groupFiletype: undefined,
        groupApproval: undefined,
        groupDept: undefined,
        groupAudit: undefined,
        groupIssue: undefined,
        groupAddres: undefined,
        groupDate: null,
        groupNote: undefined,
        groupDemand: undefined,
        groupMatters: undefined,
        attachids: "",
        cbchargeno: undefined,
        shenhechargeno: undefined,
        huishenchargeno: undefined,
        hezhunchargeno: undefined,
        dataSource: "pc",
      },
      formConfigData: {
        title: "iPEBG事業群聯絡單",
        beijingshuoming_label: `一、背景說明：`,
        beijingshuoming_text: ``,
        shixiang_label: `二、申請事項：`,
        shixiang_text: ``,
        fujian_label: `三、相關附件：`,
        fujian_text: ``,
        groupnode_label: `表單固定編碼：`,
        groupnode_text: `行政-人資Ⅶ-行政-003-2024A      `,
        cbchargeno: "承辦",
        cbchargeno_required: true,
        shenhechargeno: "審核",
        shenhechargeno_required: true,
        huishenchargeno: "會審",
        huishenchargeno_required: true,
        hezhunchargeno: "核准",
        hezhunchargeno_required: true,
      },
      rules: {
        groupUrgent: [{
          required: true,
          message: '不能為空',
          trigger: 'change'
        }],
        groupFiletype: [{
          required: true,
          message: '不能為空',
          trigger: 'change'
        }],
        groupApproval: [{
          required: true,
          message: '請輸入呈核呈核',
          trigger: 'blur'
        }],
        groupDept: [{
          required: true,
          message: '請輸入 發文單位',
          trigger: 'blur'
        }],
        groupAudit: [{
          required: true,
          message: '請輸入會審單位',
          trigger: 'blur'
        }],
        groupIssue: [{
          required: true,
          message: '請輸入發文字號',
          trigger: 'blur'
        }],
        groupAddres: [{
          required: true,
          message: '請輸入受文單位',
          trigger: 'blur'
        }],
        groupDate: [{
          required: true,
          message: '请選擇發文日期',
          trigger: 'change'
        }],
        groupNote: [{
          required: true,
          message: '請輸入主旨',
          trigger: 'blur'
        }],
        groupDemand: [{
          required: true,
          message: '請輸入背景說明',
          trigger: 'blur'
        }],
        groupMatters: [{
          required: true,
          message: '請輸入申請事項',
          trigger: 'blur'
        }],
        attachids: [],
        cbchargeno: [{
          required: true,
          message: '承辦不能為空',
          trigger: 'change'
        }],
        shenhechargeno: [{
          required: true,
          message: '審核不能為空',
          trigger: 'change'
        }],
        huishenchargeno: [{
          required: true,
          message: '會審不能為空',
          trigger: 'change'
        }],
        hezhunchargeno: [{
          required: true,
          message: '核准不能為空',
          trigger: 'change'
        }],
      },
      tableHeight: document.documentElement.scrollHeight - 245 + "px",
      // 审批意见数据
      commentList: [],
      //簽核路徑
      signPath: [],
      //任务图url
      imgUrl: '',
      isDisable: false,
      // 是否显示任务图
      showImgDialog: false,
      auditStatus: [],
      // 文件上传参数
      upload: {
        // 是否显示弹出层
        open: false,
        fileList: [],
        fileNameList: [],
        // 弹出层标题
        title: "",
        multiple: false,
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: this.getHeader(),
        // 上传的地址
        url: this.getAppBaseApi() + "/system/fileInfo/upload"
      },
      groupUrgentOptions: [],
      groupFiletypeOptions: [],
      isMobile: false,
      labelPosition: 'left',
      alias: "postgresql$_$pghrsign_ipebg_test$_$075d399c2dcaa20f94d53895254e58a1%_%design"
    }
  },
  computed: {},
  watch: {},
  created() {
    const id = this.$route.query.id;
    this.changeTagsView(this.$route.query);
    this.isMobile = this.isMobileFun()
    if (this.isMobile) {
      this.labelPosition = 'top'
    }
    getSignConfigList().then(response => {
      if (response.code === 0) {
        response.data.forEach(element => {
          const colKey = element.colKey.split('_')
          if (colKey.length > 1) {
            if (this.rules[colKey[0]] && colKey[1] === 'required') {
              this.rules[colKey[0]][0]['required'] = element.colValue === 'true'
              this.formConfigData[element.colKey] = element.colValue === 'true'
            }
            else {
              this.formConfigData[element.colKey] = element.colValue
            }
          }
          else {
            this.formConfigData[element.colKey] = element.colValue
          }
        });
      }
    })
    if (id != null && id != undefined) {
      getEntGroupLiaison(id).then(response => {
        this.formData = response.data;
        this.checkBoxParse()
        this.cascaderParse()
        taskComment(this.formData.processId).then(response => {
          this.commentList = response.data;
        });
        getSignPath(this.formData.processId).then(response => {
          this.signPath = response.data;
        });
        this.timeRangParse()
        if (this.formData.attachids) {
          let a = this.formData.attachids.split(',');
          if (a.length > 0) {
            a.forEach(item => {
              if (item) {
                getByKey(item).then(response => {
                  this.upload.fileList.push({
                    name: response.data.orignalName,
                    url: response.data.name
                  });
                })
              }
            })
          }
        }
      });
    }
    this.getGroupUrgentOptions()
    this.getGroupFiletypeOptions()
    this.getDicts("sign_status").then(response => {
      this.auditStatus = response.data;
    });
  },
  mounted() {
    this.$nextTick(function() {
      if (this.isMobile) {}
    })
  },
  methods: {
    submitForm() {
      this.$refs['elForm'].validate(valid => {
        if (valid) {
          this.isDisable = true;
          const formData = JSON.parse(JSON.stringify(this.formData))
          if (formData.id != undefined) {
            editEntGroupLiaison(formData).then(response => {
              if (response.code === 0) {
                this.msgSuccess(this.$t('tips.updateSuccess'));
                this.closeForm();
              }
              else {
                this.msgError(response.msg);
              }
              this.isDisable = false;
            });
          }
          else {
            addEntGroupLiaison(formData).then(response => {
              if (response.code === 0) {
                this.msgSuccess(this.$t('tips.createSuccess'));
                this.closeForm();
              }
              else {
                this.msgError(response.msg);
              }
              this.isDisable = false;
            });
          }
          /**
           * 提交的時候刪除被標記的需要刪除的附件
           */
          if (this.formData.attachids) {
            this.upload.fileNameList.forEach(item => {
              if (item) {
                delFileInfo(item);
              }
            })
          }
        }
      })
    },
    closeForm() {
      //关闭子页面
      if (this.$store.state.settings.tagsView) {
        this.$router.go(-1) // 返回
        this.$store.state.tagsView.visitedViews.splice(this.$store.state.tagsView.visitedViews.findIndex(
          item => item.path === this.$route.path), 1)
        this.$router.push(this.$store.state.tagsView.visitedViews[this.$store.state.tagsView.visitedViews
          .length - 1].path)
      }
      else {
        parent.postMessage("closeCurrentTabMessage", '*');
      }
    },
    statusFormat(row, column) {
      return this.selectDictLabel(this.auditStatus, row.status);
    },
    handleSubmit: function() {
      this.$refs["elForm"].validate(valid => {
        if (valid) {
          this.isDisable = true;
          const formData = JSON.parse(JSON.stringify(this.formData))
          editEntGroupLiaisonAndResubmitProcess(formData).then(response => {
            if (response.code === 0) {
              this.msgSuccess(this.$t('tips.updateSuccess'));
              this.closeForm();
            }
            else {
              this.msgError(response.msg);
            }
            this.isDisable = false;
          });
          /**
           * 提交的時候刪除被標記的需要刪除的附件
           */
          if (this.formData.attachids) {
            this.upload.fileNameList.forEach(item => {
              if (item) {
                delFileInfo(item);
              }
            })
          }
        }
      })
    },
    handleTask: function(pass) {
      this.$refs["elForm"].validate(valid => {
        this.isDisable = true;
        this.formData.pass = pass
        checkTask(this.formData).then(response => {
          if (response.code === 0) {
            this.msgSuccess(this.$t('tips.operationSuccessful'));
            this.closeForm();
          }
          else {
            this.msgError(response.msg);
          }
          this.isDisable = false;
        });
      });
    },
    getGroupUrgentOptions() {
      this.getDicts("group_urgent").then(response => {
        this.groupUrgentOptions = response.data;
      });
    },
    getGroupFiletypeOptions() {
      this.getDicts("group_filetype").then(response => {
        this.groupFiletypeOptions = response.data;
      });
    },
    attachidsBeforeUpload(file) {
      let isRightSize = file.size / 1024 / 1024 < 2
      if (!isRightSize) {
        this.$message.error('文件大小超过 2MB')
      }
      return isRightSize
    },
    // 文件上传成功处理
    uploadsucces(response, file, fileList) {
      if (response.data.name != undefined && response.data.name != "undefined") {
        this.upload.fileList.push({
          name: file.name,
          url: response.data.name
        });
        this.formData.attachids += response.data.name + ",";
      }
    },
    handleChange(file, fileList) {},
    handleExceed(file, fileList) {},
    handleRemove(file, fileList) {
      this.$emit("delUploadImage", file.name);
      const index = this.upload.fileList.indexOf(file);
      this.upload.fileList.splice(index, 1);
      if (this.formData.attachids) {
        this.formData.attachids = this.formData.attachids.replace(file.url + ",", "");
      }
      this.upload.fileNameList.push(file.url);
      // delFileInfo(file.url);
    },
    handlePreview(file) {
      previewFile(file.url)
    },
    checkBoxParse() {},
    cascaderParse() {},
    onSignFormSelected(selectEmp, modelNo, modelName) {
      this.$set(this.formData, modelNo, selectEmp.empNo)
      this.$set(this.formData, modelName, selectEmp.empName)
    },
    timeRangParse() {},
  }
}

</script>
<style scoped>
.el-upload__tip {
  line-height: 1.2;
}

.del-handler {
  font-size: 20px;
  color: #ff4949;
}

.el-dialog {
  position: relative;
  margin: 0 auto 0px;
  background: #FFFFFF;
  border-radius: 2px;
  -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 50%;
  height: 60%;
}

.el-dialog__body {
  border-top: 1px solid #dcdfe6;
  border-bottom: 1px solid #dcdfe6;
  max-height: 85% !important;
  min-height: 70%;
  overflow-y: auto;
}

.el-slider>/deep/.el-slider__runway {
  margin: 15px 0;
}

.el-checkbox-group {
  font-size: inherit;
}

.el-color-picker {
  display: inherit;
}

/deep/.el-input-number--medium {
  width: 100%;
}

.talbe-name-style {
  font-size: 16px;
  font-weight: bold;
  color: #1DB8FF;
  line-height: 50px;
}

/deep/.el-scrollbar__view table td {
  padding: 0;
}

.el-form-item {
  margin-top: 5px;
  margin-bottom: 5px;
}

.ant-card {
  color: black;
}

.el-table {
  color: black;
}

.el-form-item.edit-item {
  margin-top: 15px;
  margin-bottom: 22px;
}

</style>
