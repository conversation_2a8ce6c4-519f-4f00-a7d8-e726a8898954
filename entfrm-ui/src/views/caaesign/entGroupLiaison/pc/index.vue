<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="100px">
                                                                                                                                                                        <el-form-item :label="$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.default.createDate')">
                            <el-date-picker v-model="dateRange"
                                            size="small"
                                            style="width: 240px"
                                            value-format="yyyy-MM-dd"
                                            type="daterange"
                                            range-separator="-"
                                            :start-placeholder="$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.default.beginDate')"
                                            :end-placeholder="$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.default.endDate')"
                            ></el-date-picker>
                        </el-form-item>
                                                                                                                                                                                                                                                                                                                            <el-form-item :label="$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.workStatus')" prop="workStatus">
                            <el-select v-model="queryParams.workStatus" :placeholder="$t('common.placeholderDefault') +　$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.workStatus')" clearable size="small">
                                <el-option
                                        v-for="dict in workStatusOptions"
                                        :key="dict.value"
                                        :label="dict.label"
                                        :value="dict.value"
                                />
                            </el-select>
                        </el-form-item>
                                                                                                                                                    <el-form-item :label="$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.serialno')" prop="serialno">
                            <el-input
                                    v-model="queryParams.serialno"
                                    :placeholder="$t('common.placeholderDefault') +　$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.serialno')"
                                    clearable
                                    size="small"
                                    @keyup.enter.native="handleQuery"
                            />
                        </el-form-item>
                                                                                                                                                    <el-form-item :label="$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.makerNo')" prop="makerNo">
                            <el-input
                                    v-model="queryParams.makerNo"
                                    :placeholder="$t('common.placeholderDefault') +　$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.makerNo')"
                                    clearable
                                    size="small"
                                    @keyup.enter.native="handleQuery"
                            />
                        </el-form-item>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">{{$t('table.search')}}</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{$t('table.reset')}}</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button
                        type="primary"
                        icon="el-icon-plus"
                        size="mini"
                        @click="handleAdd"
                        v-hasPerm="['entGroupLiaison_add']"
                >{{$t('table.add')}}</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                        type="success"
                        icon="el-icon-edit"
                        size="mini"
                        :disabled="single"
                        @click="handleEdit"
                        v-hasPerm="['entGroupLiaison_edit']"
                >{{$t('table.edit')}}</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                        type="danger"
                        icon="el-icon-delete"
                        size="mini"
                        :disabled="multiple"
                        @click="handleDel"
                        v-hasPerm="['entGroupLiaison_del']"
                >{{$t('table.delete')}}</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                        type="warning"
                        icon="el-icon-download"
                        size="mini"
                        @click="handleExport"
                        v-hasPerm="['entGroupLiaison_export']"
                >{{$t('table.export')}}</el-button>
            </el-col>
            <div class="top-right-btn">
                <el-tooltip class="item" effect="dark" :content="$t('table.refresh')" placement="top">
                    <el-button size="mini" circle icon="el-icon-refresh" @click="handleQuery" />
                </el-tooltip>
                <el-tooltip class="item" effect="dark" :content="showSearch ? $t('common.hideSearch') : $t('common.showSearch')" placement="top">
                    <el-button size="mini" circle icon="el-icon-search" @click="showSearch=!showSearch" />
                </el-tooltip>
            </div>
        </el-row>

        <el-table border stripe v-loading="loading" :data="entGroupLiaisonList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" :selectable="selectInit"/>
            <el-table-column :label="$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.default.serialNumber')" align="center" type="index" :index="indexMethod" width="80"/>
                                                                                <el-table-column :label="$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.id')" align="center" prop="id"  :show-overflow-tooltip="true" v-if="false"/>
                                                                                                                                                            <el-table-column :label="$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.createTime')" align="center" prop="createTime" width="180"  :show-overflow-tooltip="true">
                        <template slot-scope="scope">
                            <span>{{ parseTime(scope.row.createTime) }}</span>
                        </template>
                    </el-table-column>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                        <el-table-column :label="$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.workStatus')" align="center" prop="workStatus" :formatter="workStatusFormat"  :show-overflow-tooltip="true"/>
                                                                                                <el-table-column :label="$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.serialno')" align="center" prop="serialno"  :show-overflow-tooltip="true"/>
                                                                                                <el-table-column :label="$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.makerNo')" align="center" prop="makerNo"  :show-overflow-tooltip="true"/>
                                                                                                <el-table-column :label="$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.makerName')" align="center" prop="makerName"  :show-overflow-tooltip="true"/>
                                                                                                <el-table-column :label="$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.completTime')" align="center" prop="completTime" width="180"  :show-overflow-tooltip="true">
                        <template slot-scope="scope">
                            <span>{{ parseTime(scope.row.completTime) }}</span>
                        </template>
                    </el-table-column>
                                                                                                                                                            <el-table-column :label="$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.signPerson')" align="center" prop="signPerson"  :show-overflow-tooltip="true"/>
                                                                                                <el-table-column :label="$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.signNode')" align="center" prop="signNode"  :show-overflow-tooltip="true"/>
                                                                                                <el-table-column :label="$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.makerdeptno')" align="center" prop="makerdeptno"  :show-overflow-tooltip="true"/>
                                                                                                <el-table-column :label="$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.makerfactoryid')" align="center" prop="makerfactoryid" :formatter="makerfactoryidFormat"  :show-overflow-tooltip="true"/>
                                                                                                                                                                                                                        <el-table-column :label="$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.groupDept')" align="center" prop="groupDept"  :show-overflow-tooltip="true"/>
                                                                                                <el-table-column :label="$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.groupAudit')" align="center" prop="groupAudit"  :show-overflow-tooltip="true"/>
                                                                                                <el-table-column :label="$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.groupIssue')" align="center" prop="groupIssue"  :show-overflow-tooltip="true"/>
                                                                                                <el-table-column :label="$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.groupAddres')" align="center" prop="groupAddres"  :show-overflow-tooltip="true"/>
                                                                                                <el-table-column :label="$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.groupDate')" align="center" prop="groupDate"  :show-overflow-tooltip="true"/>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                <el-table-column :label="$t('table.operation')" align="center" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                  <el-button
                      v-if="scope.row.workStatus == 0&&scope.row.isOwn == true"
                      size="mini"
                      type="text"
                      icon="el-icon-success"
                      @click="handleSubmit(scope.row)"
                  >
                    {{$t('table.submit')}}
                  </el-button>
                  <el-button
                      v-if="scope.row.workStatus ==4&&scope.row.isOwn == true"
                      size="mini"
                      type="text"
                      icon="el-icon-success"
                      @click="handleTask(5,scope.row.processId)"
                  >
                    {{$t('table.activity.reSubmit')}}
                  </el-button>
                  <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-edit"
                      v-if="(scope.row.workStatus == 0||scope.row.workStatus ==4)&&scope.row.isOwn == true"
                      @click="handleEdit(scope.row)"
                  >{{$t('table.edit')}}
                  </el-button>
                  <el-button
                      size="mini"
                      type="text"
                      v-if="scope.row.workStatus == 0&&scope.row.isOwn == true"
                      icon="el-icon-delete"
                      @click="handleDel(scope.row)"
                  >{{$t('table.delete')}}
                  </el-button>
                  <el-button
                      v-if="scope.row.workStatus ==4&&scope.row.isOwn == true"
                      size="mini"
                      type="text"
                      icon="el-icon-success"
                      @click="handleTask(9,scope.row.processId)"
                  >
                    {{$t('table.activity.cancle')}}
                  </el-button>
                  <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-edit"
                      @click="handleDetail(scope.row)"
                  >{{$t('table.detail')}}
                  </el-button>
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-edit"
                    @click="handlePrint(scope.row)"
                  >{{$t('table.print')}}
                  </el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination
                v-show="total>0"
                :total="total"
                :page.sync="queryParams.current"
                :limit.sync="queryParams.size"
                @pagination="getList"
        />

        <!-- 添加或修改iPEBG事業群聯絡單对话框 -->
        <el-dialog :title="title" :visible.sync="open">
            <el-form ref="form" :model="form" :rules="rules" label-width="80px">
                <el-row>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            <el-col :span="24">
                                        <el-form-item :label="$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.makerdeptno')" prop="makerdeptno">
                                            <el-input v-model="form.makerdeptno" :placeholder="$t('common.placeholderDefault') +　$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.makerdeptno') + ''" />
                                        </el-form-item>
                                    </el-col>
                                                                                                                                                                                                                                                                                                <el-col :span="24">
                                        <el-form-item :label="$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.makerfactoryid')"  prop="makerfactoryid">
                                            <el-select v-model="form.makerfactoryid" :placeholder="$t('common.select') +　$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.makerfactoryid')">
                                                <el-option
                                                        v-for="dict in makerfactoryidOptions"
                                                        :key="dict.value"
                                                        :label="dict.label"
                                                        :value="dict.value"
                                                ></el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                                                                                                                                                                                                                                                                                                                            <el-col :span="24">
                                        <el-form-item :label="$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.groupApproval')" prop="groupApproval">
                                            <el-input v-model="form.groupApproval" :placeholder="$t('common.placeholderDefault') +　$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.groupApproval') + ''" />
                                        </el-form-item>
                                    </el-col>
                                                                                                                                                                                                                                                                                                <el-col :span="24">
                                        <el-form-item :label="$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.groupDept')" prop="groupDept">
                                            <el-input v-model="form.groupDept" :placeholder="$t('common.placeholderDefault') +　$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.groupDept') + ''" />
                                        </el-form-item>
                                    </el-col>
                                                                                                                                                                                                                                                                                                <el-col :span="24">
                                        <el-form-item :label="$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.groupAudit')" prop="groupAudit">
                                            <el-input v-model="form.groupAudit" :placeholder="$t('common.placeholderDefault') +　$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.groupAudit') + ''" />
                                        </el-form-item>
                                    </el-col>
                                                                                                                                                                                                                                                                                                <el-col :span="24">
                                        <el-form-item :label="$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.groupIssue')" prop="groupIssue">
                                            <el-input v-model="form.groupIssue" :placeholder="$t('common.placeholderDefault') +　$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.groupIssue') + ''" />
                                        </el-form-item>
                                    </el-col>
                                                                                                                                                                                                                                                                                                <el-col :span="24">
                                        <el-form-item :label="$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.groupAddres')" prop="groupAddres">
                                            <el-input v-model="form.groupAddres" :placeholder="$t('common.placeholderDefault') +　$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.groupAddres') + ''" />
                                        </el-form-item>
                                    </el-col>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            <el-col :span="24">
                                        <el-form-item :label="$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.groupNote')" prop="groupNote">
                                            <el-input v-model="form.groupNote" :placeholder="$t('common.placeholderDefault') +　$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.groupNote') + ''" />
                                        </el-form-item>
                                    </el-col>
                                                                                                                                                                                                                                                                                                <el-col :span="24">
                                        <el-form-item :label="$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.groupDemand')" prop="groupDemand">
                                            <el-input v-model="form.groupDemand" :placeholder="$t('common.placeholderDefault') +　$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.groupDemand') + ''" />
                                        </el-form-item>
                                    </el-col>
                                                                                                                                                                                                                                                                                                <el-col :span="24">
                                        <el-form-item :label="$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.groupMatters')" prop="groupMatters">
                                            <el-input v-model="form.groupMatters" :placeholder="$t('common.placeholderDefault') +　$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.groupMatters') + ''" />
                                        </el-form-item>
                                    </el-col>
                                                                                                                                                                                                                                                                                                <el-col :span="24">
                                        <el-form-item :label="$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.jointDept')" prop="jointDept">
                                            <el-input v-model="form.jointDept" :placeholder="$t('common.placeholderDefault') +　$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.jointDept') + ''" />
                                        </el-form-item>
                                    </el-col>
                                                                                                                                                                                                                                                                                                <el-col :span="24">
                                        <el-form-item :label="$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.jointSignature')" prop="jointSignature">
                                            <el-input v-model="form.jointSignature" :placeholder="$t('common.placeholderDefault') +　$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.jointSignature') + ''" />
                                        </el-form-item>
                                    </el-col>
                                                                                                                                                                                                                                                                                                <el-col :span="24">
                                        <el-form-item :label="$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.jointDirections')" prop="jointDirections">
                                            <el-input v-model="form.jointDirections" :placeholder="$t('common.placeholderDefault') +　$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.jointDirections') + ''" />
                                        </el-form-item>
                                    </el-col>
                                                                                                                                                                                                                                                                                                <el-col :span="24">
                                        <el-form-item :label="$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.cbchargeno')" prop="cbchargeno">
                                            <el-input v-model="form.cbchargeno" :placeholder="$t('common.placeholderDefault') +　$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.cbchargeno') + ''" />
                                        </el-form-item>
                                    </el-col>
                                                                                                                                                                                                                                                                                                <el-col :span="24">
                                        <el-form-item :label="$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.shenhechargeno')" prop="shenhechargeno">
                                            <el-input v-model="form.shenhechargeno" :placeholder="$t('common.placeholderDefault') +　$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.shenhechargeno') + ''" />
                                        </el-form-item>
                                    </el-col>
                                                                                                                                                                                                                                                                                                <el-col :span="24">
                                        <el-form-item :label="$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.hezhunchargeno')" prop="hezhunchargeno">
                                            <el-input v-model="form.hezhunchargeno" :placeholder="$t('common.placeholderDefault') +　$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.hezhunchargeno') + ''" />
                                        </el-form-item>
                                    </el-col>
                                                                                                                                                                                                                                                                                                <el-col :span="24">
                                        <el-form-item :label="$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.huishenchargeno')" prop="huishenchargeno">
                                            <el-input v-model="form.huishenchargeno" :placeholder="$t('common.placeholderDefault') +　$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.huishenchargeno') + ''" />
                                        </el-form-item>
                                    </el-col>
                                                                                                                                                                                                                                                                                                <el-col :span="24">
                                        <el-form-item :label="$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.cbchargename')" prop="cbchargename">
                                            <el-input v-model="form.cbchargename" :placeholder="$t('common.placeholderDefault') +　$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.cbchargename') + ''" />
                                        </el-form-item>
                                    </el-col>
                                                                                                                                                                                                                                                                                                <el-col :span="24">
                                        <el-form-item :label="$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.shenhechargename')" prop="shenhechargename">
                                            <el-input v-model="form.shenhechargename" :placeholder="$t('common.placeholderDefault') +　$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.shenhechargename') + ''" />
                                        </el-form-item>
                                    </el-col>
                                                                                                                                                                                                                                                                                                <el-col :span="24">
                                        <el-form-item :label="$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.hezhunchargename')" prop="hezhunchargename">
                                            <el-input v-model="form.hezhunchargename" :placeholder="$t('common.placeholderDefault') +　$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.hezhunchargename') + ''" />
                                        </el-form-item>
                                    </el-col>
                                                                                                                                                                                                                                                                                                <el-col :span="24">
                                        <el-form-item :label="$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.huishenchargename')" prop="huishenchargename">
                                            <el-input v-model="form.huishenchargename" :placeholder="$t('common.placeholderDefault') +　$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.huishenchargename') + ''" />
                                        </el-form-item>
                                    </el-col>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        <el-col :span="24">
                                        <el-form-item :label="$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.groupUrgent')"  prop="groupUrgent">
                                            <el-radio-group v-model="form.groupUrgent">
                                                <el-radio
                                                        v-for="dict in groupUrgentOptions"
                                                        :key="dict.value"
                                                        :label="dict.value"
                                                >{{dict.label}}</el-radio>
                                            </el-radio-group>
                                        </el-form-item>
                                    </el-col>
                                                                                                                                                                                                                                                                                                <el-col :span="24">
                                        <el-form-item :label="$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.label.groupFiletype')"  prop="groupFiletype">
                                            <el-radio-group v-model="form.groupFiletype">
                                                <el-radio
                                                        v-for="dict in groupFiletypeOptions"
                                                        :key="dict.value"
                                                        :label="dict.value"
                                                >{{dict.label}}</el-radio>
                                            </el-radio-group>
                                        </el-form-item>
                                    </el-col>
                                                                                                                        </el-row>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitForm">{{$t('common.confirm')}}</el-button>
                <el-button @click="cancel">{{$t('common.cancel')}}</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    import { listEntGroupLiaison, getEntGroupLiaison, delEntGroupLiaison, addEntGroupLiaison, editEntGroupLiaison, exportEntGroupLiaison,startProcess } from "@/api/caaesign/entGroupLiaison";
    import {checkTask} from "@/api/activiti/task";
    export default {
        name: "EntGroupLiaison",
        data() {
            return {
                // 遮罩层
                loading: true,
                // 选中数组
                ids: [],
                // 非单个禁用
                single: true,
                // 非多个禁用
                multiple: true,
                // 总条数
                total: 0,
                // iPEBG事業群聯絡單表格数据
                    entGroupLiaisonList: [],
                // 弹出层标题
                title: "",
                // 是否显示弹出层
                open: false,
                // 日期范围
                dateRange: [],
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                // 審核狀態字典
                            workStatusOptions: [],
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        // 所在廠區字典
                            makerfactoryidOptions: [],
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            // 緊急程度字典
                            groupUrgentOptions: [],
                                                                                                                        // 文件類型字典
                            groupFiletypeOptions: [],
                                                    // 查询参数
                queryParams: {
                    current: 1,
                    size: 10,
                                                                                                        createTime: undefined,
                                                                                                                                                                                                                                        workStatus: undefined,
                                                                serialno: undefined,
                                                                makerNo: undefined,
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    },
            // 显示搜索条件
            showSearch: true,
                    // 表单参数
                    form: {},
            // 表单校验
            rules: {
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            }
        };
        },
        created() {
            this.changeTagsView(this.$route.query);
            this.getList();
                                                                                                                                                                                                                                                                                                                                    this.getDicts("work_status").then(response => {
                            this.workStatusOptions = response.data;
                            if (response.data.length > 0) {
                                response.data.forEach(element => {
                                    this.getOptions(element);
                                });
                            }
                        });
                                                                                                                                                                                                                                                                                                                                            this.getDicts("caaesign_factory").then(response => {
                            this.makerfactoryidOptions = response.data;
                            if (response.data.length > 0) {
                                response.data.forEach(element => {
                                    this.getOptions(element);
                                });
                            }
                        });
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                this.getDicts("group_urgent").then(response => {
                            this.groupUrgentOptions = response.data;
                            if (response.data.length > 0) {
                                response.data.forEach(element => {
                                    this.getOptions(element);
                                });
                            }
                        });
                                                                                                            this.getDicts("group_filetype").then(response => {
                            this.groupFiletypeOptions = response.data;
                            if (response.data.length > 0) {
                                response.data.forEach(element => {
                                    this.getOptions(element);
                                });
                            }
                        });
                                                        },
        methods: {
            /** 查询iPEBG事業群聯絡單列表 */
            getList() {
                this.loading = true;
                listEntGroupLiaison(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
                    this.entGroupLiaisonList = response.data;
                    this.total = response.total;
                    this.loading = false;
                });
            },
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    // 審核狀態字典翻译
                    // select
                                            workStatusFormat(row, column) {
                            if(row!=null&&row.workStatus&& row.workStatus!="null"){
                                return this.selectDictLabel(this.workStatusOptions, row.workStatus);
                            }else{
                                return "";
                            }
                        },
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            // 所在廠區字典翻译
                    // select
                                            makerfactoryidFormat(row, column) {
                            if(row!=null&&row.makerfactoryid&& row.makerfactoryid!="null"){
                                return this.selectDictLabel(this.makerfactoryidOptions, row.makerfactoryid);
                            }else{
                                return "";
                            }
                        },
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                // 緊急程度字典翻译
                    // radio
                                            groupUrgentFormat(row, column) {
                            if(row!=null&&row.groupUrgent&& row.groupUrgent!="null"){
                                return this.selectDictLabel(this.groupUrgentOptions, row.groupUrgent);
                            }else{
                                return "";
                            }
                        },
                                                                                                                                            // 文件類型字典翻译
                    // radio
                                            groupFiletypeFormat(row, column) {
                            if(row!=null&&row.groupFiletype&& row.groupFiletype!="null"){
                                return this.selectDictLabel(this.groupFiletypeOptions, row.groupFiletype);
                            }else{
                                return "";
                            }
                        },
                                                                            selectInit(row, index) {
                if (row.workStatus == 0 || row.workStatus == 4) {
                    return true  //不可勾选
                } else {
                    return false  //可勾选
                }
            },
            // 取消按钮
            cancel() {
                this.open = false;
                this.reset();
            },
            // 表单重置
            reset() {
                this.form = {
                                                            id: undefined,
                                                                                createBy: undefined,
                                                                                createTime: undefined,
                                                                                updateBy: undefined,
                                                                                updateTime: undefined,
                                                                                remarks: undefined,
                                                                                delFlag: undefined,
                                                                                deptId: undefined,
                                                                                processId: undefined,
                                                                                workStatus: undefined,
                                                                                serialno: undefined,
                                                                                makerNo: undefined,
                                                                                makerName: undefined,
                                                                                completTime: undefined,
                                                                                attachids: undefined,
                                                                                signPerson: undefined,
                                                                                signNode: undefined,
                                                                                makerdeptno: undefined,
                                                                                makerfactoryid: undefined,
                                                                                dataSource: undefined,
                                                                                groupApproval: undefined,
                                                                                groupDept: undefined,
                                                                                groupAudit: undefined,
                                                                                groupIssue: undefined,
                                                                                groupAddres: undefined,
                                                                                groupDate: undefined,
                                                                                groupNote: undefined,
                                                                                groupDemand: undefined,
                                                                                groupMatters: undefined,
                                                                                jointDept: undefined,
                                                                                jointSignature: undefined,
                                                                                jointDirections: undefined,
                                                                                cbchargeno: undefined,
                                                                                shenhechargeno: undefined,
                                                                                hezhunchargeno: undefined,
                                                                                huishenchargeno: undefined,
                                                                                cbchargename: undefined,
                                                                                shenhechargename: undefined,
                                                                                hezhunchargename: undefined,
                                                                                huishenchargename: undefined,
                                                                                createDate: undefined,
                                                                                updateDate: undefined,
                                                                                groupUrgent: "0",
                                                                                groupFiletype: "0"
                                                };
                this.resetForm("form");
            },
            /** 搜索按钮操作 */
            handleQuery() {
                this.queryParams.current = 1;
                this.getList();
            },
            /** 重置按钮操作 */
            resetQuery() {
                this.dateRange= []
                this.resetForm("queryForm");
            },
            // 多选框选中数据
            handleSelectionChange(selection) {
                this.ids = selection.map(item => item.id)
                this.single = selection.length!=1
                this.multiple = !selection.length
            },
            /** 新增按钮操作 */
            handleAdd() {
                this.$router.push('/caaesign/entGroupLiaison/EntGroupLiaisonAdd');
            },
            /** 修改按钮操作 */
            handleEdit(row) {
                const id = row.id || this.ids
                if(row.workStatus == "4"){ //駁回
                    this.$router.push({path:'/caaesign/entGroupLiaison/EntGroupLiaisonReject', query: { id: id } });
                }else{
                    this.$router.push({path:'/caaesign/entGroupLiaison/EntGroupLiaisonEdit', query: { id: id } });
                }
            },
            /** 提交按钮 */
            submitForm: function() {
                this.$refs["form"].validate(valid => {
                    if (valid) {
                        if (this.form.id != undefined) {
                            editEntGroupLiaison(this.form).then(response => {
                                if (response.code === 0) {
                                    this.msgSuccess(this.$t('tips.updateSuccess'));
                                    this.open = false;
                                    this.getList();
                                } else {
                                    this.msgError(response.msg);
                                }
                            });
                        } else {
                            addEntGroupLiaison(this.form).then(response => {
                                if (response.code === 0) {
                                    this.msgSuccess(this.$t('tips.createSuccess'));
                                    this.open = false;
                                    this.getList();
                                } else {
                                    this.msgError(response.msg);
                                }
                            });
                        }
                    }
                });
            },
            /** 删除按钮操作 */
            handleDel(row) {
                const ids = row.id || this.ids;
                let functionName = this.$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.default.functionName');
                this.$confirm(this.$t('tips.deleteConfirm',[functionName,ids]),  this.$t('tips.warm'), {
                    confirmButtonText: this.$t('common.confirmTrim'),
                    cancelButtonText: this.$t('common.cancelTrim'),
                    type: "warning"
                }).then(function() {
                    return delEntGroupLiaison(ids);
                }).then(() => {
                    this.getList();
                    this.msgSuccess(this.$t('tips.deleteSuccess'));
                }).catch(function() {});
            },
            /** 导出按钮操作 */
            handleExport() {
                const queryParams = this.queryParams;
                let functionName = this.$t('ent_group_liaison_075d399c2dcaa20f94d53895254e58a1.default.functionName');
                this.$confirm(this.$t('tips.exportConfirm',[functionName]), this.$t('tips.warm'), {
                    confirmButtonText: this.$t('common.confirmTrim'),
                    cancelButtonText: this.$t('common.cancelTrim'),
                    type: "warning"
                }).then(function() {
                    return exportEntGroupLiaison(queryParams);
                }).then(response => {
                    this.download(response.data);
                }).catch(function() {});
            },
            handleDetail(row) {
                const id = row.id || this.ids
                this.$router.push({path:'/caaesign/entGroupLiaison/EntGroupLiaisonDetail', query: { id: id } });
            },
          handlePrint(row) {
            const id = row.id || this.ids
            this.$router.push({path:'/caaesign/entGroupLiaison/EntGroupLiaisonPrint', query: { id: id } });
          },
            handleSubmit: function(row, index) {
                this.$confirm(this.$t('tips.submitConfirm' , [row.id]), this.$t('tips.warm'), {
                    confirmButtonText: this.$t('common.confirmTrim'),
                    cancelButtonText: this.$t('common.cancelTrim'),
                    type: 'warning'
                }).then(function() {
                    return startProcess(row.id)
                }).then(() => {
                    this.getList();
                    this.msgSuccess(this.$t('tips.submitSuccess'));
                })
            },
            indexMethod(index){
                return index + 1 + (this.queryParams.current - 1)*this.queryParams.size;
            },
          handleTask: function (pass,processId) {
            this.isDisabled = true;
            this.form.pass = pass
            this.form.processId = processId
            checkTask(this.form).then(response => {
              if (response.code === 0) {
                this.msgSuccess(this.$t('tips.operationSuccessful'));
                this.getList();
                this.closeForm();
              } else {
                this.msgError(response.msg);
              }
            });
          },
        }
    };
</script>
