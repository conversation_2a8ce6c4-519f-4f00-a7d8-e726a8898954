<template>
  <div class="ant-modal-content">
    <div class="ant-modal-body">
      <el-form ref="elForm" class="ant-form ant-form-horizontal" :model="formData" size="medium"
        label-width="100px" :label-position="labelPosition">
        <div class="ant-card ant-card-bordered" id="staffCard-GroupLiaison">
          <div class="ant-card-body" id="printContent">

            <div class="page-header">
              <img  class="page-header-image"  :src="imgHederUrl" alt=""/>
            </div>
            <table>
              <thead>
              <tr>
                <td>
                  <div class="page-header-space">
                    <img class="print-hide-div" style="height: 1cm;width: 100%" :src="imgHederUrl" alt=""/>
                  </div>
                </td>
              </tr>
              </thead>
              <tbody>
              <tr>
                <td>
            <el-row :gutter="15">

              <div class="titleDiv">
              <span id="staffEvectionTitle">
                iPEBG事業群<br />
                <span class="titleUnderline">聯絡單</span>
              </span>
                <div class="titleRight">
                  <el-form-item label-width="1px" label="" prop="groupUrgent">
                    <el-radio-group v-model="formData.groupUrgent" size="medium" :disabled="isDisable">
                      <el-radio v-for="(item, index) in groupUrgentOptions" :key="index" :label="item.value"
                                :disabled="item.disabled">{{item.label}}</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item label-width="1px" label="" prop="groupFiletype" >
                    <el-radio-group v-model="formData.groupFiletype" size="medium" :disabled="isDisable">
                      <el-radio v-for="(item, index) in groupFiletypeOptions" :key="index" :label="item.value"
                                :disabled="item.disabled">{{item.label}}</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  總頁次：2
                </div>
              </div>

              <el-col :span="12" :xs="24">
                <el-form-item label="呈                  核：" prop="groupApproval">
                  <div class="view-text-under ">{{formData.groupApproval}} </div>
                </el-form-item>
              </el-col>
              <el-col :span="12" :xs="24">
                <el-form-item label=" 發文單位：" prop="groupDept">
                  <div class="view-text-under ">{{formData.groupDept}} </div>
                </el-form-item>
              </el-col>
              <el-col :span="12" :xs="24">
                <el-form-item label="會審單位：" prop="groupAudit">
                  <div class="view-text-under ">{{formData.groupAudit}} </div>
                </el-form-item>
              </el-col>
              <el-col :span="12" :xs="24">
                <el-form-item label="發文字號：" prop="groupIssue">
                  <div class="view-text-under ">{{formData.groupIssue}} </div>
                </el-form-item>
              </el-col>
              <el-col :span="12" :xs="24">
                <el-form-item label="受文單位：" prop="groupAddres">
                  <div class="view-text-under ">{{formData.groupAddres}} </div>
                </el-form-item>
              </el-col>
              <el-col :span="12" :xs="24">
                <el-form-item label="發文日期：" prop="groupDate">
                  <div class="view-text-under ">{{formData.groupDate}} </div>
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24">
                <el-form-item  prop="groupNote">
                  <template v-slot:label>
                    <span class="mainFont ">主旨：</span>
                  </template>
                  <div class="view-text-under-bold ">{{formData.groupNote}} </div>
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24">
                <label-form :label=formConfigData.beijingshuoming_label labelWidth='120' labelPosition='left'
                  labelSize='14' textSize='14'></label-form>
              </el-col>
              <el-col :span="24" :xs="24">
                <el-form-item label-width="1px" label="" prop="groupDemand">
                  {{formData.groupDemand}}
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24">
                <label-form :label=formConfigData.shixiang_label labelWidth='120' labelPosition='left'
                  labelSize='14' textSize='14'></label-form>
              </el-col>
              <el-col :span="24" :xs="24">
                <el-form-item label-width="1px" label="" prop="groupMatters">
                  {{formData.groupMatters}}
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24">
                <label-form :label=formConfigData.fujian_label labelWidth='120' labelPosition='left'
                  labelSize='14' textSize='14'></label-form>
              </el-col>
              <el-col :span="24" :xs="24">
                <el-form-item label-width="1px" label="" prop="attachids">
                  <el-upload :file-list="upload.fileList" :show-file-list="true" :on-preview="handlePreview"
                    :headers="upload.headers" :action="upload.url" list-type="text" :disabled='true'>
                  </el-upload>
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24" class="print-hide-div">
                <div class="dialog-footer" align="center" style="padding-top: 5px;padding-bottom: 5px">
                  <el-button type="danger" @click="closeForm">{{$t('common.close')}}</el-button>
                  <el-button type="primary" v-print="'#printContent'">{{$t('common.print')}}</el-button>
                  <el-button type="info" @click="handleTrack"
                    v-if="formData.workStatus == 2||formData.workStatus ==3||formData.workStatus ==4">
                    {{$t('table.activity.flowTracing')}}</el-button>
                </div>
              </el-col>
              <el-col :span="24" :xs="24">
                <el-row :gutter="15" type="flex" align="bottom">
                  <el-col :span="8" class="bar-to-bottom">
                    <el-form-item label="核准：" label-width="80px">
                      <div class="sign-textarea">
                        <div  v-for="comment in hezhunSignList" class="signImageDiv">
                          <el-image
                            :key="comment.empNo"
                            :src="comment.imgUrl"
                            fit="fill"
                            class="signImage"
                          >
                          </el-image>
                          <div class="signImageUnderText">
                            <p>{{ parseTime(comment.time, '{y}-{m}-{d}') }}</p>
                          </div>
                        </div>
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" >
                    <el-form-item label="審核：" label-width="80px">
                      <div class="sign-textarea">
                        <div  v-for="comment in shenheSignList" class="signImageDiv">
                          <el-image
                            :key="comment.empNo"
                            :src="comment.imgUrl"
                            fit="fill"
                            class="signImage"
                          >
                          </el-image>
                          <div class="signImageUnderText">
                            <p>{{ parseTime(comment.time, '{y}-{m}-{d}') }}</p>
                          </div>
                        </div>
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" >
                    <el-form-item label-width="80px" label="承辦：" class="sign-label">
                      <!--                    <template v-slot:label>-->
                      <!--                      <span class="sign-label ">承辦：</span>-->
                      <!--                    </template>-->
                      <div class="sign-textarea">
                        <div  v-for="comment in chengbanSignList" class="signImageDiv">
                          <el-image
                            :key="comment.empNo"
                            :src="comment.imgUrl"
                            fit="fill"
                            class="signImage"
                          >
                          </el-image>
                          <div class="signImageUnderText">
                            <p>{{ parseTime(comment.time, '{y}-{m}-{d}') }}</p>
                          </div>
                        </div>
                      </div>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-col>
              <el-col :span="24" :xs="24">
                <img  style="height: 4cm;width: 100%" :src="imgQianheliuchengtuUrl" alt=""/>
              </el-col>

              <div class="page-break-after"></div>

              <el-col :span="24" :xs="24">
                <div class="titleDiv">
                <span id="staffEvectionTitle">
                  iPEBG事業群<br />
                  <span class="titleUnderline">聯絡單簽核頁</span>
                </span>
                  <div class="title-sign-right">
                    發文字號：[行政]xxxx-xxx
                  </div>
                </div>
              </el-col>
              <el-col :span="24" :xs="24">
                <el-form-item  prop="groupNote">
                  <template v-slot:label>
                    <span class="mainFont ">主旨：</span>
                  </template>
                  <div class="view-text-under-bold ">{{formData.groupNote}} </div>
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24">
                <label-form label="會簽" labelWidth='60' labelPosition='left'
                            labelSize='14' textSize='14'></label-form>
              </el-col>
              <el-col :span="24" :xs="24">
                <div class="sign-list-table">
                  <el-table :data="huishenSignList" >
                    <el-table-column :label="$t('table.activity.deptName')" align="center" prop="deptName" />
                    <el-table-column :label="$t('table.activity.signImage')" align="center"  >
                      <template v-slot:default="scope">
                        <div class="hq-sign-textarea">
                          <div class="hq-signImageDiv">
                            <el-image
                              :src="scope.row.imgUrl"
                              fit="fill"
                              class="hq-signImage"
                            >
                            </el-image>
                            <div class="signImageUnderText">
                              <p>{{ parseTime(scope.row.time, '{y}-{m}-{d}') }}</p>
                            </div>
                          </div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column :label="$t('table.activity.instructions')" align="center"
                                     prop="fullMessage" :show-overflow-tooltip="true" >
                      <template v-slot:default="scope">
                        <div class="hq-sign-textarea">
                          {{ scope.row.fullMessage }}
                        </div>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-col>
              <el-col :span="24" :xs="24" class="print-hide-div">
                <label-form :label=formConfigData.groupnode_label labelWidth='110' labelPosition='left'
                            labelSize='14' :textarea=formConfigData.groupnode_text textSize='14'></label-form>
              </el-col>
            </el-row></td>
              </tr>
              </tbody>
              <tfoot>
              <tr>
                <td><div class="page-footer-space"></div></td>
              </tr>
              </tfoot>
            </table>

              <!-- 任务跟踪对话框 -->
              <el-dialog :title="$t('table.activity.taskMap')" :visible.sync="showImgDialog" width="1000px">
                <img :src="imgUrl" style="padding-bottom: 60px;width:100%;">
              </el-dialog>
            <div class="page-footer">
              {{formConfigData.groupnode_label + formConfigData.groupnode_text}}
            </div>
          </div>

        </div>
      </el-form>
    </div>
  </div>
</template>
<script>
import {
  getEntGroupLiaison,
  addEntGroupLiaison,
  editEntGroupLiaison,
  getSignPath,
  updateAndCheck,
  getSignConfigList
}
from "@/api/caaesign/entGroupLiaison"
import '@/assets/styles/design-build/design-add-view.scss'
import {
  listTask,
  getTask,
  checkTask,
  taskComment
}
from "@/api/activiti/task"
import {
  getAccessToken,
  getZltAccessToken
}
from "@/utils/auth";
import {
  previewFile,
  getHeader,
  showSignatureImgWithEmpNo, parseTime
}
  from '@/utils/entfrm'
import {
  listFileInfo,
  getFileInfo,
  delFileInfo,
  addFileInfo,
  editFileInfo,
  getByKey
}
from "@/api/system/fileInfo";
import imgHeder from '@/assets/images/A3.jpg'
import imgQianheliuchengtu from '@/assets/images/qianheliuchengtu.png'
export default {
  components: {},
  props: [],
  data() {
    return {
      imgHederUrl: imgHeder,
      imgQianheliuchengtuUrl: imgQianheliuchengtu,
      formData: {
        groupUrgent: undefined,
        groupFiletype: undefined,
        groupApproval: undefined,
        groupDept: undefined,
        groupAudit: undefined,
        groupIssue: undefined,
        groupAddres: undefined,
        groupDate: null,
        groupNote: undefined,
        groupDemand: undefined,
        groupMatters: undefined,
        attachids: "",
        cbchargeno: undefined,
        shenhechargeno: undefined,
        huishenchargeno: undefined,
        hezhunchargeno: undefined,
      },
      formConfigData: {
        title: "iPEBG事業群聯絡單",
        beijingshuoming_label: `一、背景說明：`,
        beijingshuoming_text: ``,
        shixiang_label: `二、申請事項：`,
        shixiang_text: ``,
        fujian_label: `三、相關附件：`,
        fujian_text: ``,
        groupnode_label: `表單固定編碼：`,
        groupnode_text: `行政-人資Ⅶ-行政-003-2024A      `,
        cbchargeno: "承辦",
        cbchargeno_required: true,
        shenhechargeno: "審核",
        shenhechargeno_required: true,
        huishenchargeno: "會審",
        huishenchargeno_required: true,
        hezhunchargeno: "核准",
        hezhunchargeno_required: true,
      },
      rules: {
        groupUrgent: [{
          required: true,
          message: '不能為空',
          trigger: 'change'
        }],
        groupFiletype: [{
          required: true,
          message: '不能為空',
          trigger: 'change'
        }],
        groupApproval: [{
          required: true,
          message: '請輸入呈核呈核',
          trigger: 'blur'
        }],
        groupDept: [{
          required: true,
          message: '請輸入 發文單位',
          trigger: 'blur'
        }],
        groupAudit: [{
          required: true,
          message: '請輸入會審單位',
          trigger: 'blur'
        }],
        groupIssue: [{
          required: true,
          message: '請輸入發文字號',
          trigger: 'blur'
        }],
        groupAddres: [{
          required: true,
          message: '請輸入受文單位',
          trigger: 'blur'
        }],
        groupDate: [{
          required: true,
          message: '请選擇發文日期',
          trigger: 'change'
        }],
        groupNote: [{
          required: true,
          message: '請輸入主旨',
          trigger: 'blur'
        }],
        groupDemand: [{
          required: true,
          message: '請輸入背景說明',
          trigger: 'blur'
        }],
        groupMatters: [{
          required: true,
          message: '請輸入申請事項',
          trigger: 'blur'
        }],
        attachids: [],
        cbchargeno: [{
          required: true,
          message: '承辦不能為空',
          trigger: 'change'
        }],
        shenhechargeno: [{
          required: true,
          message: '審核不能為空',
          trigger: 'change'
        }],
        huishenchargeno: [{
          required: true,
          message: '會審不能為空',
          trigger: 'change'
        }],
        hezhunchargeno: [{
          required: true,
          message: '核准不能為空',
          trigger: 'change'
        }],
      },
      tableHeight: document.documentElement.scrollHeight - 245 + "px",
      // 审批意见数据
      commentList: [],
      //簽核路徑
      signPath: [],
      //任务图url
      imgUrl: '',
      isDisable: true,
      // 是否显示任务图
      showImgDialog: false,
      auditStatus: [],
      // 文件上传参数
      upload: {
        // 是否显示弹出层
        open: false,
        fileList: [],
        fileNameList: [],
        // 弹出层标题
        title: "",
        multiple: false,
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: this.getHeader(),
        // 上传的地址
        url: this.getAppBaseApi() + "/system/fileInfo/upload"
      },
      groupUrgentOptions: [],
      groupFiletypeOptions: [],
      isMobile: false,
      labelPosition: 'left',
      chengbanSignList: [],
      shenheSignList: [],
      hezhunSignList: [],
      huishenSignList: [],
    }
  },
  computed: {},
  watch: {},
  created() {
    const id = this.$route.query.id;
    this.changeTagsView(this.$route.query);
    this.isMobile = this.isMobileFun()
    if (this.isMobile) {
      this.labelPosition = 'top'
    }
    getSignConfigList().then(response => {
      if (response.code === 0) {
        response.data.forEach(element => {
          const colKey = element.colKey.split('_')
          if (colKey.length > 1) {
            if (this.rules[colKey[0]] && colKey[1] === 'required') {
              this.rules[colKey[0]][0]['required'] = element.colValue === 'true'
              this.formConfigData[element.colKey] = element.colValue === 'true'
            }
            else {
              this.formConfigData[element.colKey] = element.colValue
            }
          }
          else {
            this.formConfigData[element.colKey] = element.colValue
          }
        });
      }
    })
    if (id != null && id != undefined) {
      getEntGroupLiaison(id).then(response => {
        this.formData = response.data;
        this.checkBoxParse()
        this.cascaderParse()
        this.timeRangParse()
        taskComment(this.formData.processId).then(response => {
          this.commentList = response.data;
        }).then( _ => {
          this.chengbanSignList = []
          this.shenheSignList = []
          this.hezhunSignList = []
          this.huishenSignList = []
          let i = 0
          this.commentList.forEach(comment => {
            var userArray = comment.userId.split('/')
            if(userArray.length > 0){
              comment.empNo = userArray[0]
            }
            this.$set(comment, 'imgUrl', '');
            if(comment.id === '承辦'){
              this.chengbanSignList.unshift(comment)
            }else if(comment.id === '審核'){
              this.shenheSignList.unshift(comment)
            }else if(comment.id === '核准'){
              this.hezhunSignList.unshift(comment)
            }else {
              if(i === 0){
                comment.deptName = "iPEG人力資源處"
                i++
              }
              this.huishenSignList.push(comment)
            }
          })
          this.commentList.forEach(comment => {
            showSignatureImgWithEmpNo(comment.empNo).then(res => {
              var blob = new Blob([res.data], {type: ""})
              this.$set(comment, 'imgUrl', URL.createObjectURL(blob));
            })
          })
        });
        getSignPath(this.formData.processId).then(response => {
          this.signPath = response.data;
        });
        if (this.formData.attachids) {
          let a = this.formData.attachids.split(',');
          if (a.length > 0) {
            a.forEach(item => {
              if (item) {
                getByKey(item).then(response => {
                  this.upload.fileList.push({
                    name: response.data.orignalName,
                    url: response.data.name
                  });
                })
              }
            })
          }
        }
        this.getGroupUrgentOptions()
        this.getGroupFiletypeOptions()
        this.getDicts("group_urgent").then(response => {
          if (this.formData.groupUrgent) {
            this.formData.groupUrgentValue = JSON.parse(JSON.stringify(this.formData.groupUrgent))
            response.data.forEach(item => {
              if (this.formData.groupUrgent) {
                this.formData.groupUrgentValue = this.formData.groupUrgentValue.replace(item
                  .value, item.label);
              }
            })
            this.$set(this.formData, this.formData.groupUrgentValue, this.formData.groupUrgentValue)
          }
        });
        this.getDicts("group_filetype").then(response => {
          if (this.formData.groupFiletype) {
            this.formData.groupFiletypeValue = JSON.parse(JSON.stringify(this.formData.groupFiletype))
            response.data.forEach(item => {
              if (this.formData.groupFiletype) {
                this.formData.groupFiletypeValue = this.formData.groupFiletypeValue.replace(item
                  .value, item.label);
              }
            })
            this.$set(this.formData, this.formData.groupFiletypeValue, this.formData
              .groupFiletypeValue)
          }
        });
      });
    }
    this.getDicts("sign_status").then(response => {
      this.auditStatus = response.data;
    });
  },
  mounted() {
    this.$nextTick(function() {
      if (this.isMobile) {}
    })
  },
  methods: {
    parseTime,
    closeForm() {
      //关闭子页面
      if (this.$store.state.settings.tagsView) {
        this.$router.go(-1) // 返回
        this.$store.state.tagsView.visitedViews.splice(this.$store.state.tagsView.visitedViews.findIndex(
          item => item.path === this.$route.path), 1)
        this.$router.push(this.$store.state.tagsView.visitedViews[this.$store.state.tagsView.visitedViews
          .length - 1].path)
      }
      else {
        parent.postMessage("closeCurrentTabMessage", '*');
      }
    },
    handleTrack() {
      this.imgUrl = this.getAppBaseApi() + '/activiti/task/track/' + this.formData.processId,
        this.showImgDialog = true
    },
    statusFormat(row, column) {
      return this.selectDictLabel(this.auditStatus, row.status);
    },
    getGroupUrgentOptions() {
      this.getDicts("group_urgent").then(response => {
        this.groupUrgentOptions = response.data;
      });
    },
    getGroupFiletypeOptions() {
      this.getDicts("group_filetype").then(response => {
        this.groupFiletypeOptions = response.data;
      });
    },
    attachidsBeforeUpload(file) {
      let isRightSize = file.size / 1024 / 1024 < 2
      if (!isRightSize) {
        this.$message.error('文件大小超过 2MB')
      }
      return isRightSize
    },
    // 文件上传成功处理
    uploadsucces(response, file, fileList) {
      if (response.data.name != undefined && response.data.name != "undefined") {
        this.upload.fileList.push({
          name: file.name,
          url: response.data.name
        });
        this.formData.attachids += response.data.name + ",";
      }
    },
    handleChange(file, fileList) {},
    handleExceed(file, fileList) {},
    handleRemove(file, fileList) {
      this.$emit("delUploadImage", file.name);
      const index = this.upload.fileList.indexOf(file);
      this.upload.fileList.splice(index, 1);
      if (this.formData.attachids) {
        this.formData.attachids = this.formData.attachids.replace(file.url + ",", "");
      }
      this.upload.fileNameList.push(file.url);
      // delFileInfo(file.url);
    },
    handlePreview(file) {
      previewFile(file.url)
    },
    checkBoxParse() {},
    cascaderParse() {},
    timeRangParse() {},
  }
}

</script>
<style scoped>
.el-upload__tip {
  line-height: 1.2;
}

.del-handler {
  font-size: 20px;
  color: #ff4949;
}

.el-dialog {
  position: relative;
  margin: 0 auto 0px;
  background: #FFFFFF;
  border-radius: 2px;
  -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 50%;
  height: 60%;
}

.el-dialog__body {
  border-top: 1px solid #dcdfe6;
  border-bottom: 1px solid #dcdfe6;
  max-height: 85% !important;
  min-height: 70%;
  overflow-y: auto;
}

.el-slider>/deep/.el-slider__runway {
  margin: 15px 0;
}

.el-checkbox-group {
  font-size: inherit;
}

.el-color-picker {
  display: inherit;
}

.talbe-name-style {
  font-size: 16px;
  font-weight: bold;
  color: #1DB8FF;
  line-height: 50px;
}

/deep/.el-input-number--medium {
  width: 100%;
}

/deep/.el-scrollbar__view table td {
  padding: 0;
}

.el-form-item {
  margin-top: 5px;
  margin-bottom: 5px;
}

.ant-card {
  color: black;
}

.el-table {
  color: black;
}
.ant-modal-content .el-col:not(.el-col-no-border) {
  border: 0 solid #ccc;
  margin-top: 0;
}
/deep/input,
/deep/textarea {
  border-left-color: transparent;
  border-right-color: transparent;
  border-top-color: transparent;
  border-bottom-color: black;
  border-radius: 0;
}

#staffEvectionTitle {
  margin-bottom: 10px;
}
.titleUnderline {
  border-bottom: 2px solid;
  font-weight: 700;
  color: #000;
  font-size: 24px;
}
.titleDiv {
  height: 120px;
  position: relative;
}
.titleRight{
  position: absolute;
  right: 7.5px;
  top: 0;
  border: 1px solid black;
  padding-left: 2px;
  padding-right: 2px;
  height: 70px;
}
.title-sign-right{
  float: right;
  margin-right: 10px;
}
.titleRight .el-form-item{
  margin-top: 0px;
  margin-bottom: 0px;
}
/deep/ .el-form-item__label{
  height: 100%;
  text-align: justify;
  text-align-last: justify;
}
.mainFont {
  font-size: 20px;
  font-weight: 600;
  color: #000;
}
.view-text-under {
  width: 100%;
  height: 100%;
  border-bottom: 1px solid black;
}
.view-text-under-bold {
  width: 100%;
  height: 100%;
  border-bottom: 1px solid black;
  font-weight: 600;
  font-size: 20px;
  text-align: center;
}
.sign-textarea {
  height: 60px;
  border-bottom: 1px solid black;
  align-content: center;
  text-align: center;
}
.signImageDiv{
  width: 55px;
  height: 50px;
  display: inline-block;
  padding: 1px 1px 1px 1px;
}
.signImage {
  display: block;
  width: 100%;
  height: 25px;
}

.hq-sign-textarea {
  height: 80px;
  border-bottom: 1px solid black;
  align-content: center;
  text-align: center;
}
.hq-signImageDiv{
  width: 60px;
  height: 60px;
  display: inline-block;
  padding: 1px 1px 1px 1px;
}
.hq-signImage {
  display: block;
  width: 100%;
  height: 30px;
}
.sign-label .el-form-item__label{
  align-items: flex-end;
  line-height: 138px;
}
.signImageUnderText {
  text-align: center;
  width: 100%;
  font-size: 10px;
}
/deep/ .el-table__header-wrapper th {
  background-color: white;
  color: black;
}

.sign-list-table >>> .el-table__row>td{
  /* 去除表格线 */
  border: none;
}
.sign-list-table >>> .el-table th.is-leaf {
  /* 去除上边框 */
  border: none;
}
.sign-list-table >>> .el-table::before{
  /* 去除下边框 */
  height: 0;
}
@media print {
  .print-hide-div {
    display: none;
  }
  .page-break-after{
    page-break-after: always;
  }
  #printContent .page-header-image {
     height: 1cm !important;
     width: 100%;
  }
  #printContent .page-footer {
    /* 页脚高度 */
    height: 1cm !important;
  }
}


#staffCard-GroupLiaison {
  border-radius: 5px;
  border: 1px solid #fff;
  /* box-shadow: 0 0 1px 1px #aaa, 3px 0 5px 0 #aaa, 0 4px 7px 0 #aaa;*/
  width: 100%;
  height: auto;
  padding-bottom: 40px;

  margin: 0px auto;
  max-width: 794px;
}
/deep/ .el-upload {
    display: none;
}

#printContent .page-header {
  height: 1cm;
  display: flex;
  align-items: center;
  position: fixed;
  top: 0mm;
  width: 100%;
  z-index: 2000;
}
#printContent .page-header-image {
  height: 0;
  width: 100%;
}

/* 頁眉頁腳 start */
#printContent .page-header-space {
  /* 控制内容区距离顶部的距离，防止与页眉重叠 */
  height: 1cm;
}

#printContent .page-footer {
  /* 页脚高度 */
  height: 0cm;
  position: fixed;
  bottom: 0;
  width: 100%;
  /* border-top: 1px solid grey; */
  z-index: 2000;
}

#printContent .page-footer-space {
  /* 控制内容区距离底部的距离，防止与页脚重叠 */
  height: 1.5cm;
}

#printContent > table {
  width: 100%;
}

#printContent .content table {
  width: 100%;
  font-size: 30px;
}

#printContent .content .header {
  text-align: center;
}
/* 頁眉頁腳 end */

</style>
