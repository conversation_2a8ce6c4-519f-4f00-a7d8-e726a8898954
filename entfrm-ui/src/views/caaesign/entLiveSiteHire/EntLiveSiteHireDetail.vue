<template>
  <div class="ant-modal-content">
    <div class="ant-modal-body">
      <el-row :gutter="15">
        <el-form ref="elForm" class="ant-form ant-form-horizontal" :model="formData" :rules="rules"
          size="medium" label-width="100px" :label-position="labelPosition">
          <div class="ant-card ant-card-bordered" id="staffCard">
            <div class="ant-card-body" id="printContent">
              <span id="staffEvectionTitle" style="margin-bottom: 10px">生活服務區場地佔用申請單</span>
              <el-col :span="10" :xs="24" class="el-col-no-border" id="serialno" >
                任務編碼:{{formData.serialno?formData.serialno:'提交表單后自動生成'}}
              </el-col>
              <el-col :span="8" :xs="24" class="el-col-no-border" id="createTime">
                填單時間:{{formData.createTime}}
              </el-col>
              <el-col :span="6" :xs="24" class="el-col-no-border" id="makerNo">
                填單人:{{formData.makerNo +' / '+ formData.makerName}}
              </el-col>
              <el-col :span="24" :xs="24">
                <el-form-item label="商戶類型" prop="businessType">
                  {{formData.businessTypeName}}
                </el-form-item>
              </el-col>
              <el-col :span="5" :xs="24">
                <el-form-item label="申請人工號" prop="applyEmpNo">
                    {{formData.applyEmpNo}}
                </el-form-item>
              </el-col>
              <el-col :span="5" :xs="24">
                <el-form-item label-width="50px" label="姓名" prop="applyEmpName">
                  {{formData.applyEmpName}}
                </el-form-item>
              </el-col>
              <el-col :span="4" :xs="24">
                <el-form-item label-width="50px" label="電話" prop="applyTel">
                  {{formData.applyTel}}
                </el-form-item>
              </el-col>
              <el-col :span="5" :xs="24">
                <el-form-item label-width="80px" label="部門代碼" prop="applyDeptNo">
                  {{formData.applyDeptNo}}
                </el-form-item>
              </el-col>
              <el-col :span="5" :xs="24">
                <el-form-item label-width="50px" label="廠區" prop="makerfactoryid">
                  {{formData.siteName}}
                </el-form-item>
              </el-col>
              <el-col :span="14" :xs="24">
                <el-form-item label="申請部門" prop="applyDeptName">
                  {{formData.applyDeptName}}
                </el-form-item>
              </el-col>
              <el-col :span="10" :xs="24">
                <el-form-item label="申請日期" prop="applyDate">
                  {{formData.applyDate}}
                </el-form-item>
              </el-col>
              <el-col :span="10" :xs="24">
                <el-form-item label="活動名稱" prop="applyActivityName">
                  {{formData.applyActivityName}}
                </el-form-item>
              </el-col>
              <el-col :span="14" :xs="24">
                <el-form-item label="活動時間" prop="activityDateScope">
                  {{formData.activityDateStart}}  至  {{formData.activityDateEnd}}
                </el-form-item>
              </el-col>
              <el-col :span="14" :xs="24">
                <el-form-item label="展位個數" prop="positionCount">
                  {{formData.positionCount}}
                </el-form-item>
              </el-col>
              <el-col :span="10" :xs="24">
                <el-form-item label="費用" prop="activityCost">
                  {{formData.activityCost}}
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24">
                <el-form-item label-width="100px" label="活動主旨" prop="activityPurport">
                  {{formData.activityPurport}}
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24">
                <el-form-item label-width="110px" label="場地佈置簡述" prop="activityDescribe">
                  {{formData.activityDescribe}}
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24">
                <el-form-item label-width="150px" label="是否改動原有公共實施?是否接電、接水、接網線等？" prop="activityOtherLayout">
                  {{formData.activityOtherLayout}}
                </el-form-item>
              </el-col>
              <el-col :span="24" class="print-hide-div">
                <el-form-item label="附件" prop="attachids">
                  <el-upload :file-list="upload.fileList" :show-file-list="true" :on-preview="handlePreview"
                             :headers="upload.headers" :action="upload.url" list-type="text" :disabled='true'>
                    <el-button size="small" type="primary" icon="el-icon-upload" :disabled='true'>點擊上傳
                    </el-button>
                  </el-upload>
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24" class="print-hide-div">
                <div class="dialog-footer" align="center" style="padding-top: 5px;padding-bottom: 5px">
                  <el-button type="danger" @click="closeForm">{{$t('common.close')}}</el-button>
                  <el-button type="primary" v-print="'#printContent'" >打印</el-button>
                  <el-button type="info" @click="handleTrack"
                    v-if="formData.workStatus == 2||formData.workStatus ==3||formData.workStatus ==4">
                    {{$t('table.activity.flowTracing')}}</el-button>
                </div>
              </el-col>
              <el-col :span="24" class="print-hide-div">
                <div class="dialog-footer" style="padding-top: 5px;padding-bottom: 5px">
                  <div v-html="signPath"></div>
                </div>
              </el-col>
              <!-- 审核记录 -->
              <el-table border stripe :data="commentList" class="table-max">
                <el-table-column :label="$t('table.activity.taskId')" align="center" prop="id" />
                <el-table-column :label="$t('table.activity.approvedBy')" align="center"
                  prop="userId" :show-overflow-tooltip="true" />
                <el-table-column :label="$t('table.activity.approvalOpinions')" align="center"
                  prop="fullMessage" :show-overflow-tooltip="true" />
                <el-table-column :label="$t('table.activity.operIp')" align="center" prop="operIp"
                  :show-overflow-tooltip="true" />
                <el-table-column :label="$t('table.activity.auditStatus')" align="center"
                  prop="status" :formatter="statusFormat" :show-overflow-tooltip="true" />
                <el-table-column :label="$t('table.activity.approvalTime')" align="center"
                  prop="time" >
                  <template slot-scope="scope">
                    <span>{{ parseTime(scope.row.time) }}</span>
                  </template>
                </el-table-column>
              </el-table>
              <!-- 任务跟踪对话框 -->
              <el-dialog :title="$t('table.activity.taskMap')" :visible.sync="showImgDialog"
                width="760px">
                <img :src="imgUrl" style="padding-bottom: 60px;">
              </el-dialog>
            </div>
          </div>
        </el-form>
      </el-row>
    </div>
  </div>
</template>
<script>
import {
  getEntLiveSiteHire,
  addEntLiveSiteHire,
  editEntLiveSiteHire
}
from "@/api/caaesign/entLiveSiteHire"
import '@/assets/styles/design-build/design-add-view.scss'
import {
  listTask,
  getTask,
  checkTask,
  taskComment,
  getSignPath
}
from "@/api/activiti/task"
import {
  getAccessToken,
  getZltAccessToken
}
from "@/utils/auth";
import {
  getHeader,
  previewFile, previewFileOos
}
from "@/utils/entfrm";
import {
  listFileInfo,
  getFileInfo,
  delFileInfo,
  addFileInfo,
  editFileInfo,
  getByKey
}
from "@/api/system/fileInfo";
export default {
  components: {},
  props: [],
  data() {
    return {
      formData: {
        businessType: undefined,
        applyEmpNo: undefined,
        applyEmpName: undefined,
        applyTel: undefined,
        applyDeptNo: undefined,
        makerfactoryid: undefined,
        applyDeptName: undefined,
        applyDate: null,
        applyActivityName: undefined,
        activityDateScope: null,
        activityDateStart: null,
        activityDateEnd: null,
        positionCount: undefined,
        activityCost: undefined,
        activityPurport: undefined,
        activityDescribe: undefined,
        activityOtherLayout: undefined,
        processId: undefined,
        siteName:undefined,
        businessTypeName:undefined,
      },
      rules: {
        businessType: [{
          required: true,
          message: '商戶類型不能為空',
          trigger: 'change'
        }],
        applyEmpNo: [{
          required: true,
          message: '請輸入申請人工號',
          trigger: 'blur'
        }],
        applyEmpName: [{
          required: true,
          message: '請輸入姓名',
          trigger: 'blur'
        }],
        applyTel: [{
          required: true,
          message: '請輸入電話',
          trigger: 'blur'
        }],
        applyDeptNo: [{
          required: true,
          message: '請輸入部門代碼',
          trigger: 'blur'
        }],
        makerfactoryid: [{
          required: true,
          message: '請選擇廠區',
          trigger: 'change'
        }],
        applyDeptName: [{
          required: true,
          message: '請輸入申請部門',
          trigger: 'blur'
        }],
        applyDate: [{
          required: true,
          message: '请選擇申請日期',
          trigger: 'change'
        }],
        applyActivityName: [{
          required: true,
          message: '請輸入活動名稱',
          trigger: 'blur'
        }],
        activityDateScope: [{
          required: true,
          message: '活動時間不能為空',
          trigger: 'change'
        }],
        positionCount: [{
          required: true,
          message: '請輸入展位個數',
          trigger: 'blur'
        }, {
          pattern: /^[0-9]*$/,
          message: '只能输入数字',
          trigger: 'blur'
        }],
        activityCost: [{
          required: true,
          message: '0.00',
          trigger: 'blur'
        }, {
          pattern: /^[0-9]+(.[0-9]{0,2})?$/,
          message: '最多精確到兩位小數',
          trigger: 'blur'
        }],
        activityPurport: [{
          required: true,
          message: '請輸入活動主旨',
          trigger: 'blur'
        }],
        activityDescribe: [{
          required: true,
          message: '請輸入場地佈置簡述',
          trigger: 'blur'
        }],
        activityOtherLayout: [{
          required: true,
          message: '請輸入是否改動原有公共實施?是否接電、接水、接網線等？',
          trigger: 'blur'
        }],
      },
      tableHeight: document.documentElement.scrollHeight - 245 + "px",
      // 审批意见数据
      commentList: [],
      //任务图url
      imgUrl: '',
      isDisabled: false,
      // 是否显示任务图
      showImgDialog: false,
      signPath: "",
      auditStatus: [],
      businessTypeOptions: [],
      makerfactoryidOptions: [],
      isMobile: false,
      labelPosition: 'left',
      // 文件上传参数
      upload: {
        // 是否显示弹出层
        open: false,
        fileList: [],
        fileNameList: [],
        data: {workflowKey: "dzqh_occupationOfLivingServiceArea"},
        // 弹出层标题
        title: "",
        multiple: false,
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: {
          Authorization: "Bearer " + getZltAccessToken(),
          token: "Bearer " + getAccessToken()
        },
        // 上传的地址
        url: this.getAppBaseApi() + "/system/fileInfo/uploadWithOos"
      },
    }
  },
  computed: {},
  watch: {},
  created() {
    const id = this.$route.query.id;
    this.changeTagsView(this.$route.query);
    this.isMobile = this.isMobileFun()
    if (this.isMobile) {
      this.labelPosition = 'top'
    }
    if (id != null && id != undefined) {
      getEntLiveSiteHire(id).then(response => {
        this.formData = response.data;
        this.formData.activityDateStart = this.formData.activityDateScope[0];
        this.formData.activityDateEnd = this.formData.activityDateScope[1];
        this.checkBoxParse()
        this.cascaderParse()
        taskComment(this.formData.processId).then(response => {
          this.commentList = response.data;
        });
        getSignPath(this.formData.processId).then(response => {
          this.signPath = response.data;
        });
        if (this.formData.attachids) {
          let a = this.formData.attachids.split(',');
          if (a.length > 0) {
            a.forEach(item => {
              if (item) {
                getByKey(item).then(response => {
                  this.upload.fileList.push({
                    name: response.data.orignalName,
                    url: response.data.name
                  });
                })
              }
            })
          }
        }
        this.getMakerfactoryidOptions()
        this.getBusinessTypeOptions()
      });
    }
    this.getDicts("sign_status").then(response => {
      this.auditStatus = response.data;
    });
  },
  mounted() {
    this.$nextTick(function() {
      if (this.isMobile) {}
    })
  },
  methods: {
    closeForm() {
      // //关闭子页面
      if (this.$store.state.settings.tagsView) {
        this.$router.go(-1) // 返回
        this.$store.state.tagsView.visitedViews.splice(this.$store.state.tagsView.visitedViews.findIndex(item =>
          item.path === this.$route.path), 1)
        this.$router.push(this.$store.state.tagsView.visitedViews[this.$store.state.tagsView.visitedViews
          .length - 1].path)
      }else{
        // parent.postMessage("closeCurrentTabMessage",'*');
        parent.postMessage("closeCurrentTabMessage",'*');
      }
    },
    handleTrack() {
      this.imgUrl = process.env.VUE_APP_BASE_API + '/activiti/task/track/' + this.formData.processId,
        this.showImgDialog = true
    },
    statusFormat(row, column) {
      return this.selectDictLabel(this.auditStatus, row.status);
    },
    getBusinessTypeOptions() {
      this.getDicts("caaesign_business_type").then(response => {
        this.businessTypeOptions = response.data;
        let _self = this;
        this.businessTypeOptions.forEach(function(item){  //循環商戶類型 商戶類型對應名稱
          if(item.value==_self.formData.businessType){
            _self.formData.businessTypeName = item.label;
          }
        })
      });
    },
    getMakerfactoryidOptions() {
      this.getDicts("caaesign_factory").then(response => {
        this.makerfactoryidOptions = response.data;
        let _self = this;
        this.makerfactoryidOptions.forEach(function(item){  //循環廠區顯示 廠區對應名稱
          if(item.value==_self.formData.makerfactoryid){
            _self.formData.siteName = item.label;
          }
        })
      });
    },
    checkBoxParse() {},
    cascaderParse() {},
    handlePreview(file) {
      previewFileOos(file.url)
    },
  }
}

</script>
<style scoped>
.del-handler {
  font-size: 20px;
  color: #ff4949;
}

.el-dialog {
  position: relative;
  margin: 0 auto 0px;
  background: #FFFFFF;
  border-radius: 2px;
  -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 50%;
  height: 60%;
}

.el-dialog__body {
  border-top: 1px solid #dcdfe6;
  border-bottom: 1px solid #dcdfe6;
  max-height: 85% !important;
  min-height: 70%;
  overflow-y: auto;
}

@media print{
  .print-hide-div{
   display:none;
  }
  .el-col:not(.el-col-no-border) {
    border: 1px solid #ccc;
    margin-top: -1px;
  }
  #printContent{
    box-sizing: border-box;
    width: 1000px;
  }
  .el-form-item {
    margin-top: 22px;
    margin-bottom: 22px;
  }

  .el-form-item__label{
    text-align: left;
  }
  .el-form-item{
    min-height:36px;
  }
  #serialno,#makerNo,#createTime{
    padding:10px 0;
  }
  *{
    box-sizing: border-box;
  }
  .el-table{
    border: 1px solid #dfe6ec;
  }

  .el-table__header{
    table-layout: auto;
  }
  .table-max .el-table__body,.table-max .el-table__header{
    width:100% !important;
  }
  .table-max  col{
    width:calc(100% / 6)  !important;
  }
  .table-max .el-table__body .cell{
    width:100% !important;
  }

  html{
    background-color: #FFFFFF;
    margin: 0;
  }
}

/*去除页眉页脚*/
@page{
  size:  auto;
  margin: 3mm;
}

</style>
