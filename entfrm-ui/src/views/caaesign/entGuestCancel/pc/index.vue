<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.createTime')" prop="createTime">
        <el-date-picker clearable size="small" style="width: 240px"
                        v-model="queryParams.createTime"
                        type="datetime"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        :placeholder="$t('common.selected') +　$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.createTime')">
        </el-date-picker>
      </el-form-item>
      <el-form-item :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.workStatus')" prop="workStatus">
        <el-select v-model="queryParams.workStatus"
                   :placeholder="$t('common.placeholderDefault') +　$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.workStatus')"
                   clearable size="small">
          <el-option :key="dict.value"
                     :label="dict.label"
                     :value="dict.value" v-for="dict in workStatusOptions"/>
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.serialno')" prop="serialno">
        <el-input
          v-model="queryParams.serialno"
          :placeholder="$t('common.placeholderDefault') +　$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.serialno')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.makerNo')" prop="makerNo">
        <el-input
          v-model="queryParams.makerNo"
          :placeholder="$t('common.placeholderDefault') +　$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.makerNo')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.makerfactoryid')"
                    prop="makerfactoryid">
        <el-input
          v-model="queryParams.makerfactoryid"
          :placeholder="$t('common.placeholderDefault') +　$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.makerfactoryid')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">{{ $t('table.search') }}
        </el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{ $t('table.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPerm="['entGuestCancel_add']"
        >{{ $t('table.add') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleEdit"
          v-hasPerm="['entGuestCancel_edit']"
        >{{ $t('table.edit') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDel"
          v-hasPerm="['entGuestCancel_del']"
        >{{ $t('table.delete') }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPerm="['entGuestCancel_export']"
        >{{ $t('table.export') }}
        </el-button>
      </el-col>
      <!--      <el-col :span="1.5">-->
      <!--        <el-upload :file-list="upload.fileList" :before-upload="attachidsBeforeUpload" :show-file-list="false"-->
      <!--                   :on-change="handleChange" :disabled="single"-->
      <!--                   :on-exceed="handleExceed" :on-preview="handlePreview" :on-remove="handleRemove"-->
      <!--                   :on-success="(response, file, fileList)=>uploadsucces(response, file, fileList)"-->
      <!--                   :headers="upload.headers" :action="upload.url" :data="upload.data"-->
      <!--                   list-type="text">-->
      <!--          <el-button size="mini" type="primary" icon="el-icon-upload">補傳附件</el-button>-->
      <!--        </el-upload>-->
      <!--      </el-col>-->
      <div class="top-right-btn">
        <el-tooltip class="item" effect="dark" :content="$t('table.refresh')" placement="top">
          <el-button size="mini" circle icon="el-icon-refresh" @click="handleQuery"/>
        </el-tooltip>
        <el-tooltip class="item" effect="dark" :content="showSearch ? $t('common.hideSearch') : $t('common.showSearch')"
                    placement="top">
          <el-button size="mini" circle icon="el-icon-search" @click="showSearch=!showSearch"/>
        </el-tooltip>
      </div>
    </el-row>

    <el-table border stripe v-loading="loading" :data="entGuestCancelList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" :selectable="selectInit"/>
      <el-table-column :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.default.serialNumber')"
                       align="center" type="index" :index="indexMethod" width="80"/>
      <el-table-column :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.id')" align="center"
                       prop="id" :show-overflow-tooltip="true" v-if="false"/>
      <el-table-column :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.createTime')" align="center"
                       prop="createTime" width="180" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.workStatus')" align="center"
                       prop="workStatus" :formatter="workStatusFormat" :show-overflow-tooltip="true"/>
      <el-table-column :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.serialno')" align="center"
                       prop="serialno" :show-overflow-tooltip="true"/>
      <el-table-column :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.makerNo')" align="center"
                       prop="makerNo" :show-overflow-tooltip="true"/>
      <el-table-column :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.makerName')" align="center"
                       prop="makerName" :show-overflow-tooltip="true"/>
      <el-table-column :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.completTime')" align="center"
                       prop="completTime" width="180" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.completTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.signPerson')" align="center"
                       prop="signPerson" :show-overflow-tooltip="true"/>
      <el-table-column :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.signNode')" align="center"
                       prop="signNode" :show-overflow-tooltip="true"/>
      <el-table-column :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.makerdeptno')" align="center"
                       prop="makerdeptno" :show-overflow-tooltip="true"/>
      <el-table-column :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.makerfactoryid')"
                       align="center" prop="makerfactoryid" :show-overflow-tooltip="true"/>
      <el-table-column :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.acceptDept')" align="center"
                       prop="acceptDept" :show-overflow-tooltip="true"/>
      <el-table-column :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.postDept')" align="center"
                       prop="postDept" :show-overflow-tooltip="true"/>
      <el-table-column :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.huibanDept')" align="center"
                       prop="huibanDept" :show-overflow-tooltip="true"/>
      <el-table-column :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.acceptWords')" align="center"
                       prop="acceptWords" :show-overflow-tooltip="true"/>
      <el-table-column :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.acceptDate')" align="center"
                       prop="acceptDate" :show-overflow-tooltip="true"/>
      <el-table-column :label="$t('table.operation')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.workStatus == 0&&scope.row.isOwn == true"
            size="mini"
            type="text"
            icon="el-icon-success"
            @click="handleSubmit(scope.row)"
          >
            {{ $t('table.submit') }}
          </el-button>
          <el-button
            v-if="scope.row.workStatus ==4&&scope.row.isOwn == true"
            size="mini"
            type="text"
            icon="el-icon-success"
            @click="handleTask(5,scope.row.processId)"
          >
            {{ $t('table.activity.reSubmit') }}
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            v-if="(scope.row.workStatus == 0||scope.row.workStatus ==4)&&scope.row.isOwn == true"
            @click="handleEdit(scope.row)"
          >{{ $t('table.edit') }}
          </el-button>
          <el-button
            size="mini"
            type="text"
            v-if="scope.row.workStatus == 0&&scope.row.isOwn == true"
            icon="el-icon-delete"
            @click="handleDel(scope.row)"
          >{{ $t('table.delete') }}
          </el-button>
          <el-button
            v-if="scope.row.workStatus ==4&&scope.row.isOwn == true"
            size="mini"
            type="text"
            icon="el-icon-success"
            @click="handleTask(9,scope.row.processId)"
          >
            {{ $t('table.activity.cancle') }}
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleDetail(scope.row)"
          >{{ $t('table.detail') }}
          </el-button>
          <el-upload :file-list="upload.fileList" :before-upload="attachidsBeforeUpload" :show-file-list="false"
                     :on-change="handleChange"
                     :on-exceed="handleExceed" :on-preview="handlePreview" :on-remove="handleRemove"
                     :on-success="(response, file, fileList)=>uploadsucces(response, file, fileList,scope.row)"
                     :headers="upload.headers" :action="upload.url" :data="upload.data"
                     list-type="text">
            <el-button size="mini" type="text" icon="el-icon-upload">補傳附件</el-button>
          </el-upload>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.current"
      :limit.sync="queryParams.size"
      @pagination="getList"
    />

    <!-- 添加或修改iPEBG移轉設備銷賬聯絡單对话框 -->
    <el-dialog :title="title" :visible.sync="open">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="24">
            <el-form-item :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.applyEmpNo')"
                          prop="applyEmpNo">
              <el-input v-model="form.applyEmpNo"
                        :placeholder="$t('common.placeholderDefault') +　$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.applyEmpNo') + ''"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.applyEmpName')"
                          prop="applyEmpName">
              <el-input v-model="form.applyEmpName"
                        :placeholder="$t('common.placeholderDefault') +　$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.applyEmpName') + ''"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.applyDeptNo')"
                          prop="applyDeptNo">
              <el-input v-model="form.applyDeptNo"
                        :placeholder="$t('common.placeholderDefault') +　$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.applyDeptNo') + ''"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.applyCostNo')"
                          prop="applyCostNo">
              <el-input v-model="form.applyCostNo"
                        :placeholder="$t('common.placeholderDefault') +　$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.applyCostNo') + ''"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.applyTel')"
                          prop="applyTel">
              <el-input v-model="form.applyTel"
                        :placeholder="$t('common.placeholderDefault') +　$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.applyTel') + ''"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.applyDeptNam')"
                          prop="applyDeptNam">
              <el-input v-model="form.applyDeptNam"
                        :placeholder="$t('common.placeholderDefault') +　$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.applyDeptNam') + ''"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.applyMail')"
                          prop="applyMail">
              <el-input v-model="form.applyMail"
                        :placeholder="$t('common.placeholderDefault') +　$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.applyMail') + ''"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.acceptDept')"
                          prop="acceptDept">
              <el-input v-model="form.acceptDept"
                        :placeholder="$t('common.placeholderDefault') +　$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.acceptDept') + ''"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.postDept')"
                          prop="postDept">
              <el-input v-model="form.postDept"
                        :placeholder="$t('common.placeholderDefault') +　$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.postDept') + ''"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.huibanDept')"
                          prop="huibanDept">
              <el-input v-model="form.huibanDept"
                        :placeholder="$t('common.placeholderDefault') +　$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.huibanDept') + ''"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.acceptWords')"
                          prop="acceptWords">
              <el-input v-model="form.acceptWords"
                        :placeholder="$t('common.placeholderDefault') +　$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.acceptWords') + ''"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.copyPresent')"
                          prop="copyPresent">
              <el-input v-model="form.copyPresent"
                        :placeholder="$t('common.placeholderDefault') +　$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.copyPresent') + ''"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.keyNote')" prop="keyNote">
              <el-input v-model="form.keyNote"
                        :placeholder="$t('common.placeholderDefault') +　$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.keyNote') + ''"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.demandIllustrate')"
                          prop="demandIllustrate">
              <el-input v-model="form.demandIllustrate"
                        :placeholder="$t('common.placeholderDefault') +　$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.demandIllustrate') + ''"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.applyMatters')"
                          prop="applyMatters">
              <el-input v-model="form.applyMatters"
                        :placeholder="$t('common.placeholderDefault') +　$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.applyMatters') + ''"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.shchargeno')"
                          prop="shchargeno">
              <el-input v-model="form.shchargeno"
                        :placeholder="$t('common.placeholderDefault') +　$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.shchargeno') + ''"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.shchargename')"
                          prop="shchargename">
              <el-input v-model="form.shchargename"
                        :placeholder="$t('common.placeholderDefault') +　$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.shchargename') + ''"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.xxywchargeno')"
                          prop="xxywchargeno">
              <el-input v-model="form.xxywchargeno"
                        :placeholder="$t('common.placeholderDefault') +　$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.xxywchargeno') + ''"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.xxywchargename')"
                          prop="xxywchargename">
              <el-input v-model="form.xxywchargename"
                        :placeholder="$t('common.placeholderDefault') +　$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.xxywchargename') + ''"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.zcjgchargeno')"
                          prop="zcjgchargeno">
              <el-input v-model="form.zcjgchargeno"
                        :placeholder="$t('common.placeholderDefault') +　$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.zcjgchargeno') + ''"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.zcjgchargename')"
                          prop="zcjgchargename">
              <el-input v-model="form.zcjgchargename"
                        :placeholder="$t('common.placeholderDefault') +　$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.zcjgchargename') + ''"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.zchzchargeno')"
                          prop="zchzchargeno">
              <el-input v-model="form.zchzchargeno"
                        :placeholder="$t('common.placeholderDefault') +　$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.zchzchargeno') + ''"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.zchzchargename')"
                          prop="zchzchargename">
              <el-input v-model="form.zchzchargename"
                        :placeholder="$t('common.placeholderDefault') +　$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.zchzchargename') + ''"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.zckjchargeno')"
                          prop="zckjchargeno">
              <el-input v-model="form.zckjchargeno"
                        :placeholder="$t('common.placeholderDefault') +　$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.zckjchargeno') + ''"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.zckjchargename')"
                          prop="zckjchargename">
              <el-input v-model="form.zckjchargename"
                        :placeholder="$t('common.placeholderDefault') +　$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.zckjchargename') + ''"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.zrcbchargeno')"
                          prop="zrcbchargeno">
              <el-input v-model="form.zrcbchargeno"
                        :placeholder="$t('common.placeholderDefault') +　$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.zrcbchargeno') + ''"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.zrcbchargename')"
                          prop="zrcbchargename">
              <el-input v-model="form.zrcbchargename"
                        :placeholder="$t('common.placeholderDefault') +　$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.zrcbchargename') + ''"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.zrshchargeno')"
                          prop="zrshchargeno">
              <el-input v-model="form.zrshchargeno"
                        :placeholder="$t('common.placeholderDefault') +　$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.zrshchargeno') + ''"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.zrshchargename')"
                          prop="zrshchargename">
              <el-input v-model="form.zrshchargename"
                        :placeholder="$t('common.placeholderDefault') +　$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.zrshchargename') + ''"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.zrjgchargeno')"
                          prop="zrjgchargeno">
              <el-input v-model="form.zrjgchargeno"
                        :placeholder="$t('common.placeholderDefault') +　$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.zrjgchargeno') + ''"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.zrjgchargename')"
                          prop="zrjgchargename">
              <el-input v-model="form.zrjgchargename"
                        :placeholder="$t('common.placeholderDefault') +　$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.zrjgchargename') + ''"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.zrhzchargeno')"
                          prop="zrhzchargeno">
              <el-input v-model="form.zrhzchargeno"
                        :placeholder="$t('common.placeholderDefault') +　$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.zrhzchargeno') + ''"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.zrhzchargename')"
                          prop="zrhzchargename">
              <el-input v-model="form.zrhzchargename"
                        :placeholder="$t('common.placeholderDefault') +　$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.zrhzchargename') + ''"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.zrkjchargeno')"
                          prop="zrkjchargeno">
              <el-input v-model="form.zrkjchargeno"
                        :placeholder="$t('common.placeholderDefault') +　$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.zrkjchargeno') + ''"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.zrkjchargename')"
                          prop="zrkjchargename">
              <el-input v-model="form.zrkjchargename"
                        :placeholder="$t('common.placeholderDefault') +　$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.zrkjchargename') + ''"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.hwfrchargeno')"
                          prop="hwfrchargeno">
              <el-input v-model="form.hwfrchargeno"
                        :placeholder="$t('common.placeholderDefault') +　$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.hwfrchargeno') + ''"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.hwfrchargename')"
                          prop="hwfrchargename">
              <el-input v-model="form.hwfrchargename"
                        :placeholder="$t('common.placeholderDefault') +　$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.hwfrchargename') + ''"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.csghchargeno')"
                          prop="csghchargeno">
              <el-input v-model="form.csghchargeno"
                        :placeholder="$t('common.placeholderDefault') +　$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.csghchargeno') + ''"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.csghchargename')"
                          prop="csghchargename">
              <el-input v-model="form.csghchargename"
                        :placeholder="$t('common.placeholderDefault') +　$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.csghchargename') + ''"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.equipmentAllNumber')"
                          prop="equipmentAllNumber">
              <el-input v-model="form.equipmentAllNumber"
                        :placeholder="$t('common.placeholderDefault') +　$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.equipmentAllNumber') + ''"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.equipmentAllPrice')"
                          prop="equipmentAllPrice">
              <el-input v-model="form.equipmentAllPrice"
                        :placeholder="$t('common.placeholderDefault') +　$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.equipmentAllPrice') + ''"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.equipmentAllAmount')"
                          prop="equipmentAllAmount">
              <el-input v-model="form.equipmentAllAmount"
                        :placeholder="$t('common.placeholderDefault') +　$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.label.equipmentAllAmount') + ''"/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{ $t('common.confirm') }}</el-button>
        <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listEntGuestCancel,
  delEntGuestCancel,
  addEntGuestCancel,
  editEntGuestCancel,
  exportEntGuestCancel,
  startProcess, updateAttachids
} from "@/api/caaesign/entGuestCancel";
import {checkTask} from "@/api/activiti/task";

export default {
  name: "EntGuestCancel",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // iPEBG移轉設備銷賬聯絡單表格数据
      entGuestCancelList: [],
      workStatusOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        current: 1,
        size: 10,
        createTime: undefined,
        workStatus: undefined,
        serialno: undefined,
        makerNo: undefined,
        makerfactoryid: undefined,
      },
      // 显示搜索条件
      showSearch: true,
      // 文件上传参数
      upload: {
        // 是否显示弹出层
        open: false,
        fileList: [],
        fileNameList: [],
        data: {workflowKey: "dzqh_kezixiaozhangshenqing"},
        // 弹出层标题
        title: "補傳附件",
        multiple: true,
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: this.getHeader(),
        // 上传的地址
        url: this.getAppBaseApi() + "/system/fileInfo/uploadWithOos"
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {}
    };
  },
  created() {
    this.changeTagsView(this.$route.query);
    this.getList();
    this.getDicts("work_status").then(response => {
      this.workStatusOptions = response.data;
    });
  },
  methods: {
    /** 查询iPEBG移轉設備銷賬聯絡單列表 */
    getList() {
      this.loading = true;
      listEntGuestCancel(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.entGuestCancelList = response.data;
        this.total = response.total;
        this.loading = false;
      });
    },
    selectInit(row, index) {
      if (row.workStatus == 0 || row.workStatus == 4) {
        return true  //不可勾选
      } else {
        return false  //可勾选
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        createBy: undefined,
        createTime: undefined,
        updateBy: undefined,
        updateTime: undefined,
        remarks: undefined,
        delFlag: undefined,
        deptId: undefined,
        processId: undefined,
        workStatus: "0",
        serialno: undefined,
        makerNo: undefined,
        makerName: undefined,
        completTime: undefined,
        attachids: undefined,
        signPerson: undefined,
        signNode: undefined,
        makerdeptno: undefined,
        makerfactoryid: undefined,
        dataSource: undefined,
        applyEmpNo: undefined,
        applyEmpName: undefined,
        applyDeptNo: undefined,
        applyCostNo: undefined,
        applyTel: undefined,
        applyDeptNam: undefined,
        applyMail: undefined,
        acceptDept: undefined,
        postDept: undefined,
        huibanDept: undefined,
        acceptWords: undefined,
        copyPresent: undefined,
        keyNote: undefined,
        demandIllustrate: undefined,
        applyMatters: undefined,
        acceptDate: undefined,
        shchargeno: undefined,
        shchargename: undefined,
        xxywchargeno: undefined,
        xxywchargename: undefined,
        zcjgchargeno: undefined,
        zcjgchargename: undefined,
        zchzchargeno: undefined,
        zchzchargename: undefined,
        zckjchargeno: undefined,
        zckjchargename: undefined,
        zrcbchargeno: undefined,
        zrcbchargename: undefined,
        zrshchargeno: undefined,
        zrshchargename: undefined,
        zrjgchargeno: undefined,
        zrjgchargename: undefined,
        zrhzchargeno: undefined,
        zrhzchargename: undefined,
        zrkjchargeno: undefined,
        zrkjchargename: undefined,
        hwfrchargeno: undefined,
        hwfrchargename: undefined,
        csghchargeno: undefined,
        csghchargename: undefined,
        equipmentAllNumber: undefined,
        equipmentAllPrice: undefined,
        equipmentAllAmount: undefined,
        createDate: undefined,
        updateDate: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.current = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm("queryForm");
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length != 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$router.push('/caaesign/entGuestCancel/EntGuestCancelAdd');
    },
    /** 修改按钮操作 */
    handleEdit(row) {
      const id = row.id || this.ids
      if (row.workStatus == "4") { //駁回
        this.$router.push({path: '/caaesign/entGuestCancel/EntGuestCancelReject', query: {id: id}});
      } else {
        this.$router.push({path: '/caaesign/entGuestCancel/EntGuestCancelEdit', query: {id: id}});
      }
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != undefined) {
            editEntGuestCancel(this.form).then(response => {
              if (response.code === 0) {
                this.msgSuccess(this.$t('tips.updateSuccess'));
                this.open = false;
                this.getList();
              } else {
                this.msgError(response.msg);
              }
            });
          } else {
            addEntGuestCancel(this.form).then(response => {
              if (response.code === 0) {
                this.msgSuccess(this.$t('tips.createSuccess'));
                this.open = false;
                this.getList();
              } else {
                this.msgError(response.msg);
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDel(row) {
      const ids = row.id || this.ids;
      let functionName = this.$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.default.functionName');
      this.$confirm(this.$t('tips.deleteConfirm', [functionName, ids]), this.$t('tips.warm'), {
        confirmButtonText: this.$t('common.confirmTrim'),
        cancelButtonText: this.$t('common.cancelTrim'),
        type: "warning"
      }).then(function () {
        return delEntGuestCancel(ids);
      }).then(() => {
        this.getList();
        this.msgSuccess(this.$t('tips.deleteSuccess'));
      }).catch(function () {
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      let functionName = this.$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.default.functionName');
      this.$confirm(this.$t('tips.exportConfirm', [functionName]), this.$t('tips.warm'), {
        confirmButtonText: this.$t('common.confirmTrim'),
        cancelButtonText: this.$t('common.cancelTrim'),
        type: "warning"
      }).then(function () {
        return exportEntGuestCancel(queryParams);
      }).then(response => {
        this.download(response.data);
      }).catch(function () {
      });
    },
    handleDetail(row) {
      const id = row.id || this.ids
      this.$router.push({path: '/caaesign/entGuestCancel/EntGuestCancelDetail', query: {id: id}});
    },
    handleSubmit: function (row, index) {
      this.$confirm(this.$t('tips.submitConfirm', [row.id]), this.$t('tips.warm'), {
        confirmButtonText: this.$t('common.confirmTrim'),
        cancelButtonText: this.$t('common.cancelTrim'),
        type: 'warning'
      }).then(function () {
        return startProcess(row.id)
      }).then(() => {
        this.getList();
        this.msgSuccess(this.$t('tips.submitSuccess'));
      })
    },
    indexMethod(index) {
      return index + 1 + (this.queryParams.current - 1) * this.queryParams.size;
    },
    handleTask: function (pass, processId) {
      this.isDisabled = true;
      this.form.pass = pass
      this.form.processId = processId
      checkTask(this.form).then(response => {
        if (response.code === 0) {
          this.msgSuccess(this.$t('tips.operationSuccessful'));
          this.getList();
          this.closeForm();
        } else {
          this.msgError(response.msg);
        }
      });
    },
    attachidsBeforeUpload(file) {
      // let isRightSize = file.size / 1024 / 1024 < 2
      // if (!isRightSize) {
      //   this.$message.error('文件大小超过 2MB')
      // }
      return true;
    },
    // 文件上传成功处理
    uploadsucces(response, file, fileList, row) {
      if (response.data.name != undefined && response.data.name != "undefined") {
        this.form.id = row.id;
        // this.upload.fileList.map((file)=>{
        //   this.form.attachids += response.data.name + ",";
        // })
        this.form.attachids = response.data.name + ",";
        updateAttachids(this.form).then(response1 => {
          if (response1.code === 0) {
            this.msgSuccess("附件【" + response.data.orignalName + "】" + this.$t('tips.reUploadSuccess'));
          } else {
            this.msgError(response1.msg);
          }
        });
      }
    },
    handleChange(file, fileList) {
    },
    handleExceed(file, fileList) {
    },
    handleRemove(file, fileList) {
      // this.$emit("delUploadImage", file.name);
      // const index = this.upload.fileList.indexOf(file);
      // this.upload.fileList.splice(index, 1);
      // if (this.formData.attachids) {
      //   this.formData.attachids = this.formData.attachids.replace(file.url + ",", "");
      // }
      // this.upload.fileNameList.push(file.url);
      // delFileInfo(file.url);
    },
    handlePreview(file) {
      previewFileOos(file.url)
    },
    workStatusFormat(row, column) {
      return this.selectDictLabel(this.workStatusOptions, row.workStatus);
    },
  }
};
</script>
