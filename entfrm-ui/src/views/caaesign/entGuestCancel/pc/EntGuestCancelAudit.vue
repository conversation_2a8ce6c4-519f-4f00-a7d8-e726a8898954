<template>
  <div class="ant-modal-content">
    <div class="ant-modal-body">
      <el-form ref="elForm" class="ant-form ant-form-horizontal" :model="formData" size="medium"
        label-width="100px" :label-position="labelPosition">
        <div class="ant-card ant-card-bordered" id="staffCard">
          <div class="ant-card-body">
            <el-row :gutter="15">
              <span id="staffEvectionTitle" style="margin-bottom: 10px">{{formConfigData.title}}</span>
              <el-col :span="8" :xs="24" class="el-col-no-border bar-to-bottom">
                任務編碼:{{formData.serialno?formData.serialno:'提交表單后自動生成'}}
              </el-col>
              <el-col :span="8" :xs="24" class="el-col-no-border bar-to-bottom"> 填單時間:{{formData.createTime}}
              </el-col>
              <el-col :span="8" :xs="24" class="el-col-no-border bar-to-bottom">
                填單人:{{formData.makerNo +' / '+ formData.makerName}}
              </el-col>
              <el-col :span="12" :xs="24">
                <el-form-item label="受文單位：" prop="acceptDept">
                  {{formData.acceptDept}}
                </el-form-item>
              </el-col>
              <el-col :span="12" :xs="24">
                <el-form-item label="發文單位：" prop="postDept">
                  {{formData.postDept}}
                </el-form-item>
              </el-col>
              <el-col :span="12" :xs="24">
                <el-form-item label="會辦單位：" prop="huibanDept">
                  {{formData.huibanDept}}
                </el-form-item>
              </el-col>
              <el-col :span="12" :xs="24">
                <el-form-item label="發文字號：" prop="acceptWords">
                  {{formData.acceptWords}}
                </el-form-item>
              </el-col>
              <el-col :span="12" :xs="24">
                <el-form-item label="副本呈送：" prop="copyPresent">
                  {{formData.copyPresent}}
                </el-form-item>
              </el-col>
              <el-col :span="12" :xs="24">
                <el-form-item label="發文日期：" prop="acceptDate">
                  {{formData.acceptDate}}
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24">
                <el-form-item label-width="80px" label="主旨：" prop="keyNote">
                  {{formData.keyNote}}
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24">
                <el-form-item label="背景說明：" prop="demandIllustrate">
                  {{formData.demandIllustrate}}
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24">
                <label-form :label=formConfigData.guestnode_label labelWidth='90' labelPosition='left'
                  labelSize='14' :textarea=formConfigData.guestnode_text textSize='14'></label-form>
              </el-col>
              <el-col :span="24" :xs="24" class="talbe-name-style"> 出售相關法人信息 </el-col>
              <el-scrollbar style="width:100%;" class="child-table-entGuestItems1">
                <el-col :span="24" :xs="24" style="padding: 0px;width:100%">
                  <el-table border stripe ref="entGuestItems1DragTable" :data="formData.entGuestItems1Lists"
                    row-key="id" :max-height="tableHeight">
                    <el-table-column label="編號" type="index" width="50px" />
                    <el-table-column label="出售方法人" min-width="10%" prop="outLegal">
                      <template slot-scope="scope">
                        <el-form-item label-width="0px">
                          {{scope.row.outLegalValue}}
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column label="出售方轉單法人" min-width="10%" prop="outTransferLegal">
                      <template slot-scope="scope">
                        <el-form-item label-width="0px">
                          {{scope.row.outTransferLegalValue}}
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column label="購入方法人" min-width="10%" prop="inLegal">
                      <template slot-scope="scope">
                        <el-form-item label-width="0px">
                          {{scope.row.inLegalValue}}
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column label="購入方轉單法人" min-width="10%" prop="inTransferLegal">
                      <template slot-scope="scope">
                        <el-form-item label-width="0px">
                          {{scope.row.inTransferLegalValue}}
                        </el-form-item>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-col>
              </el-scrollbar>
              <el-col :span="24" :xs="24" class="talbe-name-style"> 出售相關設備信息 </el-col>
              <el-scrollbar style="width:100%;" class="child-table-entGuestItems2">
                <el-col :span="24" :xs="24" style="padding: 0px;width:100%">
                  <el-table border stripe ref="entGuestItems2DragTable" :data="formData.entGuestItems2Lists"
                    row-key="id" :max-height="tableHeight" show-summary
                    :summary-method="entGuestItems2_getSummaries">
                    <el-table-column label="編號" type="index" width="50px" />
                    <el-table-column label="設備名稱" min-width="10%" prop="equipmentName">
                      <template slot-scope="scope">
                        <el-form-item label-width="0px">
                          {{scope.row.equipmentName}}
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column label="設備規格" min-width="10%" prop="equipmentGuige">
                      <template slot-scope="scope">
                        <el-form-item label-width="0px">
                          {{scope.row.equipmentGuige}}
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column label="數量" min-width="10%" prop="equipmentNumber">
                      <template slot-scope="scope">
                        <el-form-item label-width="0px">
                          {{scope.row.equipmentNumber}}
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column label="幣別" min-width="10%" prop="equipmentCurrency">
                      <template slot-scope="scope">
                        <el-form-item label-width="0px">
                          {{scope.row.equipmentCurrencyValue}}
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column label="單價(未稅)" min-width="10%" prop="equipmentPrice">
                      <template slot-scope="scope">
                        <el-form-item label-width="0px">
                          {{scope.row.equipmentPrice}}
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column label="總金額(未稅)" min-width="10%" prop="equipmentTotal">
                      <template slot-scope="scope">
                        <el-form-item label-width="0px">
                          {{scope.row.equipmentTotal}}
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column label="出售單號" min-width="10%" prop="equpipmentSell">
                      <template slot-scope="scope">
                        <el-form-item label-width="0px">
                          {{scope.row.equpipmentSell}}
                        </el-form-item>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-col>
              </el-scrollbar>
              <el-col :span="24" :xs="24">
                <el-form-item label="附件" prop="attachids">
                  <el-upload :file-list="upload.fileList" :show-file-list="true" :on-preview="handlePreview"
                    :headers="upload.headers" :action="upload.url" list-type="text" :disabled='true'>
                    <el-button size="small" type="primary" icon="el-icon-upload" :disabled='true'>點擊上傳
                    </el-button>
                  </el-upload>
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24">
                <el-form-item :label="$t('table.activity.approvalOpinions')">
                  <el-input v-model="formData.comment" type="textarea" rows="3"
                    :placeholder="$t('table.activity.inputApprovalOpinions')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24" :xs="24">
                <div class="dialog-footer" align="center" style="padding-top: 5px;padding-bottom: 5px">
                  <el-button type="success" @click="handleTask(0)" :disabled="isDisable">
                    {{$t('table.activity.pass')}}
                  </el-button>
                  <el-button type="danger" @click="handleTask(1)" :disabled="isDisable">
                    {{$t('table.activity.rejection')}}
                  </el-button>
                  <el-button @click="handleTrack">{{$t('table.activity.flowTracing')}}</el-button>
                  <el-button @click="closeForm">{{$t('common.close')}}</el-button>
                </div>
              </el-col>
              <!-- 簽核線 -->
              <el-col :span="24" class="print-hide-div">
                <div class="dialog-footer" style="padding-top: 5px;padding-bottom: 5px">
                  <div v-html="signPath"></div>
                </div>
              </el-col>
              <!-- 审核记录 -->
              <el-table border stripe :data="commentList">
                <el-table-column :label="$t('table.activity.taskId')" align="center" prop="id" />
                <el-table-column :label="$t('table.activity.approvedBy')" align="center" prop="userId"
                  :show-overflow-tooltip="true" />
                <el-table-column :label="$t('table.activity.approvalOpinions')" align="center"
                  prop="fullMessage" :show-overflow-tooltip="true" />
                <el-table-column :label="$t('table.activity.operIp')" align="center" prop="operIp"
                  :show-overflow-tooltip="true" />
                <el-table-column :label="$t('table.activity.auditStatus')" align="center" prop="status"
                  :formatter="statusFormat" :show-overflow-tooltip="true" />
                <el-table-column :label="$t('table.activity.approvalTime')" align="center" prop="time"
                  width="180">
                  <template slot-scope="scope">
                    <span>{{ parseTime(scope.row.time) }}</span>
                  </template>
                </el-table-column>
              </el-table>
              <!-- 任务跟踪对话框 -->
              <el-dialog :title="$t('table.activity.taskMap')" :visible.sync="showImgDialog" width="1000px">
                <img :src="imgUrl" style="padding-bottom: 60px;width:100%;">
              </el-dialog>
            </el-row>
          </div>
        </div>
      </el-form>
    </div>
  </div>
</template>
<script>
import {
  getEntGuestCancel,
  addEntGuestCancel,
  editEntGuestCancel,
  getSignPath,
  updateAndCheck,
  getSignConfigList,
  exportEntGuestItems1,
  exportEntGuestItems2
}
from "@/api/caaesign/entGuestCancel"
import '@/assets/styles/design-build/design-add-view.scss'
import {
  listTask,
  getTask,
  checkTask,
  taskComment
}
from "@/api/activiti/task"
import {
  getAccessToken,
  getZltAccessToken
}
from "@/utils/auth";
import {
  previewFile,
  getHeader,previewFileOos
}
from "@/utils/entfrm";
import {
  listFileInfo,
  getFileInfo,
  delFileInfo,
  addFileInfo,
  editFileInfo,
  getByKey
}
from "@/api/system/fileInfo";
export default {
  components: {},
  props: [],
  data() {
    return {
      formData: {
        acceptDept: undefined,
        postDept: undefined,
        huibanDept: undefined,
        acceptWords: undefined,
        copyPresent: undefined,
        acceptDate: null,
        keyNote: undefined,
        demandIllustrate: undefined,
        entGuestItems1Lists: [],
        entGuestItems2Lists: [],
        attachids: "",
        shchargeno: undefined,
        xxywchargeno: undefined,
        zcjgchargeno: undefined,
        zchzchargeno: undefined,
        zckjchargeno: undefined,
        zrcbchargeno: undefined,
        zrshchargeno: undefined,
        zrjgchargeno: undefined,
        zrhzchargeno: undefined,
        zrkjchargeno: undefined,
        hwfrchargeno: undefined,
        csghchargeno: undefined,
      },
      formConfigData: {
        title: "iPEBG設備移轉銷賬聯絡單",
        guestnode_label: ` 申請事項：`,
        guestnode_text: `應中央財稅規劃、海外會計、國內會計要求，以此聯絡單申請設備轉賣相關作業。`,
        shchargeno: "審核",
        shchargeno_required: true,
        xxywchargeno: "iPEBG 行銷業務總處 ",
        xxywchargeno_required: true,
        zcjgchargeno: "轉出方經管",
        zcjgchargeno_required: true,
        zchzchargeno: "轉出方核准",
        zchzchargeno_required: true,
        zckjchargeno: "轉出方會計",
        zckjchargeno_required: true,
        zrcbchargeno: "轉入方承辦",
        zrcbchargeno_required: true,
        zrshchargeno: "轉入方審核",
        zrshchargeno_required: true,
        zrjgchargeno: "轉入方經管",
        zrjgchargeno_required: true,
        zrhzchargeno: "轉入方核准",
        zrhzchargeno_required: true,
        zrkjchargeno: "轉入方會計",
        zrkjchargeno_required: true,
        hwfrchargeno: "海外法人會計",
        hwfrchargeno_required: true,
        csghchargeno: "財稅規劃",
        csghchargeno_required: true,
      },
      rules: {
        acceptDept: [{
          required: true,
          message: '請輸入受文單位',
          trigger: 'blur'
        }],
        postDept: [{
          required: true,
          message: '請輸入發文單位',
          trigger: 'blur'
        }],
        huibanDept: [{
          required: true,
          message: '請輸入會辦單位',
          trigger: 'blur'
        }],
        acceptWords: [{
          required: true,
          message: '請輸入發文字號',
          trigger: 'blur'
        }],
        copyPresent: [{
          required: true,
          message: '請輸入副本呈送',
          trigger: 'blur'
        }],
        acceptDate: [{
          required: true,
          message: '请選擇發文日期',
          trigger: 'change'
        }],
        keyNote: [{
          required: true,
          message: '請輸入主旨',
          trigger: 'blur'
        }],
        demandIllustrate: [{
          required: true,
          message: '依____年____資源規劃處統籌，____廠區____廠出售給____廠區____臺____，以滿足生產需求；此批設備已驗收入賬，為保證賬實一致，現進行轉賣賬務申請',
          trigger: 'blur'
        }],
        entGuestItems2_equipmentNumber: [{
          pattern: /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/,
          message: '請輸入數字！',
          trigger: 'blur'
        }],
        attachids: [],
        shchargeno: [{
          required: true,
          message: '審核不能為空',
          trigger: 'change'
        }],
        xxywchargeno: [{
          required: true,
          message: 'iPEBG 行銷業務總處 不能為空',
          trigger: 'change'
        }],
        zcjgchargeno: [{
          required: true,
          message: '轉出方經管不能為空',
          trigger: 'change'
        }],
        zchzchargeno: [{
          required: true,
          message: '轉出方核准不能為空',
          trigger: 'change'
        }],
        zckjchargeno: [{
          required: true,
          message: '轉出方會計不能為空',
          trigger: 'change'
        }],
        zrcbchargeno: [{
          required: true,
          message: '轉入方承辦不能為空',
          trigger: 'change'
        }],
        zrshchargeno: [{
          required: true,
          message: '轉入方審核不能為空',
          trigger: 'change'
        }],
        zrjgchargeno: [{
          required: true,
          message: '轉入方經管不能為空',
          trigger: 'change'
        }],
        zrhzchargeno: [{
          required: true,
          message: '轉入方核准不能為空',
          trigger: 'change'
        }],
        zrkjchargeno: [{
          required: true,
          message: '轉入方會計不能為空',
          trigger: 'change'
        }],
        hwfrchargeno: [{
          required: true,
          message: '海外法人會計不能為空',
          trigger: 'change'
        }],
        csghchargeno: [{
          required: true,
          message: '財稅規劃不能為空',
          trigger: 'change'
        }],
      },
      tableHeight: document.documentElement.scrollHeight - 245 + "px",
      // 审批意见数据
      commentList: [],
      //簽核路徑
      signPath: [],
      //任务图url
      imgUrl: '',
      isDisable: false,
      // 是否显示任务图
      showImgDialog: false,
      auditStatus: [],
      // 文件上传参数
      uploadEntGuestItems1: {
        // 是否显示弹出层
        open: false,
        fileList: [],
        fileNameList: [],
        // 弹出层标题
        title: "",
        multiple: false,
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: this.getHeader(),
        // 上传的地址
        url: this.getAppBaseApi() + "/caaesign/entGuestCancel/importEntGuestItems1"
      },
      // 文件上传参数
      uploadEntGuestItems2: {
        // 是否显示弹出层
        open: false,
        fileList: [],
        fileNameList: [],
        // 弹出层标题
        title: "",
        multiple: false,
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: this.getHeader(),
        // 上传的地址
        url: this.getAppBaseApi() + "/caaesign/entGuestCancel/importEntGuestItems2"
      },
      // 文件上传参数
      upload: {
        // 是否显示弹出层
        open: false,
        fileList: [],
        fileNameList: [],
        // 弹出层标题
        title: "",
        multiple: false,
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: this.getHeader(),
        // 上传的地址
        url: this.getAppBaseApi() + "/system/fileInfo/uploadWithOos"
      },
      outLegalEntGuestItems1Options: [],
      outTransferLegalEntGuestItems1Options: [],
      inLegalEntGuestItems1Options: [],
      inTransferLegalEntGuestItems1Options: [],
      equipmentCurrencyEntGuestItems2Options: [],
      entGuestItems1DragTableMobileClientWidth: 0,
      entGuestItems2DragTableMobileClientWidth: 0,
      isMobile: false,
      labelPosition: 'left',
    }
  },
  computed: {},
  watch: {},
  created() {
    const id = this.$route.query.id;
    this.changeTagsView(this.$route.query);
    this.isMobile = this.isMobileFun()
    if (this.isMobile) {
      this.labelPosition = 'top'
    }
    getSignConfigList().then(response => {
      if (response.code === 0) {
        response.data.forEach(element => {
          const colKey = element.colKey.split('_')
          if (colKey.length > 1) {
            if (this.rules[colKey[0]] && colKey[1] === 'required') {
              this.rules[colKey[0]][0]['required'] = element.colValue === 'true'
              this.formConfigData[element.colKey] = element.colValue === 'true'
            }
            else {
              this.formConfigData[element.colKey] = element.colValue
            }
          }
          else {
            this.formConfigData[element.colKey] = element.colValue
          }
        });
      }
    })
    if (id != null && id != undefined) {
      getEntGuestCancel(id).then(response => {
        this.formData = response.data;
        this.checkBoxParse()
        this.cascaderParse()
        this.timeRangParse()
        if (!this.formData.entGuestItems2Lists) {
          this.formData.entGuestItems2Lists = []
        }
        taskComment(this.formData.processId).then(response => {
          this.commentList = response.data;
        });
        getSignPath(this.formData.processId).then(response => {
          this.signPath = response.data;
        });
        if (this.formData.attachids) {
          let a = this.formData.attachids.split(',');
          if (a.length > 0) {
            a.forEach(item => {
              if (item) {
                getByKey(item).then(response => {
                  this.upload.fileList.push({
                    name: response.data.orignalName,
                    url: response.data.name
                  });
                })
              }
            })
          }
        }
        this.getOutLegalEntGuestItems1Options()
        this.getOutTransferLegalEntGuestItems1Options()
        this.getInLegalEntGuestItems1Options()
        this.getInTransferLegalEntGuestItems1Options()
        this.getEquipmentCurrencyEntGuestItems2Options()
      });
    }
    this.getDicts("sign_status").then(response => {
      this.auditStatus = response.data;
    });
  },
  mounted() {
    this.$nextTick(function() {
      if (this.isMobile) {
        this.entGuestItems1DragTableMobileClientWidth = this.$refs.entGuestItems1DragTableMobile.$el
          .clientWidth
        this.entGuestItems2DragTableMobileClientWidth = this.$refs.entGuestItems2DragTableMobile.$el
          .clientWidth
      }
    })
  },
  methods: {
    closeForm() {
      //关闭子页面
      if (this.$store.state.settings.tagsView) {
        this.$router.go(-1) // 返回
        this.$store.state.tagsView.visitedViews.splice(this.$store.state.tagsView.visitedViews.findIndex(
          item => item.path === this.$route.path), 1)
        this.$router.push(this.$store.state.tagsView.visitedViews[this.$store.state.tagsView.visitedViews
          .length - 1].path)
      }
      else {
        parent.postMessage("closeCurrentTabMessage", '*');
      }
    },
    handleTrack() {
      this.imgUrl = this.getAppBaseApi() + '/activiti/task/track/' + this.formData.processId,
        this.showImgDialog = true
    },
    statusFormat(row, column) {
      return this.selectDictLabel(this.auditStatus, row.status);
    },
    handleTask: function(pass) {
      this.$refs["elForm"].validate(valid => {
        this.isDisable = true;
        if (pass == 0 || (pass == 1 && this.formData.comment != null)) {
          this.formData.pass = pass
          checkTask(this.formData).then(response => {
            if (response.code === 0) {
              this.msgSuccess(this.$t('tips.operationSuccessful'));
              this.closeForm();
            }
            else {
              this.msgError(response.msg);
            }
            this.isDisable = false;
          });
        }
        else {
          this.isDisable = false;
          this.msgInfo(this.$t('table.activity.inputApprovalOpinions'));
        }
      });
    },
    attachidsBeforeUpload(file) {
      let isRightSize = file.size / 1024 / 1024 < 2
      if (!isRightSize) {
        this.$message.error('文件大小超过 2MB')
      }
      return isRightSize
    },
    // 文件上传成功处理
    uploadsucces(response, file, fileList) {
      if (response.data.name != undefined && response.data.name != "undefined") {
        this.upload.fileList.push({
          name: file.name,
          url: response.data.name
        });
        this.formData.attachids += response.data.name + ",";
      }
    },
    handleChange(file, fileList) {},
    handleExceed(file, fileList) {},
    handleRemove(file, fileList) {
      this.$emit("delUploadImage", file.name);
      const index = this.upload.fileList.indexOf(file);
      this.upload.fileList.splice(index, 1);
      if (this.formData.attachids) {
        this.formData.attachids = this.formData.attachids.replace(file.url + ",", "");
      }
      this.upload.fileNameList.push(file.url);
      // delFileInfo(file.url);
    },
    handlePreview(file) {
      previewFileOos(file.url)
    },
    checkBoxParse() {},
    cascaderParse() {},
    timeRangParse() {},
    handleAddEntGuestItems1() {
      const cloumn = {
        outLegal: '',
        outTransferLegal: '',
        inLegal: '',
        inTransferLegal: ''
      };
      this.formData.entGuestItems1Lists.splice(this.formData.entGuestItems1Lists.length, 0, cloumn);
      for (let index in this.formData.entGuestItems1Lists) {
        this.formData.entGuestItems1Lists[index].sort = parseInt(index) + 1;
      }
    },
    handleDelEntGuestItems1(index, row) {
      let functionName = this.$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.default.functionName');
      this.$confirm(this.$t('tips.deleteConfirm', ['iPEBG移轉設備銷賬聯絡單從表1', row.id]), this.$t('tips.warm'), {
        confirmButtonText: this.$t("common.confirmTrim"),
        cancelButtonText: this.$t("common.cancelTrim"),
        type: "warning",
      }).then(() => {
        this.formData.entGuestItems1Lists.splice(index, 1);
        this.msgSuccess(this.$t("tips.deleteSuccess"));
      }).catch(function(err) {
        console.log(err);
      });
    },
    // 文件上传成功处理
    uploadsuccesEntGuestItems1(response, file, fileList) {
      if (response.code === 0) {
        this.msgSuccess(this.$t('tips.importSuccess'));
        this.formData.entGuestItems1Lists = this.formData.entGuestItems1Lists.concat(response.data);
        response.data.forEach(item => {})
      }
      else {
        this.msgError(response.msg);
      }
    },
    handleChangeEntGuestItems1(file, fileList) {},
    handleExceedEntGuestItems1(file, fileList) {},
    handleRemoveEntGuestItems1(file, fileList) {
      this.$emit("delUploadImage", file.name);
      const index = this.upload.fileList.indexOf(file);
      this.upload.fileList.splice(index, 1);
      this.formData.attachids = this.formData.attachids.replace(file.url + ",", "");
      this.upload.fileNameList.push(file.url);
      // delFileInfo(file.url);
    },
    handlePreviewEntGuestItems1(file) {
      const queryParams = this.queryParams;
      exportEntGuestItems1(queryParams).then(response => {
        this.download(response.data);
      })
    },
    entGuestItems1_getSummaries(param) {
      const {
        columns,
        data
      } = param;
      const sums = [];
      const scales = [];
      const childAttributesMap = {};
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = this.$t('common.sumText');
          return;
        }
        if (column.property && childAttributesMap[column.property] && childAttributesMap[column.property]
          .sumMasterColumn) {
          scales[index] = 0;
          if (childAttributesMap[column.property].decimalScale) {
            scales[index] = childAttributesMap[column.property].decimalScale
          }
          const values = data.map(item => Number(item[column.property]));
          if (!values.every(value => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                var x = prev + curr
                return x
              }
              else {
                return prev
              }
            }, 0);
            sums[index] = sums[index].toFixed(scales[index])
            this.formData[childAttributesMap[column.property].sumMasterColumn] = sums[index]
          }
        }
      });
      return sums;
    },
    beforeUploadEntGuestItems1(file) {
      let isRightSize = file.size / 1024 / 1024 < 2
      if (!isRightSize) {
        this.$message.error('文件大小超过 2MB')
      }
      let isAccept = new RegExp('.xls,.xlsx'.replaceAll(',', '|')).test(file.name)
      if (!isAccept) {
        this.$message.error('应该选择.xls,.xlsx类型的文件')
      }
      return isRightSize && isAccept
    },
    getOutLegalEntGuestItems1Options() {
      this.getDicts("guest_out_legal").then(response => {
        this.outLegalEntGuestItems1Options = response.data;
        this.outLegalEntGuestItems1Options.forEach(item => {
          this.formData.entGuestItems1Lists.forEach((selectInfo, n) => {
            if (item.value == selectInfo.outLegal) {
              // this.formData.entGuestItems1Lists[n].outLegalValue = item.label
              this.$set(this.formData.entGuestItems1Lists[n], 'outLegalValue', item.label)
            }
          })
        })
      });
    },
    getOutTransferLegalEntGuestItems1Options() {
      this.getDicts("guest_out_transfer").then(response => {
        this.outTransferLegalEntGuestItems1Options = response.data;
        this.outTransferLegalEntGuestItems1Options.forEach(item => {
          this.formData.entGuestItems1Lists.forEach((selectInfo, n) => {
            if (item.value == selectInfo.outTransferLegal) {
              // this.formData.entGuestItems1Lists[n].outTransferLegalValue = item.label
              this.$set(this.formData.entGuestItems1Lists[n], 'outTransferLegalValue', item.label)
            }
          })
        })
      });
    },
    getInLegalEntGuestItems1Options() {
      this.getDicts("guest_in_legal").then(response => {
        this.inLegalEntGuestItems1Options = response.data;
        this.inLegalEntGuestItems1Options.forEach(item => {
          this.formData.entGuestItems1Lists.forEach((selectInfo, n) => {
            if (item.value == selectInfo.inLegal) {
              // this.formData.entGuestItems1Lists[n].inLegalValue = item.label
              this.$set(this.formData.entGuestItems1Lists[n], 'inLegalValue', item.label)
            }
          })
        })
      });
    },
    getInTransferLegalEntGuestItems1Options() {
      this.getDicts("guest_in_transfer").then(response => {
        this.inTransferLegalEntGuestItems1Options = response.data;
        this.inTransferLegalEntGuestItems1Options.forEach(item => {
          this.formData.entGuestItems1Lists.forEach((selectInfo, n) => {
            if (item.value == selectInfo.inTransferLegal) {
              // this.formData.entGuestItems1Lists[n].inTransferLegalValue = item.label
              this.$set(this.formData.entGuestItems1Lists[n], 'inTransferLegalValue', item.label)
            }
          })
        })
      });
    },
    handleAddEntGuestItems2() {
      const cloumn = {
        equipmentName: '',
        equipmentGuige: '',
        equipmentNumber: '',
        equipmentCurrency: '',
        equipmentPrice: '',
        equipmentTotal: '',
        equpipmentSell: ''
      };
      this.formData.entGuestItems2Lists.splice(this.formData.entGuestItems2Lists.length, 0, cloumn);
      for (let index in this.formData.entGuestItems2Lists) {
        this.formData.entGuestItems2Lists[index].sort = parseInt(index) + 1;
      }
    },
    handleDelEntGuestItems2(index, row) {
      let functionName = this.$t('ent_guest_cancel_075d399c2dcaa20f94d53895254e58a1.default.functionName');
      this.$confirm(this.$t('tips.deleteConfirm', ['iPEBG移轉設備銷賬聯絡單從表2', row.id]), this.$t('tips.warm'), {
        confirmButtonText: this.$t("common.confirmTrim"),
        cancelButtonText: this.$t("common.cancelTrim"),
        type: "warning",
      }).then(() => {
        this.formData.entGuestItems2Lists.splice(index, 1);
        this.msgSuccess(this.$t("tips.deleteSuccess"));
      }).catch(function(err) {
        console.log(err);
      });
    },
    // 文件上传成功处理
    uploadsuccesEntGuestItems2(response, file, fileList) {
      if (response.code === 0) {
        this.msgSuccess(this.$t('tips.importSuccess'));
        this.formData.entGuestItems2Lists = this.formData.entGuestItems2Lists.concat(response.data);
        response.data.forEach(item => {
          this.EntGuestItems2_equipmentNumber_onchange(item)
          this.EntGuestItems2_equipmentPrice_onchange(item)
        })
      }
      else {
        this.msgError(response.msg);
      }
    },
    handleChangeEntGuestItems2(file, fileList) {},
    handleExceedEntGuestItems2(file, fileList) {},
    handleRemoveEntGuestItems2(file, fileList) {
      this.$emit("delUploadImage", file.name);
      const index = this.upload.fileList.indexOf(file);
      this.upload.fileList.splice(index, 1);
      this.formData.attachids = this.formData.attachids.replace(file.url + ",", "");
      this.upload.fileNameList.push(file.url);
      // delFileInfo(file.url);
    },
    handlePreviewEntGuestItems2(file) {
      const queryParams = this.queryParams;
      exportEntGuestItems2(queryParams).then(response => {
        this.download(response.data);
      })
    },
    EntGuestItems2_equipmentNumber_onchange(item) {
      if (!isNaN(Number(item.equipmentPrice)) && !isNaN(Number(item.equipmentNumber))) {
        item.equipmentTotal = (Number(item.equipmentNumber) * Number(item.equipmentPrice)).toFixed(2)
      }
      else {
        item.equipmentTotal = ''
      }
    },
    EntGuestItems2_equipmentPrice_onchange(item) {
      if (!isNaN(Number(item.equipmentPrice)) && !isNaN(Number(item.equipmentNumber))) {
        item.equipmentTotal = (Number(item.equipmentNumber) * Number(item.equipmentPrice)).toFixed(2)
      }
      else {
        item.equipmentTotal = ''
      }
    },
    entGuestItems2_getSummaries(param) {
      const {
        columns,
        data
      } = param;
      const sums = [];
      const scales = [];
      const childAttributesMap = {
        "equipmentNumber": {
          "sumMasterColumn": "equipmentAllNumber"
        },
        "equipmentTotal": {
          "sumMasterColumn": "equipmentAllAmount",
          "decimalScale": 2
        },
        "equipmentPrice": {
          "sumMasterColumn": "equipmentAllPrice",
          "decimalScale": 2
        }
      };
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = this.$t('common.sumText');
          return;
        }
        if (column.property && childAttributesMap[column.property] && childAttributesMap[column.property]
          .sumMasterColumn) {
          scales[index] = 0;
          if (childAttributesMap[column.property].decimalScale) {
            scales[index] = childAttributesMap[column.property].decimalScale
          }
          const values = data.map(item => Number(item[column.property]));
          if (!values.every(value => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                var x = prev + curr
                return x
              }
              else {
                return prev
              }
            }, 0);
            sums[index] = sums[index].toFixed(scales[index])
            this.formData[childAttributesMap[column.property].sumMasterColumn] = sums[index]
          }
        }
      });
      return sums;
    },
    beforeUploadEntGuestItems2(file) {
      let isRightSize = file.size / 1024 / 1024 < 2
      if (!isRightSize) {
        this.$message.error('文件大小超过 2MB')
      }
      let isAccept = new RegExp('.xls,.xlsx'.replaceAll(',', '|')).test(file.name)
      if (!isAccept) {
        this.$message.error('应该选择.xls,.xlsx类型的文件')
      }
      return isRightSize && isAccept
    },
    getEquipmentCurrencyEntGuestItems2Options() {
      this.getDicts("guest_eqp_currency").then(response => {
        this.equipmentCurrencyEntGuestItems2Options = response.data;
        this.equipmentCurrencyEntGuestItems2Options.forEach(item => {
          this.formData.entGuestItems2Lists.forEach((selectInfo, n) => {
            if (item.value == selectInfo.equipmentCurrency) {
              // this.formData.entGuestItems2Lists[n].equipmentCurrencyValue = item.label
              this.$set(this.formData.entGuestItems2Lists[n], 'equipmentCurrencyValue', item
                .label)
            }
          })
        })
      });
    },
  }
}

</script>
<style scoped>
.el-upload__tip {
  line-height: 1.2;
}

.del-handler {
  font-size: 20px;
  color: #ff4949;
}

.el-dialog {
  position: relative;
  margin: 0 auto 0px;
  background: #FFFFFF;
  border-radius: 2px;
  -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 50%;
  height: 60%;
}

.el-dialog__body {
  border-top: 1px solid #dcdfe6;
  border-bottom: 1px solid #dcdfe6;
  max-height: 85% !important;
  min-height: 70%;
  overflow-y: auto;
}

.el-slider>/deep/.el-slider__runway {
  margin: 15px 0;
}

.el-checkbox-group {
  font-size: inherit;
}

.el-color-picker {
  display: inherit;
}

/deep/.el-input-number--medium {
  width: 100%;
}

.talbe-name-style {
  font-size: 16px;
  font-weight: bold;
  color: #1DB8FF;
  line-height: 50px;
}

/deep/.el-scrollbar__view table td {
  padding: 0;
}

.el-form-item {
  margin-top: 5px;
  margin-bottom: 5px;
}

.ant-card {
  color: black;
}

.el-table {
  color: black;
}

.el-form-item.edit-item {
  margin-top: 15px;
  margin-bottom: 22px;
}

</style>
