<template>
  <el-card>
    <el-tabs v-model="activeName">
      <el-tab-pane :label="$t(&quot;design_gen_editTable.default.basic&quot;)" name="basic">
        <basic-info-form ref="basicInfo" :info="info" />
      </el-tab-pane>
      <el-tab-pane :label="$t(&quot;design_gen_editTable.default.cloum&quot;)" name="cloum">
        <el-row :gutter="10" class="mb8">
          <el-col :span="8" >
            <el-button
              type="primary"
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
            >{{$t('design_gen_basicInfoForm.default.add')}}</el-button>
          </el-col>
      <el-col  :span="12">
        {{$t('design_gen_basicInfoForm.label.parentTableId')}}
              <el-select
                v-model="info.parentTableId"
                :placeholder="$t(&quot;common.placeholderSelectDefault&quot;) +　$t(&quot;design_gen_basicInfoForm.label.parentTableId&quot;)"
                @change="handleParentTableClick"
                clearable
              >
                <el-option
                   v-for="datatable in tableIdOptions"
                  :key="datatable.id"
                  :label="datatable.tableName"
                  :value="datatable.id"
                ></el-option>
              </el-select>
      </el-col>
        </el-row>

        <el-table ref="dragTable" :data="cloumns" row-key="id" :max-height="tableHeight">
          <el-table-column
            :label="$t(&quot;design_gen_editTable.label.id&quot;)"
            type="index"
            min-width="5%"
          />
          <el-table-column
            :label="$t(&quot;design_gen_editTable.label.columnName&quot;)"
            min-width="10%"
          >
            <template slot-scope="scope">
              <el-input
                v-model.trim="scope.row.columnName"
                :disabled="scope.row.disable"
                :placeholder="$t(&quot;design_gen_editTable.label.columnName&quot;)"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t(&quot;design_gen_editTable.label.columnComment&quot;)"
            min-width="10%"
          >
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.columnComment"
                :disabled="scope.row.disable"
                :placeholder="$t(&quot;design_gen_editTable.label.columnComment&quot;)"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t(&quot;design_gen_editTable.label.columnType&quot;)"
            min-width="10%"
          >
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.columnType"
                :disabled="scope.row.disable"
                :placeholder="$t(&quot;design_gen_editTable.label.columnType&quot;)"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t(&quot;design_gen_editTable.label.javaType&quot;)"
            min-width="11%"
          >
            <template slot-scope="scope">
              <el-select v-model="scope.row.javaType" :disabled="scope.row.disable">
                <el-option label="Long" value="Long" />
                <el-option label="String" value="String" />
                <el-option label="Integer" value="Integer" />
                <el-option label="Double" value="Double" />
                <el-option label="BigDecimal" value="BigDecimal" />
                <el-option label="Date" value="Date" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t(&quot;design_gen_editTable.label.defValue&quot;)"
            min-width="10%"
          >
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.defValue"
                :disabled="scope.row.disable"
                :placeholder="$t(&quot;design_gen_editTable.label.defValue&quot;)"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column :label="$t(&quot;design_gen_editTable.label.isPk&quot;)" min-width="5%">
            <template slot-scope="scope">
              <el-checkbox
                true-label="1"
                :disabled="scope.row.disable"
                false-label="0"
                v-model="scope.row.isPk"
              ></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column :label="$t(&quot;design_gen_editTable.label.isAdd&quot;)"   min-width="5%">
            <template slot-scope="scope">
              <el-checkbox
                true-label="1"
                :disabled="scope.row.disable"
                false-label="0"
                v-model="scope.row.isAdd"
              ></el-checkbox>
            </template>
          </el-table-column>
          <!--<el-table-column
            :label="$t(&quot;design_gen_editTable.label.isEdit&quot;)"
            min-width="5%"
            v-if="ifShow"
          >
            <template slot-scope="scope">
              <el-checkbox
                true-label="1"
                :disabled="scope.row.disable"
                false-label="0"
                v-model="scope.row.isEdit"
              ></el-checkbox>
            </template>
          </el-table-column>-->
          <el-table-column
            :label="$t(&quot;design_gen_editTable.label.isList&quot;)"
            min-width="5%"
            v-if="ifShow"
          >
            <template slot-scope="scope">
              <el-checkbox
                true-label="1"
                :disabled="scope.row.disable"
                false-label="0"
                v-model="scope.row.isList"
              ></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t(&quot;design_gen_editTable.label.isExcel&quot;)"
            min-width="5%"
          >
            <template slot-scope="scope">
              <el-checkbox
                true-label="1"
                :disabled="scope.row.disable"
                false-label="0"
                v-model="scope.row.isExcel"
              ></el-checkbox>
            </template>
          </el-table-column>

          <el-table-column
            :label="$t(&quot;design_gen_editTable.label.isQuery&quot;)"
            min-width="5%"
            v-if="ifShow"
          >
            <template slot-scope="scope">
              <el-checkbox
                true-label="1"
                :disabled="scope.row.disable"
                false-label="0"
                v-model="scope.row.isQuery"
              ></el-checkbox>
            </template>
          </el-table-column>


          <el-table-column
            :label="$t(&quot;design_gen_editTable.label.queryType&quot;)"
            min-width="10%"
            v-if="ifShow"
          >
            <template slot-scope="scope">
              <el-select v-model="scope.row.queryType" :disabled="scope.row.disable">
                <el-option label="=" value="eq" />
                <el-option label="!=" value="ne" />
                <el-option label=">" value="gt" />
                <el-option label=">=" value="ge" />
                <el-option label="<" value="lt" />
                <el-option label="<=" value="le" />
                <el-option label="in" value="in" />
                <el-option label="like" value="like" />
                <el-option label="isNull" value="isNull" />
                <el-option label="isNotNull" value="isNotNull" />
                <el-option label="between" value="between" />
                <el-option label="notBetween" value="notBetween" />
              </el-select>
            </template>
          </el-table-column>

          <el-table-column
            :label="$t(&quot;design_gen_editTable.label.signOrder&quot;)"
            min-width="10%"
            v-if="ifShow"
          >
          <template slot="header" slot-scope="scope">
            {{$t('design_gen_editTable.label.signOrder')}}
            <el-tooltip :content="$t(&quot;design_gen_editTable.label.signOrderTip&quot;)" placement="top">
              <i class="el-icon-question"></i>
            </el-tooltip>
           </template>
            <template slot-scope="scope">
              <el-input-number v-model="scope.row.signOrder" controls-position="right" :min="1" :max="999" @change="handleChange(scope.$index, scope.row)"></el-input-number>
            </template>
          </el-table-column>
          <el-table-column
            label="簽核類型"
            min-width="10%"
            v-if="ifShow"
          >
            <template slot-scope="scope">
              <el-select v-model="scope.row.isHuiqian" :disabled="scope.row.disable" clearable @change="handleIsHuiqianChange(scope.$index, scope.row)">
                <el-option label="普通" value="0"/>
                <el-option label="會簽" value="1" />
              </el-select>
            </template>
          </el-table-column>

          <el-table-column
            :label="$t(&quot;design_gen_editTable.label.isBatch&quot;)"
            min-width="5%"
            v-if="ifShow"
          >
            <template slot-scope="scope">
              <el-checkbox
                true-label="1"
                :disabled="scope.row.disable"
                false-label="0"
                v-model="scope.row.isBatch"
              ></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t(&quot;design_gen_editTable.label.isRequired&quot;)"
            min-width="5%"
          >
            <template slot-scope="scope">
              <el-checkbox
                true-label="1"
                :disabled="scope.row.disable"
                false-label="0"
                v-model="scope.row.isRequired"
              ></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t(&quot;design_gen_editTable.label.htmlType&quot;)"
            min-width="12%"
          >
            <template slot-scope="scope">
              <el-select v-model="scope.row.htmlType" :disabled="scope.row.disable">
                <el-option
                  :label="$t(&quot;design_gen_editTable.default.input&quot;)"
                  value="input"
                />
                <el-option
                  :label="$t(&quot;design_gen_editTable.default.textarea&quot;)"
                  value="textarea"
                />
                <el-option
                  :label="$t(&quot;design_gen_editTable.default.number&quot;)"
                  value="number"
                />
                <el-option
                  :label="$t(&quot;design_gen_editTable.default.password&quot;)"
                  value="password"
                />
                <el-option
                  :label="$t(&quot;design_gen_editTable.default.select&quot;)"
                  value="select"
                />
                <el-option
                  :label="$t(&quot;design_gen_editTable.default.cascader&quot;)"
                  value="cascader"
                />
                <el-option
                  :label="$t(&quot;design_gen_editTable.default.radio&quot;)"
                  value="radio"
                />
                <el-option
                  :label="$t(&quot;design_gen_editTable.default.checkbox&quot;)"
                  value="checkbox"
                />
                <el-option
                  :label="$t(&quot;design_gen_editTable.default.time&quot;)"
                  value="time"
                />

                <el-option
                  :label="$t(&quot;design_gen_editTable.default.timeRange&quot;)"
                  value="time-range"
                />
                <el-option
                  :label="$t(&quot;design_gen_editTable.default.date&quot;)"
                  value="date"
                />
                <el-option
                  :label="$t(&quot;design_gen_editTable.default.datetime&quot;)"
                  value="datetime"
                />

                <el-option
                  :label="$t(&quot;design_gen_editTable.default.dateRange&quot;)"
                  value="date-range"
                />

                <el-option :label="$t(&quot;design_gen_editTable.default.dept&quot;)" value="dept" />
                <el-option :label="$t(&quot;design_gen_editTable.default.user&quot;)" value="user" />
                <el-option
                  :label="$t(&quot;design_gen_editTable.default.fileUpload&quot;)"
                  value="fileUpload"
                />
                <el-option
                  :label="$t(&quot;design_gen_editTable.default.switch&quot;)"
                  value="switch"
                />
                <el-option
                  :label="$t(&quot;design_gen_editTable.default.slider&quot;)"
                  value="slider"
                />
                <el-option
                  :label="$t(&quot;design_gen_editTable.default.rate&quot;)"
                  value="rate"
                />
                <el-option
                  :label="$t(&quot;design_gen_editTable.default.color&quot;)"
                  value="color"
                />

              </el-select>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t(&quot;design_gen_editTable.label.dictType&quot;)"
            min-width="12%"
          >
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.dictType"
                :disabled="scope.row.disable"
                clearable
                filterable
                :placeholder="$t(&quot;common.select&quot;)"
              >
                <el-option
                  v-for="dict in dictOptions"
                  :key="dict.type"
                  :label="dict.name"
                  :value="dict.type"
                >
                  <span style="float: left">{{ dict.name }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">{{ dict.type }}</span>
                </el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            :label="$t(&quot;design_gen_editTable.default.operation&quot;)"
            width="80"
          >
            <template slot-scope="scope">
              <svg-icon
                v-show="scope.row.show"
                class="drag-handler"
                icon-class="drag"
                class-name="allowDrag"
              />
              <el-button
                v-show="scope.row.show"
                type="text"
                @click="handleDel(scope.$index, scope.row)"
                class="del-handler"
                icon="el-icon-delete"
              ></el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane
        :label="$t(&quot;design_gen_editTable.default.genInfo&quot;)"
        name="genInfo"
        height="320px;"
      >
        <gen-info-form ref="genInfo" :info="info" />
      </el-tab-pane>
    </el-tabs>
    <el-form label-width="100px">
      <el-form-item style="text-align: center;margin-left:-100px;margin-top:10px;">
        <el-button
          type="primary"
          @click="save()"
        >{{$t('design_gen_editTable.default.save')}}</el-button>
        <el-button
          type="primary"
          @click="submit()"
        >{{$t('design_gen_editTable.default.submit')}}</el-button>
        <!--<el-button
          type="primary"
          @click="submitForm()"
        >{{$t('design_gen_editTable.default.submit')}}</el-button>-->
        <el-button @click="close()">{{$t('design_gen_editTable.default.return')}}</el-button>
      </el-form-item>
    </el-form>
  </el-card>
</template>
<script>
import { getGenTable, updateGenTable, menuTree,updateSave,submitSave } from "@/api/design/datatable";
import { dictByUserList } from "@/api/system/dict";
import { camelCase, titleCase } from "@/utils/index";
import { designTableList } from "@/api/design/datatable";
import basicInfoForm from "./basicInfoForm";
import genInfoForm from "./genInfoForm";
import Sortable from "sortablejs";
export default {
  name: "designGenEdit",
  components: {
    basicInfoForm,
    genInfoForm,
  },
  data() {
    return {
      // 选中选项卡的 name
      activeName: "basic",
      // 表格的高度
      tableHeight: document.documentElement.scrollHeight - 245 + "px",
      // 表列信息
      cloumns: [],
      // 字典信息
      dictOptions: [],
      // 删除表字段名
      delNames: [],
      // 菜单信息
      // menus: [],
      // 表详细信息
      info: { menus: [] ,},
      // 數據庫ID
      dataSourceId: "",
      //表的下拉框選項
      tableIdOptions: [],
      //父节点的index
      pIdIndex: 8,
      pIdCloumn: {
          columnName: 'pid',
          columnComment: "父表ID",
          columnType: "varchar(64)",
          javaType: "String",
          javaField: "",
          defValue: "",
          queryType: "eq",
          htmlType: "input",
          addData: true,
          disable: true,
          show: false,
          disabled: "true",
          showMove: "false",
        },
      mainTableList:[],
      ifShow:true,
    };
  },
  beforeCreate() {
    // created() {
    const { tableName, tableComment, dataSourceId } = this.$route.query;
    this.dataSourceId = dataSourceId;
    // 获取表详细信息
    getGenTable(tableName, tableComment, dataSourceId).then((res) => {

      this.info = res.data;
      if( this.info.parentTableId!=""&& this.info.parentTableId!=null){
        this.ifShow = false
      }
       //將未設置簽核順序字段設置為空
      let cloumnsClone = new Array();
        res.data.columns.forEach(item=>{
        if(item.signOrder == null||item.signOrder== '0'){
          item.signOrder = undefined
        }
        cloumnsClone.push(item);
      })
      this.cloumns = cloumnsClone;
      if (this.info.options) {
        const options = JSON.parse(this.info.options);
        this.info.treeId = options.treeId;
        this.info.treeParentId = options.treeParentId;
        this.info.treeName = options.treeName;
      }
      this.info.menus = this.handleTree(res.data.menus, "id","parentid");

      designTableList(dataSourceId).then((response) => {
        this.tableIdOptions = response.data;
        for (let index in this.tableIdOptions) {
          if (this.tableIdOptions[index].id == this.info.id) {
            this.tableIdOptions.splice(index, 1);
          }
        }
      });
    });
    /** 查询字典下拉列表 */
    dictByUserList().then((response) => {
      this.dictOptions = response.data;
    });
  },
  created() {
    if("" == this.info.parentTableId||this.info.parentTableId == null ){
      this.ifShow = true
      if(this.cloumns[this.pIdIndex] != null && this.cloumns[this.pIdIndex].columnName == this.pIdCloumn.columnName){
        if (this.cloumns[this.pIdIndex].id) {
          this.delNames.push(this.cloumns[this.pIdIndex].columnName);
        }
        this.cloumns.splice(this.pIdIndex, 1);
      }
      if(this.mainTableList.length>0){
        this.cloumns.splice(this.pIdIndex, 0,...this.mainTableList);
      }
    }else{
      this.ifShow = false
      let disableNum = 0
      let ifPid = true
      this.cloumns.forEach(item=>{
        if(item.disable){
          if(item.columnName === "pid"){
            ifPid = false
          }
          disableNum++
        }
      })
      this.mainTableList = this.cloumns.splice(this.pIdIndex,disableNum-this.pIdIndex)
      if(ifPid){
        this.cloumns.splice(this.pIdIndex, 0, this.pIdCloumn);
        for (let index in this.cloumns) {
          this.cloumns[index].sort = parseInt(index) + 1;
        }
      }
    }

  },
  mounted() {
    const { tableName, tableComment, dataSourceId } = this.$route.query;
    this.dataSourceId = dataSourceId;
    const el = this.$refs.dragTable.$el.querySelectorAll(
      ".el-table__body-wrapper > table > tbody"
    )[0];
    const sortable = Sortable.create(el, {
      handle: ".allowDrag",
      onEnd: (evt) => {
        const targetRow = this.cloumns.splice(evt.oldIndex, 1)[0];
        this.cloumns.splice(evt.newIndex, 0, targetRow);
        for (let index in this.cloumns) {
          this.cloumns[index].sort = parseInt(index) + 1;
        }
      },
    });
  },
  watch: {
    // eslint-disable-next-line func-names
    "info.tableName": function (val, oldVal) {
      // console.log(camelCase(val))
      if (val) {
        this.info.tableName = val.trim().toLowerCase();
        this.info.className = titleCase(camelCase(val.trim()));
        this.info.businessName = camelCase(val.trim());
      }else{
        this.info.className = '';
        this.info.businessName = '';
      }
    },
  },
  methods: {
    /** 提交按钮 */
    submitForm() {
      const basicForm = this.$refs.basicInfo.$refs.basicInfoForm;
      const genForm = this.$refs.genInfo.$refs.genInfoForm;
      // debugger
      Promise.all([basicForm, genForm].map(this.getFormPromise)).then((res) => {
        const validateResult = res.every((item) => !!item);
        if (validateResult) {
          const genTable = Object.assign({}, basicForm.model, genForm.model);
          //console.log(this.cloumns)
          //生成新sort
          for (let index in this.cloumns) {
            this.cloumns[index].sort = parseInt(index) + 1;
          }
          genTable.columns = this.cloumns;
          genTable.params = {
            treeId: genTable.treeId,
            treeName: genTable.treeName,
            treeParentId: genTable.treeParentId,
          };
          //console.log('delNames：' + JSON.stringify(this.delNames))
          if (this.delNames && this.delNames.length > 0) {
            genTable.delNames = this.delNames.join();
          }
          genTable.dataSourceId = this.dataSourceId;
          //console.log('genTable：' + JSON.stringify(genTable))
          updateGenTable(genTable).then((res) => {
            this.msgSuccess(res.msg);
            // debugger
            if (res.code === 0) {
              this.close();
            }
          });
        } else {
          this.msgError(
            this.$t("design_gen_editTable.default.formValidateResult")
          );
        }
      });
    },
    getFormPromise(form) {
      return new Promise((resolve) => {
        form.validate((res) => {
          resolve(res);
        });
      });
    },
    handleAdd() {
      const cloumn = {
        // columnName: '',
        columnComment: "",
        columnType: "",
        javaType: "String",
        javaField: "",
        defValue: "",
        queryType: "eq",
        htmlType: "input",
        addData: true,
        disable: false,
        disabled: "false",
        showMove: "true",
        show: true,
      };
      this.cloumns.splice(this.cloumns.length, 0, cloumn);
      for (let index in this.cloumns) {
        this.cloumns[index].sort = parseInt(index) + 1;
      }
    },
    /** 关闭按钮 */
    close() {
      this.$store.dispatch("tagsView/delView", this.$route);
      this.$router.push({
        path: "/designTool/design_datatable",
        query: { t: Date.now() },
      });
    },
    handleDel(index, row) {
      this.$confirm(
        this.$t("design_gen_editTable.default.deleteConfirm", [row.columnName]),
        this.$t("tips.warm"),
        {
          confirmButtonText: this.$t("common.confirmTrim"),
          cancelButtonText: this.$t("common.cancelTrim"),
          type: "warning",
        }
      )
        .then(() => {
          if (row.id) {
            this.delNames.push(row.columnName);
          }
          this.cloumns.splice(index, 1);
          this.msgSuccess(this.$t("tips.deleteSuccess"));
        })
        .catch(function (err) {
          console.log(err);
        });
    },
    handleChange(index, row) {
      if (row.signOrder == null || row.signOrder == '0' || row.signOrder == undefined) {
        row.isHuiqian = undefined;
      }else if(row.isHuiqian==''||row.isHuiqian==undefined){
        row.signOrder =this.getMaxSignOrder()==undefined?1:this.getMaxSignOrder()+1;
        row.isHuiqian = '0';
      }
    },
    handleIsHuiqianChange(index, row) {
      if (row.isHuiqian==''||row.isHuiqian==undefined) {
        row.signOrder = undefined;
      }else if(row.isHuiqian!=''||row.isHuiqian!=undefined){
        if (row.signOrder == null || row.signOrder == '0' || row.signOrder == undefined) {
          row.signOrder = '1';
          row.signOrder =this.getMaxSignOrder()==undefined?1:this.getMaxSignOrder()+1;
        }
      }
    },
    getMaxSignOrder() {
      let maxSignOrder = new Array();
      this.cloumns.forEach(item=> {
        if (item.signOrder != null && item.signOrder != undefined) {
          maxSignOrder.push(item.signOrder);
        }
      });
      if (maxSignOrder.length > 1) {
        return Math.max.apply(Math, maxSignOrder);
      }
    },
    handleParentTableClick(data,oldData){
        if("" == data){
          this.ifShow = true
          if(this.cloumns[this.pIdIndex] != null && this.cloumns[this.pIdIndex].columnName == this.pIdCloumn.columnName){
            if (this.cloumns[this.pIdIndex].id) {
              this.delNames.push(this.cloumns[this.pIdIndex].columnName);
            }
            this.cloumns.splice(this.pIdIndex, 1);
          }
          if(this.mainTableList.length>0){
            this.cloumns.splice(this.pIdIndex, 0,...this.mainTableList);
            this.mainTableList = []
          }
        }else{
          this.ifShow = false
          let disableNum = 0
          let ifPid = true
          let i = 1
          this.cloumns.forEach(item=>{
               if(item.disable){
                    if(item.columnName === "pid"){
                         ifPid = false
                    }
                    disableNum++
               }
            i += 1
          })
          console.log("mainTableList1==",this.mainTableList)
          console.log("disableNum==",disableNum)
          if(this.mainTableList.length === 0){
            this.mainTableList = this.cloumns.splice(this.pIdIndex,disableNum + 1 - this.pIdIndex)
          }
          if(ifPid){
            this.cloumns.splice(this.pIdIndex, 0, this.pIdCloumn);
            for (let index in this.cloumns) {
              this.cloumns[index].sort = parseInt(index) + 1;
            }
          }
          // console.log('cloumns2',this.cloumns)
        }

    },
    /** 保存按钮 */
    save() {
      const basicForm = this.$refs.basicInfo.$refs.basicInfoForm;
      const genForm = this.$refs.genInfo.$refs.genInfoForm;
      // debugger
      Promise.all([basicForm, genForm].map(this.getFormPromise)).then((res) => {
        const validateResult = res.every((item) => !!item);
        if (validateResult) {
          const genTable = Object.assign({}, basicForm.model, genForm.model);
          //console.log(this.cloumns)
          //生成新sort
          for (let index in this.cloumns) {
            this.cloumns[index].sort = parseInt(index) + 1;
          }
          genTable.columns = this.cloumns;
          genTable.params = {
            treeId: genTable.treeId,
            treeName: genTable.treeName,
            treeParentId: genTable.treeParentId,
          };
          //console.log('delNames：' + JSON.stringify(this.delNames))
          if (this.delNames && this.delNames.length > 0) {
            genTable.delNames = this.delNames.join();
          }
          genTable.dataSourceId = this.dataSourceId;
          //console.log('genTable：' + JSON.stringify(genTable))
          updateSave(genTable).then((res) => {
            this.msgSuccess(res.msg);
            // debugger
            if (res.code === 0) {
              //this.close();
            }
          });
        } else {
          this.msgError(
            this.$t("design_gen_editTable.default.formValidateResult")
          );
        }
      });
    },
    /** 提交按钮 */
    submit() {
      const basicForm = this.$refs.basicInfo.$refs.basicInfoForm;
      const genForm = this.$refs.genInfo.$refs.genInfoForm;
      // debugger
      Promise.all([basicForm, genForm].map(this.getFormPromise)).then((res) => {
        const validateResult = res.every((item) => !!item);
        if (validateResult) {
          const genTable = Object.assign({}, basicForm.model, genForm.model);
          //console.log(this.cloumns)
          //生成新sort
          for (let index in this.cloumns) {
            this.cloumns[index].sort = parseInt(index) + 1;
          }
          genTable.columns = this.cloumns;
          genTable.params = {
            treeId: genTable.treeId,
            treeName: genTable.treeName,
            treeParentId: genTable.treeParentId,
          };
          //console.log('delNames：' + JSON.stringify(this.delNames))
          if (this.delNames && this.delNames.length > 0) {
            genTable.delNames = this.delNames.join();
          }
          genTable.dataSourceId = this.dataSourceId;
          //console.log('genTable：' + JSON.stringify(genTable))
          submitSave(genTable).then((res) => {
            this.msgSuccess(res.msg);
            // debugger
            if (res.code === 0) {
              this.close();
            }
          });
        } else {
          this.msgError(
            this.$t("design_gen_editTable.default.formValidateResult")
          );
        }
      });
    },

  },
};
</script>
<style scoped>
.drag-handler {
  width: 20px;
  height: 20px;
  cursor: pointer;
  color: #1890ff;
}

.del-handler {
  font-size: 20px;
  color: #ff4949;
}
.el-input-number{
    width:80px;
}
/deep/.el-input-number.is-controls-right .el-input__inner{
  padding-left:5px;
  padding-right:40px;
}
</style>
