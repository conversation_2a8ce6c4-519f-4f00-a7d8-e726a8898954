export default {
ent_recruit_salary_075d399c2dcaa20f94d53895254e58a1:{
    default: {
      beginDate: '開始日期',
      endDate: '結束日期',
      createDate: '創建日期',
      selectOption: '请选择字典生成',
      functionName:'iPEG試用期滿薪資異動匯總審批表',
      add: '新增',
      serialNumber: '序號',
    },
    label: {
              id: '编号',
              createBy: '創建人',
              createTime: '創建時間',
              updateBy: '更新人',
              updateTime: '更新時間',
              remarks: '備註',
              delFlag: '刪除標識',
              deptId: '機構ID',
              processId: '流程定義標示',
              workStatus: '審核狀態',
              serialno: '表單編號',
              makerNo: '填單人工號',
              makerName: '填單人姓名',
              completTime: '完成時間',
              attachids: '附件ids',
              signPerson: '當前簽核人',
              signNode: '當前簽核節點',
              makerdeptno: '填單人所在部門',
              makerfactoryid: '所在廠區',
              dataSource: '數據來源(pc、app)',
              applyEmpNo: '申請人工號',
              applyEmpName: '申請人姓名',
              applyDeptNo: '單位代碼',
              applyCostNo: '費用代碼',
              applyTel: '聯繫方式',
              applyDeptNam: '單位名稱',
              applyMail: '聯繫郵箱',
              recruitUrgent: '緊急程度',
              recruitFiletype: '文件類型',
              cbchargeno: '承辦',
              shenhechargeno: '審核',
              hezhunchargeno: '核准',
              cbchargename: '承辦姓名',
              shenhechargename: '審核姓名',
              hezhunchargename: '核准姓名',
              createDate: '創建日期',
              updateDate: '更新日期',
              recruitSerialNo: '流水碼',
      }
  }
}

