export default {
ent_group_liaison_075d399c2dcaa20f94d53895254e58a1:{
    default: {
      beginDate: '開始日期',
      endDate: '結束日期',
      createDate: '創建日期',
      selectOption: '请选择字典生成',
      functionName:'iPEBG事業群聯絡單',
      add: '新增',
      serialNumber: '序號',
    },
    label: {
              id: '编号',
              createBy: '創建人',
              createTime: '創建時間',
              updateBy: '更新人',
              updateTime: '更新時間',
              remarks: '備註',
              delFlag: '刪除標識',
              deptId: '機構ID',
              processId: '流程定義標示',
              workStatus: '審核狀態',
              serialno: '表單編號',
              makerNo: '填單人工號',
              makerName: '填單人姓名',
              completTime: '完成時間',
              attachids: '附件ids',
              signPerson: '當前簽核人',
              signNode: '當前簽核節點',
              makerdeptno: '填單人所在部門',
              makerfactoryid: '所在廠區',
              dataSource: '數據來源(pc、app)',
              groupApproval: '呈核',
              groupDept: '發文單位',
              groupAudit: '會審單位',
              groupIssue: '發文字號',
              groupAddres: '受文單位',
              groupDate: '發文日期',
              groupNote: '主旨',
              groupDemand: '背景說明',
              groupMatters: '申請事項',
              jointDept: '會審單位名稱',
              jointSignature: '會審簽名',
              jointDirections: '會審批示',
              cbchargeno: '承辦',
              shenhechargeno: '審核',
              hezhunchargeno: '核准',
              huishenchargeno: '會審',
              cbchargename: '承辦姓名',
              shenhechargename: '審核姓名',
              hezhunchargename: '核准姓名',
              huishenchargename: '會審姓名',
              createDate: '創建日期',
              updateDate: '更新日期',
              groupUrgent: '緊急程度',
              groupFiletype: '文件類型',
      }
  }
}

