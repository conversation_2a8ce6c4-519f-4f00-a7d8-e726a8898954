export default {
  //vue i18n 多语言文本指定位置动态改变, 实现占位替换
  // export const message = {
  //   alarm_1010: "ip为{id}xxxxxx",
  // }
  // 或
  // export const message = {
  //   alarm_1010: "ip为{0}xxxxxxxx{1}xxxxxx",
  // }login
  //使用
// {{$t('message.alarm_1010',{id:id})}}
// 或
// {{$t('message.alarm_1010',['test','test'])}}
  route: {
    dashboard: '系統主頁',
    profile: '個人中心'
  },
  navbar: {
    setting: '系統設置',
    github: '項目地址',
    logOut: '退出登錄',
    profile: '個人中心',
    docs: '項目文檔',
    theme: '換膚',
    size: '布局大小',
    deleteCache: '清除緩存'
  },
  login: {
    sysName:'EntFrm開發平臺',
    title: 'entfrm開發平臺',
    userLogin: '用戶登錄',
    logIn: '登 錄',
    logging: '登 录 中...',
    tenant: '企業',
    username: '賬號',
    password: '密碼',
    code: '驗證碼',
    remenberPwd:'記住密碼',
    ortherLoginType: '其他登錄方式',
    chooseToSignIn: '選擇以下賬號登錄：',
    type: {
      up: '賬號密碼登錄',
      social: '第三方賬號登錄'
    }
  },
  documentation: {
    documentation: '項目文檔',
    github: '項目地址'
  },
  table: {
    tenant: {
      code: '企業編碼',
      name: '企業名稱',
      type: '類型',
      status: '狀態',
      duty: '責任人',
      expirationTime: '有效期',
      logo: 'logo',
      describe: '企業簡介',
      passwordExpire: '密碼有效期',
      isMultipleLogin: '是否允許多地登錄',
      passwordErrorNum: '密碼輸錯次數',
      passwordErrorLockTime: '賬號鎖定時間'
    },
    globalUser: {
      tenantCode: '企業編碼',
      account: '賬號',
      mobile: '手機',
      name: '姓名',
      email: '郵箱',
      password: '密碼',
      confirmPassword: '確認密碼'
    },
    application:{
      appName:'應用名稱',
      appType:'應用類型',
      serialNumber:'序號',
      appStatus:'狀態',
      appDaterange:'創建時間',
      beginTime:'開始時間',
      endTime:'結束時間',
      industry:'適用範圍',
      version:'版本',
      description:'描述',
      addApp:'添加應用',
      editApp:'修改應用',
      sureDeleteModel:'是否確認刪除應用名稱為{0}的數據項?',
      exportConfirm: '是否確認導出所有應用數據項?',
      publicKey:"公鑰",
      authorizationCode:"授權碼",
      secretKey:"密文",
      tip:{
        appName:'請輸入應用名稱',
        appType:'請選擇應用類型',
        appStatus:'請選擇狀態',
        industry:'請輸入適用範圍',
        version:'請輸入版本',
        description:'請輸入描述',
        secretKey:"請輸入秘鑰",
      }
    },
    user: {
      username: '工號',
      password: '用戶密碼',
      sex: '性別',
      email: '郵箱',
      dept: '部門',
      role: '角色',
      mobile: '手機號碼',
      status: '狀態',
      createTime: '創建時間',
      modifyTime: '修改時間',
      lastLoginTime: '最後登錄時間',
      desc: '個人描述',
      oldPassword: '舊密碼',
      newPassword: '新密碼',
      confirmPassword: '再次確認',
      social: '第三方賬號',
      userId: '序號',
      nickName: '姓名',
      deptName: '機構名稱',
      deptId: '歸屬機构',
      remark: '備註',
      addUser:'添加用户',
      editUser:'修改用户',
      postName:'崗位',
      roleName:'角色名稱',
      confirmPasswordOnce: '確認密碼',
      upHeadImg:'點擊上傳頭像',
      up:'上傳',
      submit:'提 交',
      modifyTitle:'修改頭像',
      typeError:'文件格式錯誤，請上傳圖片類型，如：JPG，PNG後綴的文件。',
      tip:{
        organizationName:'請輸入機構名稱',
        username:'請輸入賬號名稱',
        nickName:'請輸入用戶名稱',
        phoneNumber:'請輸入手機號碼',
        status:'使用者狀態',
        startTime:'開始日期',
        endTime:'結束時間',
        deptId:'請選擇歸屬機构',
        email:'請輸入郵箱',
        password:'請輸入用戶密碼',
        remark:'請輸入備註',
        promptPasswrod:'請輸入',
        promptNewPasswrod:'的新密碼',
        msgSuccess:'修改成功，新密碼是：',
        import:'用戶導入',
        importResult:'導入結果',
        importAllResult:'是否確認匯出所有用戶資料？',
        deleteUserId:'是否确认删除用户工号为',
        data:'的数据项?',
        nameUnNull:'角色名稱不能為空',
        postName:'請選擇崗位',
        roleName:'請選擇角色名稱',
        oldPassword:'請輸入舊密碼',
        newPassword:'請輸入新密碼',
        confirmPasswordOnce:'請確認密碼',
        twoPwdDiff:'兩次輸入的密碼不一致',
        oldPwdNotNull:'舊密碼不能為空',
        newPwdNotNull:'新密碼不能為空',
        lengthSize:'長度在 6 到 20 個字符',
        pwdNotNull:'確認密碼不能為空',
        postNameUnNull:'崗位不能為空'
      },
      profile:{
        information:'個人資訊',
        informationPro:'基本資料',
        resetPassword:'修改密碼',
      }
    },
    role: {
      role:'角色',
      roleName: '角色名稱',
      remark: '角色描述',
      createTime: '創建時間',
      perms: '角色權限',
      status: '狀態',
      beginTime: '開始時間',
      endTime: '結束時間',
      id: '序號',
      sort:'顯示順序',
      roleSort:'角色順序',
      dateAuthority:'數據權限',
      menuAuthority:'菜單權限',
      loadTip:'加載中,請稍後',
      remark1:'備註',
      code:'權限代碼',
      dataScope:'權限範圍',
      allDateAuthority:'全部數據權限',
      autoDateAuthority:'自定義數據權限',
      orgDateAuthority:'本機構數據權限',
      orgManageDateAuthority:'本機構及以下數據權限',
      selfDateAuthority:'僅本人數據權限',
      enable:'啟用',
      unEnable:'停用',
      updateTip:'确认要{0}{1}角色吗?',
      addRole:'添加角色',
      modifyRole:'修改角色',
      dinDateAuthority:'分配數據權限',
      deleteConfirm:'是否確認刪除角色名稱為{0}的數據項?',
      tip:{
        roleName: '請輸入角色名稱',
        status: '角色狀態',
        remark1:'請輸入備註',
        nameUnNull:'角色名稱不能為空',
        orderUnNull:'角色順序不能為空',
      }
    },
    menu: {
      parentId: '上級ID',
      menuName: '名稱',
      type: '類型',
      icon: '圖標',
      component: '組件',
      path: 'URL',
      orderNum: '排序',
      perms: '權限',
      name:'菜單名稱',
      status:'狀態',
      sort:'排序',
      permsMark:'權限標識',
      componentPath:'組件路徑',
      createTime:'創建時間',
      parentMenu:'上級菜單',
      selectParentMenu:'選擇上級菜單',
      menuType:'菜單類型',
      catalog:'目錄',
      menu:'菜單',
      source:'資源',
      menuIcon:'菜單圖標',
      clickIcon:'點擊選擇圖標',
      routAddr:'路由地址',
      order:'顯示排序',
      cache:'是否緩存',
      menuStatus:'菜單狀態',
      mainIndex:'主類目',
      addMenu:'添加菜單',
      modifyMenu:'修改菜單',
      permissionUnNull:'權限標識不能為空',
      sureDeleteModel:'是否確認刪除名稱為{0}的數據項?',
      isApp:'是否App',
      belongSys:'所屬系統',
      belongType:'所屬類別',
      tip:{
        name:'請輸入菜單名稱',
        status:'菜單狀態',
        routAddr:'請輸入路由地址',
        componentPath:'請輸入組件路徑',
        permsMark:'請輸入權限標識',
        menuName:'菜單名稱不能為空',
        menuOrder:'菜單順序不能為空',
        rout:'路由地址不能為空',
        isApp:'請選擇是否App',
      }
    },
    dict:{
      name:'字典名稱',
      type:'字典類型',
      status:'狀態',
      dictStatus:'字典狀態',
      createTime:'創建時間',
      beginTime:'開始時間',
      endTime:'結束時間',
      code:'序號',
      isSys:'系統內置',
      remark:'備註',
      list:'列表',
      addDictType:'添加字典類型',
      modifyDictType:'修改字典類型',
      sureDeleteModel:'是否刪除字典名稱為{0}的數據項?',
      sureDelete:'是否刪除字典標籤為{0}的數據項?',
      sureExportAllDate:'是否導出所有類型數據項?',
      label:'字典標籤',
      value:'字典鍵值',
      sort:'字典排序',
      dataLabel:'數據標籤',
      valueLabel:'數據鍵值',
      showSort:'顯示排序',
      addDataDict:'添加數據字典',
      modifyDataDict:'修改數據字典',
      tip:{
        name:'請輸入字典名稱',
        type:'請輸入字典類型',
        remark:'請輸入備註',
        dictName:'字典名稱不能為空',
        dictType:'字典類型不能為空',
        label:'請輸入字典標籤',
        dataLabel:'請輸入數據標籤',
        valueLabel:'請輸入數據鍵值',
        labelUnNull:'數據標籤不能為空',
        vauleUnNull:'數據鍵值不能為空',
        orderUnNull:'數據順序不能為空'
      }
    },
    dept: {
      deptName: '部門名稱',
      parentId: '上級ID',
      orderNum: '排序',
      name:'機構名稱',
      status:'狀態',
      code:'機構代碼',
      orgLevel:'機構層級',
      costCode:'費用代碼',
      sort:'排序',
      createTime:'創建時間',
      parentOrg:'上級機構',
      contactsCode:'工號',
      contacts:'聯繫人',
      phone:'聯繫電話',
      email:'郵箱',
      addOrg:'添加機構',
      editOrg:'修改機構',
      sureDeleteModel:'是否確認刪除名稱為{0}的數據項?',
      tip:{
        name:'請輸入機構名稱',
        status:'機構狀態',
        parentOrg:'請選擇上級機構',
        code:'請輸入機構代碼',
        orgLevel:'請輸入機構層級',
        costCode:'請輸入費用代碼',
        contactsCode:'請輸入工號',
        contacts:'請輸入聯繫人',
        phone:'請輸入聯繫電話',
        email:'請輸入郵箱',
        parentId:'上級機構不能為空',
        orgName:'機構名稱不能為空',
        orgCode:'機構代碼不能為空',
        MenuSort:'菜單順序不能為空'
      }
    },
    config:{
      name:'參數名稱',
      key:'參數鍵名',
      isSys:'系統內置',
      createTime:'創建時間',
      beginDate:'開始日期',
      endDate:'結束日期',
      id:'序號',
      value:'參數鍵值',
      remark:'備註',
      addParameters:'添加參數',
      modifyParameters:'修改參數',
      sureDeleteModel:'是否刪除參數名稱為{0}的數據項?',
      tip:{
        name:'請輸入參數名稱',
        key:'請輸入參數鍵名',
        value:'請輸入參數鍵值',
        remark:'請輸入備註',
        nameDisNull:'參數名稱不能為空',
        keyDisNull:'參數鍵名不能為空',
        valueDisNull:'參數鍵值不能為空'
      }
    },
    systemLog: {
      username: '操作人',
      operation: '操作描述',
      createTime: '操作時間',
      time: '耗時',
      method: '操作方法',
      params: '方法參數',
      ip: 'IP',
      location: '操作地點'
    },
    loginLog: {
      username: '用戶名',
      loginUser:'登錄用戶',
      loginTime: '登錄時間',
      ip: 'IP',
      location: '登錄地點',
      system: '登錄系統',
      browser: '瀏覽器',
      loginIp:'登錄地址',
      stauts:'狀態',
      beginTime:'開始時間',
      endTime:'結束時間',
      clearAll:'清空',
      export:'導出',
      accessNumber:'序號',
      loginName:'登錄用戶',
      loginType:'登錄類型',
      loginAddr:'登錄地點',
      userAgent:'用戶代理',
      msg:'異常信息',
      loginDate:'登錄日期',
      sureDeleteDate:'是否確認刪除登錄時間為{0}的数据项',
      sureDeleteAllDate:'是否確認清空所有登錄日誌數據項',
      clearAllSuccess:'清空成功',
      sureExportAllDate:'是否確認導出所有操作日誌數據項',
      tip:{
        loginUser:'請輸入登錄用戶',
        loginStatus:'登錄狀態',
        stauts:'請選擇狀態',
        loginIp:'請輸入登錄地址'
      }
    },
    operLog:{
      title:'標題',
      operName:'操作人員',
      status:'狀態',
      operation:'操作時間',
      startTime:'開始時間',
      endTime:'結束時間',
      clear:'清空',
      logCode:'序號',
      method:'請求方式',
      operIp:'IP地址',
      operAddr:'操作地點',
      client:'客戶端',
      executeTime:'請求時間',
      operationStatus:'操作狀態',
      operationDate:'操作日期',
      detail:'詳細',
      operationDetail:'操作日誌詳細',
      operationModel:'操作模塊',
      loginInfo:'登錄信息',
      operUrl:'請求地址',
      userAgent:'用戶代理',
      operParam:'請求參數',
      ok:'正常',
      fail:'失敗',
      operationTime:'操作時間',
      errorMsg:'異常信息',
      close:'關 閉',
      sureDeleteDate:'是否確認刪除日誌操作時間為{0}的數據項',
      sureDeleteAllDate:'是否確認清空所有操作日誌數據項',
      clearAllSuccess:'清空成功',
      sureExportAllDate:'是否確認導出所有操作日誌數據項',
      tip:{
        title:'請輸入標題',
        operName:'請輸入操作人員',
        status:'操作狀態',
      }
    },
    gen: {
      config: {
        author: '作者名稱',
        basePackage: '基礎包名',
        entityPackage: 'entity包名',
        mapperPackage: 'mapper包名',
        mapperXmlPackage: 'mapperXml包名',
        servicePackage: 'service包名',
        serviceImplPackage: 'serviceImpl包名',
        controllerPackage: 'controller包名',
        isTrim: '是否去除表前綴',
        trimValue: '表前綴'
      },
      generate: {
        tableName: '表名',
        remark: '備註',
        dataRows: '數據量（行）',
        createTime: '創建時間',
        updateTime: '更新時間'
      }
    },
    activity:{
      modelName:'模型名稱',
      inputModelName:'請輸入模型名稱',
      modelType:'模型分類',
      inputModelType:'請選擇模型分類',
      modelId:'模型id',
      modelKey:'模型key',
      inputModelKey:'請輸入模型key',
      modelVersion:'版本號',
      lastUpdateTime:'最後更新時間',
      modelDesign: '模型設計',
      deploy: '部署',
      modelDesc: '模型描述',
      inputModelDesc: '請輸入模型描述',
      modelNameShouldNotBeEmpty: '模型名稱不能為空',
      modelTypeShouldNotBeEmpty: '模型分類不能為空',
      modelKeyShouldNotBeEmpty: '模型標籤不能為空',
      modelDescShouldNotBeEmpty: '模型描述不能為空',
      addModel: '添加模型',
      sureDeployModel: '是否確認部署模型',
      sureDeleteModel: '是否確認刪除模型編號為"{0}"的數據項?',
      deploySucess: '模型部署成功',

      processName: '流程名稱',
      viewProcess: '查看流程',
      inputProcessName: '請輸入流程名稱',
      processType: '流程分類',
      inputProcessType: '請選擇流程分類',
      processId: '流程id',
      processKey: '流程標籤',
      processVersion: '版本號',
      processStatus: '表單狀態',
      hangUp: '掛起',
      activity: '活動',
      deployTime: '部署時間',
      deployPic: '流程圖',
      activation: '激活',
      export: '導出',
      convertToModel: '轉換為模型',
      sureChangeProcess: '確認要改變流程',
      sureChangeProcessStatus: '的狀態嗎?',
      sureDeleteProcess: '確認要刪除流程編號為',
      sureDeleteProcessStatus: '的數據項嗎?',

      taskName: '節點名稱',
      inputTaskName: '請輸入任務名稱',
      toDeal: '待辦',
      dealed: '已辦',
      taskId: '節點名稱',
      serialno: '表單編號',
      makerNo: '申請人工號',
      makerName: '申請人姓名',
      pdName: '表單名稱',
      taskKey: '節點key',
      submissionTime: '簽核時間',
      submissionTimes: '填單時間',
      approval: '審批',
      detail: '詳情',
      rejection: '駁回',
      pass: '通過',
      bathRejection: '批量駁回',
      batchPass: '批量通過',
      cancle: '取消申請',
      reSubmit: '重新提交',
      operIp: '審核IP',
      auditStatus: '審核狀態',
      approvalOpinions: '審批意見',
      flowTracing: '流程追蹤',
      taskApproval: '任務審批',
      taskIds: '任務編號',
      leaveType: '請假類型',
      pleaseDummy: '請假人',
      pleaseTime: '請假時間',
      reasonsForLeave: '請假事由',
      approvedBy: '審批人',
      approvalTime: '審批時間',
      inputApprovalOpinions: '請輸入審批意見',
      taskMap: '任務圖',
      uploadTip: '请上传後綴格式為.bpmn20.xml的文件',
      importActiviti: '導入流程',
      deptName: '單位名稱',
      signImage: '簽名',
      instructions: '批示',
    },
    qhUserSelect:{
      account: '賬號',
      accountName: '主管名稱',
      remarks: '備注',
      title: '簽核人員選擇',
      serialNumber: '序號',
      multiple: '多選',
      single: '單選'
    },
    eximport: {
      field1: '字段1',
      field2: '字段2',
      field3: '字段3',
      createTime: '導入時間'
    },
    infoConfig:{
      name:'名稱',
      type:'類型',
      status:'狀態',
      createTime:'創建時間',
      beginDate:'開始日期',
      endDate:'結束日期',
      code:'序號',
      templateCode:'模板',
      host:'地址',
      signName:'簽名',
      modelCode:'模板Code',
      fromBy:'發送人',
      port:'端口',
      user:'用戶名',
      pass:'密碼',
      otherInfo:'其它信息',
      remark:'備註',
      addTip:'添加消息配置',
      modifyTip:'修改消息配置',
      sureDeleteModel:'是否刪除消息配置編號為{0}的數據項?',
      exportConfirm:'是否導出所有消息配置數據項?',
      tip:{
        name:'請輸入名稱',
        type:'請選擇類型',
        status:'請選擇狀態',
        accessKeyId:'請輸入keyId',
        accessKeySecret:'請輸入keySecret',
        signName:'請輸入簽名',
        modelCode:'請輸入模板Code',
        fromBy:'請輸入發送人',
        host:'請輸入地址',
        port:'請輸入端口',
        user:'請輸入用戶名',
        pass:'請輸入密碼',
        otherInfo:'請輸入內容',
        remark:'請輸入備註',
        nameUnNull:'名稱不能為空',
        typeUnNull:'類型不能為空'
      }
    },
    infoContent:{
      title:'消息標題',
      type:'消息類型',
      createTime:'創建時間',
      beginDate:'開始日期',
      endDate:'結束日期',
      msgCode:'序號',
      content:'消息內容',
      extend:'接收人',
      noticeType:'通知',
      extendPerson:'接收者',
      addMsgInfo:'添加消息信息',
      modifyMsgInfo:'修改消息信息',
      sureDeleteMsg:'是否確定刪除消息內容編號為{0}的數據項?',
      sureExportAllMsg:'是否確認導出所有消息內容數據項?',
      tip:{
        title:'請輸入消息標題',
        type:'請選擇消息類型',
        content:'請輸入消息內容',
        extendPerson:'請選擇接收者',
        titleUnNull:'標題不能為空',
        typeUnNull:'類型不能為空',
        noticUnNull:'通知不能為空',
        contentUnNull:'內容不能為空',
        acceptUnNull:'接收者不能為空',
      }
    },
    infoTemplate:{
      name:'模板名稱',
      type:'模板類型',
      serialNumber:'序號',
      status:'狀態',
      createTime:'創建時間',
      beginDate:'開始日期',
      endDate:'結束日期',
      content:'模板內容',
      tplKey:'模板鍵值',
      tplContent:'模板內容',
      processKey:'流程Key',
      mailType:'郵件類型',
      remarks:'備註',
      addMsgTemplate:'添加消息模板',
      modifyMsgTemplate:'修改消息模板',
      sureDeleteTemplate:'是否確認刪除消息模板名稱為{0}的數據項?',
      sureExportTemplateInfo:'是否確認導出所有消息模板數據項?',
      mailAcceptPerson:'郵件接收人姓名',
      applyName:'申請單名稱',
      applyId:'申請單單號',
      signUrl:'審核跳轉鏈接',
      mailMain:'郵件主旨',
      tip:{
        name:'請輸入模板名稱',
        type:'請選擇模板類型',
        status:'請選擇狀態',
        tplKey:'請輸入模板鍵值',
        processKey:'請輸入流程Key',
        mailType:'請輸入郵件類型',
        tplContent:'請輸入模板內容',
        remarks:'請輸入備註',
        nameUnNull:'名稱不能為空',
        typeUnNull:'類型不能為空',
        keyUnNull:'鍵值不能為空',
        contentUnNull:'內容不能不為',
        processKeyUnNull:'流程Key不能為空',
        mailTypeKeyUnNull:'郵件類型不能為空',
        mailMain:'請輸入郵件主旨',
        messageUnNull:'郵件主旨不可為空'
      }
    },
    infoPush:{
      acceptPerson:'接收人',
      isRead:'是否閱讀',
      createTime:'創建時間',
      beginDate:'開始日期',
      endDate:'結束日期',
      code:'序號',
      msgCode:'消息編號',
      receiveId:'接收人',
      readTime:'閱讀時間',
      addMsgPush:'添加消息推送',
      modifyMsgPush:'修改消息推送',
      sureDeleteMsg:'是否確認消息推送編號為{0}的數據項?',
      sureExportMsg:'是否確認導出所有消息推送數據項?',
      msgTielt:'消息標題',
      tip:{
         acceptPerson:'請輸入接收人',
         isRead:'請選擇是否閱讀',
         receiveId:'請輸入接收人',
         readTime:'請選擇閱讀時間',
         msgCodeUnNull:'消息編號不能為空',
         acceptPersonUnNull:'接收人不能為空',
         isReadUnNull:'是否閱讀不能為空',
      }
    },
    job:{
      jobName:'任務名稱',
      jobType:'任務類型',
      jobStatus:'任務狀態',
      startJob:'啟動全部任務',
      stopJob:'暫停全部任務',
      restJob:'重置全部任務',
      jobLog:'日誌',
      jobCode:'序號',
      jobGroup:'任務組名',
      executePath:'執行路徑/文件.方法',
      paramsValue:'執行參數',
      cronExpression:'cron表達式',
      previousTime:'上次執行時間',
      nextTime:'下次執行時間',
      runOne:'運行一次',
      runJob:'啟動',
      suspendJob:'暫停',
      detail:'詳細',
      delete:'刪除',
      pleaseSelect:'請選擇',
      misfirePolicy:'錯誤策略',
      immediatelyDo:'立即執行',
      doOne:'執行一次',
      giveUpDo:'放棄執行',
      jarExp:'JAR包調用示例',
      restExp:'Rest調用示例',
      beanExp:'BeanExp調用示例',
      classExp:'Class類調用示例',
      params:'參數說明：支持字符串，布爾類型，長整型，浮點型，整型，指令等',
      remark:'備註',
      jobDetail:'任務詳情',
      executionStrategy:'執行策略',
      createTime:'創建時間',
      defaultPolicy:'默認策略',
      close:'關 閉',
      sureDoOnceJob:'確定要運行一次{0}任務嗎?',
      sureStartJob:'确认要启动{0}任务吗?',
      doSuccess:'執行成功',
      sureStopJob:'確定要暫停{0}任務嗎?',
      addJob:'添加任務',
      modifyJob:'修改任務',
      sureStartdeleteJob:'確認刪除定時任務名稱為{0}的數據項?',
      sureStartAllJob:'確認要啟動全部任務嗎?',
      sureStopAllJob:'確認要暫停全部任務嗎?',
      sureResetAllJob:'確認要重置全部任務嗎?',
      sureExportAllJob:'確認要導出所有定時任務數據項?',
      jobLogStatus:'執行狀態',
      doTime:'執行時間',
      beginTime:'開始時間',
      endTime:'結束時間',
      jobMessage:'日誌信息',
      doWithDetail:'調度日誌詳細',
      jobSerialNo:'日誌序號',
      taskGroup:'任務分組',
      callMethod:'調用方法',
      success:'成功',
      fail:'失敗',
      errorInfo:'異常信息',
      sureDeleteDate:'確認要刪除調度日誌執行時間為{0}的數據項?',
      sureDeleteAllDate:'是否確認清空所有調度日誌數據項?',
      deleteAllSuccess:'清空成功',
      sureExportAllDate:'是否確認導出所有調度日誌數據項?',
      unAbleDelete:'運行中任務不可刪除',
      unAbleModify:'運行中任務不可修改',
      runOnebeforeRun:'運行前請先啟動任務',
      tip:{
        jobName:'請輸入任務名稱',
        jobType:'請選擇任務類型',
        jobStatus:'請選擇任務狀態',
        jobGroup:'請輸入任務組名',
        executePath:'請輸入執行路徑/文件.方法',
        paramsValue:'請輸入執行參數',
        cronExpression:'請輸入cron執行表達式',
        remark:'請輸入備註',
        jobNameUnNull:'任務名稱不能為空',
        jobTypeUnNull:'任務類型不能為空',
        jobGroupUnNull:'任務組名不能為空',
        jobPathUnNull:'執行路徑/文件.方法不能為空',
        cronUnNull:'cron執行表達式不能為空',
        jobLogStatus:'請選擇執行狀態',
      }
    },
    refresh: '刷新',
    operation: '操作',
    search: '搜索',
    reset: '重置',
    more: '更多操作',
    add: '添加',
    edit: '修改',
    design: '設計',
    submit: '提交',
    export: '導出',
    exportPreview: '導出預覽',
    import: '導入',
    templateDownload: '模板下載',
    delete: '刪除',
    detail: '詳情',
    print: '列印',
    resetPassword: '密碼重置',
    openInNewPage: '新頁面打開',
    createTime: '創建時間',
    updateTime: '修改時間',
    systemData: '是否內置'
  },
  tagsView: {
    refresh: '刷新當前',
    close: '關閉當前',
    closeOthers: '關閉其它',
    closeAll: '關閉所有'
  },
  settings: {
    title: '系統布局配置',
    theme: '主題色',
    tagsView: '開啟 Tags-View',
    fixedHeader: '固定 Header',
    sidebarLogo: '側邊欄 Logo'
  },
  system: {
    title: 'zuihou-admin'
  },
  tips: {
    systemError: '系統維護中，請稍微再試~',
    usernameShouldNotBeEmpty: '用戶名不能為空',
    nicknameShouldNotBeEmpty: '用戶暱稱不能為空',
    deptIdShouldNotBeEmpty: '歸屬機構名稱不能為空',
    emailShouldNotBeEmpty: '用戶郵箱不能為空',
    emailShouldBeRight: '請輸入正確的郵箱地址',
    phoneShouldNotBeEmpty: '手機號碼不能為空',
    phoneShouldBeRight: '請輸入正確的手機號碼',
    passwordShouldNotBeEmpty: '用戶密碼不能為空',
    codeNotBeEmpty: '驗證碼不能為空',
    switchLanguageSuccess: '切換語言成功',
    loginSuccess: '登錄成功',
    loginFail: '登錄失敗',
    defaultPassword: '用戶的默認密碼為1234qwer',
    getDataFail: '獲取數據失敗',
    createSuccess: '新增成功',
    updateSuccess: '修改成功',
    deleteSuccess: '刪除成功',
    genCodeSuccess: '生成成功',
    submitSuccess: '提交成功',
    importSuccess: '導入成功',
    deleteConfirm: '是否確認刪除 {0} 編號為 {1}的數據項?',
    deleteConfirm1: '是否確認刪除 {0} key為 {1}的數據項?',
    exportConfirm: '是否確認導出所有 {0} 的數據項?',
    submitConfirm: '是否確認提交編號為 {0} 的數據項?',
    systemData: '系統數據，不能操作',
    noDataSelected: '請先選擇需要操作的數據',
    confirmDelete: '選中數據將被永久刪除, 是否繼續？',
    confirmDeleteCache: '是否立即清除用戶權限緩存？',
    containCurrentUser: '包含當前登錄用戶，操作已取消',
    neverLogin: '從未登錄過系統',
    nothing: '這家夥很懶，什麽都沒留下',
    topId: '值為0時表示頂級節點',
    choose: '已選擇：',
    chooseNothing: '尚未選擇任何圖標',
    onlyChooseOne: '只能選擇壹個節點作為父節點',
    noNodeSelected: '請先選擇節點',
    confirmDeleteNode: '選中節點及其子結點將被永久刪除, 是否繼續？',
    iframeGrant: '用戶名：FEBS 密碼：123456',
    notEqual: '兩次輸入不壹致',
    oldPasswordIncorrect: '原密碼不正確',
    uploadSuccess: '上傳成功',
    reUploadSuccess: '補傳成功',
    uploadFailed: '上傳失敗',
    reUploadSuccess: '補傳成功',
    onlySupportXlsx: '只支持Xlsx類型文件',
    updating: '修改中',
    updateFailed: '修改失敗',
    noPermission: '無權限',
    confirmRestPassword: '確定重置所選用戶密碼？',
    resetPasswordSuccess: '所選用戶密碼重置已被重置為1234qwer',
    getCodeImageFailed: '獲取圖形驗證碼失敗',
    tooManyRequest: '獲取驗證碼過於頻繁，請1分鐘後再試',
    clientOriginSecret: '該客戶端原始密碼為：',
    warm:'警告',
    operationSuccessful:'操作成功',
    signFormNoSelectedData: '請選擇簽核人員',
    addOne: '添加一位',
    removeOne: '是否刪除工號為 {0} 的簽核人員嗎?',
    fillInCompletely: '請填寫完後再添加',
    theChildTableHasSelected: '該子表控件已選擇',
  },
  rules: {
    require: '不能為空',
    range2to10: '長度在 2 到 10 個字符',
    range3to10: '長度在 3 到 10 個字符',
    range3to20: '長度在 3 到 20 個字符',
    range4to10: '長度在 4 到 10 個字符',
    range6to20: '長度在 6 到 20 個字符',
    email: '請輸入正確的郵箱地址',
    mobile: '請輸入合法的手機號',
    accountExist: '該賬號已存在',
    usernameExist: '該用戶名已存在',
    clientIdExist: '該Client ID已存在',
    roleNameExist: '該角色名稱已存在',
    noMoreThan10: '長度不能超過10個字符',
    noMoreThan11: '長度不能超過11個字符',
    noMoreThan20: '長度不能超過20個字符',
    noMoreThan50: '長度不能超過50個字符',
    noMoreThan100: '長度不能超過100個字符',
    invalidInteger: '請輸入大於零的整數',
    invalidURL: '不是有效的URL'
  },
  common: {
    system: '開發&運營後臺',
    desc: {
      a: '基於Spring Boot 2.1.2 & Spring Cloud Greenwich.RELEASE',
      b: '使用Jwt自定義統壹認證',
      c: '企業級設計和配置，拒絕demo項目',
      d: '前後端分離架構，提高軟件開發效率',
      e: '集成多種監控，為微服務保駕護航',
      f: '提供詳細的文檔，手把手教妳從零搭建到部署'
    },
    view: '查看',
    viewDetail: '查看詳情',
    refreshOk:'刷新成功',
    pullUpLoad:'上拉加載更多',
    noMore:'沒有更多數據',
    tips: '提示',
    clear: '清除',
    sucess: '成功',
    confirm: '確 定',
    cleanQhUser: '置空該節點主管',
    confirmTrim: '確定',
    select: '請選擇',
    selected: '選擇',
    cancel: '取 消',
    test:'測試數據源',
    cancelTrim: '取消',
    add: '新增',
    serialNumber: '編號',
    save: '保存',
    close:'關 閉',
    return: '返 回',
    edit: '修改',
    yes: '是',
    no: '否',
    print :'打印',
    sex: {
      male: '男',
      female: '女',
      secret: '保密'
    },
    status: {
      valid: '啟用',
      invalid: '停用'
    },
    menu: {
      menu: '菜單',
      button: '按鈕'
    },
    tab: {
      common: '通用類',
      directivity: '指向性',
      solid: '填充類',
      food: '食品類'
    },
    aboutMe: '關於我',
    changeAvatar: '更換頭像',
    lastLoginTime: '上次登錄時間',
    goodMorning: '早上好',
    goodAfternoon: '下午好',
    goodEvening: '晚上好',
    randomMessage: {
      a: '喝杯咖啡休息下吧☕',
      b: '要不要和朋友打局LOL',
      c: '今天又寫了幾個Bug🐞呢',
      d: '今天在群裏吹水了嗎',
      e: '今天吃了什麽好吃的呢',
      f: '今天您微笑了嗎😊',
      g: '今天幫別人解決問題了嗎',
      h: '準備吃些什麽呢',
      i: '周末要不要去看電影？'
    },
    notNull:'不能為空',
    docDetails: '了解更多',
    allProject: '所有項目',
    noDept: '暫無部門',
    noRole: '暫無角色',
    noWorkDescribe: '這家夥很懶，什麽也沒留下~',
    firstLogin: '第壹次登錄系統',
    todayIp: '今日IP',
    todayVisit: '今日訪問',
    TotalVisit: '總訪問量',
    you: '您',
    total: '總數',
    visitTitle: '近十天系統訪問記錄',
    timeline: '登錄時間',
    account: '賬號信息',
    password: '個人密碼',
    importResult: '導入結果',
    hthz: '後田花子',
    al: '阿裏系',
    lm: '臉萌',
    ctc: '點擊選擇',
    pleaseInputUrl: '請輸入URL',
    pleaseInputContent: '請輸入內容',
    bind: '綁定',
    unbind: '解綁',
    confirmUnbind: '確定解綁該第三方賬號？',
    unbindSuccess: '解綁成功',
    bindSuccess: '綁定成功',
    bindLogin: '綁定並登錄',
    signLogin: '註冊並登錄',
    current: '當前',
    socialAccount: '賬號',
    socialTips: '尚未綁定任何系統賬戶，您可以綁定系統賬戶或者註冊壹個新的賬戶並綁定。',
    hideSearch:'隱藏搜索',
    showSearch:'顯示搜索',
    placeholderDefault:'請輸入',
    placeholderSelectDefault:'請選擇',
    file:'將檔案拖到此處，或',
    upload:'點擊上傳',
    fileUpdate:'是否更新已經存在的用戶數據',
    download:'下載範本',
    fileTip:'提示：僅允許導入“xls”或“xlsx”格式檔案！',
    sureTo:'確認要',
    users:'用户吗?',
    fileUpdateAll:'是否更新已經存在的數據',
    beginDate: '開始日期',
    endDate: '結束日期',
    beginTime: '開始时间',
    endTime: '結束时间',
    rangeSeparator: '至',
    sumText: '合計'
  },
  ljsq:{
    default: {
      beginDate: '開始日期',
      endDate: '結束日期',
      createDate: '創建日期',
      selectOption: '請選擇字典生成',
    },
    label: {
      id: '编号',
      empNo: '工號',
      empName: '姓名',
      ljTyp: '禮金類型',
      aplyDate: '申請日期',
      memo: '備註',
      createBy: '创建人',
      createTime: '创建时间',
      deptId: '机构ID',
      updateBy: '更新人',
      updateTime: '更新时间',
      remarks: '备注',
      delFlag: '删除标识',
    },
  },
  design_data_source:{
  default: {
    beginDate: '開始日期',
    endDate: '結束日期',
    createDate: '創建日期',
    selectOption: '请选择字典生成',
    functionName:'數據庫',
    testConnection: '測試連接',
    testOK: '連接成功',
  },
  label: {
      id: '编号',
      createBy: '创建人',
      createTime: '创建时间',
      updateBy: '更新人',
      updateTime: '更新时间',
      remarks: '备注',
      delFlag: '删除标识',
      deptId: '机构ID',
      dbName: '數據庫名稱',
      driverClassName: '數據驅動',
      url: 'JdbcUrl地址',
      username: '用戶名',
      password: '密碼',
      alias: '別名',
      appName: '應用名稱',
      dbGroup: '數據源分組',
  }
},
design_table:{
  default: {
    beginDate: '開始日期',
    endDate: '結束日期',
    createDate: '創建日期',
    selectOption: '请选择字典生成',
    functionName:'設計表',
    newTabel:'新增表',
    build:'生成',
    deploy:'配置表',
    delete:'刪除配置',
    deleteTableOld:'刪除原表',
    nickName:'數據表暱稱',
    phone:'手機號碼',
    email:'郵箱',
    datatableName:'數據表名稱',
    status:'狀態',
    remarks:'備註',
    deleteConfirm: '是否確認刪除數據庫表為 {0}的數據項?',
    deleteWaring: '是否確認刪除數據庫表 {0}？刪除后將無法恢復！',
    selectDbPlease:'請選擇數據庫',
    genInterface: '生成此表接口',

  },
  label: {
      id: '編號',
      tableName: '表名',
      tableComment: '注釋',
      className: '實體類名稱',
      tplCategory: '使用的模板',
      packageName: '生成包路徑',
      moduleName: '生成模塊名',
      businessName: '生成業務名',
      functionName: '生成功能名',
      functionAuthor: '生成功能作者',
      cols: '分欄',
      options: '其它生成選項',
      menuId: '父級菜單',
      genPath: '生成路徑',
      createBy: '創建者创建者',
      createTime: '創建時間',
      updateBy: '更新者',
      updateTime: '更新時間',
      remarks: '備註',
      delFlag: '0-正常，1-刪除',
      deptId: '機構ID',
  }
},
design_gen_editTable:{
  default: {
    basic: '基本信息',
    cloum: '字段信息',
    operation: '操作',
    submit: '生成表結構',
    save: '保存配置信息',
    return: '返回',
    input: '文本框',
    textarea: '文本域',
    select: '下拉框',
    radio: '單選框',
    checkbox: '複選框',
    cascader:'下拉級聯',
    number:'數字框',
    password:'密碼框',
    switch:'開關按鈕',
    slider:'進度條',
    time:'時間控件',
    timeRange:'時間範圍',
    date:'日期控件',
    datetime:'日期時間控件',
    dateRange: '日期範圍',
    rate:'評分',
    color:'顏色',
    genInfo: '生成信息',
    dept: '部門控件',
    user: '用戶控件',
    fileUpload: '文件上傳控件',
    formValidateResult: '表單校驗未通過，請重新檢查提交內容',
    deleteConfirm: '是否確認刪除字段列名為 {0}的數據項?',

  },
  label: {
      id: '編號',
      columnName: '字段列名',
      columnComment: '字段描述',
      columnType: '物理類型',
      javaType: 'Java類型',
      defValue: '默認值',
      isPk: '主鍵',
      isAdd: '插入',
      isEdit: '編輯',
      isList: '列表',
      isQuery: '查詢',
      signOrder:'簽核順序',
      queryType: '查詢方式',
      isRequired: '必填',
      htmlType: '顯示類型',
      dictType: '字典類型',
      signOrderTip:'請將順序設置於簽核節點工號之上',
      isExcel:'excel',
      isBatch:'批量'
  }
},
design_gen_genInfoForm:{
  default: {
    otherInfo: '其他信息',
    genPathPlaceholder: '填写项目路径，如D://entfrm-boot',
  },
  selectOption: {
    tplCategory1:'单表（增刪改查）',
    tplCategory2:'樹表（增删改查）',
    cols1: '一欄',
    cols2: '二欄',
    cols3: '三欄',
    cols4: '四欄',
  },
  tooltip: {
    packageName: '生成在哪個ava包下，；例如 com.entfrm.biz.system',
    moduleName: '可理解為子系統名，例如 system',
    businessName: '可理解為功能英文名，例如 user',
    functionName: '用作類描述，例如 用户',
    genPath: '只填写项目路径即可',
    treeId: '树显示的编号字段名， 如：id',
    treeParentId: '树显示的父编号字段名， 如：parent_Id',
    treeName: '树节点的显示名称字段名， 如：name',
  },
  label: {
    tplCategory: '生成模板',
    cols: '生成分栏',
    menuId: '父级菜单',
    packageName: '生成包路径',
    moduleName: '生成模块名',
    businessName: '生成业务名',
    functionName: '生成功能名',
    genPath: '生成文件路径',
    treeId: '树编号字段',
    treeParentId: '树父编号字段',
    treeName: '树名称字段',

  }
},
design_gen_basicInfoForm:{
  pattern: {
    tableName: '必須以2個字母開頭'
  },
  default: {
    otherInfo: '其他信息',
    genPathPlaceholder: '填写项目路径，如D://entfrm-boot',
    add: '新增字段',
  },
  selectOption: {
    tplCategory1:'单表（增刪改查）',
    tplCategory2:'樹表（增删改查）',
    cols1: '一欄',
    cols2: '二欄',
    cols3: '三欄',
    cols4: '四欄',
  },
  tooltip: {
    tableName: '请使用英文单词命名，且单词之间用下划线 ‘_’ 分隔,例如system_user',
    packageName: '生成在哪個ava包下，；例如 com.entfrm.biz.system',
    moduleName: '可理解為子系統名，例如 system',
    businessName: '可理解為功能英文名，例如 user',
    functionName: '用作類描述，例如 用户',
    genPath: '只填写项目路径即可',
    treeId: '树显示的编号字段名， 如：id',
    treeParentId: '树显示的父编号字段名， 如：parent_Id',
    treeName: '树节点的显示名称字段名， 如：name',
  },
  label: {
    parentTableId: '父表',
    tableName: '表名稱',
    tableComment: '表描述',
    className: '實體類名稱',
    functionAuthor: '作者',
    remarks: '備註',
  }
},
  wf_config:{
    default: {
      beginDate: '開始日期',
      endDate: '結束日期',
      createDate: '創建日期',
      selectOption: '请选择字典生成',
      functionName:'流程信息配置',
    },
    label: {
      workflowName: '流程名稱',
      auditAction: '表單簽核處理方法',
      modAction: '表單修改處理方法',
      saveAction: '表單臨時保存方法',
      detailAction: '表單詳情方法',
      tableName: '表名',
      dynfield02: '預留欄位2',
      dynfield03: '預留欄位3',
      id: '主鍵',
      createBy: '創建人',
      createTime: '創建時間',
      updateBy: '更新者',
      updateTime: '更新時間',
      delFlag: '刪除標識',
      workflowKey: '流程啟動key',
      version: '版本',
    }
  },
  out_apply:{
    default: {
      beginDate: '開始日期',
      endDate: '結束日期',
      createDate: '創建日期',
      selectOption: '请选择字典生成',
      functionName:'外出申請',
    },
    label: {
        id: '编号',
        createBy: '创建人',
        createTime: '创建时间',
        updateBy: '更新人',
        updateTime: '更新时间',
        remarks: '备注',
        delFlag: '删除标识',
        deptId: '机构ID',
        outPlace: '外出地點',
        outTime: '外出時間',
        outType: '外出類型',
        reason: '原因',
    }
  },
  entfrm_child_table_form:{
    addColumn: '添加列',
    multiplicableCols: '可以添加的列',
    serialNumber: '序號',
    columnName: '列名',
    columnComment: '描述'

  }

}
