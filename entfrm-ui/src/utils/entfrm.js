import axios from "axios";
import {getAccessToken, getZltAccessToken} from "@/utils/auth";
import {resolveBlob} from "@/utils/zipdownload";
/**
 * 通用js方法封装处理
 * Copyright (c) 2019 entfrm
 */

const baseURL = process.env.VUE_APP_BASE_API

const { body } = document
const WIDTH = 992 // refer to Bootstrap's responsive design
//是否開啟微服務模式
const isMicro = false


// 日期格式化
export function parseTime(time, pattern) {
	if (arguments.length === 0 || !time) {
		return null
	}
	let format;
	if(time.length > 10){
		format = pattern || '{y}-{m}-{d} {h}:{i}:{s}'
	}else{
		format = pattern || '{y}-{m}-{d}'
	}
	let date
	if (typeof time === 'object') {
		date = time
	} else {
		if ((typeof time === 'string') && (/^[0-9]+$/.test(time))) {
			time = parseInt(time)
		}
		if ((typeof time === 'number') && (time.toString().length === 10)) {
			time = time * 1000
		}
		date = new Date(time)
	}
	const formatObj = {
		y: date.getFullYear(),
		m: date.getMonth() + 1,
		d: date.getDate(),
		h: date.getHours(),
		i: date.getMinutes(),
		s: date.getSeconds(),
		a: date.getDay()
	}

  if(isNaN(formatObj["a"]))
    return time
	const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
		let value = formatObj[key]
		// Note: getDay() returns 0 on Sunday
		if (key === 'a') { return ['日', '一', '二', '三', '四', '五', '六'][value] }
		if (result.length > 0 && value < 10) {
			value = '0' + value
		}
		return value || 0
	})
	return time_str
}

// 表单重置
export function resetForm(refName) {
	if (this.$refs[refName]) {
		this.$refs[refName].resetFields();
	}
}

// 添加日期范围
export function addDateRange(params, dateRange) {
	var search = params;
	search.beginTime = "";
	search.endTime = "";
	if (null != dateRange && '' != dateRange) {
		search.beginTime = this.dateRange[0];
		search.endTime = this.dateRange[1];
	}
	return search;
}
export function addDateSignRange(params, dateSignRange) {
  var search = params;
  search.beginSignTime = "";
  search.endSignTime = "";
  if (null != dateSignRange && '' != dateSignRange) {
    search.beginSignTime = this.dateSignRange[0];
    search.endSignTime = this.dateSignRange[1];
  }
  return search;
}

// 回显数据字典
export function selectDictLabel(datas, value) {
	var actions = [];
	Object.keys(datas).map((key) => {
		if (datas[key].value == ('' + value)) {
			actions.push(datas[key].label);
			return false;
		}
	})
	return actions.join('');
}

// 回显数据字典
export function selectDictLabels(datas, values) {
	var actions = [];
	if(values != undefined && values != null){
		let valueArray = JSON.parse(values)
		if(valueArray instanceof Array){
			valueArray.forEach(value => {
				Object.keys(datas).map((key) => {
					if (datas[key].value == ('' + value)) {
						actions.push(datas[key].label);
					}
				})
			})
			return actions.join(',');
		}
	}
	return values
}
export function selectDictLabelss(datas, values) {
  var actions = [];
  if(values != undefined && values != null){
    let valueArray = values
    if(valueArray instanceof Array){
      valueArray.forEach(value => {
        Object.keys(datas).map((key) => {
          if (datas[key].value == ('' + value)) {
            actions.push(datas[key].label);
          }
        })
      })
      return actions.join(',');
    }
  }
  return values
}

    // 回显數據名
export function selectDataLabel(datas, value,valueName,labelName) {
	var actions = [];
	Object.keys(datas).map((key) => {
		if (datas[key][valueName] == ('' + value)) {
			actions.push(datas[key][labelName]);
			return false;
		}
	})
	return actions.join('');
	}
// 通用下载方法
export function download(fileName) {
	window.location.href = baseURL + "/common/download?fileName=" + encodeURI(fileName) + "&delete=" + true;
}
export function previewFile(name) {//唯一表示uuid
  axios({
    method: 'get',
    url: process.env.VUE_APP_BASE_API + "/system/fileInfo/download?fileName=" + encodeURI(name) + "&delete=" + false,
    responseType: 'blob',
    // headers: {Authorization: "Bearer " + getZltAccessToken() ,token:"Bearer "+getAccessToken()},
    headers: getHeader(),
  }).then(res => {
    resolveBlob(res, "")
  })
}
export function previewFileOos(name) {//唯一表示uuid
  axios({
    method: 'get',
    url: process.env.VUE_APP_BASE_API + "/system/fileInfo/downloadWithOos?fileName=" + encodeURI(name) + "&delete=" + false,
    responseType: 'blob',
    // headers: {Authorization: "Bearer " + getZltAccessToken() ,token:"Bearer "+getAccessToken()},
    headers: getHeader(),
  }).then(res => {
    resolveBlob(res, "")
  })
}
export function showSignatureImgWithEmpNo(empNo) {//唯一表示uuid
  return axios({
    method: 'get',
    url: process.env.VUE_APP_BASE_API + "/esignSystem/showSignatureImgWithEmpNo?empNo=" + encodeURI(empNo),
    responseType: 'blob',
    // headers: {Authorization: "Bearer " + getZltAccessToken() ,token:"Bearer "+getAccessToken()},
    headers: getHeader(),
  })
}
// 地址转换
export function newPath(path) {
  return baseURL + path;
}

// 字符串格式化(%s )
export function sprintf(str) {
	var args = arguments, flag = true, i = 1;
	str = str.replace(/%s/g, function () {
		var arg = args[i++];
		if (typeof arg === 'undefined') {
			flag = false;
			return '';
		}
		return arg;
	});
	return flag ? str : '';
}

// 转换字符串，undefined,null等转化为""
export function praseStrEmpty(str) {
    if (!str || str == "undefined" || str == "null") {
        return "0";
    }
    return str;
}

/**
 * 构造树型结构数据
 * @param {*} data 数据源
 * @param {*} id id字段 默认 'id'
 * @param {*} parentId 父节点字段 默认 'parentId'
 * @param {*} children 孩子节点字段 默认 'children'
 * @param {*} rootId 根Id 默认 0
 */
export function handleTree(data, id, parentId, children, rootId) {
  id = id || 'id'
  parentId = parentId || 'parentId'
  children = children || 'children'
  rootId = rootId || 0
  id = id || 'id'
  var rootIds = new Array();
  //对源数据深度克隆
  const cloneData = JSON.parse(JSON.stringify(data))
  //循環所有項 保存所有id
  cloneData.forEach(item => rootIds.push(item[id]));
  cloneData.forEach(father => {
    //循環所有項 將可以找到父節點的 id刪除
    cloneData.forEach(child => {
      if(father[parentId] === child[id]){
        if(rootIds.indexOf(father[id])>=0){
          rootIds.splice(rootIds.indexOf(father[id]),1);
        }
      }
    })
  });
  //循环所有项
  const treeData =  cloneData.filter(father => {
    let branchArr = cloneData.filter(child => {
      //返回每一项的子级数组
      return father[id] === child[parentId]
    });

    branchArr.length > 0 ? father.children = branchArr : '';
    //返回第一层
    var ifReturn = false;
    rootIds.forEach(rootId => {
      if(father[id] === rootId){
        ifReturn = true;
      }
    })
    return ifReturn
  });
  return treeData != '' ? treeData : data;
  }

  export function isMobileOrPc() {
    if( /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ) {
	　　return true;
	}else{
		return false;
	}
}

export function isMobileFun() {
	const rect = body.getBoundingClientRect()
	return rect.width - 1 < WIDTH
  }

export function getBoundingClientRect() {
	return body.getBoundingClientRect()
  }
// 转换字符串，undefined,null等转化为""
export function getHeader() {
  if(isMicro){
    return {
      Authorization: "Bearer " + getZltAccessToken(),
      token: "Bearer " + getAccessToken()
    }
  }else{
    return {
      Authorization: "Bearer " + getAccessToken()
    }
  }
}

//是否需要顯示tagsView
export function changeTagsView(query) {
  //如果是第三方跳轉,則隱藏tagsView
  if (query.appId) {
    this.$store.dispatch('settings/changeSetting', {
      key: 'tagsView',
      value: false
    })
  }
}

//關閉本頁面
export function closeFormDefault() {
  // debugger
  //关闭子页面
  if(this.$route.query.routerHistory){
    this.$router.go(this.$router.query.routerHistory) // 返回
  }else{
    this.$router.go(-1) // 返回
  }
  //如果是第三方跳轉,則隱藏tagsView
  if (this.$store.state.settings.tagsView) {
    this.$store.state.tagsView.visitedViews.splice(this.$store.state.tagsView.visitedViews.findIndex(item =>
      item.path === this.$route.path), 1)
    this.$router.push(this.$store.state.tagsView.visitedViews[this.$store.state.tagsView.visitedViews
      .length - 1].path)
  }

}
export function downloadExcel(data, fileName) {
  download0(data, fileName, 'application/vnd.ms-excel');
}

// 下载 Word 方法
export function downloadWord(data, fileName) {
  download0(data, fileName, 'application/msword');
}

// 下载 Zip 方法
export function downloadZip(data, fileName) {
  download0(data, fileName, 'application/zip');
}

// 下载 Html 方法
export function downloadHtml(data, fileName) {
  download0(data, fileName, 'text/html');
}

// 下载 Markdown 方法
export function downloadMarkdown(data, fileName) {
  download0(data, fileName, 'text/markdown');
}
function download0(data, fileName, mineType) {
  // 创建 blob
  let blob = new Blob([data], {type: mineType});
  // 创建 href 超链接，点击进行下载
  window.URL = window.URL || window.webkitURL;
  let href = URL.createObjectURL(blob);
  let downA = document.createElement("a");
  downA.href =  href;
  downA.download = fileName;
  downA.click();
  // 销毁超连接
  window.URL.revokeObjectURL(href);
}

export function verifyMathematical(data,obj){
// 剔除空白符
  data = data.replace(/\s/g, '');

  // 错误情况，空字符串
  if("" === data){
    return false;
  }

  // 错误情况，运算符连续
  if( /[\+\-\*\/]{2,}/.test(data) ){
    return false;
  }

  // 空括号
  if(/\(\)/.test(data)){
    return false;
  }

  // 错误情况，括号不配对
  var stack = [];
  for(var i = 0, item; i < data.length; i++){
    item = data.charAt(i);
    if('(' === item){
      stack.push('(');
    }else if(')' === item){
      if(stack.length > 0){
        stack.pop();
      }else{
        return false;
      }
    }
  }
  if(0 !== stack.length){
    return false;
  }

  // 错误情况，(后面是运算符
  if(/\([\+\-\*\/]/.test(data)){
    return false;
  }

  // 错误情况，)前面是运算符
  if(/[\+\-\*\/]\)/.test(data)){
    return false;
  }

  // 错误情况，(前面不是运算符
  if(/[^\+\-\*\/]\(/.test(data)){
    return false;
  }

  // 错误情况，)后面不是运算符
  if(/\)[^\+\-\*\/]/.test(data)){
    return false;
  }
  //错误情况，使用除()+-*/之外的字符
  if(/[^\+\-\*\/0-9.a-zA-Z\(\)]/.test(data)){
    return false;
  }

  //运算符号不能在首末位
  if(/^[\+\-\*\/.]|[\+\-\*\/.]$/.test(data)){
    return false;
  }
  // 错误情况，变量没有来自“待选公式变量”
  var tmpStr = data.replace(/[\(\)\+\-\*\/]{1,}/g, '`');
  var array = tmpStr.split('`');
  for(var i = 0, item; i < array.length; i++){
    item = array[i];
    if( /[A-Z]/i.test(item) && 'undefined' === typeof(obj[item]) ){
      return false;
    }
  }
  // 测试
  // var fields = {
  //   'ID': 1,
  //   'TOTAL': 1,
  //   'AVL' : 1,
  //   'NUM' : 1
  // };
  return true;
}

/**
 * 解析表達式
 * @param data
 */
export function analysisMathematical(data){
// 剔除空白符
  data = data.replace(/\s/g, '');
  const tmpStr = data.replace(/[\(\)\+\-\*\/]{1,}/g, '`');
  const arrayList = []
  const array = tmpStr.split('`');
  for(let i = 0, item; i < array.length; i++){
    item = array[i];
    if(/[A-Z]/i.test(item) ){
      arrayList.push(item)
    }
  }
  return arrayList
}
export function getAppBaseApi() {
  return process.env.VUE_APP_BASE_API;
}
