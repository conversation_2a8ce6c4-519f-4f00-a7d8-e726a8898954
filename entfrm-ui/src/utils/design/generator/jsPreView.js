import { isArray } from 'util'
import { exportDefault, titleCase } from '@/utils/index'
import { trigger,htmlTypeConvertForm } from './config'

const units = {
  KB: '1024',
  MB: '1024 / 1024',
  GB: '1024 / 1024 / 1024'
}
let confGlobal
const inheritAttrs = {
  file: '',
  dialog: 'inheritAttrs: false,'
}


export function makeUpPreViewJs(conf, type) {
  confGlobal = conf = JSON.parse(JSON.stringify(conf))
  const dataList = []
  const ruleList = []
  const optionsList = []
  const childDragTableMobileClientWidthList = []  //子表在適配手機時的寬度列表
  const propsList = []
  const methodList = mixinMethod(conf,type)
  const uploadVarList = []
  const createdList = []

  const childList = []
  conf.fields.forEach(el => {
    buildAttributes(el, dataList, ruleList, optionsList, methodList, propsList, uploadVarList,createdList,conf)
    if(el.tag =="entfrm-child-table-form"){
      childList.push(el.element)
    }
  })
  buildCheckBoxObjMethod(conf, methodList,childList);
  buildCascaderObjMethod(conf, methodList,childList);
  buildTimeRangObjMethod(conf, methodList,childList);
  buildFillAutoObjMethod(conf, methodList);
  buildCheckPointObjMethod(conf, methodList);

  childList.forEach(childTable => {
    buildChildTableAttributes(conf,childTable,dataList,methodList,optionsList,createdList, ruleList)
    childDragTableMobileClientWidthList.push(`${childTable.entityName}DragTableMobileClientWidth: 0,`)
  })
  //加入填單人信息
  dataList.push('makerNo:this.$store.state.user.empNo,')
  dataList.push('makerName: this.$store.state.user.name,')
  dataList.push('dataSource: "pc",')

  const script = buildexport(
    conf,
    type,
    dataList.join('\n'),
    ruleList.join('\n'),
    optionsList.join('\n'),
    childDragTableMobileClientWidthList.join('\n'),
    uploadVarList.join('\n'),
    propsList.join('\n'),
    methodList.join('\n'),
    createdList.join('\n')
  )
  confGlobal = null
  return script
}


function buildAttributes(el, dataList, ruleList, optionsList, methodList, propsList, uploadVarList,createdList,conf) {
  buildData(el, dataList)
  buildRules(el, ruleList)

  if (el.dictType) {
    // debugger
    buildOptions(el, optionsList)
    // if (el.dataType === 'dynamic') {
    // const model = `${el.vModel}Options`
    const vModel = titleCase(el.vModel)
    if(vModel){
      buildOptionMethod(`get${vModel}Options`, methodList,el)
      buildCreatedMethod(`get${vModel}Options`, createdList)
    }

    // }/
  }

  // if (el.tag === 'el-cascader') {
  //       buildOptions(el, optionsList)
  // }

  if (el.props && el.props.props) {
    buildProps(el, propsList)
  }

  if (el.action && el.tag === 'el-upload') {
    // uploadVarList.push(
    //   `${el.vModel}Action: '${el.action}',
    //   ${el.vModel}fileList: [],`
    // )
    methodList.push(buildBeforeUpload(el))
    methodList.push(buildFunctionUpload(conf,el))
    // if (!el['auto-upload']) {
    //   methodList.push(buildSubmitUpload(el))
    // }
    //debugger
    const multiple = el.multiple ? 'true' : 'false'
    uploadVarList.push(
      `
      // 文件上传参数
      upload: {
        // 是否显示弹出层
        open: false,
        fileList: [],
        fileNameList: [],
        // 弹出层标题
        title: "",
        multiple: ${multiple},
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: this.getHeader(),
        // 上传的地址
        url: this.getAppBaseApi() + "/system/fileInfo/uploadWithOos"
      },`)
  }
  if(el.tag=="entfrm-child-table-form"){
    conf.childTables.forEach(childs => {
      uploadVarList.push(
        `
        // 文件上传参数
        upload${childs.className}: {
          // 是否显示弹出层
          open: false,
          fileList: [],
          fileNameList: [],
          // 弹出层标题
          title: "",
          multiple: false,
          // 是否禁用上传
          isUploading: false,
          // 设置上传的请求头部
          headers: this.getHeader(),
          // 上传的地址
          url: this.getAppBaseApi() + "/${childs.moduleName}/${conf.businessName}/import${childs.className}"
        },`)
    });
  }
  if (el.children) {
    el.children.forEach(el2 => {
      buildAttributes(el2, dataList, ruleList, optionsList, methodList, propsList, uploadVarList,createdList,conf)
    })
  }
}
function buildCheckBoxObjMethod(conf,methodList,childList){
  let masterCheckBoxStr = ''
  let childCheckboxStr = ''
  childList.forEach(childTable => {
    childCheckboxStr = childCheckboxStr + buildCheckBoxParseMixinMethod(childTable,conf)
  })

  conf.fields.forEach(el => {
    if(el.tag === 'el-checkbox-group'){
      masterCheckBoxStr = masterCheckBoxStr + `
      if(this.${conf.formModel}.${el.vModel}){
        this.${conf.formModel}.${el.vModel} = JSON.parse(this.${conf.formModel}.${el.vModel})
      }\n`
    }
  })
  const str = `checkBoxParse() {
                ${masterCheckBoxStr}\n
               ${childCheckboxStr}
              },
  `
  methodList.push(str)
}

function buildCascaderObjMethod(conf,methodList,childList){
  let masterCascaderStr = ''
  let childCheckboxStr = ''
  childList.forEach(childTable => {
    childCheckboxStr = childCheckboxStr + buildCascaderMixinMethod(childTable,conf)
  })
  conf.fields.forEach(el => {
    if(el.tag === 'el-cascader'){
      masterCascaderStr = masterCascaderStr + `
      if(this.${conf.formModel}.${el.vModel}){
        this.${conf.formModel}.${el.vModel} = JSON.parse(this.${conf.formModel}.${el.vModel})
      }\n`
    }
  })
  const str = `cascaderParse() {
                ${masterCascaderStr}
                ${childCheckboxStr}
              },
  `
  methodList.push(str)
}

function buildTimeRangObjMethod(conf,methodList,childList){
  let masterTimeRangStr = ''
  let childTimeRangStr = ''
  childList.forEach(childTable => {
    childTimeRangStr = childTimeRangStr + buildTimeRangMixinMethod(childTable,conf)
  })
  conf.fields.forEach(el => {
    if((el.tag === 'el-date-picker'&&el.type==='daterange')||(el.tag === 'el-time-picker'&&el["is-range"])){
      masterTimeRangStr = masterTimeRangStr + `
      if(this.${conf.formModel}.${el.vModel}){
        this.${conf.formModel}.${el.vModel} = JSON.parse(this.${conf.formModel}.${el.vModel})
      }\n`
    }
  })
  const str = `timeRangParse() {
                ${masterTimeRangStr}\n
               ${childTimeRangStr}
              },
  `
  methodList.push(str)
}

function buildFillAutoObjMethod(conf,methodList){

  // debugger
  //onChange事件帶出的方法集合
  let fillChangeMethods = {  };
  //所有需要產生@Change方法的控件
  let onChangeEls = []
  function addOnChangeStage(el,func){
    if(!el.onChangeStages){
      el.onChangeStages = []
    }
    el.onChangeStages.push(func)

  }
  conf.fields.forEach(el => {
    if (el.onchange !== '' && el.onchange !== undefined) {
      fillChangeMethods[`${el.onchange}`] = { key: el, els: [] }
      addOnChangeStage(el,(element)=>{
        let columnStr = ''
        let columnClearStr = ''
        fillChangeMethods[element.onchange].els.forEach(el => {
          columnStr += `this.${conf.formModel}.${el.el.vModel} = response.data.${el.fillColumn} \n`
          columnClearStr += `this.${conf.formModel}.${el.el.vModel} = '' \n`
        })
        if (element.onchange === 'getInfoUserByEmpno') {
          columnStr += `this.${conf.formModel}.applyFactoryId =  response.data.factoryid \n`
          columnClearStr += `this.${conf.formModel}.applyFactoryId =  ''`
        }
        let fillChangeMethodStr = `
          this.${element.onchange}(data).then(response => {
           if (response.code !== 0) {
              this.msgError(response.msg);
            } else {
              if(response.data != null){
                ${columnStr}
              }else{
                ${columnClearStr}
              }
            }
         });
         `
        return fillChangeMethodStr;
      })
    }
    if (conf.onChangeCalculateExpressionsMap[el.vModel]) {
      el.onChangeCalculateExpressions = conf.onChangeCalculateExpressionsMap[el.vModel]
      addOnChangeStage(el,(element)=>{
        var calculateExpressionChangeMethodStrs = ''
        el.onChangeCalculateExpressions.forEach(expression=>{
          calculateExpressionChangeMethodStrs += `
          if(${expression.isNumberValueCondition}){
            this.${conf.formModel}.${expression.key} = (${expression.calculateExpressions}).toFixed(${expression.calculateExpressionsDecimalScale})
          }else {
            this.${conf.formModel}.${expression.key} = ''
          } \n
          `
        })
        return calculateExpressionChangeMethodStrs
      })
    }
    if(el.onChangeStages){
      onChangeEls.push(el)
    }
  })
  conf.fields.forEach(el => {
    if (el.fillAuto !== '' && el.fillAuto !== undefined && el.fillAuto.length > 0) {
      fillChangeMethods[`${el.fillAuto[0]}`].els.push({ 'fillColumn': `${el.fillAuto[1]}`, 'el': el })
    }
  })
  onChangeEls.forEach(el =>{
    var changeMethodStr = `${el.vModel}_onchange(data){ \n`
    el.onChangeStages.forEach(stage => {
      let str = stage(el)
      changeMethodStr += str? str :''
    })
    changeMethodStr += ' \n},'
    methodList.push(changeMethodStr)
  })
}

function buildCheckPointObjMethod(conf,methodList){
  const methods = `onSignFormSelected(selectEmp,modelNo,modelName){
                      this.$set(this.${conf.formModel},modelNo,selectEmp.empNo)
                      this.$set(this.${conf.formModel},modelName,selectEmp.empName)
                  },`;
  methodList.push(methods);
}

function buildChildTableAttributes(conf,childTable,dataList,methodList,optionsList,createdList, ruleList){
  // dataList.push(`${childTable.entityName}Lists: [],`)
  buildChildListMethod(childTable,methodList,conf)
  buildChildOptions(childTable, optionsList,conf)
  buildChildOptionMethod(childTable, methodList,conf)
  buildChildCreatedMethod(childTable, createdList,conf)
  // buildChildRules(conf, ruleList,childTable)

}

function buildexport(conf, type, data, rules, selectOptions,childDragTableMobileClientWidths, uploadVar, props, methods,createds) {
  let checkboxStr = buildCheckBoxObjStr(conf)
  let cascaderStr = buildCascaderObjStr(conf)
  let timeRang = buildTimeRangObjStr(conf)
  let fileUploadStr = buildUploadStr(conf)
  let dragTableMobileClientWidthStr = buildDragTableMobileClientWidthStr(conf)


  let dbAlias = conf.dbAlias?`alias:"${conf.dbAlias}"`:`alias:""`
  const str =  `${exportDefault}{
  ${inheritAttrs[type]}
  components: {},
  props: [],
  data () {
    return {
      ${conf.formModel}: {
        ${data}
      },
      tableHeight: document.documentElement.scrollHeight - 245 + "px",
      ${conf.formRules}: {
        ${rules}
      },
      ${uploadVar}
      ${selectOptions}
      ${props}
      ${childDragTableMobileClientWidths}
      isMobile: false,
      isDisable: false,
      labelPosition: '${conf.labelPosition}',
      ${dbAlias}
    }
  },
  computed: {},
  watch: {},
  created () {
    const id = this.$route.query.id;
    this.changeTagsView(this.$route.query);
    this.isMobile = this.isMobileFun()
    if(this.isMobile){
      this.labelPosition = 'top'
    }
    if (id != null&&id!=undefined) {
      get${conf.className}(id).then(response => {
        this.${conf.formModel} = response.data;
        ${checkboxStr}
        ${cascaderStr}
        ${timeRang}
        ${fileUploadStr}
      });
    }
    ${createds}
  },
  mounted () {
    this.$nextTick(function () {
      if(this.isMobile){
        ${dragTableMobileClientWidthStr}
      }
    })
  },
  methods: {
    ${methods}
  }
}`
  return str
}

function buildData(conf, dataList) {
  console.log('88888')
  if(conf.layout=="signFormItem"){  //簽核節點
    if(conf.required){
      let defaultValue
      if (typeof (conf.defaultValue) === 'string' && !conf.multiple) {
        defaultValue = `'${conf.defaultValue}'`
      } else {
        defaultValue = `${JSON.stringify(conf.defaultValue)}`
      }
      if (conf.modelNo === undefined || conf.modelNo === '') return
      dataList.push(`${conf.modelNo}: ${defaultValue},`)
    }
  }else if("childTableItem" === conf.layout ){
    dataList.push(`${conf.element.entityName}Lists: [],`)
  }else {
    if (conf.vModel === undefined || conf.vModel === '') return
    let defaultValue
    if (typeof (conf.defaultValue) === 'string' && !conf.multiple) {
      defaultValue = `'${conf.defaultValue}'`
    } else {
      defaultValue = `${JSON.stringify(conf.defaultValue)}`
    }
    // if(conf.options){
    //   dataList.push(`${conf.vModel}Options: [],`)
    // }
    if (conf.action && conf.tag === 'el-upload') {
      defaultValue = `""`
    }
    dataList.push(`${conf.vModel}: ${defaultValue},`)
  }
}



function mixinMethod(conf,type) {
  const list = [];
  let masterCheckBoxStr = ''
  let masterCascaderStr = ''
  let checkboxStr = ''
  let dateRange = ''
  let timeRange = ''

  let childList =[];

  conf.fields.forEach(el => {
    if(el.tag === 'el-checkbox-group'){
      masterCheckBoxStr = masterCheckBoxStr + `${conf.formModel}.${el.vModel} = JSON.stringify(${conf.formModel}.${el.vModel}) \n`
    }else if(el.tag === 'el-cascader'){  //級聯
      masterCascaderStr = masterCascaderStr + `${conf.formModel}.${el.vModel} = JSON.stringify(${conf.formModel}.${el.vModel}) \n`
    }else if(el.tag === 'el-date-picker'&&el.type == 'daterange' ){  //日期範圍
      dateRange = dateRange + `${conf.formModel}.${el.vModel} = JSON.stringify(${conf.formModel}.${el.vModel}) \n`
    }else if(el.tag === 'el-time-picker'&&el["is-range"]){  //時間範圍
      timeRange = timeRange + `${conf.formModel}.${el.vModel} = JSON.stringify(${conf.formModel}.${el.vModel}) \n`
    }else if(el.tag === 'entfrm-child-table-form'){ //子表
      childList.push(el.element)
    }
  })
  childList.forEach(childTable => {
    checkboxStr = checkboxStr + buildCheckBoxStringifyMixinMethod(childTable,conf)
  })

  const minxins = {
    file: confGlobal.formBtns ? {
      submitForm: `submitForm() {
        this.$refs['${confGlobal.formRef}'].validate(valid => {
          if (valid) {
            this.isDisable = true;
            const ${conf.formModel} = JSON.parse(JSON.stringify(this.${conf.formModel}))
            ${masterCheckBoxStr}
            ${checkboxStr}
            ${masterCascaderStr}
            ${dateRange}
            ${timeRange}
            if(${conf.formModel}.id != undefined) {
              edit${conf.className}(${conf.formModel}).then(response => {
                if (response.code === 0) {
                  this.msgSuccess(this.$t('tips.updateSuccess'));
                  this.closeForm();
                } else {
                    this.msgError(response.msg);
                }
                this.isDisable = false;
              });
            }else{
              add${conf.className}(${conf.formModel}).then(response => {
                if (response.code === 0) {
                    this.msgSuccess(this.$t('tips.createSuccess'));
                    this.closeForm();
                } else {
                    this.msgError(response.msg);
                }
                this.isDisable = false;
              });
            }
            /**
             * 提交的時候刪除被標記的需要刪除的附件
             */
            if (this.${conf.formModel}.attachids) {
              this.upload.fileNameList.forEach(item => {
                if (item) {
                  delFileInfo(item);
                }
              })
            }
          }
        })
      },`,
      handleSubmit: `handleSubmit: function() {
            this.$refs["${conf.formRef}"].validate(valid => {
              if (valid) {
                this.isDisable = true;
                const formData = JSON.parse(JSON.stringify(this.${conf.formModel}))
                ${masterCheckBoxStr}
                ${checkboxStr}
                ${masterCascaderStr}
                ${dateRange}
                ${timeRange}
                if (formData.id != undefined) {
                       edit${conf.className}AndStartProcess(${conf.formModel}).then(response => {
                          if (response.code === 0) {
                            this.msgSuccess(this.$t('tips.updateSuccess'));
                            this.closeForm();
                          }
                          else {
                            this.msgError(response.msg);
                          }
                          this.isDisable = false;
                        });
                }else {
                      add${conf.className}AndStartProcess(${conf.formModel}).then(response => {
                                  if (response.code === 0) {
                                    this.msgSuccess(this.$t('tips.createSuccess'));
                                    this.closeForm();
                                  }
                                  else {
                                    this.msgError(response.msg);
                                  }
                                  this.isDisable = false;
                                });
                }
                /**
                 * 提交的時候刪除被標記的需要刪除的附件
                 */
                if (this.${conf.formModel}.attachids) {
                  this.upload.fileNameList.forEach(item => {
                    if (item) {
                      delFileInfo(item);
                    }
                  })
                }
              }
            })
      }, `,
      resetForm: `resetForm() {
        this.$refs['${confGlobal.formRef}'].resetFields()
      },`,
      closeForm: `closeForm(){
          //关闭子页面
          if (this.$store.state.settings.tagsView) {
            this.$router.go(-1) // 返回
            this.$store.state.tagsView.visitedViews.splice(this.$store.state.tagsView.visitedViews.findIndex(item =>
              item.path === this.$route.path), 1)
            this.$router.push(this.$store.state.tagsView.visitedViews[this.$store.state.tagsView.visitedViews
              .length - 1].path)
          }else{
            parent.postMessage("closeCurrentTabMessage",'*');
          }
        },`
    } : null,
    dialog: {
      onOpen: 'onOpen() {},',
      onClose: `onClose() {
        this.$refs['${confGlobal.formRef}'].resetFields()
      },`,
      close: `close() {
        this.$emit('update:visible', false)
      },`,
      handelConfirm: `handelConfirm() {
        this.$refs['${confGlobal.formRef}'].validate(valid => {
          if(!valid) return
          this.close()
        })
      },`
    }
  }

  const methods = minxins[type]
  if (methods) {
    Object.keys(methods).forEach(key => {
      list.push(methods[key])
    })
  }

  return list
}
//checkbox数据由对象转换为string
function buildCheckBoxStringifyMixinMethod(childTable, conf) {
  let checkboxStr = ''
  let dataList = []
  childTable.tableCols.forEach(column => {
    if(column.isAdd === '1' && column.dictType && column.dictType != ''&&column.htmlType != 'select'&&column.htmlType != 'radio'){
      dataList.push(`${conf.formModel}.${childTable.businessName}Lists[index].${column.javaField} = JSON.stringify(${conf.formModel}.${childTable.businessName}Lists[index].${column.javaField})`)
    }
    if(column.isAdd === '1'  && (column.htmlType == 'time-range'||column.htmlType == 'date-range')){
      dataList.push(`${conf.formModel}.${childTable.businessName}Lists[index].${column.javaField} = JSON.stringify(${conf.formModel}.${childTable.businessName}Lists[index].${column.javaField})`)
    }

  })
  if(dataList.length>0){
    checkboxStr = `  ${checkboxStr}
      for (let index in ${conf.formModel}.${childTable.businessName}Lists) {
          ${dataList.join("\n")}
      }`
  }
  return checkboxStr
}

function buildRules(conf, ruleList) {

  if(conf.layout=="signFormItem") {  //簽核節點
    if(conf.required){
      const rules = []
      const type = isArray(conf.defaultValue) ? 'type: \'array\',' : ''
      let message = isArray(conf.defaultValue) ? `請至少選擇一個${conf.label}` : conf.placeholder
      if (message === undefined) message = `${conf.label}不能為空`
      rules.push(`{ required: true, ${type} message: '${message}', trigger: '${"change"}' }`)
      ruleList.push(`${conf.modelNo}: [${rules.join(',')}],`)
    }
  }else if("childTableItem" === conf.layout ){
    buildChildRules(conf,ruleList)
  }else{
    if (conf.vModel === undefined) return
    const rules = []
    if (trigger[conf.tag]) {
      if (conf.required) {
        const type = isArray(conf.defaultValue) ? 'type: \'array\',' : ''
        let message = isArray(conf.defaultValue) ? `請至少選擇一個${conf.label}` : conf.placeholder
        if (message === undefined) message = `${conf.label}不能為空`
        rules.push(`{ required: true, ${type} message: '${message}', trigger: '${trigger[conf.tag]}' }`)
      }
      if (conf.regList && isArray(conf.regList)) {
        conf.regList.forEach(item => {
          if (item.pattern) {
            rules.push(`{ pattern: ${eval(item.pattern)}, message: '${item.message}', trigger: '${trigger[conf.tag]}' }`)
          }
        })
      }
      ruleList.push(`${conf.vModel}: [${rules.join(',')}],`)
    }
  }
}
function buildOptions(conf, optionsList) {
  if (conf.vModel === undefined) return
  // debugger
  if (conf.dataType === 'dynamic') {
    conf.options = []
  }
  // const str = `${conf.vModel}Options: ${JSON.stringify(conf.options)},`
  const str = `${conf.vModel}Options: [],`
  optionsList.push(str)
}
function buildOptionMethod(methodName, methodList,conf) {
  if (conf.tag === 'el-cascader') {
    const str = `${methodName}() {
        this.getDicts("${conf.dictType}").then(response => {
          this.${conf.vModel}Options = response.data;
            if(response.data.length > 0){
              response.data.forEach(element => {
                this.getOptions(element);
              });
            }
      });
    },`
    methodList.push(str)
  }else{
    const str = `${methodName}() {
        this.getDicts("${conf.dictType}").then(response => {
          this.${conf.vModel}Options = response.data;
      });
    },`
    methodList.push(str)

  }
}
function buildCreatedMethod(methodName, createdList) {
  const str = `this.${methodName}()`
  createdList.push(str)
}
//設置從表表單驗證規則
function buildChildRules(conf, ruleList) {
  let childTable = conf.element
  childTable.tableCols.forEach(column => {
    const rules = []
    if(column.isAdd == '1'){
      if (column.isRequired === '1') {
        if (trigger[htmlTypeConvertForm[column.htmlType]]) {
          const type = htmlTypeConvertForm[column.htmlType] === 'el-checkbox-group' ? 'type: \'array\',' : ''
          let message = htmlTypeConvertForm[column.htmlType] === 'el-checkbox-group' ? `請至少選擇一個${column.columnComment}` : column.placeholder
          if (message === undefined) message = `${column.columnComment}不能為空`
          rules.push(`{ required: true, ${type} message: '${message}', trigger: '${trigger[htmlTypeConvertForm[column.htmlType]]}' }`)
        }
      }
    }
    let regList = conf.childRegMap[column.javaField]
    if (regList) {
      regList.forEach(item => {
        if (item.pattern) {
          rules.push(`{ pattern: ${eval(item.pattern)}, message: '${item.message}', trigger: '${trigger[htmlTypeConvertForm[column.htmlType]]}' }`)
        }
      })
    }
    if(rules.length>0){
      ruleList.push(`${childTable.entityName}_${column.javaField}: [${rules.join(',')}],`)
    }
  })
}
//checkbox数据由string转换为对象
function buildCheckBoxParseMixinMethod(childTable, conf) {
  let checkboxObjStr = ''
  let checkboxObjs = []
  childTable.tableCols.forEach(column => {
    if(column.isAdd === '1' && column.htmlType == 'checkbox' && column.dictType && column.dictType != ''){
      checkboxObjs.push(`
        if(this.${conf.formModel}.${childTable.businessName}Lists[index].${column.javaField}){
          this.${conf.formModel}.${childTable.businessName}Lists[index].${column.javaField} = JSON.parse(this.${conf.formModel}.${childTable.businessName}Lists[index].${column.javaField})
        }`)
    }
  })
  if(checkboxObjs.length>0){
    checkboxObjStr = `for (let index in this.${conf.formModel}.${childTable.businessName}Lists) {
                          ${checkboxObjs.join("\n")}
                      }`
  }
  return checkboxObjStr
}
//Cascader
function buildCascaderMixinMethod(childTable, conf) {
  let checkboxObjStr = ''
  let checkboxObjs = []
  childTable.tableCols.forEach(column => {
    if(column.isAdd === '1' && column.htmlType == 'cascader' && column.dictType && column.dictType != ''){
      checkboxObjs.push(`
        if(this.${conf.formModel}.${childTable.businessName}Lists[index].${column.javaField}){
          this.${conf.formModel}.${childTable.businessName}Lists[index].${column.javaField} = JSON.parse(this.${conf.formModel}.${childTable.businessName}Lists[index].${column.javaField})
        }`)
    }
  })
  if(checkboxObjs.length>0){
    checkboxObjStr = `for (let index in this.${conf.formModel}.${childTable.businessName}Lists) {
                          ${checkboxObjs.join("\n")}
                      }`
  }
  return checkboxObjStr
}
//TimeRang
function buildTimeRangMixinMethod(childTable, conf) {
  let checkboxObjStr = ''
  let checkboxObjs = []
  childTable.tableCols.forEach(column => {
    if(column.isAdd === '1' && (column.htmlType == 'time-range' ||column.htmlType == 'date-range')){
      checkboxObjs.push(`if(this.${conf.formModel}.${childTable.businessName}Lists[index].${column.javaField}){
          this.${conf.formModel}.${childTable.businessName}Lists[index].${column.javaField} = JSON.parse(this.${conf.formModel}.${childTable.businessName}Lists[index].${column.javaField})
        }`)
    }
  })
  if(checkboxObjs.length>0){
    checkboxObjStr = `for (let index in this.${conf.formModel}.${childTable.businessName}Lists) {
                          ${checkboxObjs.join("\n")}
                      }`
  }
  return checkboxObjStr
}
function buildChildListMethod(childTable, methodList,conf) {
  const dataList = []
  childTable.tableCols.forEach(column => {
    if(column.isAdd === '1'){
      buildChildData(column, dataList)
    }
  })
  var childTableElement = conf.fields.find(item => item.childTableName && item.childTableName === childTable.tableName)
  let buildChildOnChangeMethodObject = buildChildOnChangeMethod(childTableElement);
  let childTableShowSummaryStr = buildChildTableShowSummaryObjMethod(childTableElement);
  // buildData(el, dataList)
  const str = `
  handleAdd_${childTable.tableName}() {
    const cloumn = {
      ${dataList}
    };
    this.${conf.formModel}.${childTable.entityName}Lists.splice(this.${conf.formModel}.${childTable.entityName}Lists.length, 0, cloumn);
    for (let index in this.${conf.formModel}.${childTable.entityName}Lists) {
      this.${conf.formModel}.${childTable.entityName}Lists[index].sort = parseInt(index) + 1;
    }
  },
  handleDel_${childTable.tableName}(index, row) {
    let functionName = this.$t('${conf.tableName}_${conf.dbId}.default.functionName');
    this.$confirm(this.$t('tips.deleteConfirm',['${childTable.tableComment}',row.id]),  this.$t('tips.warm'), {
        confirmButtonText: this.$t("common.confirmTrim"),
        cancelButtonText: this.$t("common.cancelTrim"),
        type: "warning",
      }
    )
      .then(() => {
        this.${conf.formModel}.${childTable.entityName}Lists.splice(index, 1);
        this.msgSuccess(this.$t("tips.deleteSuccess"));
      })
      .catch(function (err) {
        console.log(err);
      });
  },
   // 文件上传成功处理
      uploadsucces${childTable.className}(response, file, fileList) {
         this.formData.${childTable.entityName}Lists=this.formData.${childTable.entityName}Lists.concat(response.data);
        response.data.forEach(item => {
        ${buildChildOnChangeMethodObject.methodNames}
        })
      },
      handleChange${childTable.className}(file, fileList) {},
      handleExceed${childTable.className}(file, fileList) {},
      handleRemove${childTable.className}(file, fileList) {
        this.$emit("delUploadImage", file.name);
        const index = this.upload.fileList.indexOf(file);
        this.upload.fileList.splice(index, 1);
        this.formData.attachids = this.formData.attachids.replace(file.url + ",", "");
        this.upload.fileNameList.push(file.url);
        // delFileInfo(file.url);
      },
      handlePreview${childTable.className}(file) {
        const queryParams = this.queryParams;
            this.request({
    url: '/dept1/aaaQingjiefw/exportAaaQingjiefwtbTemplet',
    method: 'get',
    params: queryParams
  }).then(response => {
          this.download(response.data);
        })
      },
      ${buildChildOnChangeMethodObject.methods}
      ${childTableShowSummaryStr}
  `
  methodList.push(str)
}
function buildChildData(conf, dataList) {
  let defaultValue
  if (conf.htmlType == 'checkbox'||conf.htmlType == 'cascader'||conf.htmlType == 'date-range') {
    defaultValue = `[]`
  } else if(conf.htmlType == 'slider'||conf.htmlType == 'number'||conf.htmlType == 'rate'){
    defaultValue = 0
  }else if (typeof (conf.defValue) === 'string') {
    defaultValue = `'${conf.defValue}'`
  } else if (conf.defValue == null) {
    defaultValue = `''`
  } else{
    defaultValue = `${JSON.stringify(conf.defValue)}`
  }
  dataList.push(`${conf.javaField}: ${defaultValue}`)
}
function buildChildOnChangeMethod(el)
{
  // console.log('22222')
  //所有需要產生@Change方法的控件
  let onChangeEls = []
  function addOnChangeStage(el,func){
    if(!el.onChangeStages){
      el.onChangeStages = []
    }
    el.onChangeStages.push(func)

  }
  let buildChildOnChangeMethodMap = new Map()
  buildChildOnChangeMethodMap.methods=``
  buildChildOnChangeMethodMap.methodNames=``
  let changeMethods = [];
  let childName = el.element.className
  for (let key in el.childAttributesMap) {
    var childAttribute = el.childAttributesMap[key]
    if (childAttribute.onchange) {
      changeMethods[`${childName}_${key}_onchange`] = {
        eventName: childAttribute.onchange,
        columnName: key,
        fillAutoStr: '',
        fillAutoClearStr: '',
      }
      childAttribute.changeMethod = changeMethods[`${childName}_${key}_onchange`]
      addOnChangeStage(childAttribute,(childAttribute)=>{
        return `
          this.${childAttribute.changeMethod.eventName}(item.${childAttribute.changeMethod.columnName}).then(responseInfo => {
            if (responseInfo.code !== 0) {
              this.msgError(responseInfo.msg);
            }
            else {
              if (responseInfo.data != null) {
                ${childAttribute.changeMethod.fillAutoStr}
              }else{
                ${childAttribute.changeMethod.fillAutoClearStr}
              }
            }
            });
         `
      })
    }
    //添加表達式的方法
    if (el.onChangeCalculateExpressionsMap && el.onChangeCalculateExpressionsMap[key]) {
      childAttribute.onChangeCalculateExpressions = el.onChangeCalculateExpressionsMap[key]
      addOnChangeStage(childAttribute,(childAttribute)=>{
        var calculateExpressionChangeMethodStrs = ''
        childAttribute.onChangeCalculateExpressions.forEach(expression=>{
          calculateExpressionChangeMethodStrs += `
          if(${expression.isNumberValueCondition}){
            item.${expression.key} = (${expression.calculateExpressions}).toFixed(${expression.calculateExpressionsDecimalScale})
          }else {
            item.${expression.key} = ''
          } \n
          `
        })
        return calculateExpressionChangeMethodStrs
      })
    }
    if(childAttribute.onChangeStages){
      childAttribute.methodName = `${childName}_${key}_onchange`
      onChangeEls.push(childAttribute)
    }
  }
  //onChangeCalculateExpressionsMap
  for (let key in el.childAttributesMap) {
    if (el.childAttributesMap[key].fillAuto !== '' && el.childAttributesMap[key].fillAuto !== undefined && el.childAttributesMap[key].fillAuto.length > 0) {
      for (let changeMethod in changeMethods) {
        if (el.childAttributesMap[key].fillAuto[0] == changeMethods[changeMethod].eventName)//&& key==changeMethods[changeMethod].columnName
        {
          changeMethods[changeMethod].fillAutoStr += `item.${key}=responseInfo.data.${el.childAttributesMap[key].fillAuto[1]} \n`
          changeMethods[changeMethod].fillAutoClearStr += `item.${key}='' \n`
        }
      }
    }
  }
  onChangeEls.forEach(onChangeEl =>{
    var changeMethodStr = `${onChangeEl.methodName}(item){ \n`
    onChangeEl.onChangeStages.forEach(stage => {
      let str = stage(onChangeEl)
      changeMethodStr += str? str :''
    })
    changeMethodStr += ' \n},'
    buildChildOnChangeMethodMap.methods += `${changeMethodStr} \n`
    buildChildOnChangeMethodMap.methodNames += `this.${onChangeEl.methodName}(item)\n`
  })
  return buildChildOnChangeMethodMap
}
function buildChildTableShowSummaryObjMethod(childTablefiled){
  if(!childTablefiled.childAttributesMap){
    return ""
  }
  var childAttributesMap = {}
  Object.entries(childTablefiled.childAttributesMap).forEach((childAttributes) => {
    if(childAttributes[1].sumMasterColumn || childAttributes[1].decimalScale) {
      childAttributesMap[childAttributes[0]] = {}
    }
    if (childAttributes[1].sumMasterColumn) {
      childAttributesMap[childAttributes[0]].sumMasterColumn = childAttributes[1].sumMasterColumn
    }
    if (childAttributes[1].decimalScale) {
      childAttributesMap[childAttributes[0]].decimalScale = childAttributes[1].decimalScale
    }
  })
  const childAttributesMapStr = JSON.stringify(childAttributesMap)
  const str = `
  ${childTablefiled.element.entityName}_getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      const scales = [];
      const childAttributesMap = ${childAttributesMapStr};
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = this.$t('common.sumText');
          return;
        }
        if(column.property && childAttributesMap[column.property] && childAttributesMap[column.property].sumMasterColumn) {
          scales[index] = 0;
          if(childAttributesMap[column.property].decimalScale){
            scales[index] = childAttributesMap[column.property].decimalScale
          }
          const values = data.map(item => Number(item[column.property]));
          if (!values.every(value => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                var x = prev + curr
                return x
              } else {
                return prev
              }
            }, 0);
            sums[index] = sums[index].toFixed(scales[index])
            this.formData[childAttributesMap[column.property].sumMasterColumn] = sums[index]
          }
        }
      });
      return sums;
    },
  `
  return str
}
//添加子表表單控件的集合
function buildChildOptions(childTable, optionsList,conf) {

  childTable.tableCols.forEach(column => {
    if(column.isAdd === '1' && column.dictType && column.dictType != ''){
      optionsList.push(`${column.javaField}${childTable.className}Options: [],`)
    }
  })
}
function buildChildOptionMethod(childTable, methodList,conf) {
  childTable.tableCols.forEach(column => {
    let methodName = 'get' + titleCase(column.javaField) + childTable.className + 'Options'
    if(column.isAdd === '1' && column.dictType && column.dictType != ''){
      if (column.htmlType === 'cascader') {
        const str = `${methodName}() {
          this.getDicts("${column.dictType}").then(response => {
            this.${column.javaField}${childTable.className}Options = response.data;
              if(response.data.length > 0){
                response.data.forEach(element => {
                  this.getOptions(element);
                });
              }
            });
          },`
        methodList.push(str)
      }else{
        const str = `${methodName}() {
              this.getDicts("${column.dictType}").then(response => {
                this.${column.javaField}${childTable.className}Options = response.data;
            });
          },`
        methodList.push(str)
      }
    }
  })
}
function buildChildCreatedMethod(childTable, createdList,conf) {
  childTable.tableCols.forEach(column => {
    if(column.isAdd === '1' && column.dictType && column.dictType != ''){
      let methodName = 'get' + titleCase(column.javaField) +childTable.className+ 'Options'
      const str = `this.${methodName}() `
      createdList.push(str)
    }
  })
}
function buildCheckBoxObjStr(conf){
  return 'this.checkBoxParse()'
}
function buildCascaderObjStr(conf){
  return 'this.cascaderParse()'
}
function buildTimeRangObjStr(conf){
  return 'this.timeRangParse()'
}
function buildUploadStr(conf){
  return `
  if (this.${conf.formModel}.attachids) {
    let a = this.${conf.formModel}.attachids.split(',');
    if (a.length > 0) {
      a.forEach(item => {
        if (item) {
          getByKey(item).then(response => {
            this.upload.fileList.push({name: response.data.orignalName, url: response.data.name});
          })
        }
      })
    }
  }`
}
//生成渲染後,調整子表column寬度的字符串
function buildDragTableMobileClientWidthStr(conf){
  let childDragTableMobileClientWidthStr = ''
  conf.childTables.forEach(childTable => {
    childDragTableMobileClientWidthStr = childDragTableMobileClientWidthStr + ` this.${childTable.entityName}DragTableMobileClientWidth = this.$refs.${childTable.entityName}DragTableMobile.$el.clientWidth  \n`
  })
  return childDragTableMobileClientWidthStr
}
function buildBeforeUpload(conf) {
  const unitNum = units[conf.sizeUnit]; let rightSizeCode = ''; let acceptCode = ''; const
    returnList = []
  if (conf.fileSize) {
    rightSizeCode = `let isRightSize = file.size / ${unitNum} < ${conf.fileSize}
    if(!isRightSize){
      this.$message.error('文件大小超过 ${conf.fileSize}${conf.sizeUnit}')
    }`
    returnList.push('isRightSize')
  }
  if (conf.accept) {
    acceptCode = `let isAccept = new RegExp('${conf.accept}').test(file.type)
    if(!isAccept){
      this.$message.error('应该选择${conf.accept}类型的文件')
    }`
    returnList.push('isAccept')
  }
  const str = `${conf.vModel}BeforeUpload(file) {
    ${rightSizeCode}
    ${acceptCode}
    return ${returnList.join('&&')}
  },`
  return returnList.length ? str : ''
}
function buildFunctionUpload(conf,el) {
  const str = `
  // 文件上传成功处理
  uploadsucces(response, file, fileList) {
    if (response.data.name != undefined && response.data.name != "undefined") {
      this.upload.fileList.push({name: file.name, url: response.data.name});
      this.${conf.formModel}.${el.vModel} += response.data.name + ",";
    }
  },
  handleChange(file, fileList) {

  },
  handleExceed(file, fileList) {

  },
  handleRemove(file, fileList) {
    this.$emit("delUploadImage", file.name);
    const index = this.upload.fileList.indexOf(file);
    this.upload.fileList.splice(index, 1);
    if(this.${conf.formModel}.${el.vModel}){
          this.${conf.formModel}.${el.vModel} = this.${conf.formModel}.${el.vModel}.replace(file.url + ",", "");
    }
    this.upload.fileNameList.push(file.url);
    // delFileInfo(file.url);
  },
  handlePreview(file) {
    previewFile(file.url)
  },`
  return str
}
function buildProps(conf, propsList) {
  // debugger
  if (conf.dataType === 'dynamic') {
    conf.valueKey !== 'value' && (conf.props.props.value = conf.valueKey)
    conf.labelKey !== 'label' && (conf.props.props.label = conf.labelKey)
    conf.childrenKey !== 'children' && (conf.props.props.children = conf.childrenKey)
  }
  const str = `${conf.vModel}Props: ${JSON.stringify(conf.props.props)},`
  propsList.push(str)
}

