import { isArray } from 'util'
import { exportDefault, titleCase } from '@/utils/index'
import { trigger,htmlTypeConvertForm } from './config'
import { isUndefined } from 'element-ui/src/utils/types'


const units = {
  KB: '1024',
  MB: '1024 / 1024',
  GB: '1024 / 1024 / 1024'
}
let confGlobal
const inheritAttrs = {
  file: '',
  dialog: 'inheritAttrs: false,'
}


export function makeUpJs(conf, type) {
  confGlobal = conf = JSON.parse(JSON.stringify(conf))
  const configDataList = []
  const dataList = []
  const ruleList = []
  const optionsList = []
  const childDragTableMobileClientWidthList = []  //子表在適配手機時的寬度列表
  const propsList = []
  const methodList = mixinMethod(conf,type)
  const uploadVarList = []
  const createdList = []

  const childList = []

  //加入標題信息
  configDataList.push(`title: "${conf.formName}",`)

  conf.fields.forEach(el => {
    buildAttributes(el, dataList, ruleList, optionsList, methodList, propsList, uploadVarList,createdList,conf,configDataList)
    if(el.tag =="entfrm-child-table-form"){
      childList.push(el.element)
    }
  })
  buildCheckBoxObjMethod(conf, methodList,childList);
  buildCascaderObjMethod(conf, methodList,childList);
  buildTimeRangObjMethod(conf, methodList,childList);
  buildFillAutoObjMethod(conf, methodList);
  buildCheckPointObjMethod(conf, methodList);

  childList.forEach(childTable => {
    buildChildTableAttributes(conf,childTable,dataList,methodList,optionsList,createdList, ruleList)
    childDragTableMobileClientWidthList.push(`${childTable.entityName}DragTableMobileClientWidth: 0,`)
  })
  //加入填單人信息
  dataList.push('makerNo: this.$store.state.user.empNo,')
  dataList.push('makerName: this.$store.state.user.name,')
  dataList.push('dataSource: "pc",')

  const script = buildexport(
    conf,
    type,
    dataList.join('\n'),
    ruleList.join('\n'),
    optionsList.join('\n'),
    childDragTableMobileClientWidthList.join('\n'),
    uploadVarList.join('\n'),
    propsList.join('\n'),
    methodList.join('\n'),
    createdList.join('\n'),
    configDataList.join('\n'),
  )
  confGlobal = null
  return script
}
function buildChildTableAttributesDetail(conf,childTable,dataList,methodList,optionsList,createdList, ruleList){
  buildChildListMethod(childTable,methodList,conf)
  buildChildOptions(childTable, optionsList,conf)
  buildChildOptionMethodDetail(childTable, methodList,conf)
  buildChildCreatedMethod(childTable, createdList,conf)
}
function buildChildTableAttributes(conf,childTable,dataList,methodList,optionsList,createdList, ruleList){
  // dataList.push(`${childTable.entityName}Lists: [],`)
  buildChildListMethod(childTable,methodList,conf)
  buildChildOptions(childTable, optionsList,conf)
  buildChildOptionMethod(childTable, methodList,conf)
  buildChildCreatedMethod(childTable, createdList,conf)
  // buildChildRules(conf, ruleList,childTable)

}
export function makeUpDetailJs(conf, type) {
  confGlobal = conf = JSON.parse(JSON.stringify(conf))
  const configDataList = []
  const dataList = []
  const ruleList = []
  const optionsList = []
  const propsList = []
  const childDragTableMobileClientWidthList = []  //子表在適配手機時的寬度列表
  const methodList = mixinDetailMethod(conf,type)
  const uploadVarList = []
  const createdList = []
  const childList = []

  //加入標題信息
  configDataList.push(`title: "${conf.formName}",`)

  conf.fields.forEach(el => {
    buildAttributesDetail(el, dataList, ruleList, optionsList, methodList, propsList, uploadVarList,createdList,conf,configDataList)
    if(el.tag =="entfrm-child-table-form"){
      childList.push(el.element)
    }
  })
  buildCheckBoxObjMethod(conf, methodList,childList);//複選框
  buildCascaderObjMethod(conf, methodList,childList);  //級聯選擇
  buildTimeRangObjMethod(conf, methodList,childList);
  childList.forEach(childTable => {
    buildChildTableAttributesDetail(conf,childTable,dataList,methodList,optionsList,createdList, ruleList)
    childDragTableMobileClientWidthList.push(`${childTable.entityName}DragTableMobileClientWidth: 0,`)
  })
  const script = buildDetailExport(
    conf,
    type,
    dataList.join('\n'),
    ruleList.join('\n'),
    optionsList.join('\n'),
    childDragTableMobileClientWidthList.join('\n'),
    uploadVarList.join('\n'),
    propsList.join('\n'),
    methodList.join('\n'),
    createdList.join('\n'),
    configDataList.join('\n'),
  )
  confGlobal = null
  return script
}

export function makeUpAuditJs(conf, type) {
  confGlobal = conf = JSON.parse(JSON.stringify(conf))
  const configDataList = []
  const dataList = []
  const ruleList = []
  const optionsList = []
  const propsList = []
  const childDragTableMobileClientWidthList = []  //子表在適配手機時的寬度列表
  const methodList = mixinAuditMethod(conf,type)
  const uploadVarList = []
  const createdList = []
  const childList = []
  //加入標題信息
  configDataList.push(`title: "${conf.formName}",`)
  conf.fields.forEach(el => {
    buildAttributesDetail(el, dataList, ruleList, optionsList, methodList, propsList, uploadVarList,createdList,conf,configDataList)
    if(el.tag =="entfrm-child-table-form"){
      childList.push(el.element)
    }
  })

  buildCheckBoxObjMethod(conf,methodList,childList)   //複選框
  buildCascaderObjMethod(conf, methodList,childList);  //級聯選擇
  buildTimeRangObjMethod(conf, methodList,childList);
  childList.forEach(childTable => {
    buildChildTableAttributesDetail(conf,childTable,dataList,methodList,optionsList,createdList, ruleList)
    childDragTableMobileClientWidthList.push(`${childTable.entityName}DragTableMobileClientWidth: 0,`)
  })
  const script = buildAuditExport(
    conf,
    type,
    dataList.join('\n'),
    ruleList.join('\n'),
    optionsList.join('\n'),
    childDragTableMobileClientWidthList.join('\n'),
    uploadVarList.join('\n'),
    propsList.join('\n'),
    methodList.join('\n'),
    createdList.join('\n'),
    configDataList.join('\n'),
  )
  confGlobal = null
  return script
}
export function makeUpRejectJs(conf, type) {
  confGlobal = conf = JSON.parse(JSON.stringify(conf))
  const configDataList = []
  const dataList = []
  const ruleList = []
  const optionsList = []
  const propsList = []
  const childDragTableMobileClientWidthList = []  //子表在適配手機時的寬度列表
  const methodList = mixinRejectMethod(conf,type)
  const uploadVarList = []
  const createdList = []
  const childList = []

  //加入標題信息
  configDataList.push(`title: "${conf.formName}",`)

  conf.fields.forEach(el => {
    buildAttributes(el, dataList, ruleList, optionsList, methodList, propsList, uploadVarList,createdList,conf,configDataList)
    if(el.tag =="entfrm-child-table-form"){
      childList.push(el.element)
    }
  })
  buildCheckBoxObjMethod(conf,methodList,childList)
  buildCascaderObjMethod(conf, methodList,childList);
  buildFillAutoObjMethod(conf, methodList);
  buildCheckPointObjMethod(conf, methodList);
  buildTimeRangObjMethod(conf, methodList,childList);
  childList.forEach(childTable => {
    buildChildTableAttributes(conf,childTable,dataList,methodList,optionsList,createdList, ruleList)
    childDragTableMobileClientWidthList.push(`${childTable.entityName}DragTableMobileClientWidth: 0,`)
  })

  dataList.push('dataSource: "pc",')
  const script = buildRejectExport(
    conf,
    type,
    dataList.join('\n'),
    ruleList.join('\n'),
    optionsList.join('\n'),
    childDragTableMobileClientWidthList.join('\n'),
    uploadVarList.join('\n'),
    propsList.join('\n'),
    methodList.join('\n'),
    createdList.join('\n'),
    configDataList.join('\n'),
  )
  confGlobal = null
  return script
}

function buildAttributesDetail(el, dataList, ruleList, optionsList, methodList, propsList, uploadVarList,createdList,conf,configDataList) {
  buildData(el, dataList,configDataList)
  buildRules(el, ruleList)
  if (el.dictType) {
    // debugger
    buildOptions(el, optionsList)
    // if (el.dataType === 'dynamic') {
    // const model = `${el.vModel}Options`
    const vModel = titleCase(el.vModel)
    buildOptionMethodDetail(`get${vModel}Options`, methodList,el,conf.formModel)
    if(el.tag!="el-cascader"){ buildCreatedMethod(`get${vModel}Options`, createdList)}
    // }
  }

  // if (el.tag === 'el-cascader') {
  //       buildOptions(el, optionsList)
  // }

  if (el.props && el.props.props) {
    buildProps(el, propsList)
  }

  if (el.action && el.tag === 'el-upload') {
    // uploadVarList.push(
    //   `${el.vModel}Action: '${el.action}',
    //   ${el.vModel}fileList: [],`
    // )
    methodList.push(buildBeforeUpload(el))
    methodList.push(buildFunctionUpload(conf,el))
    // if (!el['auto-upload']) {
    //   methodList.push(buildSubmitUpload(el))
    // }
    const multiple = el.multiple ? 'true' : 'false'
    uploadVarList.push(
      `
      // 文件上传参数
      upload: {
        // 是否显示弹出层
        open: false,
        fileList: [],
        fileNameList: [],
        // 弹出层标题
        title: "",
        multiple: ${multiple},
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: this.getHeader(),
        // 上传的地址
        url: this.getAppBaseApi() + "/system/fileInfo/uploadWithOos"
      },`
    )
  }
  if(el.tag=="entfrm-child-table-form"){
    conf.childTables.forEach(childs => {
      if(childs.tableName === el.childTableName) {
        uploadVarList.push(
          `
        // 文件上传参数
        upload${childs.className}: {
          // 是否显示弹出层
          open: false,
          fileList: [],
          fileNameList: [],
          // 弹出层标题
          title: "",
          multiple: false,
          // 是否禁用上传
          isUploading: false,
          // 设置上传的请求头部
          headers: this.getHeader(),
          // 上传的地址
          url: this.getAppBaseApi() + "/${childs.moduleName}/${conf.businessName}/import${childs.className}"
        },`)
      }
    });
  }
  if (el.children) {
    el.children.forEach(el2 => {
      buildAttributesDetail(el2, dataList, ruleList, optionsList, methodList, propsList, uploadVarList,createdList,conf,configDataList)
    })
  }
}

function buildAttributes(el, dataList, ruleList, optionsList, methodList, propsList, uploadVarList,createdList,conf,configDataList) {
  buildData(el, dataList,configDataList)
  buildRules(el, ruleList)

  if (el.dictType) {
    // debugger
    buildOptions(el, optionsList)
    // if (el.dataType === 'dynamic') {
      // const model = `${el.vModel}Options`
      const vModel = titleCase(el.vModel)
      if(vModel){
        buildOptionMethod(`get${vModel}Options`, methodList,el)
        buildCreatedMethod(`get${vModel}Options`, createdList)
      }

    // }/
  }

  // if (el.tag === 'el-cascader') {
  //       buildOptions(el, optionsList)
  // }

  if (el.props && el.props.props) {
    buildProps(el, propsList)
  }

  if (el.action && el.tag === 'el-upload') {
    // uploadVarList.push(
    //   `${el.vModel}Action: '${el.action}',
    //   ${el.vModel}fileList: [],`
    // )
    methodList.push(buildBeforeUpload(el))
    methodList.push(buildFunctionUpload(conf,el))
    // if (!el['auto-upload']) {
    //   methodList.push(buildSubmitUpload(el))
    // }
    //debugger
    const multiple = el.multiple ? 'true' : 'false'
    uploadVarList.push(
      `
      // 文件上传参数
      upload: {
        // 是否显示弹出层
        open: false,
        fileList: [],
        fileNameList: [],
        // 弹出层标题
        title: "",
        multiple: ${multiple},
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: this.getHeader(),
        // 上传的地址
        url: this.getAppBaseApi() + "/system/fileInfo/uploadWithOos"
      },`)
    }
  if(el.tag=="entfrm-child-table-form"){
    conf.childTables.forEach(childs => {
      if(childs.tableName === el.childTableName) {
        uploadVarList.push(
          `
        // 文件上传参数
        upload${childs.className}: {
          // 是否显示弹出层
          open: false,
          fileList: [],
          fileNameList: [],
          // 弹出层标题
          title: "",
          multiple: false,
          // 是否禁用上传
          isUploading: false,
          // 设置上传的请求头部
          headers: this.getHeader(),
          // 上传的地址
          url: this.getAppBaseApi() + "/${childs.moduleName}/${conf.businessName}/import${childs.className}"
        },`)
      }
    });
  }
  if (el.children) {
    el.children.forEach(el2 => {
      buildAttributes(el2, dataList, ruleList, optionsList, methodList, propsList, uploadVarList,createdList,conf)
    })
  }
}
//checkbox数据由对象转换为string
function buildCheckBoxStringifyMixinMethod(childTable, conf) {
  let checkboxStr = ''
  let dataList = []
  childTable.tableCols.forEach(column => {
    if(column.isAdd === '1' && column.dictType && column.dictType != ''&&column.htmlType != 'select'&&column.htmlType != 'radio'){
      dataList.push(`${conf.formModel}.${childTable.businessName}Lists[index].${column.javaField} = JSON.stringify(${conf.formModel}.${childTable.businessName}Lists[index].${column.javaField})`)
    }
    if(column.isAdd === '1'  && (column.htmlType == 'time-range'||column.htmlType == 'date-range')){
      dataList.push(`${conf.formModel}.${childTable.businessName}Lists[index].${column.javaField} = JSON.stringify(${conf.formModel}.${childTable.businessName}Lists[index].${column.javaField})`)
    }

  })
  if(dataList.length>0){
    checkboxStr = `  ${checkboxStr}
      for (let index in ${conf.formModel}.${childTable.businessName}Lists) {
          ${dataList.join("\n")}
      }`
  }
  return checkboxStr
}
//checkbox数据由string转换为对象
function buildCheckBoxParseMixinMethod(childTable, conf) {
  let checkboxObjStr = ''
  let checkboxObjs = []
  childTable.tableCols.forEach(column => {
    if(column.isAdd === '1' && column.htmlType == 'checkbox' && column.dictType && column.dictType != ''){
      checkboxObjs.push(`
        if(this.${conf.formModel}.${childTable.businessName}Lists[index].${column.javaField}){
          this.${conf.formModel}.${childTable.businessName}Lists[index].${column.javaField} = JSON.parse(this.${conf.formModel}.${childTable.businessName}Lists[index].${column.javaField})
        }`)
    }
  })
  if(checkboxObjs.length>0){
    checkboxObjStr = `for (let index in this.${conf.formModel}.${childTable.businessName}Lists) {
                          ${checkboxObjs.join("\n")}
                      }`
  }
  return checkboxObjStr
}

//子表導入時checkbox数据由string转换为对象
function buildImportCheckBoxParseMixinMethod(childTable, conf) {
  let checkboxObjStr = ''
  let checkboxObjs = []
  childTable.tableCols.forEach(column => {
    if(column.isAdd === '1' && column.htmlType == 'checkbox' && column.dictType && column.dictType != ''){
      checkboxObjs.push(`
        if(response.data[index].${column.javaField}){
          response.data[index].${column.javaField} = JSON.parse(response.data[index].${column.javaField})
        }`)
    }
  })
  if(checkboxObjs.length>0){
    checkboxObjStr = `for (let index in response.data) {
                          ${checkboxObjs.join("\n")}
                      }`
  }
  return checkboxObjStr
}
//Cascader
function buildCascaderMixinMethod(childTable, conf) {
  let checkboxObjStr = ''
  let checkboxObjs = []
  childTable.tableCols.forEach(column => {
    if(column.isAdd === '1' && column.htmlType == 'cascader' && column.dictType && column.dictType != ''){
      checkboxObjs.push(`
        if(this.${conf.formModel}.${childTable.businessName}Lists[index].${column.javaField}){
          this.${conf.formModel}.${childTable.businessName}Lists[index].${column.javaField} = JSON.parse(this.${conf.formModel}.${childTable.businessName}Lists[index].${column.javaField})
        }`)
    }
  })
  if(checkboxObjs.length>0){
    checkboxObjStr = `for (let index in this.${conf.formModel}.${childTable.businessName}Lists) {
                          ${checkboxObjs.join("\n")}
                      }`
  }
  return checkboxObjStr
}

//TimeRang
function buildTimeRangMixinMethod(childTable, conf) {
  let checkboxObjStr = ''
  let checkboxObjs = []
  childTable.tableCols.forEach(column => {
    if(column.isAdd === '1' && (column.htmlType == 'time-range' ||column.htmlType == 'date-range')){
      checkboxObjs.push(`if(this.${conf.formModel}.${childTable.businessName}Lists[index].${column.javaField}){
          this.${conf.formModel}.${childTable.businessName}Lists[index].${column.javaField} = JSON.parse(this.${conf.formModel}.${childTable.businessName}Lists[index].${column.javaField})
        }`)
    }
  })
  if(checkboxObjs.length>0){
    checkboxObjStr = `for (let index in this.${conf.formModel}.${childTable.businessName}Lists) {
                          ${checkboxObjs.join("\n")}
                      }`
  }
  return checkboxObjStr
}

function mixinMethod(conf,type) {
  const list = [];
  let masterCheckBoxStr = ''
  let masterCascaderStr = ''
  let checkboxStr = ''
  let dateRange = ''
  let timeRange = ''

  let childList =[];

  conf.fields.forEach(el => {
    if(el.tag === 'el-checkbox-group'){
      masterCheckBoxStr = masterCheckBoxStr + `
      if(${conf.formModel}.${el.vModel}){
        ${conf.formModel}.${el.vModel} = JSON.stringify(${conf.formModel}.${el.vModel})
      }\n`
    }else if(el.tag === 'el-cascader'){  //級聯
      masterCascaderStr = masterCascaderStr + `
      if(${conf.formModel}.${el.vModel}){
        ${conf.formModel}.${el.vModel} = JSON.stringify(${conf.formModel}.${el.vModel})
      } \n`
    }else if(el.tag === 'el-date-picker'&&el.type == 'daterange' ){  //日期範圍
      dateRange = dateRange + `
      if(${conf.formModel}.${el.vModel}){
        ${conf.formModel}.${el.vModel} = JSON.stringify(${conf.formModel}.${el.vModel})
      } \n`
    }else if(el.tag === 'el-time-picker'&&el["is-range"]){  //時間範圍
      timeRange = timeRange + `
      if(${conf.formModel}.${el.vModel}){
        ${conf.formModel}.${el.vModel} = JSON.stringify(${conf.formModel}.${el.vModel})
      } \n`
    }else if(el.tag === 'entfrm-child-table-form'){ //子表
      childList.push(el.element)
    }
  })
  childList.forEach(childTable => {
    checkboxStr = checkboxStr + buildCheckBoxStringifyMixinMethod(childTable,conf)
  })

  let subBtn=`handleSubmit: function() {
            this.$refs["${conf.formRef}"].validate(valid => {
              if (valid) {
                this.isDisable = true;
                const formData = JSON.parse(JSON.stringify(this.${conf.formModel}))
                ${masterCheckBoxStr}
                ${checkboxStr}
                ${masterCascaderStr}
                ${dateRange}
                ${timeRange}
                if (formData.id != undefined) {
                       edit${conf.className}AndStartProcess(${conf.formModel}).then(response => {
                          if (response.code === 0) {
                            this.msgSuccess(this.$t('tips.updateSuccess'));
                            this.closeForm();
                          }
                          else {
                            this.msgError(response.msg);
                          }
                          this.isDisable = false;
                        });
                }else {
                      add${conf.className}AndStartProcess(${conf.formModel}).then(response => {
                                  if (response.code === 0) {
                                    this.msgSuccess(this.$t('tips.createSuccess'));
                                    this.closeForm();
                                  }
                                  else {
                                    this.msgError(response.msg);
                                  }
                                  this.isDisable = false;
                                });
                }
                /**
                 * 提交的時候刪除被標記的需要刪除的附件
                 */
                if (this.${conf.formModel}.attachids) {
                  this.upload.fileNameList.forEach(item => {
                    if (item) {
                      delFileInfo(item);
                    }
                  })
                }
              }
            })
      }, `;
  let saveBtn=`submitForm() {
        this.$refs['${confGlobal.formRef}'].validate(valid => {
          if (valid) {
            this.isDisable = true;
            const ${conf.formModel} = JSON.parse(JSON.stringify(this.${conf.formModel}))
            ${masterCheckBoxStr}
            ${checkboxStr}
            ${masterCascaderStr}
            ${dateRange}
            ${timeRange}
            if(${conf.formModel}.id != undefined) {
              edit${conf.className}(${conf.formModel}).then(response => {
                if (response.code === 0) {
                  this.msgSuccess(this.$t('tips.updateSuccess'));
                  this.closeForm();
                } else {
                    this.msgError(response.msg);
                }
                this.isDisable = false;
              });
            }else{
              add${conf.className}(${conf.formModel}).then(response => {
                if (response.code === 0) {
                    this.msgSuccess(this.$t('tips.createSuccess'));
                    this.closeForm();
                } else {
                    this.msgError(response.msg);
                }
                this.isDisable = false;
              });
            }
            /**
             * 提交的時候刪除被標記的需要刪除的附件
             */
            if (this.${conf.formModel}.attachids) {
              this.upload.fileNameList.forEach(item => {
                if (item) {
                  delFileInfo(item);
                }
              })
            }
          }
        })
      },`

  let returnBtn=`closeForm(){
          //关闭子页面
          if (this.$store.state.settings.tagsView) {
            this.$router.go(-1) // 返回
            this.$store.state.tagsView.visitedViews.splice(this.$store.state.tagsView.visitedViews.findIndex(item =>
              item.path === this.$route.path), 1)
            this.$router.push(this.$store.state.tagsView.visitedViews[this.$store.state.tagsView.visitedViews
              .length - 1].path)
          }else{
            parent.postMessage("closeCurrentTabMessage",'*');
          }
        },`


  if(conf.isPreView){
    saveBtn=`submitForm: function() {
                 this.$message({ showClose: true, message: "預覽狀態保存按鈕不可用", type: "success", offset: 50});
              }, `
    subBtn = `handleSubmit: function() {
                 this.$message({ showClose: true, message: "預覽狀態提交按鈕不可用", type: "success", offset: 50});
              }, `
    returnBtn = `closeForm(){
                      this.$parent.$parent.$parent.$parent.$parent.$parent.closeForm();
                 },`
  }

  const minxins = {
      file: confGlobal.formBtns ? {
        submitForm: saveBtn,
        handleSubmit: subBtn,
        resetForm: `resetForm() {
        this.$refs['${confGlobal.formRef}'].resetFields()
      },`,
        closeForm:returnBtn
      } : null,
      dialog: {
        onOpen: 'onOpen() {},',
        onClose: `onClose() {
        this.$refs['${confGlobal.formRef}'].resetFields()
      },`,
        close: `close() {
        this.$emit('update:visible', false)
      },`,
        handelConfirm: `handelConfirm() {
        this.$refs['${confGlobal.formRef}'].validate(valid => {
          if(!valid) return
          this.close()
        })
      },`
      }
    }

  const methods = minxins[type]
  if (methods) {
    Object.keys(methods).forEach(key => {
      list.push(methods[key])
    })
  }

  return list
}

function mixinDetailMethod(conf,type) {
  const list = [];
  const minxins = {
    file: {  closeForm: `closeForm(){
          //关闭子页面
          if (this.$store.state.settings.tagsView) {
            this.$router.go(-1) // 返回
            this.$store.state.tagsView.visitedViews.splice(this.$store.state.tagsView.visitedViews.findIndex(item =>
              item.path === this.$route.path), 1)
            this.$router.push(this.$store.state.tagsView.visitedViews[this.$store.state.tagsView.visitedViews
              .length - 1].path)
          }else{
            parent.postMessage("closeCurrentTabMessage",'*');
          }
        },`,
        handleTrack: `handleTrack() {
          this.imgUrl = this.getAppBaseApi() + '/activiti/task/track/' + this.${conf.formModel}.processId,
            this.showImgDialog = true
        },`,
        statusFormat: `statusFormat(row, column) {
          return this.selectDictLabel(this.auditStatus, row.status);
        },`
      }
    }

  const methods = minxins[type]
  if (methods) {
    Object.keys(methods).forEach(key => {
      list.push(methods[key])
    })
  }

  return list
}

function mixinRejectMethod(conf,type) {
  const list = [];
  let masterCheckBoxStr = ''
  let masterCascaderStr = ''
  let checkboxStr = ''
  let dateRange = ''
  let timeRange = ''
  let checkboxObjStr = buildCheckBoxObjStr(conf)
  let cascaderObjStr = buildCascaderObjStr(conf)
  let childList = []
  conf.fields.forEach(el => {
    if(el.tag === 'el-checkbox-group'){
      masterCheckBoxStr = masterCheckBoxStr + `${conf.formModel}.${el.vModel} = JSON.stringify(${conf.formModel}.${el.vModel}) \n`
    }else if(el.tag === 'el-cascader'){  //級聯
      masterCascaderStr = masterCascaderStr + `${conf.formModel}.${el.vModel} = JSON.stringify(${conf.formModel}.${el.vModel}) \n`
    }else if(el.tag === 'el-date-picker'&&el.type == 'daterange' ){  //日期範圍
      dateRange = dateRange + `${conf.formModel}.${el.vModel} = JSON.stringify(${conf.formModel}.${el.vModel}) \n`
    }else if(el.tag === 'el-time-picker'&&el["is-range"]){  //時間範圍
      timeRange = timeRange + `${conf.formModel}.${el.vModel} = JSON.stringify(${conf.formModel}.${el.vModel}) \n`
    }else if(el.tag === 'entfrm-child-table-form'){ //子表
      childList.push(el.element)
    }
  })

  childList.forEach(childTable => {
    checkboxStr = checkboxStr + buildCheckBoxStringifyMixinMethod(childTable,conf)
  })
  const minxins = {
    file: {
      submitForm: `submitForm() {
        this.$refs['${confGlobal.formRef}'].validate(valid => {
          if (valid) {
            this.isDisable=true;
            const ${conf.formModel} = JSON.parse(JSON.stringify(this.${conf.formModel}))
            ${masterCheckBoxStr}
            ${checkboxStr}
            ${masterCascaderStr}
            ${dateRange}
            ${timeRange}
            if(${conf.formModel}.id != undefined) {
              edit${conf.className}(${conf.formModel}).then(response => {
                if (response.code === 0) {
                  this.msgSuccess(this.$t('tips.updateSuccess'));
                  this.closeForm();
                } else {
                    this.msgError(response.msg);
                }
                this.isDisable=false;
              });
            }else{
              add${conf.className}(${conf.formModel}).then(response => {
                if (response.code === 0) {
                    this.msgSuccess(this.$t('tips.createSuccess'));
                    this.closeForm();
                } else {
                    this.msgError(response.msg);
                }
                this.isDisable=false;
              });
            }
            /**
             * 提交的時候刪除被標記的需要刪除的附件
             */
            if (this.${conf.formModel}.attachids) {
              this.upload.fileNameList.forEach(item => {
                if (item) {
                  delFileInfo(item);
                }
              })
            }
          }
        })
      },`,
      closeForm: `closeForm(){
          //关闭子页面
          if (this.$store.state.settings.tagsView) {
            this.$router.go(-1) // 返回
            this.$store.state.tagsView.visitedViews.splice(this.$store.state.tagsView.visitedViews.findIndex(item =>
              item.path === this.$route.path), 1)
            this.$router.push(this.$store.state.tagsView.visitedViews[this.$store.state.tagsView.visitedViews
              .length - 1].path)
          }else{
            parent.postMessage("closeCurrentTabMessage",'*');
          }
        },`,
      statusFormat: `statusFormat(row, column) {
          return this.selectDictLabel(this.auditStatus, row.status);
        },`,
      handleSubmit: `handleSubmit: function() {
            this.$refs["${conf.formRef}"].validate(valid => {
              if (valid) {
                this.isDisable = true;
                const formData = JSON.parse(JSON.stringify(this.${conf.formModel}))
                ${masterCheckBoxStr}
                ${checkboxStr}
                ${masterCascaderStr}
                 ${dateRange}
                ${timeRange}
               edit${conf.className}AndResubmitProcess(${conf.formModel}).then(response => {
                  if (response.code === 0) {
                    this.msgSuccess(this.$t('tips.updateSuccess'));
                    this.closeForm();
                  }
                  else {
                    this.msgError(response.msg);
                  }
                  this.isDisable = false;
                });
                /**
                 * 提交的時候刪除被標記的需要刪除的附件
                 */
                if (this.${conf.formModel}.attachids) {
                  this.upload.fileNameList.forEach(item => {
                    if (item) {
                      delFileInfo(item);
                    }
                  })
                }
              }
            })
      }, `,
      handleTask: `handleTask: function (pass) {
          this.$refs["${conf.formRef}"].validate(valid => {
              this.isDisable=true;
              this.${conf.formModel}.pass = pass
              checkTask(this.${conf.formModel}).then(response => {
                if (response.code === 0) {
                  this.msgSuccess(this.$t('tips.operationSuccessful'));
                  this.closeForm();
                } else {
                  this.msgError(response.msg);
                }
                this.isDisable=false;
              });
          });
        },`
    }
  }
  const methods = minxins[type]
  if (methods) {
    Object.keys(methods).forEach(key => {
      list.push(methods[key])
    })
  }

  return list
}

function mixinAuditMethod(conf,type) {
  const list = [];

  let masterCheckBoxStr = ''
  let masterCascaderStr = ''
  let checkboxStr = ''
  let dateRange = ''
  let timeRange = ''

  let childList =[];

  conf.fields.forEach(el => {
    if(el.tag === 'el-checkbox-group'){
      masterCheckBoxStr = masterCheckBoxStr + `
      if(${conf.formModel}.${el.vModel}){
        ${conf.formModel}.${el.vModel} = JSON.stringify(${conf.formModel}.${el.vModel})
      }
      \n`
    }else if(el.tag === 'el-cascader'){  //級聯
      masterCascaderStr = masterCascaderStr + `
      if(${conf.formModel}.${el.vModel}){
        ${conf.formModel}.${el.vModel} = JSON.stringify(${conf.formModel}.${el.vModel})
      }\n`
    }else if(el.tag === 'el-date-picker'&&el.type == 'daterange' ){  //日期範圍
      dateRange = dateRange + `
      if(${conf.formModel}.${el.vModel}){
        ${conf.formModel}.${el.vModel} = JSON.stringify(${conf.formModel}.${el.vModel})
      }\n`
    }else if(el.tag === 'el-time-picker'&&el["is-range"]){  //時間範圍
      timeRange = timeRange + `
      if(${conf.formModel}.${el.vModel}){
        ${conf.formModel}.${el.vModel} = JSON.stringify(${conf.formModel}.${el.vModel})
      }\n`
    }else if(el.tag === 'entfrm-child-table-form'){ //子表
      childList.push(el.element)
    }
  })
  childList.forEach(childTable => {
    checkboxStr = checkboxStr + buildCheckBoxStringifyMixinMethod(childTable,conf)
  })

  let ifUpdate = false;
  let conditionUpdate = ``
  conf.fields.forEach(el => {
    if(el.currentorder!=""&&el.currentorder!=undefined){
      ifUpdate = true
      conditionUpdate=conditionUpdate+'this.formData.currentorder=='+el.currentorder+' || ';
    }
  })
  if (conditionUpdate !='')
  {
    conditionUpdate=conditionUpdate+'  false '
  }

  let urlStr=``;
  if(ifUpdate)
  {
    urlStr=` if(pass == 0 && (${conditionUpdate}) ){
            if (valid) {
              const formData = JSON.parse(JSON.stringify(this.formData));
              const leaveDto = JSON.parse(JSON.stringify(this.formData));
                formData.leaveDto = leaveDto;
                ${masterCheckBoxStr}
                ${checkboxStr}
                ${masterCascaderStr}
                ${dateRange}
                ${timeRange}
              updateAndCheck(formData).then(response => {
                if (response.code === 0) {
                  this.msgSuccess(this.$t('tips.operationSuccessful'));
                  this.closeForm();
                }
                else {
                  this.msgError(response.msg);
                }
                this.isDisable = false;
              });
              }else{
              this.isDisable = false;
            }

  }else{
  checkTask(this.${conf.formModel}).then(response => {
                if (response.code === 0) {
                  this.msgSuccess(this.$t('tips.operationSuccessful'));
                  this.closeForm();
                } else {
                  this.msgError(response.msg);
                }
                this.isDisable=false;
              });
  }`
  }else{
    urlStr=` checkTask(this.${conf.formModel}).then(response => {
                if (response.code === 0) {
                  this.msgSuccess(this.$t('tips.operationSuccessful'));
                  this.closeForm();
                } else {
                  this.msgError(response.msg);
                }
                this.isDisable=false;
              });`
  }
  const minxins = {
    file: {  closeForm: `closeForm(){
          //关闭子页面
          if (this.$store.state.settings.tagsView) {
            this.$router.go(-1) // 返回
            this.$store.state.tagsView.visitedViews.splice(this.$store.state.tagsView.visitedViews.findIndex(item =>
              item.path === this.$route.path), 1)
            this.$router.push(this.$store.state.tagsView.visitedViews[this.$store.state.tagsView.visitedViews
              .length - 1].path)
          }else{
            parent.postMessage("closeCurrentTabMessage",'*');
          }
        },`,
        handleTrack: `handleTrack() {
          this.imgUrl = this.getAppBaseApi() + '/activiti/task/track/' + this.${conf.formModel}.processId,
            this.showImgDialog = true
        },`,
        statusFormat: `statusFormat(row, column) {
          return this.selectDictLabel(this.auditStatus, row.status);
        },`,
        handleTask: `handleTask: function (pass) {
          this.$refs["${conf.formRef}"].validate(valid => {
          this.isDisable=true;
            if (pass==0||(pass==1&&this.${conf.formModel}.comment!=null)) {
              this.${conf.formModel}.pass = pass
              ${urlStr}
            }else{
              this.isDisable=false;
              this.msgInfo(this.$t('table.activity.inputApprovalOpinions'));
            }
          });
        },
        `
      }
    }

  const methods = minxins[type]
  if (methods) {
    Object.keys(methods).forEach(key => {
      list.push(methods[key])
    })
  }

  return list
}
function buildData(el, dataList,configDataList) {
  if(el.layout=="signFormItem"){  //簽核節點
     if(el.required){
       let defaultValue
       if (typeof (el.defaultValue) === 'string' && !el.multiple) {
         defaultValue = `'${el.defaultValue}'`
       } else {
         defaultValue = `${JSON.stringify(el.defaultValue)}`
       }
       dataList.push(`${el.modelNo}: ${defaultValue},`)
     }

    configDataList.push(`${el.modelNo}: "${el.label}",`)
    configDataList.push(`${el.modelNo}_required: ${el.required},`)
  }else if("childTableItem" === el.layout ){
    dataList.push(`${el.element.entityName}Lists: [],`)
  }else if("labelItem" === el.layout) {
    if (el.isLabelDynamic) {
      configDataList.push(`${el.labelDynamicKey}_label: \`${el.label}\`,`)
      configDataList.push(`${el.labelDynamicKey}_text: \`${el.textarea}\`,`)
    }
  }else {
      if (el.vModel === undefined) return
      let defaultValue
      if (typeof (el.defaultValue) === 'string' && !el.multiple) {
        defaultValue = `'${el.defaultValue}'`
      } else {
        defaultValue = `${JSON.stringify(el.defaultValue)}`
      }
      // if(conf.options){
      //   dataList.push(`${conf.vModel}Options: [],`)
      // }
      if (el.action && el.tag === 'el-upload') {
        defaultValue = `""`
      }
      dataList.push(`${el.vModel}: ${defaultValue},`)
  }
}
function buildChildData(conf, dataList) {
  let defaultValue
  if (conf.htmlType == 'checkbox'||conf.htmlType == 'cascader'||conf.htmlType == 'date-range') {
    defaultValue = `[]`
  } else if(conf.htmlType == 'slider'||conf.htmlType == 'number'||conf.htmlType == 'rate'){
    defaultValue = 0
  } else if(conf.htmlType == 'el-switch'){
    defaultValue = "false"
  }  else if (typeof (conf.defValue) === 'string') {
    defaultValue = `'${conf.defValue}'`
  } else if (conf.defValue == null) {
    defaultValue = `''`
  } else{
    defaultValue = `${JSON.stringify(conf.defValue)}`
  }
  dataList.push(`${conf.javaField}: ${defaultValue}`)
}
function buildRules(conf, ruleList) {
  if(conf.layout=="signFormItem") {  //簽核節點
      // if(conf.required){
            const rules = []
            const type = isArray(conf.defaultValue) ? 'type: \'array\',' : ''
            let message = isArray(conf.defaultValue) ? `請至少選擇一個${conf.label}` : conf.placeholder
            if (message === undefined) message = `${conf.label}不能為空`
            rules.push(`{ required: ${conf.required}, ${type} message: '${message}', trigger: '${"change"}' }`)
          ruleList.push(`${conf.modelNo}: [${rules.join(',')}],`)
      // }
  }else if("childTableItem" === conf.layout ){
    buildChildRules(conf,ruleList)
  }else{
        if (conf.vModel === undefined) return
        const rules = []
        if (trigger[conf.tag]) {
          if (conf.required) {
            const type = isArray(conf.defaultValue) ? 'type: \'array\',' : ''
            let message = isArray(conf.defaultValue) ? `請至少選擇一個${conf.label}` : conf.placeholder
            if (message === undefined) message = `${conf.label}不能為空`
            rules.push(`{ required: true, ${type} message: '${message}', trigger: '${trigger[conf.tag]}' }`)
          }
          if (conf.regList && isArray(conf.regList)) {
            conf.regList.forEach(item => {
              if (item.pattern) {
                rules.push(`{ pattern: ${eval(item.pattern)}, message: '${item.message}', trigger: '${trigger[conf.tag]}' }`)
              }
            })
          }
          ruleList.push(`${conf.vModel}: [${rules.join(',')}],`)
        }
  }
}
//設置從表表單驗證規則
function buildChildRules(conf, ruleList) {
  let childTable = conf.element
  childTable.tableCols.forEach(column => {
    const rules = []
    if(column.isAdd == '1'){
      if (column.isRequired === '1') {
        if (trigger[htmlTypeConvertForm[column.htmlType]]) {
          const type = htmlTypeConvertForm[column.htmlType] === 'el-checkbox-group' ? 'type: \'array\',' : ''
          let message = htmlTypeConvertForm[column.htmlType] === 'el-checkbox-group' ? `請至少選擇一個${column.columnComment}` : column.placeholder
          if (message === undefined) message = `${column.columnComment}不能為空`
          rules.push(`{ required: true, ${type} message: '${message}', trigger: '${trigger[htmlTypeConvertForm[column.htmlType]]}' }`)
        }
      }
    }
    let regList = conf.childRegMap[column.javaField]
    if (regList) {
      regList.forEach(item => {
        if (item.pattern) {
          rules.push(`{ pattern: ${eval(item.pattern)}, message: '${item.message}', trigger: '${trigger[htmlTypeConvertForm[column.htmlType]]}' }`)
        }
      })
    }
    if(rules.length>0){
      ruleList.push(`${childTable.entityName}_${column.javaField}: [${rules.join(',')}],`)
    }
  })
}



function buildOptions(conf, optionsList) {
  if (conf.vModel === undefined) return
    // debugger
  if (conf.dataType === 'dynamic') {
     conf.options = []
    }
  // const str = `${conf.vModel}Options: ${JSON.stringify(conf.options)},`
  const str = `${conf.vModel}Options: [],`
  optionsList.push(str)
}

function buildProps(conf, propsList) {
  // debugger
  if (conf.dataType === 'dynamic') {
    conf.valueKey !== 'value' && (conf.props.props.value = conf.valueKey)
    conf.labelKey !== 'label' && (conf.props.props.label = conf.labelKey)
    conf.childrenKey !== 'children' && (conf.props.props.children = conf.childrenKey)
  }
  const str = `${conf.vModel}Props: ${JSON.stringify(conf.props.props)},`
  propsList.push(str)
}

function buildBeforeUpload(conf) {
  const unitNum = units[conf.sizeUnit];
  let rightSizeCode = '';
  let acceptCode = '';
  const returnList = []
  if (conf.fileSize) {
    rightSizeCode = `let isRightSize = file.size / ${unitNum} < ${conf.fileSize}
    if(!isRightSize){
      this.$message.error('文件大小超过 ${conf.fileSize}${conf.sizeUnit}')
    }`
    returnList.push('isRightSize')
  }
  if (conf.accept) {
    // console.log(conf)
    acceptCode = `let isAccept = new RegExp('${conf.accept}'.replaceAll(',','|')).test(file.name)
    if(!isAccept){
      this.$message.error('应该选择${conf.accept}类型的文件')
    }`
    returnList.push('isAccept')
  }
  const str = `${conf.vModel}BeforeUpload(file) {
    ${rightSizeCode}
    ${acceptCode}
    return ${returnList.join('&&')}
  },`
  return returnList.length ? str : ''
}

function buildChildTableBeforeUpload(childTable,childTableElement) {
  const unitNum = units[childTableElement.sizeUnit];
  let rightSizeCode = '';
  let acceptCode = '';
  const returnList = []
  if (childTableElement.fileSize) {
    rightSizeCode = `let isRightSize = file.size / ${unitNum} < ${childTableElement.fileSize}
    if(!isRightSize){
      this.$message.error('文件大小超过 ${childTableElement.fileSize}${childTableElement.sizeUnit}')
    }`
    returnList.push('isRightSize')
  }
  if (childTableElement.accept) {
    // console.log(childTableElement)
    acceptCode = `let isAccept = new RegExp('${childTableElement.accept}'.replaceAll(',','|')).test(file.name)
    if(!isAccept){
      this.$message.error('应该选择${childTableElement.accept}类型的文件')
    }`
    returnList.push('isAccept')
  }
  const str = `beforeUpload${childTable.className}(file) {
    ${rightSizeCode}
    ${acceptCode}
    return ${returnList.join('&&')}
  },`
  return returnList.length ? str : ''
}
function buildFunctionUpload(conf,el) {
  const str = `
  // 文件上传成功处理
  uploadsucces(response, file, fileList) {
    if (response.data.name != undefined && response.data.name != "undefined") {
      this.upload.fileList.push({name: file.name, url: response.data.name});
      this.${conf.formModel}.${el.vModel} += response.data.name + ",";
    }
  },
  handleChange(file, fileList) {

  },
  handleExceed(file, fileList) {

  },
  handleRemove(file, fileList) {
    this.$emit("delUploadImage", file.name);
    const index = this.upload.fileList.indexOf(file);
    this.upload.fileList.splice(index, 1);
    if(this.${conf.formModel}.${el.vModel}){
          this.${conf.formModel}.${el.vModel} = this.${conf.formModel}.${el.vModel}.replace(file.url + ",", "");
    }
    this.upload.fileNameList.push(file.url);
    // delFileInfo(file.url);
  },
  handlePreview(file) {
    previewFileOos(file.url)
  },`
  return str
}

function buildSubmitUpload(conf) {
  const str = `submitUpload() {
    this.$refs['${conf.vModel}'].submit()
  },`
  return str
}


function buildOptionMethodDetail(methodName, methodList,conf,formModel) {
  if (conf.tag === 'el-cascader') {
    const str = `${methodName}(key) {
      if(!key){ key = "${conf.dictType}"}
      this.getDicts(key).then(response => {
        if (response.data.length > 0) {
          response.data.forEach(element => {
            if(element.value == this.${formModel}.${conf.vModel}Value[0] ) {
              this.${formModel}.${conf.vModel}Value.splice(0,1)
              if(this.${conf.vModel}Value!=undefined){this.${conf.vModel}Value += "/"+ element.label
              }else{this.${conf.vModel}Value = element.label}
              if(this.${formModel}.${conf.vModel}Value!=null&&this.${formModel}.${conf.vModel}Value.length>0){
                this.${methodName}(element.value)
              }
            }
          });
        }
        this.${conf.vModel}Options = response.data;
        response.data.forEach(element => {
          this.getOptions(element);
        });
      });
    },`
    methodList.push(str)
  }else{
    const str = `${methodName}() {
        this.getDicts("${conf.dictType}").then(response => {
          this.${conf.vModel}Options = response.data;
      });
    },`
    methodList.push(str)

  }
}

function buildOptionMethod(methodName, methodList,conf) {
  if (conf.tag === 'el-cascader') {
    const str = `${methodName}() {
        this.getDicts("${conf.dictType}").then(response => {
          this.${conf.vModel}Options = response.data;
            if(response.data.length > 0){
              response.data.forEach(element => {
                this.getOptions(element);
              });
            }
      });
    },`
    methodList.push(str)
  }else{
    const str = `${methodName}() {
        this.getDicts("${conf.dictType}").then(response => {
          this.${conf.vModel}Options = response.data;
      });
    },`
    methodList.push(str)

  }
}

function buildChildOptionMethod(childTable, methodList,conf) {
  childTable.tableCols.forEach(column => {
    let methodName = 'get' + titleCase(column.javaField) + childTable.className + 'Options'
    if(column.isAdd === '1' && column.dictType && column.dictType != ''){
        if (column.htmlType === 'cascader') {
          const str = `${methodName}() {
          this.getDicts("${column.dictType}").then(response => {
            this.${column.javaField}${childTable.className}Options = response.data;
              if(response.data.length > 0){
                response.data.forEach(element => {
                  this.getOptions(element);
                });
              }
            });
          },`
          methodList.push(str)
        }else{
          const str = `${methodName}() {
              this.getDicts("${column.dictType}").then(response => {
                this.${column.javaField}${childTable.className}Options = response.data;
            });
          },`
          methodList.push(str)
        }
    }
  })
}
function buildChildOptionMethodDetail(childTable, methodList,conf) {
  childTable.tableCols.forEach(column => {
    let dataStr = ""
    if(column.isAdd === '1' && column.dictType && column.dictType != ''){
      let methodName = 'get' + titleCase(column.javaField) +childTable.className+ 'Options'
      let changeSelect = ""
      let cascaderStr = ""
      if(column.htmlType === "select"||column.htmlType === "radio"){
        changeSelect = `this.${column.javaField}${childTable.className}Options.forEach(item=>{
                            this.${conf.formModel}.${childTable.entityName}Lists.forEach((selectInfo,n)=>{
                                if(item.value==selectInfo.${column.javaField}){
                                       // this.${conf.formModel}.${childTable.entityName}Lists[n].${column.javaField}Value = item.label
                                       this.$set(this.${conf.formModel}.${childTable.entityName}Lists[n],'${column.javaField}Value', item.label)
                                }
                            })
                         })`
      }
      if(column.htmlType === "checkbox"){
        changeSelect = `this.${conf.formModel}.${childTable.entityName}Lists.forEach((selectInfo,n)=>{
                            if(selectInfo.${column.javaField}){
                                  let selectValue = JSON.parse(JSON.stringify(selectInfo.${column.javaField})).join()
                                  this.${column.javaField}${childTable.className}Options.forEach(item=>{
                                      selectValue = selectValue.replace(item.value, item.label);
                                  })
                                  this.$set(this.${conf.formModel}.${childTable.entityName}Lists[n],'${column.javaField}Value', selectValue)

                            }
                         })`
      }
      if(column.htmlType === "cascader"){
        cascaderStr = ` ${column.javaField}${childTable.className}Format(rowColumn) {
                                let value = JSON.parse(JSON.stringify(rowColumn))
                                let result = ""
                                if(value.length > 0){
                                    this.${column.javaField}${childTable.className}Options.forEach(item => {
                                        if(item.value == value[0]){
                                              this.${column.javaField}${childTable.className}Value = item.label
                                              value.splice(0, 1)
                                              if(value.length>0){
                                                  result = this.${column.javaField}${childTable.className}Cascader(item.children,value,result)
                                              }
                                        }
                                    })
                                }
                                return this.${column.javaField}${childTable.className}Value;
                            },
                            ${column.javaField}${childTable.className}Cascader(item,value,result) {
                                let returnInfo = ''
                                if(item){
                                    item.forEach(childItem => {
                                        if(childItem.value == value[0]){
                                            this.${column.javaField}${childTable.className}Value += "/" + childItem.label
                                            value.splice(0, 1)
                                            if(value.length>0){
                                                this.${column.javaField}${childTable.className}Cascader(childItem.children,value)
                                            }
                                            if(value.length>0){
                                                let childReturn = this.${column.javaField}${childTable.className}Cascader(childItem.children,value,result)
                                                if(childReturn){
                                                    returnInfo = childReturn
                                                }
                                            }else{
                                                returnInfo = result
                                            }
                                        }
                                    })
                                }
                                if(returnInfo!=''){
                                    return  returnInfo
                                }
                            },`
      }
      if(column.htmlType=="cascader"){
        changeSelect = `if(response.data.length > 0){
                            response.data.forEach(element => {
                            this.getOptions(element);
                             })
                         }`
      }
      const str = `${methodName}() {
          this.getDicts("${column.dictType}").then(response => {
                this.${column.javaField}${childTable.className}Options = response.data;
                ${changeSelect}
            });
          },`

      methodList.push(cascaderStr)
      methodList.push(str)
    }
    if(column.htmlType === "time-range"||column.htmlType === "date-range"){
      dataStr = `${column.javaField}${childTable.className}Format(rowColumn) {
                       let returnInfo = ""
                       if(rowColumn){
                          returnInfo = JSON.parse(JSON.stringify(rowColumn)).join("  至  ")
                       }
                       return returnInfo
                   },`
    }
    methodList.push(dataStr)
  })
}


//添加子表表單控件的集合
function buildChildOptions(childTable, optionsList,conf) {

  childTable.tableCols.forEach(column => {
    if(column.isAdd === '1' && column.dictType && column.dictType != ''){
      optionsList.push(`${column.javaField}${childTable.className}Options: [],`)
    }
  })
}
function buildCheckBoxObjMethod(conf,methodList,childList){
  let masterCheckBoxStr = ''
  let childCheckboxStr = ''
  childList.forEach(childTable => {
    childCheckboxStr = childCheckboxStr + buildCheckBoxParseMixinMethod(childTable,conf)
  })

  conf.fields.forEach(el => {
     if(el.tag === 'el-checkbox-group'){
      masterCheckBoxStr = masterCheckBoxStr + `
      if(this.${conf.formModel}.${el.vModel}){
        this.${conf.formModel}.${el.vModel} = JSON.parse(this.${conf.formModel}.${el.vModel})
      }\n`
     }
  })
  const str = `checkBoxParse() {
                ${masterCheckBoxStr}\n
               ${childCheckboxStr}
              },
  `
  methodList.push(str)
}
function buildTimeRangObjMethod(conf,methodList,childList){
  let masterTimeRangStr = ''
  let childTimeRangStr = ''
  childList.forEach(childTable => {
    childTimeRangStr = childTimeRangStr + buildTimeRangMixinMethod(childTable,conf)
  })
  conf.fields.forEach(el => {
    if((el.tag === 'el-date-picker'&&el.type==='daterange')||(el.tag === 'el-time-picker'&&el["is-range"])){
      masterTimeRangStr = masterTimeRangStr + `
      if(this.${conf.formModel}.${el.vModel}){
        this.${conf.formModel}.${el.vModel} = JSON.parse(this.${conf.formModel}.${el.vModel})
      }\n`
    }
  })
  const str = `timeRangParse() {
                ${masterTimeRangStr}\n
               ${childTimeRangStr}
              },
  `
  methodList.push(str)
}

function buildCascaderObjMethod(conf,methodList,childList){
  let masterCascaderStr = ''
  let childCheckboxStr = ''
  childList.forEach(childTable => {
    childCheckboxStr = childCheckboxStr + buildCascaderMixinMethod(childTable,conf)
  })
   conf.fields.forEach(el => {
     if(el.tag === 'el-cascader'){
      masterCascaderStr = masterCascaderStr + `
      if(this.${conf.formModel}.${el.vModel}){
        this.${conf.formModel}.${el.vModel} = JSON.parse(this.${conf.formModel}.${el.vModel})
      }\n`
     }
  })
  const str = `cascaderParse() {
                ${masterCascaderStr}
                ${childCheckboxStr}
              },
  `
  methodList.push(str)
}
function buildFillAutoObjMethod(conf,methodList){

  // debugger
  //onChange事件帶出的方法集合
  let fillChangeMethods = {  };
  //所有需要產生@Change方法的控件
  let onChangeEls = []
  function addOnChangeStage(el,func){
    if(!el.onChangeStages){
      el.onChangeStages = []
    }
    el.onChangeStages.push(func)

  }
  conf.fields.forEach(el => {
    if (el.onchange !== '' && el.onchange !== undefined) {
      fillChangeMethods[`${el.onchange}`] = { key: el, els: [] }
      addOnChangeStage(el,(element)=>{
        let columnStr = ''
        let columnClearStr = ''
        fillChangeMethods[element.onchange].els.forEach(el => {
          columnStr += `this.${conf.formModel}.${el.el.vModel} = response.data.${el.fillColumn} \n`
          columnClearStr += `this.${conf.formModel}.${el.el.vModel} = '' \n`
        })
        if (element.onchange === 'getInfoUserByEmpno') {
          columnStr += `this.${conf.formModel}.applyFactoryId =  response.data.factoryid \n`
          columnClearStr += `this.${conf.formModel}.applyFactoryId =  ''`
        }
        let fillChangeMethodStr = `
          this.${element.onchange}(data).then(response => {
           if (response.code !== 0) {
              this.msgError(response.msg);
            } else {
              if(response.data != null){
                ${columnStr}
              }else{
                ${columnClearStr}
              }
            }
         });
         `
        return fillChangeMethodStr;
      })
    }
    if (conf.onChangeCalculateExpressionsMap[el.vModel]) {
      el.onChangeCalculateExpressions = conf.onChangeCalculateExpressionsMap[el.vModel]
      addOnChangeStage(el,(element)=>{
        var calculateExpressionChangeMethodStrs = ''
        el.onChangeCalculateExpressions.forEach(expression=>{
          calculateExpressionChangeMethodStrs += `
          if(${expression.isNumberValueCondition}){
            this.${conf.formModel}.${expression.key} = (${expression.calculateExpressions}).toFixed(${expression.calculateExpressionsDecimalScale})
          }else {
            this.${conf.formModel}.${expression.key} = ''
          } \n
          `
        })
        return calculateExpressionChangeMethodStrs
      })
    }
    if(el.onChangeStages){
      onChangeEls.push(el)
    }
  })
  conf.fields.forEach(el => {
    if (el.fillAuto !== '' && el.fillAuto !== undefined && el.fillAuto.length > 0) {
      if(fillChangeMethods[`${el.fillAuto[0]}`])
      {
        fillChangeMethods[`${el.fillAuto[0]}`].els.push({ 'fillColumn': `${el.fillAuto[1]}`, 'el': el })
      }
    }
  })
  onChangeEls.forEach(el =>{
    var changeMethodStr = `${el.vModel}_onchange(data){ \n`
    el.onChangeStages.forEach(stage => {
      let str = stage(el)
      changeMethodStr += str? str :''
    })
    changeMethodStr += ' \n},'
    methodList.push(changeMethodStr)
  })
}
function buildCheckPointObjMethod(conf,methodList){
  const methods = `onSignFormSelected(selectEmp,modelNo,modelName){
                      this.$set(this.${conf.formModel},modelNo,selectEmp.empNo)
                      this.$set(this.${conf.formModel},modelName,selectEmp.empName)
                  },`;
  methodList.push(methods);
}
function buildChildListMethod(childTable, methodList,conf) {
  const dataList = []
  childTable.tableCols.forEach(column => {
    if(column.isAdd === '1'){
      buildChildData(column, dataList)
    }
  })
  var childTableElement = conf.fields.find(item => item.childTableName && item.childTableName === childTable.tableName)
  let buildChildOnChangeMethodObject = buildChildOnChangeMethod(childTableElement);
  let childTableShowSummaryStr = buildChildTableShowSummaryObjMethod(childTableElement);
  let childCheckboxStr = buildImportCheckBoxParseMixinMethod(childTable,conf)
  var beforeUploadStr = buildChildTableBeforeUpload(childTable,childTableElement)
  // buildData(el, dataList)
  const str = `
  handleAdd${childTable.className}() {
    const cloumn = {
      ${dataList}
    };
    this.${conf.formModel}.${childTable.entityName}Lists.splice(this.${conf.formModel}.${childTable.entityName}Lists.length, 0, cloumn);
    for (let index in this.${conf.formModel}.${childTable.entityName}Lists) {
      this.${conf.formModel}.${childTable.entityName}Lists[index].sort = parseInt(index) + 1;
    }
  },
  handleDel${childTable.className}(index, row) {
    let functionName = this.$t('${conf.tableName}_${conf.dbId}.default.functionName');
    this.$confirm(this.$t('tips.deleteConfirm',['${childTable.tableComment}',row.id]),  this.$t('tips.warm'), {
        confirmButtonText: this.$t("common.confirmTrim"),
        cancelButtonText: this.$t("common.cancelTrim"),
        type: "warning",
      }
    )
      .then(() => {
        this.${conf.formModel}.${childTable.entityName}Lists.splice(index, 1);
        this.msgSuccess(this.$t("tips.deleteSuccess"));
      })
      .catch(function (err) {
        console.log(err);
      });
  },
   // 文件上传成功处理
      uploadsucces${childTable.className}(response, file, fileList) {
      if (response.code === 0) {
        this.msgSuccess(this.$t('tips.importSuccess'));
        ${childCheckboxStr}
        this.formData.${childTable.entityName}Lists=this.formData.${childTable.entityName}Lists.concat(response.data);
        response.data.forEach(item => {
          ${buildChildOnChangeMethodObject.methodNames}
        })
      }else {
        this.msgError(response.msg);
      }
      },
      handleChange${childTable.className}(file, fileList) {},
      handleExceed${childTable.className}(file, fileList) {},
      handleRemove${childTable.className}(file, fileList) {
        this.$emit("delUploadImage", file.name);
        const index = this.upload.fileList.indexOf(file);
        this.upload.fileList.splice(index, 1);
        this.formData.attachids = this.formData.attachids.replace(file.url + ",", "");
        this.upload.fileNameList.push(file.url);
        // delFileInfo(file.url);
      },
      handlePreview${childTable.className}(file) {
        const queryParams = this.queryParams;
        export${childTable.className}(queryParams).then(response => {
          this.download(response.data);
        })
      },
      ${buildChildOnChangeMethodObject.methods}
      ${childTableShowSummaryStr}
      ${beforeUploadStr}
  `
  methodList.push(str)
}
function buildChildOnChangeMethod(el)
{
  // console.log('22222')
  //所有需要產生@Change方法的控件
  let onChangeEls = []
  function addOnChangeStage(el,func){
    if(!el.onChangeStages){
      el.onChangeStages = []
    }
    el.onChangeStages.push(func)

  }
  let buildChildOnChangeMethodMap = new Map()
  buildChildOnChangeMethodMap.methods=``
  buildChildOnChangeMethodMap.methodNames=``
  let changeMethods = [];
  let childName = el.element.className
  for (let key in el.childAttributesMap) {
    var childAttribute = el.childAttributesMap[key]
    if (childAttribute.onchange) {
      changeMethods[`${childName}_${key}_onchange`] = {
        eventName: childAttribute.onchange,
        columnName: key,
        fillAutoStr: '',
        fillAutoClearStr: '',
      }
      childAttribute.changeMethod = changeMethods[`${childName}_${key}_onchange`]
      addOnChangeStage(childAttribute,(childAttribute)=>{
        return `
          this.${childAttribute.changeMethod.eventName}(item.${childAttribute.changeMethod.columnName}).then(responseInfo => {
            if (responseInfo.code !== 0) {
              this.msgError(responseInfo.msg);
            }
            else {
              if (responseInfo.data != null) {
                ${childAttribute.changeMethod.fillAutoStr}
              }else{
                ${childAttribute.changeMethod.fillAutoClearStr}
              }
            }
            });
         `
      })
    }
    //添加表達式的方法
    if (el.onChangeCalculateExpressionsMap && el.onChangeCalculateExpressionsMap[key]) {
      childAttribute.onChangeCalculateExpressions = el.onChangeCalculateExpressionsMap[key]
      addOnChangeStage(childAttribute,(childAttribute)=>{
        var calculateExpressionChangeMethodStrs = ''
        childAttribute.onChangeCalculateExpressions.forEach(expression=>{
          calculateExpressionChangeMethodStrs += `
          if(${expression.isNumberValueCondition}){
            item.${expression.key} = (${expression.calculateExpressions}).toFixed(${expression.calculateExpressionsDecimalScale})
          }else {
            item.${expression.key} = ''
          } \n
          `
        })
        return calculateExpressionChangeMethodStrs
      })
    }
    if(childAttribute.onChangeStages){
      childAttribute.methodName = `${childName}_${key}_onchange`
      onChangeEls.push(childAttribute)
    }
  }
  //onChangeCalculateExpressionsMap
  for (let key in el.childAttributesMap) {
    if (el.childAttributesMap[key].fillAuto !== '' && el.childAttributesMap[key].fillAuto !== undefined && el.childAttributesMap[key].fillAuto.length > 0) {
      for (let changeMethod in changeMethods) {
        if (el.childAttributesMap[key].fillAuto[0] == changeMethods[changeMethod].eventName)//&& key==changeMethods[changeMethod].columnName
        {
          changeMethods[changeMethod].fillAutoStr += `item.${key}=responseInfo.data.${el.childAttributesMap[key].fillAuto[1]} \n`
          changeMethods[changeMethod].fillAutoClearStr += `item.${key}='' \n`
        }
      }
    }
  }
  onChangeEls.forEach(onChangeEl =>{
    var changeMethodStr = `${onChangeEl.methodName}(item){ \n`
    onChangeEl.onChangeStages.forEach(stage => {
      let str = stage(onChangeEl)
      changeMethodStr += str? str :''
    })
    changeMethodStr += ' \n},'
    buildChildOnChangeMethodMap.methods += `${changeMethodStr} \n`
    buildChildOnChangeMethodMap.methodNames += `this.${onChangeEl.methodName}(item)\n`
  })
  return buildChildOnChangeMethodMap
}
function buildChildTableShowSummaryObjMethod(childTablefiled){
  if(!childTablefiled.childAttributesMap){
    return ""
  }
  var childAttributesMap = {}
  Object.entries(childTablefiled.childAttributesMap).forEach((childAttributes) => {
    if(childAttributes[1].sumMasterColumn || childAttributes[1].decimalScale) {
      childAttributesMap[childAttributes[0]] = {}
    }
    if (childAttributes[1].sumMasterColumn) {
      childAttributesMap[childAttributes[0]].sumMasterColumn = childAttributes[1].sumMasterColumn
    }
    if (childAttributes[1].decimalScale) {
      childAttributesMap[childAttributes[0]].decimalScale = childAttributes[1].decimalScale
    }
  })
  const childAttributesMapStr = JSON.stringify(childAttributesMap)
  const str = `
  ${childTablefiled.element.entityName}_getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      const scales = [];
      const childAttributesMap = ${childAttributesMapStr};
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = this.$t('common.sumText');
          return;
        }
        if(column.property && childAttributesMap[column.property] && childAttributesMap[column.property].sumMasterColumn) {
          scales[index] = 0;
          if(childAttributesMap[column.property].decimalScale){
            scales[index] = childAttributesMap[column.property].decimalScale
          }
          const values = data.map(item => Number(item[column.property]));
          if (!values.every(value => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                var x = prev + curr
                return x
              } else {
                return prev
              }
            }, 0);
            sums[index] = sums[index].toFixed(scales[index])
            this.formData[childAttributesMap[column.property].sumMasterColumn] = sums[index]
          }
        }
      });
      return sums;
    },
  `
  return str
}
function buildChildCreatedMethod(childTable, createdList,conf) {
  childTable.tableCols.forEach(column => {
    if(column.isAdd === '1' && column.dictType && column.dictType != ''){
      let methodName = 'get' + titleCase(column.javaField) +childTable.className+ 'Options'
      const str = `this.${methodName}() `
      createdList.push(str)
    }
  })
}
function buildCreatedMethod(methodName, createdList) {
  const str = `this.${methodName}()`
  createdList.push(str)
}
function buildImport(conf){
  let importStr = `import {get${conf.className}, add${conf.className}, edit${conf.className}
  ,add${conf.className}AndStartProcess,edit${conf.className}AndStartProcess,
  getSignConfigList`;
  conf.childTables.forEach(childs => {
    importStr += `,export${childs.className}`;
  });
  importStr += `} from "@/api/${conf.moduleName}/${conf.businessName}" \n`
    + `import '@/assets/styles/design-build/design-add-view.scss' \n`
    + `import {getAccessToken, getZltAccessToken} from "@/utils/auth"; \n`
    + `import {previewFileOos , getHeader} from "@/utils/entfrm"; \n`
    + `import {listFileInfo, getFileInfo, delFileInfo, addFileInfo, editFileInfo, getByKey} from "@/api/system/fileInfo"; \n`;
  return importStr
}

function buildDetailImport(conf){
  let importStr = `import {get${conf.className}, add${conf.className}, edit${conf.className},getSignPath,updateAndCheck,getSignConfigList`
  conf.childTables.forEach(childs => {
    importStr += `,export${childs.className}`;
  });
  importStr += `} from "@/api/${conf.moduleName}/${conf.businessName}" \n`
  + `import '@/assets/styles/design-build/design-add-view.scss' \n`
  + `import {listTask, getTask, checkTask, taskComment} from "@/api/activiti/task" \n`
  + `import {getAccessToken, getZltAccessToken} from "@/utils/auth"; \n`
  + `import {previewFileOos, getHeader} from "@/utils/entfrm"; \n`
  + `import {listFileInfo, getFileInfo, delFileInfo, addFileInfo, editFileInfo, getByKey} from "@/api/system/fileInfo"; \n`
  return importStr
}
function buildRejectImport(conf){
  let importStr = `import {get${conf.className}, add${conf.className}, edit${conf.className},getSignPath,edit${conf.className}AndResubmitProcess,getSignConfigList`;
  conf.childTables.forEach(childs => {
    importStr += `,export${childs.className}`;
  });
  importStr += `} from "@/api/${conf.moduleName}/${conf.businessName}" \n`
    + `import '@/assets/styles/design-build/design-add-view.scss' \n`
    + `import {listTask, getTask, checkTask, taskComment} from "@/api/activiti/task" \n`
    + `import {getAccessToken, getZltAccessToken} from "@/utils/auth"; \n`
    + `import {previewFileOos, getHeader} from "@/utils/entfrm"; \n`
    + `import {listFileInfo, getFileInfo, delFileInfo, addFileInfo, editFileInfo, getByKey} from "@/api/system/fileInfo"; \n`
  return importStr
}


function buildexport(conf, type, data, rules, selectOptions,childDragTableMobileClientWidths, uploadVar, props, methods,createds,configData) {
  let checkboxStr = buildCheckBoxObjStr(conf)
  let cascaderStr = buildCascaderObjStr(conf)
  let timeRang = buildTimeRangObjStr(conf)
  let fileUploadStr = buildUploadStr(conf)
  let dragTableMobileClientWidthStr = buildDragTableMobileClientWidthStr(conf)


  let dbAlias = conf.dbAlias?`alias:"${conf.dbAlias}"`:`alias:""`
  let buildImportStr=``
  if (!conf.isPreView)
  {
    buildImportStr=buildImport(conf)
  }
  const str =  buildImportStr+ `${exportDefault}{
  ${inheritAttrs[type]}
  components: {},
  props: [],
  data () {
    return {
      ${conf.formModel}: {
        ${data}
      },
      formConfigData: {
        ${configData}
      },
      tableHeight: document.documentElement.scrollHeight - 245 + "px",
      ${conf.formRules}: {
        ${rules}
      },
      ${uploadVar}
      ${selectOptions}
      ${props}
      ${childDragTableMobileClientWidths}
      isMobile: false,
      isDisable: false,
      labelPosition: '${conf.labelPosition}',
      ${dbAlias}
    }
  },
  computed: {},
  watch: {},
  created () {
    const id = this.$route.query.id;
    this.changeTagsView(this.$route.query);
    this.isMobile = this.isMobileFun()
    if(this.isMobile){
      this.labelPosition = 'top'
    }

    getSignConfigList().then(
      response =>{
        if (response.code === 0) {
          response.data.forEach(element => {
            const colKey = element.colKey.split('_')
            if (colKey.length > 1) {
              if (this.rules[colKey[0]] && colKey[1] === 'required') {
                this.rules[colKey[0]][0]['required'] = element.colValue === 'true'
                this.formConfigData[element.colKey] = element.colValue === 'true'
              }else{
                this.formConfigData[element.colKey] = element.colValue
              }
            }else{
              this.formConfigData[element.colKey] = element.colValue
            }
          });
        }
      }
    )

    if (id != null&&id!=undefined) {
      get${conf.className}(id).then(response => {
        this.${conf.formModel} = response.data;
        ${checkboxStr}
        ${cascaderStr}
        ${timeRang}
        ${fileUploadStr}
      });
    }
    ${createds}
  },
  mounted () {
    this.$nextTick(function () {
      if(this.isMobile){
        ${dragTableMobileClientWidthStr}
      }
    })
  },
  methods: {
    ${methods}
  }
}`
  return str
}
function buildUploadStr(conf){
  return `
  if (this.${conf.formModel}.attachids) {
    let a = this.${conf.formModel}.attachids.split(',');
    if (a.length > 0) {
      a.forEach(item => {
        if (item) {
          getByKey(item).then(response => {
            this.upload.fileList.push({name: response.data.orignalName, url: response.data.name});
          })
        }
      })
    }
  }`
}
function buildCheckBoxObjStr(conf){
  return 'this.checkBoxParse()'
}
function buildCascaderObjStr(conf){
  return 'this.cascaderParse()'
}
function buildTimeRangObjStr(conf){
  return 'this.timeRangParse()'
}
//生成渲染後,調整子表column寬度的字符串
function buildDragTableMobileClientWidthStr(conf){
  let childDragTableMobileClientWidthStr = ''
  conf.childTables.forEach(childTable => {
    childDragTableMobileClientWidthStr = childDragTableMobileClientWidthStr + ` this.${childTable.entityName}DragTableMobileClientWidth = this.$refs.${childTable.entityName}DragTableMobile.$el.clientWidth  \n`
  })
  return childDragTableMobileClientWidthStr
}

function buildAuditExport(conf, type, data, rules, selectOptions,childDragTableMobileClientWidthList, uploadVar, props, methods,createds,configData){

  let fileUploadStr = buildUploadStr(conf)
  let dragTableMobileClientWidthStr = buildDragTableMobileClientWidthStr(conf)

  let items = conf.fields;
  let mendths = "";
  let rangStr = "";
  let cascaderStr = "";
  let cascaderParameters = "";
  //如果在审核节点可以编辑子表属性，getById时候会将childTableItemsList数据初始化为undefined,所以需要重新赋值为[]
  let childTableItemsListsStr = "";

  let checkboxStr = buildCheckBoxObjStr(conf)
  let cascaderObjStr = buildCascaderObjStr(conf)
  let timeRang = buildTimeRangObjStr(conf)

  conf.fields.forEach(el => {
    if(el.tag =="entfrm-child-table-form"){
      childTableItemsListsStr = `
      if(!this.${conf.formModel}.${el.element.entityName}Lists){
        this.${conf.formModel}.${el.element.entityName}Lists = []
      }\n`
    }
  })
  items.forEach(item=>{
    if(item.tag=="el-checkbox-group"){
      mendths +=`this.getDicts("${item.dictType}").then(response => {
                        if(this.${conf.formModel}.${item.vModel}){
                          this.${conf.formModel}.${item.vModel}Value = JSON.parse(JSON.stringify(this.${conf.formModel}.${item.vModel})).join()
                          // this.${conf.formModel}.${item.vModel} = JSON.parse(this.${conf.formModel}.${item.vModel})
                          response.data.forEach( item =>{
                               if(this.${conf.formModel}.${item.vModel}){
                                          this.${conf.formModel}.${item.vModel}Value = this.${conf.formModel}.${item.vModel}Value.replace(item.value,item.label);
                               }
                          })
                        }
                 });`
    }
    if(item.tag=="el-select"||item.tag=="el-radio-group"){
      mendths +=`this.getDicts("${item.dictType}").then(response => {
                      if(this.${conf.formModel}.${item.vModel}){
                        this.${conf.formModel}.${item.vModel}Value = JSON.parse(JSON.stringify(this.${conf.formModel}.${item.vModel}))
                        response.data.forEach( item =>{
                            if(this.${conf.formModel}.${item.vModel}){
                                 this.${conf.formModel}.${item.vModel}Value = this.${conf.formModel}.${item.vModel}Value.replace(item.value,item.label);
                            }
                        })
                        this.$set(this.${conf.formModel}, this.${conf.formModel}.${item.vModel}Value, this.${conf.formModel}.${item.vModel}Value)

                      }
                 });`
    }
    if((item.tag=="el-time-picker"&&item["is-range"])||(item.tag=="el-date-picker"&&item.type=="daterange")){
      rangStr += `if(this.${conf.formModel}.${item.vModel}){
                      this.${conf.formModel}.${item.vModel}Value = JSON.parse(JSON.stringify(this.${conf.formModel}.${item.vModel})).join(" 至 ")
                      // this.${conf.formModel}.${item.vModel} = JSON.parse(this.${conf.formModel}.${item.vModel})
                  }
                  `;
    }
    if(item.tag=="el-cascader"){
      let methodName = titleCase(item.vModel)
      cascaderStr += `
                      if(this.${conf.formModel}.${item.vModel}){
                           this.${conf.formModel}.${item.vModel}Value = JSON.parse(JSON.stringify(this.${conf.formModel}.${item.vModel}))
                           // this.${conf.formModel}.${item.vModel} = JSON.parse(this.${conf.formModel}.${item.vModel})
                      }
                      this.get${methodName}Options()
                      `;
      cascaderParameters  += `${item.vModel}Value:undefined,`;
    }
  })

  const str = buildDetailImport(conf) + `${exportDefault}{
  ${inheritAttrs[type]}
  components: {},
  props: [],
  data () {
    return {
      ${conf.formModel}: {
        ${data}
      },
      formConfigData: {
        ${configData}
      },
      ${conf.formRules}: {
        ${rules}
      },
      tableHeight: document.documentElement.scrollHeight - 245 + "px",
      // 审批意见数据
      commentList: [],
      //簽核路徑
      signPath:[],
      //任务图url
      imgUrl: '',
      isDisable:false,
      // 是否显示任务图
      showImgDialog: false,
      auditStatus: [],
      ${cascaderParameters}
      ${uploadVar}
      ${selectOptions}
      ${props}
      ${childDragTableMobileClientWidthList}
      isMobile: false,
      labelPosition: 'left',
    }
  },
  computed: {},
  watch: {},
  created () {
    const id = this.$route.query.id;
    this.changeTagsView(this.$route.query);
    this.isMobile = this.isMobileFun()
    if(this.isMobile){
      this.labelPosition = 'top'
    }
    getSignConfigList().then(
      response =>{
        if (response.code === 0) {
          response.data.forEach(element => {
            const colKey = element.colKey.split('_')
            if (colKey.length > 1) {
              if (this.rules[colKey[0]] && colKey[1] === 'required') {
                this.rules[colKey[0]][0]['required'] = element.colValue === 'true'
                this.formConfigData[element.colKey] = element.colValue === 'true'
              }else{
                this.formConfigData[element.colKey] = element.colValue
              }
            }else{
              this.formConfigData[element.colKey] = element.colValue
            }
          });
        }
      }
    )
    if (id != null&&id!=undefined) {
      get${conf.className}(id).then(response => {
        this.${conf.formModel} = response.data;
        ${checkboxStr}
        ${cascaderObjStr}
        ${timeRang}

        ${cascaderStr}

        ${childTableItemsListsStr}
        taskComment(this.${conf.formModel}.processId).then(response => {
          this.commentList = response.data;
        });
        getSignPath(this.${conf.formModel}.processId).then(response => {
          this.signPath = response.data;
        });
        ${fileUploadStr}
        ${rangStr}
        ${cascaderStr}
        ${createds}
        ${mendths}
      });
    }
    this.getDicts("sign_status").then(response => {
      this.auditStatus = response.data;
    });
  },
  mounted () {
    this.$nextTick(function () {
      if(this.isMobile){
        ${dragTableMobileClientWidthStr}
      }
    })
  },
  methods: {
    ${methods}
  }
}`
  return str
}

function buildRejectExport(conf, type, data, rules, selectOptions,childDragTableMobileClientWidthList, uploadVar, props, methods,createds,configData) {

  let checkboxStr = buildCheckBoxObjStr(conf)
  let cascaderObjStr = buildCascaderObjStr(conf)
  let timeRang = buildTimeRangObjStr(conf)
  let fileUploadStr = buildUploadStr(conf)
  let dragTableMobileClientWidthStr = buildDragTableMobileClientWidthStr(conf)

  let iniFactoryIdStr=``

  conf.fields.forEach(el => {
    if (el.onchange !== '' && el.onchange !== undefined) {
      if ( el.onchange === 'getInfoUserByEmpno') {
        iniFactoryIdStr=`  this.getInfoUserByEmpno(this.formData.${el.vModel}).then(response => {
          if (response.code !== 0) {
            this.msgError(response.msg);
          }
          else {
            if (response.data != null) {
              this.formData.applyFactoryId = response.data.factoryid
            }
          }
        });`
      }
    }
  })

  let dbAlias = conf.dbAlias?`alias:"${conf.dbAlias}"`:`alias:""`
  const str = buildRejectImport(conf) + `${exportDefault}{
  ${inheritAttrs[type]}
  components: {},
  props: [],
  data () {
    return {
      ${conf.formModel}: {
        ${data}
      },
      formConfigData: {
        ${configData}
      },
      ${conf.formRules}: {
        ${rules}
      },
      tableHeight: document.documentElement.scrollHeight - 245 + "px",
      // 审批意见数据
      commentList: [],
      //簽核路徑
      signPath:[],
      //任务图url
      imgUrl: '',
      isDisable:false,
      // 是否显示任务图
      showImgDialog: false,
      auditStatus: [],
      ${uploadVar}
      ${selectOptions}
      ${props}
      ${childDragTableMobileClientWidthList}
      isMobile: false,
      labelPosition: 'left',
      ${dbAlias}
    }
  },
  computed: {},
  watch: {},
  created () {
    const id = this.$route.query.id;
    this.changeTagsView(this.$route.query);
    this.isMobile = this.isMobileFun()
    if(this.isMobile){
      this.labelPosition = 'top'
    }
    getSignConfigList().then(
      response =>{
        if (response.code === 0) {
          response.data.forEach(element => {
            const colKey = element.colKey.split('_')
            if (colKey.length > 1) {
              if (this.rules[colKey[0]] && colKey[1] === 'required') {
                this.rules[colKey[0]][0]['required'] = element.colValue === 'true'
                this.formConfigData[element.colKey] = element.colValue === 'true'
              }else{
                this.formConfigData[element.colKey] = element.colValue
              }
            }else{
              this.formConfigData[element.colKey] = element.colValue
            }
          });
        }
      }
    )
    if (id != null&&id!=undefined) {
      get${conf.className}(id).then(response => {
        this.${conf.formModel} = response.data;
        ${iniFactoryIdStr}
        ${checkboxStr}
        ${cascaderObjStr}
        taskComment(this.${conf.formModel}.processId).then(response => {
          this.commentList = response.data;
        });
        getSignPath(this.${conf.formModel}.processId).then(response => {
          this.signPath = response.data;
        });
        ${timeRang}
        ${fileUploadStr}
      });
    }
    ${createds}
    this.getDicts("sign_status").then(response => {
      this.auditStatus = response.data;
    });
  },
  mounted () {
    this.$nextTick(function () {
      if(this.isMobile){
        ${dragTableMobileClientWidthStr}
      }
    })
  },
  methods: {
    ${methods}
  }
}`
  return str
}

function buildDetailExport(conf, type, data, rules, selectOptions,childDragTableMobileClientWidthList, uploadVar, props, methods,createds,configData) {
  let fileUploadStr = buildUploadStr(conf)
  let dragTableMobileClientWidthStr = buildDragTableMobileClientWidthStr(conf)

  let items = conf.fields;
  let mendths = "";
  let rangStr = "";
  let cascaderStr = "";
  let cascaderParameters = "";
  //如果在审核节点可以编辑子表属性，getById时候会将childTableItemsList数据初始化为undefined,所以需要重新赋值为[]
  let childTableItemsListsStr = "";

  let checkboxStr = buildCheckBoxObjStr(conf)
  let cascaderObjStr = buildCascaderObjStr(conf)
  let timeRang = buildTimeRangObjStr(conf)

  conf.fields.forEach(el => {
    if(el.tag =="entfrm-child-table-form"){
      childTableItemsListsStr = `
      if(!this.${conf.formModel}.${el.element.entityName}Lists){
        this.${conf.formModel}.${el.element.entityName}Lists = []
      }\n`
    }
  })
  items.forEach(item=>{
    if(item.tag=="el-checkbox-group"){
      mendths +=`this.getDicts("${item.dictType}").then(response => {
                        if(this.${conf.formModel}.${item.vModel}){
                          this.${conf.formModel}.${item.vModel}Value = JSON.parse(JSON.stringify(this.${conf.formModel}.${item.vModel})).join()
                          // this.${conf.formModel}.${item.vModel} = JSON.parse(this.${conf.formModel}.${item.vModel})
                          response.data.forEach( item =>{
                               if(this.${conf.formModel}.${item.vModel}){
                                          this.${conf.formModel}.${item.vModel}Value = this.${conf.formModel}.${item.vModel}Value.replace(item.value,item.label);
                               }
                          })
                          this.$set(this.${conf.formModel}, this.${conf.formModel}.${item.vModel}Value, this.${conf.formModel}.${item.vModel}Value)
                        }
                 });`
    }
    if(item.tag=="el-select"||item.tag=="el-radio-group"){
      mendths +=`this.getDicts("${item.dictType}").then(response => {
                      if(this.${conf.formModel}.${item.vModel}){

                        this.${conf.formModel}.${item.vModel}Value = JSON.parse(JSON.stringify(this.${conf.formModel}.${item.vModel}))
                        response.data.forEach( item =>{
                            if(this.${conf.formModel}.${item.vModel}){
                                 this.${conf.formModel}.${item.vModel}Value = this.${conf.formModel}.${item.vModel}Value.replace(item.value,item.label);
                            }
                        })
                        this.$set(this.${conf.formModel}, this.${conf.formModel}.${item.vModel}Value, this.${conf.formModel}.${item.vModel}Value)
                      }
                 });`
    }
    if((item.tag=="el-time-picker"&&item["is-range"])||(item.tag=="el-date-picker"&&item.type=="daterange")){
      rangStr += `if(this.${conf.formModel}.${item.vModel}){
                      this.${conf.formModel}.${item.vModel}Value = JSON.parse(JSON.stringify(this.${conf.formModel}.${item.vModel})).join(" 至 ")
                      // this.${conf.formModel}.${item.vModel} = JSON.parse(this.${conf.formModel}.${item.vModel})
                  }
                  `;
    }
    if(item.tag=="el-cascader"){
      let methodName = titleCase(item.vModel)
      cascaderStr += `
                      if(this.${conf.formModel}.${item.vModel}){
                           this.${conf.formModel}.${item.vModel}Value = JSON.parse(JSON.stringify(this.${conf.formModel}.${item.vModel}))
                           // this.${conf.formModel}.${item.vModel} = JSON.parse(this.${conf.formModel}.${item.vModel})
                      }
                      this.get${methodName}Options()
                      `;
      cascaderParameters  += `${item.vModel}Value:undefined,`;
    }
  })

  const str = buildDetailImport(conf) + `${exportDefault}{
  ${inheritAttrs[type]}
  components: {},
  props: [],
  data () {
    return {
      ${conf.formModel}: {
        ${data}
      },
      formConfigData: {
        ${configData}
      },
      ${conf.formRules}: {
        ${rules}
      },
      tableHeight: document.documentElement.scrollHeight - 245 + "px",
      // 审批意见数据
      commentList: [],
      //簽核路徑
      signPath:[],
      //任务图url
      imgUrl: '',
      isDisable:false,
      // 是否显示任务图
      showImgDialog: false,
      auditStatus: [],
      ${cascaderParameters}
      ${uploadVar}
      ${selectOptions}
      ${props}
      ${childDragTableMobileClientWidthList}
      isMobile: false,
      labelPosition: 'left',
    }
  },
  computed: {},
  watch: {},
  created () {
    const id = this.$route.query.id;
    this.changeTagsView(this.$route.query);
    this.isMobile = this.isMobileFun()
    if(this.isMobile){
      this.labelPosition = 'top'
    }
    getSignConfigList().then(
      response =>{
        if (response.code === 0) {
          response.data.forEach(element => {
            const colKey = element.colKey.split('_')
            if (colKey.length > 1) {
              if (this.rules[colKey[0]] && colKey[1] === 'required') {
                this.rules[colKey[0]][0]['required'] = element.colValue === 'true'
                this.formConfigData[element.colKey] = element.colValue === 'true'
              }else{
                this.formConfigData[element.colKey] = element.colValue
              }
            }else{
              this.formConfigData[element.colKey] = element.colValue
            }
          });
        }
      }
    )
    if (id != null&&id!=undefined) {
      get${conf.className}(id).then(response => {
        this.${conf.formModel} = response.data;
        ${checkboxStr}
        ${cascaderObjStr}
        ${timeRang}

        ${cascaderStr}

        ${childTableItemsListsStr}
        taskComment(this.${conf.formModel}.processId).then(response => {
          this.commentList = response.data;
        });
        getSignPath(this.${conf.formModel}.processId).then(response => {
          this.signPath = response.data;
        });
        ${fileUploadStr}
        ${rangStr}
        ${cascaderStr}
        ${createds}
        ${mendths}
      });
    }
    this.getDicts("sign_status").then(response => {
      this.auditStatus = response.data;
    });
  },
  mounted () {
    this.$nextTick(function () {
      if(this.isMobile){
        ${dragTableMobileClientWidthStr}
      }
    })
  },
  methods: {
    ${methods}
  }
}`
  return str
}
