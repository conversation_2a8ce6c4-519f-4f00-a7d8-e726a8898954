import { isArray } from 'util'
import { exportDefault, titleCase } from '@/utils/index'
import { trigger,htmlTypeConvertForm } from './config'

const units = {
  KB: '1024',
  MB: '1024 / 1024',
  GB: '1024 / 1024 / 1024'
}
let confGlobal
const inheritAttrs = {
  file: '',
  dialog: 'inheritAttrs: false,'
}

export function makeUpJsApp(conf, type) {
  confGlobal = conf = JSON.parse(JSON.stringify(conf))
  const configDataList = []
  const dataList = []
  const ruleList = []
  const optionsList = []
  const childDragTableMobileClientWidthList = []  //子表在適配手機時的寬度列表
  const propsList = []
  const methodList = mixinMethod(conf,type)
  const uploadVarList = []
  const createdList = []
  const mountedList = []
  const childList = []

  //加入標題信息
  configDataList.push(`title: "${conf.formName}",`)

  conf.fields.forEach(el => {
    buildAttributesApp(el, dataList, ruleList, optionsList, methodList, propsList, uploadVarList,createdList,conf,mountedList,configDataList)
    if(el.tag =="entfrm-child-table-form"){
      childList.push(el.element)
    }
  })
  buildCheckBoxObjMethod(conf, methodList,childList);
  buildCascaderObjMethod(conf, methodList,childList);
  buildTimeRangObjMethod(conf, methodList,childList);
  buildFillAutoObjMethod(conf, methodList);
  buildCheckPointObjMethod(conf, methodList);
  childList.forEach(childTable => {
    buildChildTableAttributes(conf,childTable,dataList,methodList,optionsList,createdList, ruleList,mountedList)
    childDragTableMobileClientWidthList.push(`${childTable.entityName}DragTableMobileClientWidth: 0,`)
  })
  //加入填單人信息
  dataList.push('makerNo:this.$store.state.user.empNo,')
  dataList.push('makerName: this.$store.state.user.name,')
  dataList.push('dataSource: "app",')
  const script = buildexport(
    conf,
    type,
    dataList.join('\n'),
    ruleList.join('\n'),
    optionsList.join('\n'),
    childDragTableMobileClientWidthList.join('\n'),
    uploadVarList.join('\n'),
    propsList.join('\n'),
    methodList.join('\n'),
    createdList.join('\n'),
    mountedList,
    configDataList.join('\n'),
  )
  confGlobal = null
  return script
}


export function makeUpAuditJsApp(conf, type) {
  confGlobal = conf = JSON.parse(JSON.stringify(conf))
  const configDataList = []
  const dataList = []
  const ruleList = []
  const optionsList = []
  const propsList = []
  const methodList = mixinAuditMethod(conf,type)  //提交返回方法
  const uploadVarList = []
  const createdList = []
  let childTablesActive = ''
  const childList = []
  const mountedList = []
  const childDragTableMobileClientWidthList = []
  const watchList = []

  //加入標題信息
  configDataList.push(`title: "${conf.formName}",`)

  conf.fields.forEach(el => {
    buildAttributesAudit(el, dataList, ruleList, optionsList, methodList, propsList, uploadVarList,createdList,conf,mountedList,configDataList)
    if(el.tag =="entfrm-child-table-form"){ //子表
      el.element["currentorder"] = el.currentorder
      childList.push(el.element)
    }
  })

  buildTimeRangObjMethodAudit(conf, methodList,childList);
  buildFillAutoObjMethodAudit(conf, methodList);
  buildCheckBoxObjMethod(conf,methodList,childList)   //複選框
  buildCascaderObjMethod(conf, methodList,childList);  //級聯選擇

  childList.forEach(childTable => {
    if(childTable.currentorder!=""&&childTable.currentorder!=undefined){   //當前字段可編輯
      childTablesActive += `${childTable.entityName}ActiveNames:["1"],
                            ${childTable.entityName}MainActiveNames:[""],  \n                    `
      buildChildTableAttributesAudit(conf,childTable,dataList,methodList,optionsList,createdList, ruleList,mountedList,watchList)
      buildChildTableAttributesDetail(conf,childTable,dataList,methodList,optionsList,createdList, ruleList)
      childDragTableMobileClientWidthList.push(`${childTable.entityName}DragTableMobileClientWidth: 0,`)
    }else{
      childTablesActive += `${childTable.entityName}ActiveNames:["1"],
                            ${childTable.entityName}MainActiveNames:[""],  \n                    `
      buildChildTableAttributesDetail(conf,childTable,dataList,methodList,optionsList,createdList, ruleList)
    }
  })

  dataList.push('dataSource: "app",')
  dataList.push('currentorder: "",')
  //加入填單人信息
  dataList.push('makerNo:this.$store.state.user.empNo,')
  dataList.push('makerName: this.$store.state.user.name,')
  const script = buildAuditExport(
    conf,
    type,
    dataList.join('\n'),
    ruleList.join('\n'),
    optionsList.join('\n'),
    uploadVarList.join('\n'),
    propsList.join('\n'),
    methodList.join('\n'),
    createdList.join('\n'),
    childTablesActive,
    childDragTableMobileClientWidthList.join('\n'),
    mountedList,
    watchList.join('\n'),
    configDataList.join('\n'),
  )
  confGlobal = null
  return script
}


export function makeUpDetailJsApp(conf, type) {
  confGlobal = conf = JSON.parse(JSON.stringify(conf))
  const configDataList = []
  const dataList = []
  const ruleList = []
  const optionsList = []
  const propsList = []
  const methodList = mixinDetailMethod(conf,type)
  const uploadVarList = []
  const createdList = []
  let childTablesActive = ''
  const childList = []
  //加入標題信息
  configDataList.push(`title: "${conf.formName}",`)

  conf.fields.forEach(el => {
    buildAttributesDetail(el, dataList, ruleList, optionsList, methodList, propsList, uploadVarList,createdList,conf,configDataList)
    if(el.tag =="entfrm-child-table-form"){
      childList.push(el.element)
    }
  })

  buildCheckBoxObjMethod(conf,methodList,childList)   //複選框
  buildCascaderObjMethod(conf, methodList,childList);  //級聯選擇

  childList.forEach(childTable => {
    childTablesActive += `${childTable.entityName}ActiveNames:["1"],
                          ${childTable.entityName}MainActiveNames:[""],  \n
                         `
    buildChildTableAttributesDetail(conf,childTable,dataList,methodList,optionsList,createdList, ruleList)
  })
  const script = buildDetailExport(
    conf,
    type,
    dataList.join('\n'),
    ruleList.join('\n'),
    optionsList.join('\n'),
    uploadVarList.join('\n'),
    propsList.join('\n'),
    methodList.join('\n'),
    createdList.join('\n'),
    childTablesActive,
    configDataList.join('\n'),
  )
  confGlobal = null
  return script
}


export function makeUpRejectJsApp(conf, type) {
  confGlobal = conf = JSON.parse(JSON.stringify(conf))
  const configDataList = []
  const dataList = []
  const ruleList = []
  const optionsList = []
  const childDragTableMobileClientWidthList = []  //子表在適配手機時的寬度列表
  const propsList = []
  const methodList = mixinMethod(conf,type)
  const uploadVarList = []
  const createdList = []
  const childList = []
  const mountedList = []
  //加入標題信息
  configDataList.push(`title: "${conf.formName}",`)

  conf.fields.forEach(el => {
    buildAttributesApp(el, dataList, ruleList, optionsList, methodList, propsList, uploadVarList,createdList,conf,mountedList,configDataList)
    if(el.tag =="entfrm-child-table-form"){
      childList.push(el.element)
    }
  })
  buildCheckBoxObjMethod(conf, methodList,childList);
  buildCascaderObjMethod(conf, methodList,childList);
  buildTimeRangObjMethod(conf, methodList,childList);
  buildFillAutoObjMethod(conf, methodList);
  buildCheckPointObjMethod(conf, methodList);
  childList.forEach(childTable => {
    buildChildTableAttributes(conf,childTable,dataList,methodList,optionsList,createdList, ruleList,mountedList)
    childDragTableMobileClientWidthList.push(`${childTable.entityName}DragTableMobileClientWidth: 0,`)
  })
  dataList.push('dataSource: "app",')
  //加入填單人信息
  dataList.push('makerNo:this.$store.state.user.empNo,')
  dataList.push('makerName: this.$store.state.user.name,')
  const script = buildRejectExport(
    conf,
    type,
    dataList.join('\n'),
    ruleList.join('\n'),
    optionsList.join('\n'),
    childDragTableMobileClientWidthList.join('\n'),
    uploadVarList.join('\n'),
    propsList.join('\n'),
    methodList.join('\n'),
    createdList.join('\n'),
    configDataList.join('\n'),

  )
  confGlobal = null
  return script
}
/* 生成審核頁面JS代碼（APP） 開始 */





function mixinAuditMethod(conf,type) {
  const list = [];
  let masterCheckBoxStr = ''
  let masterCascaderStr = ''
  let checkboxStr = ''
  let dateRange = ''
  let timeRange = ''
  let childList =[];
  let ifUpdate = false
  conf.fields.forEach(el => {
    if(el.tag === 'el-checkbox-group'){    //當前字段可編輯
      masterCheckBoxStr = masterCheckBoxStr + `if(typeof ${conf.formModel}.${el.vModel} == "object"){
          ${conf.formModel}.${el.vModel} = JSON.stringify(${conf.formModel}.${el.vModel})
      }\n`
    }else if(el.tag === 'el-cascader'){  //級聯  當前字段可編輯
      masterCascaderStr = masterCascaderStr + `if(typeof ${conf.formModel}.${el.vModel} == "object"){
          ${conf.formModel}.${el.vModel} = JSON.stringify(${conf.formModel}.${el.vModel})
      }\n`
    }else if(el.tag === 'el-date-picker'&&el.type == 'daterange'){  //日期範圍  當前字段可編輯
      dateRange = dateRange + `if(typeof ${conf.formModel}.${el.vModel} == "object"){
         ${conf.formModel}.${el.vModel} = JSON.stringify(${conf.formModel}.${el.vModel})
      }\n`
    }else if(el.tag === 'el-time-picker'&&el["is-range"]){  //時間範圍  當前字段可編輯
      timeRange = timeRange + `if(typeof ${conf.formModel}.${el.vModel} == "object"){
           ${conf.formModel}.${el.vModel} = JSON.stringify(${conf.formModel}.${el.vModel})
      }\n`
    }else if(el.tag === 'entfrm-child-table-form'){ //子表 當前字段可編輯
      childList.push(el.element)
    }
    if(el.currentorder!=""&&el.currentorder!=undefined){
      ifUpdate = true
    }
  })
  childList.forEach(childTable => {
    checkboxStr = checkboxStr + buildCheckBoxStringifyMixinMethod(childTable,conf)
  })

  const minxins = {
    file: {  closeForm: `closeForm(){
          //关闭子页面
            this.$router.go(-1) // 返回
        },`,
      handleTrack: `handleTrack() {
          this.imgUrl = this.getAppBaseApi() + '/activiti/task/track/' + this.${conf.formModel}.processId,
            this.showImgDialog = true
        },`,
      statusFormat: `statusFormat(row, column) {
          return this.selectDictLabel(this.auditStatus, row.status);
        },`,
      handleTask: `handleTask: function (pass) {
          this.isDisabled=true;
          this.$refs["${conf.formRef}"].validate(valid => {
            if (pass==0||(pass==1&&this.${conf.formModel}.comment!=null)) {
              this.${conf.formModel}.pass = pass
              checkTask(this.${conf.formModel}).then(response => {
                if (response.code === 0) {
                  this.$message({ showClose: true, message: this.$t('tips.operationSuccessful'), type: "success", offset: 50});
                  this.closeForm();
                } else {
                  this.msgError(response.msg);
                }
              });
            }else{
              this.isDisabled=false;
              this.msgInfo(this.$t('table.activity.inputApprovalOpinions'));
            }
          });
        },
        `
    }
  }
  const updateHandleTask =  `updateHandleTask: function (pass) {
          this.isDisabled=true;
          this.$refs["${conf.formRef}"].validate(valid => {
            if(pass==0){  //通過
                if(valid){
                    this.${conf.formModel}.leaveDto = {"pass":pass,"processId":this.${conf.formModel}.processId,"comment":this.${conf.formModel}.comment}
                    const ${conf.formModel} = JSON.parse(JSON.stringify(this.${conf.formModel}))
                    ${masterCheckBoxStr}
                    ${checkboxStr}
                    ${masterCascaderStr}
                    ${dateRange}
                    ${timeRange}
                    updateAndCheck(${conf.formModel}).then(response => {
                        if (response.code === 0) {
                          this.$message({ showClose: true, message: this.$t('tips.operationSuccessful'), type: "success", offset: 50});
                          this.closeForm();
                        } else {
                          this.msgError(response.msg);
                        }
                    });
                }else{
                  this.isDisabled=false;
                }

            }else if(pass==1&&this.${conf.formModel}.comment!=null){ //駁回
              this.${conf.formModel}.pass = pass
              checkTask(this.${conf.formModel}).then(response => {
                  if (response.code === 0) {
                    this.$message({ showClose: true, message: this.$t('tips.operationSuccessful'), type: "success", offset: 50});
                    this.closeForm();
                  } else {
                    this.msgError(response.msg);
                  }
              });
            }else{
              this.isDisabled=false;
              this.msgInfo(this.$t('table.activity.inputApprovalOpinions'));
            }
          });
        },`
  const methods = minxins[type]
  if (methods) {
    Object.keys(methods).forEach(key => {
      list.push(methods[key])
    })
  }
  if(ifUpdate){
    list.push(updateHandleTask)
  }
  return list
}



function buildAttributesAudit(el, dataList, ruleList, optionsList, methodList, propsList, uploadVarList,createdList,conf,mountedList,configDataList) {
  buildData(el, dataList,configDataList)
  buildRulesAudit(el, ruleList)
  let showTimeStr = ''
  let createStr = ''

  if (el.dictType) {
    buildOptions(el, optionsList)
    const vModel = titleCase(el.vModel)

    buildOptionMethodAuditEdit(`get${vModel}Options`, methodList,el,conf)
    buildOptionMethodAudit(`get${vModel}Options`, methodList,el,conf.formModel)
    buildCreatedMethod(`get${vModel}Options`, createdList)

  }
  if((el.tag == "el-date-picker" ||el.tag == "el-time-picker")&&((el.currentorder!=""&&el.currentorder!=undefined))){  //當前字段可編輯

    showTimeStr += `${el.vModel}ShowDate(){
                                this.${el.vModel}SegmentPicker.show()
                        },`

    let selectValue = el.format
    //獲取當前日期是當年第幾周
    let getWeek = `let wn = function(nowDate){
        let firstWeekDay = new Date(nowDate.getFullYear(),0,(7 - new Date(nowDate.getFullYear(),0,1).getDay())); //本年第一周第一天的前一天日期
        let week = 0;
        if(firstWeekDay>=nowDate){ //時間小於當前年份第一周第一天則 歸為上一年最後一周
            firstWeekDay = new Date(nowDate.getFullYear()-1,0,(8 - new Date(nowDate.getFullYear()-1,0,1).getDay())); //本年第一周第一天日期
            week = Math.ceil((nowDate - firstWeekDay)/(86400000*7))
        }else{
            week = Math.ceil((nowDate - firstWeekDay)/(86400000*7))
        }
        return week;
    }`
    //判斷年份是否變化
    let getYear = `let getYear = function(nowDate){
                          let result = nowDate.getFullYear()
                          let firstWeekDay = new Date(nowDate.getFullYear(),0,(8 - new Date(nowDate.getFullYear(),0,1).getDay())); //本年第一周第一天的日期
                          if(firstWeekDay>nowDate){
                            result = nowDate.getFullYear() - 1
                          }
                          return result
                    }`
    if(selectValue.indexOf("WW")<0){
      getWeek = ""
    }
    selectValue = selectValue.replace(/yyyy|MM|dd|DD|WW|HH|hh|mm|ss/g, function(a){
      switch(a){
        case 'yyyy':
          if(selectValue.indexOf("WW")<0){
            return `"+selectDate.getFullYear()+"`;
          }else{
            return `"+getYear(selectDate)+"`;
          }
          break;
        case 'MM':
          return `"+tf(selectDate.getMonth()+1)+"`;
          break;
        case 'dd':
          return `"+tf(selectDate.getDate())+"`;
          break;
        case 'DD':
          return `"+tf(selectDate.getDay())+"`;
          break;
        case 'WW':
          return `"+wn(selectDate)+"`;
          break;
        case 'HH':
          return `"+tf(selectDate.getHours())+"`;
          break;
        case 'hh':
          return `"+tf(selectDate.getHours())+"`;
          break;
        case 'mm':
          return `"+tf(selectDate.getMinutes())+"`;
          break;
        case 'ss':
          return `"+tf(selectDate.getSeconds())+"`;
          break;
      }
    })

    if(selectValue.indexOf("\"+")==0){
      selectValue = selectValue.substr(2,selectValue.length);
    }
    if(selectValue.lastIndexOf("+\"")==selectValue.length-2){
      selectValue = selectValue.substr(0,selectValue.length-2);
    }
    if(el.type==='daterange'||el["is-range"]){
      createStr = ` this.${el.vModel}SegmentPicker = this.$createSegmentPicker({
                          data: this.${el.vModel}DateValue,
                          onSelect: (selectedDates, selectedVals, selectedTexts) => {
                            let tf = function(i){return (i < 10 ? '0' : '') + i};
                            ${getWeek}
                            ${getYear}
                            let value = new Array();
                            selectedDates.forEach(item=>{
                            let selectDate = new Date(item)
                            value.push(${selectValue})
                            })
                            this.formData.${el.vModel} = value;
                            this.${el.vModel}Value = value.join("  至  ")
                          },
                          onNext: (i, selectedDate, selectedValue, selectedText) => {
                            this.${el.vModel}DateValue[1].min = selectedDate
                            if (i === 0) {
                              this.${el.vModel}SegmentPicker.$updateProps({
                                data: this.${el.vModel}DateValue
                              })
                            }
                          }
                        });
                        `
      mountedList.push(createStr)
    }else{
      createStr = ` this.${el.vModel}SegmentPicker = this.$createSegmentPicker({
                          data: this.${el.vModel}DateValue,
                          onSelect: (selectedDates, selectedVals, selectedTexts) => {
                            var tf = function(i){return (i < 10 ? '0' : '') + i};
                            ${getWeek}
                            ${getYear}
                            selectedDates.forEach(item=>{
                            let selectDate = new Date(item)
                            this.formData.${el.vModel} = ${selectValue}
                            })
                            this.${el.vModel}Value = this.formData.${el.vModel}
                          }
                        });
                        `
      mountedList.push(createStr)
    }
    methodList.push(showTimeStr)
  }



  if (el.props && el.props.props) {
    buildProps(el, propsList)
  }

  if (el.action && el.tag === 'el-upload') {
    methodList.push(buildBeforeUpload(el))
    methodList.push(buildFunctionUpload(conf,el))
    const multiple = el.multiple ? 'true' : 'false'
    uploadVarList.push(
      `
      // 文件上传参数
      upload: {
        // 是否显示弹出层
        open: false,
        fileList: [],
        fileNameList: [],
        // 弹出层标题
        title: "",
        multiple: ${multiple},
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: this.getHeader(),
        // 上传的地址
        url: this.getAppBaseApi() + "/system/fileInfo/uploadWithOos"
      },`
    )
  }

  if (el.children) {
    el.children.forEach(el2 => {
      buildAttributesAudit(el2, dataList, ruleList, optionsList, methodList, propsList, uploadVarList,createdList,conf,configDataList)
    })
  }
}


function buildAuditExport(conf, type, data, rules, selectOptions, uploadVar, props, methods,createds,childTablesActive,childDragTableMobileClientWidths,mountedList,watchList,configData) {
  let fileUploadStr = buildUploadStr(conf)

  let checkboxStr = buildCheckBoxObjStr(conf)
  let cascaderLoadStr = buildCascaderObjStr(conf)
  let timeRang = buildTimeRangObjStr(conf)

  let items = conf.fields;
  let mendths = "";
  let rangStr = "";
  let cascaderStr = "";
  let cascaderParameters = "";
  let dateCreateStr = ""

  let rangStrValue = "";
  items.forEach(item=>{
    if(item.tag=="el-checkbox-group"){
      mendths +=`this.getDicts("${item.dictType}").then(response => {
                        if(this.${conf.formModel}.${item.vModel}&&this.${conf.formModel}.${item.vModel}!="null"){
                              this.${item.vModel}Value = JSON.parse(this.${conf.formModel}.${item.vModel}).join()
                              this.${conf.formModel}.${item.vModel} = JSON.parse(this.${conf.formModel}.${item.vModel})
                              response.data.forEach( item =>{
                                              this.${item.vModel}Value = this.${item.vModel}Value.replace(item.value,item.label);
                              })
                        }
                 });`
    }
    if((item.tag=="el-radio-group")){
      mendths +=`this.getDicts("${item.dictType}").then(response => {
                     if(this.${conf.formModel}.${item.vModel}&&this.${conf.formModel}.${item.vModel}!="null"){
                        this.${item.vModel}Value = this.${conf.formModel}.${item.vModel}
                        response.data.forEach( item =>{
                            if(this.${conf.formModel}.${item.vModel}&&this.${conf.formModel}.${item.vModel}!="null"){
                                 this.${item.vModel}Value = this.${item.vModel}Value.replace(item.value,item.label);
                            }
                        })
                      }
                 });`
    }
    if(((item.tag=="el-time-picker"&&item["is-range"])||(item.tag=="el-date-picker"&&item.type=="daterange"))){
      rangStr += `if(this.${conf.formModel}.${item.vModel}&&this.${conf.formModel}.${item.vModel}!="null"){
                      this.${item.vModel}Value = JSON.parse(this.${conf.formModel}.${item.vModel}).join("  至  ")
                   }
                 `;
    }
    if(item.tag=="el-cascader"){
      let methodName = titleCase(item.vModel)
      cascaderStr += `this.get${methodName}Options()
                      if(this.${conf.formModel}.${item.vModel}&&this.${conf.formModel}.${item.vModel}!="null"){
                         this.${item.vModel}Value = JSON.parse(this.${conf.formModel}.${item.vModel})
                      }
                      `;
      cascaderParameters  += `${item.vModel}Value:undefined,`;
    }
    if((item.tag=="el-time-picker"||item.tag=="el-date-picker")&&(item.currentorder!=""&&item.currentorder!=undefined)){  //當前字段可編輯

      let formatItem = {
        'yyyy':el => {return `year: 'YYYY'`},
        'MM':el => {return `month: 'MM'`},
        'dd':el => {return `date: 'DD'`},
        'DD':el => {return `date: 'DD'`},
        'WW':el => {return `date: 'DD'`},
        'HH':el => {return `hour: 'hh'`},
        'hh':el => {return `hour: 'hh'`},
        'mm':el => {return `minute: 'mm'`},
        'ss':el => {return `second: 'ss'`},
      }
      let formatIndex = {'yyyy': 1,'MM':2,'dd':3,'DD':3,'WW':3,'HH':4,'hh':4,'mm':5,'ss':6}
      let startColumn = ""  //格式開始
      let endColumn = ""    //格式結束
      let formats = new Array();
      for(let oneFormat in formatItem){
        if(item.format.indexOf(oneFormat)>=0){
          if(startColumn==""){
            startColumn =  oneFormat;
          }
          endColumn = oneFormat;
          formats.push(formatItem[oneFormat]())
        }
      }
      let format = `format: {${formats.join(",")}},`
      let columnCount = 6;
      if((formatIndex[startColumn]<4&&formatIndex[endColumn]<4)){
        columnCount = 3;
        startColumn = 'year'
      }else if((formatIndex[startColumn]>3&&formatIndex[endColumn]>3)){
        columnCount = 3;
        startColumn = 'hour'
      }else{
        startColumn = 'year'
      }
      rangStrValue += `${item.vModel}Value:undefined,`;
      if(item["is-range"]||item.type=="daterange"){ //範圍
        dateCreateStr+= `${item.vModel}DateValue :[{
                                is: 'cube-date-picker',
                                title: '${item["start-placeholder"]}',
                                ${format}
                                value: new Date(),
                                swipeTime :1000,
                                startColumn: '${startColumn}',
                                columnCount: ${columnCount}
                              },{
                                is: 'cube-date-picker',
                                title: '${item["end-placeholder"]}',
                                ${format}
                                value: new Date(),
                                swipeTime :1000,
                                startColumn: '${startColumn}',
                                columnCount: ${columnCount}
                              }],`
      }else{
        dateCreateStr+= `${item.vModel}DateValue :[{
                                is: 'cube-date-picker',
                                title: '${item.label}',
                                ${format}
                                value: new Date(),
                                swipeTime :1000,
                                  startColumn: '${startColumn}',
                                columnCount: ${columnCount}
                              }],`
      }
    }
  })

  const str = buildAuditImport(conf) + `${exportDefault}{
  ${inheritAttrs[type]}
  components: {},
  props: [],
  data () {
    return {
      ${conf.formModel}: {
        ${data}
      },
      formConfigData: {
        ${configData}
      },
      tableHeight: document.documentElement.scrollHeight - 245 + "px",
      ${conf.formRules}: {
        ${rules}
      },
      // 审批意见数据
      commentList: [],
      //簽核路徑
      signPath:[],
      activeCount: -1,
      signPathActive: -1,
      //任务图url
      imgUrl: '',
      isDisabled:false,
      // 是否显示任务图
      showImgDialog: false,
      auditStatus: [],
      ${cascaderParameters}
      ${uploadVar}
      ${selectOptions}
      ${props}
      ${childTablesActive}
      ${childDragTableMobileClientWidths}
      isMobile: true,
      labelPosition: 'left',
      ${rangStrValue}
      ${dateCreateStr}
    }
  },
  computed: {},
  watch: {
    ${watchList}
    },
  created () {
    const id = this.$route.query.id;
    this.changeTagsView(this.$route.query);
    this.isMobile = true

    getSignConfigList().then(
      response =>{
        if (response.code === 0) {
          response.data.forEach(element => {
            const colKey = element.colKey.split('_')
            if (colKey.length > 1) {
              if (this.rules[colKey[0]] && colKey[1] === 'required') {
                this.rules[colKey[0]][0]['required'] = element.colValue === 'true'
                this.formConfigData[element.colKey] = element.colValue === 'true'
              }else{
                this.formConfigData[element.colKey] = element.colValue
              }
            }else{
              this.formConfigData[element.colKey] = element.colValue
            }
          });
        }
      }
    )

    if (id != null&&id!=undefined) {
      get${conf.className}(id).then(response => {
        this.${conf.formModel} = response.data;
        taskComment(this.${conf.formModel}.processId).then(response => {
          this.commentList = response.data;
        });
        getSignPathApp(this.${conf.formModel}.processId).then(response => {
           this.signPath = response.data.info;
           this.activeCount = response.data.activeCount;
        });
        ${fileUploadStr}
        ${checkboxStr}
        ${cascaderLoadStr}
        ${rangStr}
        ${timeRang}
        ${createds}
        ${mendths}
      });
    }
    this.getDicts("sign_status").then(response => {
      this.auditStatus = response.data;
    });
  },
  mounted () {
    ${mountedList.join('\n')}
  },
  methods: {
    ${methods}
  }
}`
  return str
}


function buildDetailExport(conf, type, data, rules, selectOptions, uploadVar, props, methods,createds,childTablesActive,configData) {
  let fileUploadStr = buildUploadStr(conf)

  let items = conf.fields;
  let mendths = "";
  let rangStr = "";
  let cascaderStr = "";
  let cascaderParameters = "";
  items.forEach(item=>{
    if(item.tag=="el-checkbox-group"){
      mendths +=`this.getDicts("${item.dictType}").then(response => {
                        if(this.${conf.formModel}.${item.vModel}&&this.${conf.formModel}.${item.vModel}!="null"){
                            this.${conf.formModel}.${item.vModel} = JSON.parse(this.${conf.formModel}.${item.vModel}).join()
                        }
                        response.data.forEach( item =>{
                             if(this.${conf.formModel}.${item.vModel}&&this.${conf.formModel}.${item.vModel}!="null"){
                                        this.${conf.formModel}.${item.vModel} = this.${conf.formModel}.${item.vModel}.replace(item.value,item.label);
                             }
                        })
                 });`
    }
    if(item.tag=="el-select"||item.tag=="el-radio-group"){
      mendths +=`this.getDicts("${item.dictType}").then(response => {
                        response.data.forEach( item =>{
                            if(this.${conf.formModel}.${item.vModel}&&this.${conf.formModel}.${item.vModel}!="null"){
                                 this.${conf.formModel}.${item.vModel} = this.${conf.formModel}.${item.vModel}.replace(item.value,item.label);
                            }
                        })
                 });`
    }
    if((item.tag=="el-time-picker"&&item["is-range"])||(item.tag=="el-date-picker"&&item.type=="daterange")){
      rangStr += `if(this.${conf.formModel}.${item.vModel}&&this.${conf.formModel}.${item.vModel}!="null"){
                      this.${conf.formModel}.${item.vModel} = JSON.parse(this.${conf.formModel}.${item.vModel}).join("  至  ")
                   }
                 `;
    }
    if(item.tag=="el-cascader"){
      let methodName = titleCase(item.vModel)
      cascaderStr += `this.get${methodName}Options()
                      if(this.${conf.formModel}.${item.vModel}){
                         this.${conf.formModel}.${item.vModel} = JSON.parse(this.${conf.formModel}.${item.vModel})
                      }
                      `;
      cascaderParameters  += `${item.vModel}Value:undefined,`;
    }
  })
  const str = buildAuditImport(conf) + `${exportDefault}{
  ${inheritAttrs[type]}
  components: {},
  props: [],
  data () {
    return {
      ${conf.formModel}: {
        ${data}
      },
      formConfigData: {
        ${configData}
      },
      tableHeight: document.documentElement.scrollHeight - 245 + "px",
      // 审批意见数据
      commentList: [],
      //簽核路徑
      signPath:[],
      activeCount: -1,
      signPathActive: -1,
      //任务图url
      imgUrl: '',
      isDisabled:false,
      // 是否显示任务图
      showImgDialog: false,
      auditStatus: [],
      ${cascaderParameters}
      ${uploadVar}
      ${selectOptions}
      ${props}
      ${childTablesActive}
      isMobile: true,
      labelPosition: 'left',
    }
  },
  computed: {},
  watch: {},
  created () {
    const id = this.$route.query.id;
    this.changeTagsView(this.$route.query);
    this.isMobile = true

    getSignConfigList().then(
      response =>{
        if (response.code === 0) {
          response.data.forEach(element => {
            const colKey = element.colKey.split('_')
            if (colKey.length > 1) {
              if (this.rules[colKey[0]] && colKey[1] === 'required') {
                this.rules[colKey[0]][0]['required'] = element.colValue === 'true'
                this.formConfigData[element.colKey] = element.colValue === 'true'
              }else{
                this.formConfigData[element.colKey] = element.colValue
              }
            }else{
              this.formConfigData[element.colKey] = element.colValue
            }
          });
        }
      }
    )

    if (id != null&&id!=undefined) {
      get${conf.className}(id).then(response => {
        this.${conf.formModel} = response.data;
        taskComment(this.${conf.formModel}.processId).then(response => {
          this.commentList = response.data;
        });
        getSignPathApp(this.${conf.formModel}.processId).then(response => {
           this.signPath = response.data.info;
           this.activeCount = response.data.activeCount;
        });
        ${fileUploadStr}
        ${rangStr}
        ${cascaderStr}
        ${createds}
        ${mendths}
      });
    }
    this.getDicts("sign_status").then(response => {
      this.auditStatus = response.data;
    });
  },
  methods: {
    ${methods}
  }
}`
  return str
}

function buildAuditImport(conf){
  const importStr = `import {get${conf.className}, add${conf.className}, edit${conf.className}
  ,getSignPathApp,updateAndCheck,getSignConfigList} from "@/api/${conf.moduleName}/${conf.businessName}" \n`
    + `import '@/assets/styles/design-build/design-add-view.scss' \n`
    + `import {listTask, getTask, checkTask, taskComment} from "@/api/activiti/task" \n`
    + `import {getAccessToken, getZltAccessToken} from "@/utils/auth"; \n`
    + `import {previewFile, getHeader} from "@/utils/entfrm"; \n`
    + `import {getOptionsAndValues} from "@/api/system/dictData"; \n`
    + `import {listFileInfo, getFileInfo, delFileInfo, addFileInfo, editFileInfo, getByKey} from "@/api/system/fileInfo"; \n`
  return importStr
}


/* 生成審核頁面JS代碼（APP） 結束 */




function buildChildTableAttributesDetail(conf,childTable,dataList,methodList,optionsList,createdList, ruleList){
  dataList.push(`${childTable.entityName}Lists: [],`)
  buildChildListMethod(childTable,methodList,conf)
  buildChildOptions(childTable, optionsList,conf)
  buildChildOptionMethodDetail(childTable, methodList,conf)
  buildChildCreatedMethod(childTable, createdList,conf)
}

function buildChildTableAttributes(conf,childTable,dataList,methodList,optionsList,createdList, ruleList,mountedList){
  dataList.push(`${childTable.entityName}Lists: [],`)
  dataList.push(`${childTable.entityName}ActiveNames: [],`)
  buildChildListMethod(childTable,methodList,conf)  //定義參數
  buildChildOptions(childTable, optionsList,conf)   //定義字典參數
  buildChildOptionMethod(childTable, methodList,conf,mountedList)  //獲取字典數據 及創建時間組件
  buildChildCreatedMethod(childTable, createdList,conf)
}

function buildChildTableAttributesAudit(conf,childTable,dataList,methodList,optionsList,createdList, ruleList,mountedList,watchList){
  dataList.push(`${childTable.entityName}ActiveNames: [],`)
  buildChildOptionMethodAudit(childTable, methodList,conf,mountedList,watchList)  //獲取字典數據 及創建時間組件
  buildChildCreatedMethodAudit(childTable, createdList,conf)
}





function buildAttributesDetail(el, dataList, ruleList, optionsList, methodList, propsList, uploadVarList,createdList,conf,configDataList) {
  buildData(el, dataList,configDataList)
  buildRules(el, ruleList)

  //console.log(el)
  if (el.dictType) {
    // debugger
    buildOptions(el, optionsList)
    // if (el.dataType === 'dynamic') {
    // const model = `${el.vModel}Options`
    const vModel = titleCase(el.vModel)
    buildOptionMethodDetail(`get${vModel}Options`, methodList,el,conf.formModel)
    /* if(el.tag!="el-cascader"){ buildCreatedMethod(`get${vModel}Options`, createdList)}*/
    // }
  }

  // if (el.tag === 'el-cascader') {
  //       buildOptions(el, optionsList)
  // }

  if (el.props && el.props.props) {
    buildProps(el, propsList)
  }

  if (el.action && el.tag === 'el-upload') {
    // uploadVarList.push(
    //   `${el.vModel}Action: '${el.action}',
    //   ${el.vModel}fileList: [],`
    // )
    methodList.push(buildBeforeUpload(el))
    methodList.push(buildFunctionUpload(conf,el))
    // if (!el['auto-upload']) {
    //   methodList.push(buildSubmitUpload(el))
    // }
    const multiple = el.multiple ? 'true' : 'false'
    uploadVarList.push(
      `
      // 文件上传参数
      upload: {
        // 是否显示弹出层
        open: false,
        fileList: [],
        fileNameList: [],
        // 弹出层标题
        title: "",
        multiple: ${multiple},
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: this.getHeader(),
        // 上传的地址
        url: this.getAppBaseApi() + "/system/fileInfo/uploadWithOos"
      },`
    )
  }

  if (el.children) {
    el.children.forEach(el2 => {
      buildAttributesDetail(el2, dataList, ruleList, optionsList, methodList, propsList, uploadVarList,createdList,conf,configDataList)
    })
  }
}

function buildAttributes(el, dataList, ruleList, optionsList, methodList, propsList, uploadVarList,createdList,conf,configDataList) {
  buildData(el, dataList,configDataList)
  buildRules(el, ruleList)

  if (el.dictType) {
    // debugger
    buildOptions(el, optionsList)
    // if (el.dataType === 'dynamic') {
    // const model = `${el.vModel}Options`
    const vModel = titleCase(el.vModel)
    buildOptionMethod(`get${vModel}Options`, methodList,el,conf)
    buildCreatedMethod(`get${vModel}Options`, createdList)
    // }
  }

  // if (el.tag === 'el-cascader') {
  //       buildOptions(el, optionsList)
  // }

  if (el.props && el.props.props) {
    buildProps(el, propsList)
  }

  if (el.action && el.tag === 'el-upload') {
    // uploadVarList.push(
    //   `${el.vModel}Action: '${el.action}',
    //   ${el.vModel}fileList: [],`
    // )
    methodList.push(buildBeforeUpload(el))
    methodList.push(buildFunctionUpload(conf,el))
    // if (!el['auto-upload']) {
    //   methodList.push(buildSubmitUpload(el))
    // }
    const multiple = el.multiple ? 'true' : 'false'
    uploadVarList.push(
      `
      // 文件上传参数
      upload: {
        // 是否显示弹出层
        open: false,
        fileList: [],
        fileNameList: [],
        // 弹出层标题
        title: "",
        multiple: ${multiple},
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: this.getHeader(),
        // 上传的地址
        url: this.getAppBaseApi() + "/system/fileInfo/uploadWithOos"
      },`
    )
  }

  if (el.children) {
    el.children.forEach(el2 => {
      buildAttributes(el2, dataList, ruleList, optionsList, methodList, propsList, uploadVarList,createdList,conf,configDataList)
    })
  }
}

function buildAttributesApp(el, dataList, ruleList, optionsList, methodList, propsList, uploadVarList,createdList,conf,mountedList,configDataList) {
  buildData(el, dataList,configDataList)
  buildRules(el, ruleList)
  let showTimeStr = ''
  let createStr = ''
  if (el.dictType) {
    buildOptions(el, optionsList)

    const vModel = titleCase(el.vModel)
    buildOptionMethod(`get${vModel}Options`, methodList,el,conf)
    buildCreatedMethod(`get${vModel}Options`, createdList)
  }

  if(el.tag == "el-date-picker" ||el.tag == "el-time-picker"){

    showTimeStr += `${el.vModel}ShowDate(){
                                this.${el.vModel}SegmentPicker.show()
                        },`

    let selectValue = el.format
    //獲取當前日期是當年第幾周
    let getWeek = `let wn = function(nowDate){
        let firstWeekDay = new Date(nowDate.getFullYear(),0,(7 - new Date(nowDate.getFullYear(),0,1).getDay())); //本年第一周第一天的前一天日期
        let week = 0;
        if(firstWeekDay>=nowDate){ //時間小於當前年份第一周第一天則 歸為上一年最後一周
            firstWeekDay = new Date(nowDate.getFullYear()-1,0,(8 - new Date(nowDate.getFullYear()-1,0,1).getDay())); //本年第一周第一天日期
            week = Math.ceil((nowDate - firstWeekDay)/(86400000*7))
        }else{
            week = Math.ceil((nowDate - firstWeekDay)/(86400000*7))
        }
        return week;
    }`
    //判斷年份是否變化
    let getYear = `let getYear = function(nowDate){
                          let result = nowDate.getFullYear()
                          let firstWeekDay = new Date(nowDate.getFullYear(),0,(8 - new Date(nowDate.getFullYear(),0,1).getDay())); //本年第一周第一天的日期
                          if(firstWeekDay>nowDate){
                            result = nowDate.getFullYear() - 1
                          }
                          return result
                    }`
    if(selectValue.indexOf("WW")<0){
      getWeek = ""
    }
    selectValue = selectValue.replace(/yyyy|MM|dd|DD|WW|HH|hh|mm|ss/g, function(a){
      switch(a){
        case 'yyyy':
          if(selectValue.indexOf("WW")<0){
            return `"+selectDate.getFullYear()+"`;
          }else{
            return `"+getYear(selectDate)+"`;
          }
          break;
        case 'MM':
          return `"+tf(selectDate.getMonth()+1)+"`;
          break;
        case 'dd':
          return `"+tf(selectDate.getDate())+"`;
          break;
        case 'DD':
          return `"+tf(selectDate.getDay())+"`;
          break;
        case 'WW':
          return `"+wn(selectDate)+"`;
          break;
        case 'HH':
          return `"+tf(selectDate.getHours())+"`;
          break;
        case 'hh':
          return `"+tf(selectDate.getHours())+"`;
          break;
        case 'mm':
          return `"+tf(selectDate.getMinutes())+"`;
          break;
        case 'ss':
          return `"+tf(selectDate.getSeconds())+"`;
          break;
      }
    })

    if(selectValue.indexOf("\"+")==0){
      selectValue = selectValue.substr(2,selectValue.length);
    }
    if(selectValue.lastIndexOf("+\"")==selectValue.length-2){
      selectValue = selectValue.substr(0,selectValue.length-2);
    }
    if(el.type==='daterange'||el["is-range"]){
      createStr = ` this.${el.vModel}SegmentPicker = this.$createSegmentPicker({
                          data: this.${el.vModel}DateValue,
                          onSelect: (selectedDates, selectedVals, selectedTexts) => {
                            let tf = function(i){return (i < 10 ? '0' : '') + i};
                            ${getWeek}
                            ${getYear}
                            let value = new Array();
                            selectedDates.forEach(item=>{
                            let selectDate = new Date(item)
                            value.push(${selectValue})
                            })
                            this.formData.${el.vModel} = value;
                            this.${el.vModel}Value = value.join("  至  ")
                          },
                          onNext: (i, selectedDate, selectedValue, selectedText) => {
                            this.${el.vModel}DateValue[1].min = selectedDate
                            if (i === 0) {
                              this.${el.vModel}SegmentPicker.$updateProps({
                                data: this.${el.vModel}DateValue
                              })
                            }
                          }
                        });
                        `
      mountedList.push(createStr)
    }else{
      createStr = ` this.${el.vModel}SegmentPicker = this.$createSegmentPicker({
                          data: this.${el.vModel}DateValue,
                          onSelect: (selectedDates, selectedVals, selectedTexts) => {
                            var tf = function(i){return (i < 10 ? '0' : '') + i};
                            ${getWeek}
                            ${getYear}
                            selectedDates.forEach(item=>{
                            let selectDate = new Date(item)
                            this.formData.${el.vModel} = ${selectValue}
                            })
                            this.${el.vModel}Value = this.formData.${el.vModel}
                          }
                        });
                        `
      mountedList.push(createStr)
    }
    methodList.push(showTimeStr)
  }

  if (el.props && el.props.props) {
    buildProps(el, propsList)
  }

  if (el.action && el.tag === 'el-upload') {

    methodList.push(buildBeforeUpload(el))
    methodList.push(buildFunctionUpload(conf,el))

    const multiple = el.multiple ? 'true' : 'false'
    uploadVarList.push(
      `
      // 文件上传参数
      upload: {
        // 是否显示弹出层
        open: false,
        fileList: [],
        fileNameList: [],
        // 弹出层标题
        title: "",
        multiple: ${multiple},
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: this.getHeader(),
        // 上传的地址
        url: this.getAppBaseApi() + "/system/fileInfo/uploadWithOos"
      },`
    )
  }
}
//checkbox数据由对象转换为string
function buildCheckBoxStringifyMixinMethod(childTable, conf) {
  let checkboxStr = ''
  let dataList = []

  childTable.tableCols.forEach(column => {
    if(column.isAdd === '1' && column.dictType && column.dictType != ''&&column.htmlType != 'select'&&column.htmlType != 'radio'){
      dataList.push(`if(typeof ${conf.formModel}.${childTable.businessName}Lists[index].${column.javaField} == "object"){
              ${conf.formModel}.${childTable.businessName}Lists[index].${column.javaField} = JSON.stringify(${conf.formModel}.${childTable.businessName}Lists[index].${column.javaField})
      }`)
    }
    if(column.isAdd === '1'  && (column.htmlType == 'time-range'||column.htmlType == 'date-range')){
      dataList.push(`if(typeof ${conf.formModel}.${childTable.businessName}Lists[index].${column.javaField} == "object"){
             ${conf.formModel}.${childTable.businessName}Lists[index].${column.javaField} = JSON.stringify(${conf.formModel}.${childTable.businessName}Lists[index].${column.javaField})
      }`)
    }

  })
  if(dataList.length>0){
    checkboxStr = `  ${checkboxStr}
      for (let index in ${conf.formModel}.${childTable.businessName}Lists) {
          ${dataList.join("\n")}
      }`
  }
  return checkboxStr
}
//checkbox数据由string转换为对象
function buildCheckBoxParseMixinMethod(childTable, conf) {
  let checkboxObjStr = ''
  let checkboxObjs = []
  childTable.tableCols.forEach(column => {
    if(column.isAdd === '1' && column.htmlType == 'checkbox' && column.dictType && column.dictType != ''){
      checkboxObjs.push(`
        if(this.${conf.formModel}.${childTable.businessName}Lists[index].${column.javaField}){
          this.${conf.formModel}.${childTable.businessName}Lists[index].${column.javaField}Value = JSON.parse(this.${conf.formModel}.${childTable.businessName}Lists[index].${column.javaField})
        }`)
    }
  })
  if(checkboxObjs.length>0){
    checkboxObjStr = `for (let index in this.${conf.formModel}.${childTable.businessName}Lists) {
                          ${checkboxObjs.join("\n")}
                      }`
  }
  return checkboxObjStr
}


//Cascader
function buildCascaderMixinMethod(childTable, conf) {
  let checkboxObjStr = ''
  let checkboxObjs = []
  childTable.tableCols.forEach(column => {
    if(column.isAdd === '1' && column.htmlType == 'cascader' && column.dictType && column.dictType != ''){
      checkboxObjs.push(`
        if(this.${conf.formModel}.${childTable.businessName}Lists[index].${column.javaField}){
          this.${conf.formModel}.${childTable.businessName}Lists[index].${column.javaField} = JSON.parse(this.${conf.formModel}.${childTable.businessName}Lists[index].${column.javaField})
        }`)
    }
  })
  if(checkboxObjs.length>0){
    checkboxObjStr = `for (let index in this.${conf.formModel}.${childTable.businessName}Lists) {
                          ${checkboxObjs.join("\n")}
                      }`
  }
  return checkboxObjStr
}

//TimeRang
function buildTimeRangMixinMethod(childTable, conf) {
  let checkboxObjStr = ''
  let checkboxObjs = []
  childTable.tableCols.forEach(column => {
    if(column.isAdd === '1' && (column.htmlType == 'time-range' ||column.htmlType == 'date-range')){
      checkboxObjs.push(`if(this.${conf.formModel}.${childTable.businessName}Lists[index].${column.javaField}){
          this.${conf.formModel}.${childTable.businessName}Lists[index].${column.javaField} = JSON.parse(this.${conf.formModel}.${childTable.businessName}Lists[index].${column.javaField})
        }`)
    }
  })
  if(checkboxObjs.length>0){
    checkboxObjStr = `for (let index in this.${conf.formModel}.${childTable.businessName}Lists) {
                          ${checkboxObjs.join("\n")}
                      }`
  }
  return checkboxObjStr
}

//TimeRang
function buildTimeRangMixinMethodAudit(childTable, conf) {
  let checkboxObjStr = ''
  let checkboxObjs = []
  childTable.tableCols.forEach(column => {
    if(column.isAdd === '1' && (column.htmlType == 'time-range' ||column.htmlType == 'date-range')&&(childTable.currentorder!=""&&childTable.currentorder!=undefined)){   //當前字段可編輯
      checkboxObjs.push(`if(this.${conf.formModel}.${childTable.businessName}Lists[index].${column.javaField}){
          this.${conf.formModel}.${childTable.businessName}Lists[index].${column.javaField}${childTable.className}Value = JSON.parse(this.${conf.formModel}.${childTable.businessName}Lists[index].${column.javaField}).join(" 至 ")
        }`)
    }
  })
  if(checkboxObjs.length>0){
    checkboxObjStr = `for (let index in this.${conf.formModel}.${childTable.businessName}Lists) {
                          ${checkboxObjs.join("\n")}
                      }`
  }
  return checkboxObjStr
}

function mixinMethod(conf,type) {
  const list = [];
  let masterCheckBoxStr = ''
  let masterCascaderStr = ''
  let checkboxStr = ''
  let dateRange = ''
  let timeRange = ''
  let childList =[];

  conf.fields.forEach(el => {
    if(el.tag === 'el-checkbox-group'){
      masterCheckBoxStr = masterCheckBoxStr + `${conf.formModel}.${el.vModel} = JSON.stringify(${conf.formModel}.${el.vModel}) \n`
    }else if(el.tag === 'el-cascader'){  //級聯
      masterCascaderStr = masterCascaderStr + `${conf.formModel}.${el.vModel} = JSON.stringify(${conf.formModel}.${el.vModel}) \n`
    }else if(el.tag === 'el-date-picker'&&el.type == 'daterange' ){  //日期範圍
      dateRange = dateRange + `${conf.formModel}.${el.vModel} = JSON.stringify(${conf.formModel}.${el.vModel}) \n`
    }else if(el.tag === 'el-time-picker'&&el["is-range"]){  //時間範圍
      timeRange = timeRange + `${conf.formModel}.${el.vModel} = JSON.stringify(${conf.formModel}.${el.vModel}) \n`
    }else if(el.tag === 'entfrm-child-table-form'){ //子表
      childList.push(el.element)
    }
  })
  childList.forEach(childTable => {
    checkboxStr = checkboxStr + buildCheckBoxStringifyMixinMethod(childTable,conf)
  })

  let subBtn = `handleSubmit: function() {
            this.$refs["${conf.formRef}"].validate(valid => {
              if (valid) {
                const formData = JSON.parse(JSON.stringify(this.${conf.formModel}))
                ${masterCheckBoxStr}
                ${checkboxStr}
                ${masterCascaderStr}
                ${dateRange}
                ${timeRange}
                if (formData.id != undefined) {
                       edit${conf.className}AndStartProcess(${conf.formModel}).then(response => {
                          if (response.code === 0) {
                            this.$message({ showClose: true, message: this.$t('tips.updateSuccess'), type: "success", offset: 50});
                            this.closeForm();
                          }
                          else {
                            this.msgError(response.msg);
                          }
                        });
                }else {
                      add${conf.className}AndStartProcess(${conf.formModel}).then(response => {
                                  if (response.code === 0) {
                                    this.$message({ showClose: true, message: this.$t('tips.createSuccess'), type: "success", offset: 50});
                                    this.closeForm();
                                  }
                                  else {
                                    this.msgError(response.msg);
                                  }
                                });
                }
                /**
                 * 提交的時候刪除被標記的需要刪除的附件
                 */
                if (this.${conf.formModel}.attachids) {
                  this.upload.fileNameList.forEach(item => {
                    if (item) {
                      delFileInfo(item);
                    }
                  })
                }
              }
            })
      }, `
  let returnBtn = `closeForm(){
                      //关闭子页面
                        this.$router.go(-1) // 返回
                   },`
  if(conf.isPreView){
    subBtn = `handleSubmit: function() {
                 this.$message({ showClose: true, message: "預覽狀態提交按鈕不可用", type: "success", offset: 50});
              }, `
    returnBtn = `closeForm(){
                      this.$message({ showClose: true, message: "預覽狀態返回按鈕不可用", type: "success", offset: 50});
                 },`
  }

  const minxins = {
    file: confGlobal.formBtns ? {
      submitForm: `submitForm() {
        this.$refs['${confGlobal.formRef}'].validate(valid => {
          if (valid) {
            const ${conf.formModel} = JSON.parse(JSON.stringify(this.${conf.formModel}))
            ${masterCheckBoxStr}
            ${checkboxStr}
            ${masterCascaderStr}
            ${dateRange}
            ${timeRange}
            if(${conf.formModel}.id != undefined) {
              edit${conf.className}(${conf.formModel}).then(response => {
                if (response.code === 0) {
                  this.$message({ showClose: true, message: this.$t('tips.updateSuccess'), type: "success", offset: 50});
                  this.closeForm();
                } else {
                    this.msgError(response.msg);
                }
              });
            }else{
              add${conf.className}(${conf.formModel}).then(response => {
                if (response.code === 0) {
                    this.$message({ showClose: true, message: this.$t('tips.createSuccess'), type: "success", offset: 50});
                    this.closeForm();
                } else {
                    this.msgError(response.msg);
                }
              });
            }
            /**
             * 提交的時候刪除被標記的需要刪除的附件
             */
            if (this.${conf.formModel}.attachids) {
              this.upload.fileNameList.forEach(item => {
                if (item) {
                  delFileInfo(item);
                }
              })
            }
          }
        })
      },`,
      handleSubmit: subBtn,
      resetForm: `resetForm() {
        this.$refs['${confGlobal.formRef}'].resetFields()
      },`,
      closeForm: returnBtn,
    } : null,
    dialog: {
      onOpen: 'onOpen() {},',
      onClose: `onClose() {
        this.$refs['${confGlobal.formRef}'].resetFields()
      },`,
      close: `close() {
        this.$emit('update:visible', false)
      },`,
      handelConfirm: `handelConfirm() {
        this.$refs['${confGlobal.formRef}'].validate(valid => {
          if(!valid) return
          this.close()
        })
      },`
    }
  }

  const methods = minxins[type]
  if (methods) {
    Object.keys(methods).forEach(key => {
      list.push(methods[key])
    })
  }

  return list
}

function mixinDetailMethod(conf,type) {
  const list = [];
  const minxins = {
    file: {  closeForm: `closeForm(){
          //关闭子页面
            this.$router.go(-1) // 返回
        },`,
      handleTrack: `handleTrack() {
          this.imgUrl = this.getAppBaseApi() + '/activiti/task/track/' + this.${conf.formModel}.processId,
            this.showImgDialog = true
        },`,
      statusFormat: `statusFormat(row, column) {
          return this.selectDictLabel(this.auditStatus, row.status);
        },`
    }
  }

  const methods = minxins[type]
  if (methods) {
    Object.keys(methods).forEach(key => {
      list.push(methods[key])
    })
  }

  return list
}

function mixinRejectMethod(conf,type) {
  const list = [];
  let masterCheckBoxStr = ''
  let masterCascaderStr = ''
  let checkboxStr = ''
  let childList =[];
  conf.fields.forEach(el => {
    if(el.tag === 'el-checkbox-group'){
      masterCheckBoxStr = masterCheckBoxStr + `${conf.formModel}.${el.vModel} = JSON.stringify(${conf.formModel}.${el.vModel}) \n`
    }else if(el.tag === 'el-cascader'){
      masterCascaderStr = masterCascaderStr + `${conf.formModel}.${el.vModel} = JSON.stringify(${conf.formModel}.${el.vModel}) \n`
    }else if(el.tag === 'entfrm-child-table-form'){ //子表
      childList.push(el.element)
    }
  })
  childList.forEach(childTable => {
    checkboxStr = checkboxStr + buildCheckBoxStringifyMixinMethod(childTable,conf)
  })
  const minxins = {
    file: {
      submitForm: `submitForm() {
        this.$refs['${confGlobal.formRef}'].validate(valid => {
          if (valid) {
            const ${conf.formModel} = JSON.parse(JSON.stringify(this.${conf.formModel}))
            ${masterCheckBoxStr}
            ${checkboxStr}
            ${masterCascaderStr}
            if(${conf.formModel}.id != undefined) {
              edit${conf.className}(${conf.formModel}).then(response => {
                if (response.code === 0) {
                  this.$message({ showClose: true, message: this.$t('tips.updateSuccess'), type: "success", offset: 50});
                  this.closeForm();
                } else {
                    this.msgError(response.msg);
                }
              });
            }else{
              add${conf.className}(${conf.formModel}).then(response => {
                if (response.code === 0) {
                    this.$message({ showClose: true, message: this.$t('tips.createSuccess'), type: "success", offset: 50});
                    this.closeForm();
                } else {
                    this.msgError(response.msg);
                }
              });
            }
            /**
             * 提交的時候刪除被標記的需要刪除的附件
             */
            if (this.${conf.formModel}.attachids) {
              this.upload.fileNameList.forEach(item => {
                if (item) {
                  delFileInfo(item);
                }
              })
            }
          }
        })
      },`,
      closeForm: `closeForm(){
            this.$router.go(-1) // 返回
        },`,
      statusFormat: `statusFormat(row, column) {
          return this.selectDictLabel(this.auditStatus, row.status);
        },`,
      handleTask: `handleTask: function (pass) {
          this.isDisabled=true;
          this.$refs["${conf.formRef}"].validate(valid => {
              this.${conf.formModel}.pass = pass
              checkTask(this.${conf.formModel}).then(response => {
                if (response.code === 0) {
                  this.$message({ showClose: true, message: this.$t('tips.operationSuccessful'), type: "success", offset: 50});
                  this.closeForm();
                } else {
                  this.msgError(response.msg);
                }
              });
          });
        },`,
      handleSubmit: `handleSubmit: function() {
            this.$refs["${conf.formRef}"].validate(valid => {
              if (valid) {
                const formData = JSON.parse(JSON.stringify(this.${conf.formModel}))
                ${masterCheckBoxStr}
                ${checkboxStr}
                ${masterCascaderStr}
               edit${conf.className}AndResubmitProcess(${conf.formModel}).then(response => {
                  if (response.code === 0) {
                    this.$message({ showClose: true, message: this.$t('tips.updateSuccess'), type: "success", offset: 50});
                    this.closeForm();
                  }
                  else {
                    this.msgError(response.msg);
                  }
                });
                /**
                 * 提交的時候刪除被標記的需要刪除的附件
                 */
                if (this.${conf.formModel}.attachids) {
                  this.upload.fileNameList.forEach(item => {
                    if (item) {
                      delFileInfo(item);
                    }
                  })
                }
              }
            })
      }, `
    }
  }
  const methods = minxins[type]
  if (methods) {
    Object.keys(methods).forEach(key => {
      list.push(methods[key])
    })
  }

  return list
}


function buildData(conf, dataList,configDataList) {
  if(conf.layout=="signFormItem"){  //簽核節點
    if(conf.required){
      let defaultValue
      if (typeof (conf.defaultValue) === 'string' && !conf.multiple) {
        defaultValue = `'${conf.defaultValue}'`
      } else {
        defaultValue = `${JSON.stringify(conf.defaultValue)}`
      }
      dataList.push(`${conf.modelNo}: ${defaultValue},`)
    }
    configDataList.push(`${conf.modelNo}: "${conf.label}",`)
    configDataList.push(`${conf.modelNo}_required: ${conf.required},`)
  }else if("labelItem" === conf.layout) {
    if (conf.isLabelDynamic) {
      configDataList.push(`${conf.labelDynamicKey}_label: \`${conf.label}\`,`)
      configDataList.push(`${conf.labelDynamicKey}_text: \`${conf.textarea}\`,`)
    }
  }else{
    if (conf.vModel === undefined) return
    let defaultValue
    if (typeof (conf.defaultValue) === 'string' && !conf.multiple) {
      defaultValue = `'${conf.defaultValue}'`
    } else {
      defaultValue = `${JSON.stringify(conf.defaultValue)}`
    }
    // if(conf.options){
    //   dataList.push(`${conf.vModel}Options: [],`)
    // }
    if (conf.action && conf.tag === 'el-upload') {
      defaultValue = `""`
    }
    dataList.push(`${conf.vModel}: ${defaultValue},`)
  }
}
function buildChildData(conf, dataList) {
  let defaultValue
  if (conf.htmlType == 'checkbox'||conf.htmlType == 'cascader'||conf.htmlType == 'date-range') {
    defaultValue = `[]`
  } else if(conf.htmlType == 'slider'||conf.htmlType == 'number'||conf.htmlType == 'rate'){
    defaultValue = 0
  } else if(conf.htmlType == 'el-switch'){
    defaultValue = "false"
  }  else if (typeof (conf.defValue) === 'string') {
    defaultValue = `'${conf.defValue}'`
  } else if (conf.defValue == null) {
    defaultValue = `''`
  } else{
    defaultValue = `${JSON.stringify(conf.defValue)}`
  }
  dataList.push(`${conf.javaField}: ${defaultValue}`)
}


function buildRulesAudit(conf, ruleList) {
  if(conf.layout=="signFormItem" && conf.isShowApp && (conf.currentorder!=""&&conf.currentorder!=undefined)) {  //簽核節點   當前字段可編輯
    if(conf.required){
      const rules = []
      const type = isArray(conf.defaultValue) ? 'type: \'array\',' : ''
      let message = isArray(conf.defaultValue) ? `請至少選擇一個${conf.label}` : conf.placeholder
      if (message === undefined) message = `${conf.label}不能為空`
      rules.push(`{ required: true, ${type} message: '${message}', trigger: '${"change"}' }`)
      ruleList.push(`${conf.modelNo}: [${rules.join(',')}],`)
    }
  }else if("childTableItem" === conf.layout && (conf.currentorder!=""&&conf.currentorder!=undefined)){  //  當前字段可編輯
    buildChildRules(conf,ruleList)
  }else if((conf.currentorder!=""&&conf.currentorder!=undefined)){  //  當前字段可編輯
    if (conf.vModel === undefined) return
    const rules = []
    if (trigger[conf.tag]  && conf.isShowApp) {
      if (conf.required) {
        const type = isArray(conf.defaultValue) ? 'type: \'array\',' : ''
        let message = isArray(conf.defaultValue) ? `請至少選擇一個${conf.label}` : conf.placeholder
        if (message === undefined) message = `${conf.label}不能為空`
        rules.push(`{ required: true, ${type} message: '${message}', trigger: '${trigger[conf.tag]}' }`)
      }
      if (conf.regList && isArray(conf.regList)) {
        conf.regList.forEach(item => {
          if (item.pattern) {
            rules.push(`{ pattern: ${eval(item.pattern)}, message: '${item.message}', trigger: '${trigger[conf.tag]}' }`)
          }
        })
      }
      ruleList.push(`${conf.vModel}: [${rules.join(',')}],`)
    }
  }
}

function buildRules(conf, ruleList) {
  if(conf.layout=="signFormItem" && conf.isShowApp) {  //簽核節點
    if(conf.required){
      const rules = []
      const type = isArray(conf.defaultValue) ? 'type: \'array\',' : ''
      let message = isArray(conf.defaultValue) ? `請至少選擇一個${conf.label}` : conf.placeholder
      if (message === undefined) message = `${conf.label}不能為空`
      rules.push(`{ required: true, ${type} message: '${message}', trigger: '${"change"}' }`)
      ruleList.push(`${conf.modelNo}: [${rules.join(',')}],`)
    }
  }else if("childTableItem" === conf.layout ){
    buildChildRules(conf,ruleList)
  }else{
    if (conf.vModel === undefined) return
    const rules = []
    if (trigger[conf.tag]  && conf.isShowApp) {
      if (conf.required) {
        const type = isArray(conf.defaultValue) ? 'type: \'array\',' : ''
        let message = isArray(conf.defaultValue) ? `請至少選擇一個${conf.label}` : conf.placeholder
        if (message === undefined) message = `${conf.label}不能為空`
        rules.push(`{ required: true, ${type} message: '${message}', trigger: '${trigger[conf.tag]}' }`)
      }
      if (conf.regList && isArray(conf.regList)) {
        conf.regList.forEach(item => {
          if (item.pattern) {
            rules.push(`{ pattern: ${eval(item.pattern)}, message: '${item.message}', trigger: '${trigger[conf.tag]}' }`)
          }
        })
      }
      ruleList.push(`${conf.vModel}: [${rules.join(',')}],`)
    }
  }
}


//設置從表表單驗證規則
function buildChildRules(conf, ruleList) {
  let childTable = conf.element
  childTable.tableCols.forEach(column => {
    const rules = []
    //if(conf.childAttributesMap[column.javaField] && conf.childAttributesMap[column.javaField].isShowApp) //是否app顯示字段 存在且必須為true
    if (!conf.childAttributesMap[column.javaField] || conf.childAttributesMap[column.javaField].isShowApp !=false) //是否app顯示字段不存在默認為顯示 不為false則為顯示
    {
      if (column.isAdd == '1') {//conf.childAttributes.isShowApp
        if (column.isRequired === '1') {
          if (trigger[htmlTypeConvertForm[column.htmlType]]) {
            const type = htmlTypeConvertForm[column.htmlType] === 'el-checkbox-group' ? 'type: \'array\',' : ''
            let message = htmlTypeConvertForm[column.htmlType] === 'el-checkbox-group' ? `請至少選擇一個${column.columnComment}` : column.placeholder
            if (message === undefined) message = `${column.columnComment}不能為空`
            rules.push(`{ required: true, ${type} message: '${message}', trigger: '${trigger[htmlTypeConvertForm[column.htmlType]]}' }`)
          }
        }
      }
      let regList = conf.childRegMap[column.javaField]
      if (regList) {
        regList.forEach(item => {
          if (item.pattern) {
            rules.push(`{ pattern: ${eval(item.pattern)}, message: '${item.message}', trigger: '${trigger[htmlTypeConvertForm[column.htmlType]]}' }`)
          }
        })
      }
      if(rules.length>0){
        ruleList.push(`${childTable.entityName}_${column.javaField}: [${rules.join(',')}],`)
      }
    }
  })
}

function buildOptions(conf, optionsList) {
  if (conf.vModel === undefined) return
  // debugger
  if (conf.dataType === 'dynamic') {
    conf.options = []
  }
  // const str = `${conf.vModel}Options: ${JSON.stringify(conf.options)},`
  const str = `${conf.vModel}Options: [],`
  const strValues = `${conf.vModel}Value: null,`
  optionsList.push(str)
  optionsList.push(strValues)
}

function buildProps(conf, propsList) {
  // debugger
  if (conf.dataType === 'dynamic') {
    conf.valueKey !== 'value' && (conf.props.props.value = conf.valueKey)
    conf.labelKey !== 'label' && (conf.props.props.label = conf.labelKey)
    conf.childrenKey !== 'children' && (conf.props.props.children = conf.childrenKey)
  }
  const str = `${conf.vModel}Props: ${JSON.stringify(conf.props.props)},`
  propsList.push(str)
}

function buildBeforeUpload(conf) {
  const unitNum = units[conf.sizeUnit]; let rightSizeCode = ''; let acceptCode = ''; const
    returnList = []
  if (conf.fileSize) {
    rightSizeCode = `let isRightSize = file.size / ${unitNum} < ${conf.fileSize}
    if(!isRightSize){
      this.$message.error('文件大小超过 ${conf.fileSize}${conf.sizeUnit}')
    }`
    returnList.push('isRightSize')
  }
  if (conf.accept) {
    acceptCode = `let isAccept = new RegExp('${conf.accept}').test(file.type)
    if(!isAccept){
      this.$message.error('应该选择${conf.accept}类型的文件')
    }`
    returnList.push('isAccept')
  }
  const str = `${conf.vModel}BeforeUpload(file) {
    ${rightSizeCode}
    ${acceptCode}
    return ${returnList.join('&&')}
  },`
  return returnList.length ? str : ''
}

function buildFunctionUpload(conf,el) {
  const str = `
  // 文件上传成功处理
  uploadsucces(response, file, fileList) {
    if (response.data.name != undefined && response.data.name != "undefined") {
      this.upload.fileList.push({name: file.name, url: response.data.name});
      this.${conf.formModel}.${el.vModel} += response.data.name + ",";
    }
  },
  handleChange(file, fileList) {

  },
  handleExceed(file, fileList) {

  },
  handleRemove(file, fileList) {
    this.$emit("delUploadImage", file.name);
    const index = this.upload.fileList.indexOf(file);
    this.upload.fileList.splice(index, 1);
    if(this.${conf.formModel}.${el.vModel}&&this.${conf.formModel}.${el.vModel}!="null"){
          this.${conf.formModel}.${el.vModel} = this.${conf.formModel}.${el.vModel}.replace(file.url + ",", "");
    }
    this.upload.fileNameList.push(file.url);
    // delFileInfo(file.url);
  },
  handlePreview(file) {
    previewFile(file.url)
  },`
  return str
}

function buildSubmitUpload(conf) {
  const str = `submitUpload() {
    this.$refs['${conf.vModel}'].submit()
  },`
  return str
}


function buildOptionMethodDetail(methodName, methodList,conf,formModel) {

  if (conf.tag === 'el-cascader') {
    const str = `${methodName}(key) {
      if(!key){ key = "CAATY"}
      this.getDicts(key).then(response => {
        if (response.data.length > 0) {
          response.data.forEach(element => {
            if(element.value == this.${formModel}.${conf.vModel}[0] ) {
              this.${formModel}.${conf.vModel}.splice(0,1)
              if(this.${conf.vModel}Value!=undefined){this.${conf.vModel}Value += "/"+ element.label
              }else{this.${conf.vModel}Value = element.label}
              if(this.${formModel}.${conf.vModel}!=null&&this.${formModel}.${conf.vModel}.length>0){
                this.${methodName}(element.value)
              }
            }
          });
        }
      });
    },`
    methodList.push(str)
  }
  /*else{
    const str = `${methodName}() {
        this.getDicts("${conf.dictType}").then(response => {
          this.${conf.vModel}Options = response.data;
      });
    },`
    methodList.push(str)
  }*/
}


function buildOptionMethodAudit(methodName, methodList,conf,formModel) {
  if (conf.tag === 'el-cascader'&&(conf.currentorder==""||conf.currentorder==undefined)) {  //當前字段不可編輯
    const str = `${methodName}(key) {
      if(!key){
         key = "CAATY"
         this.${conf.vModel}CascaderOption =  JSON.stringify(this.${formModel}.${conf.vModel})
         this.${conf.vModel}CascaderOption =  JSON.parse(this.${conf.vModel}CascaderOption)
      }
      this.getDicts(key).then(response => {
        if (response.data.length > 0) {
          response.data.forEach(element => {
            if(element.value == this.${formModel}.${conf.vModel}[0] ) {
              this.${conf.vModel}CascaderOption.splice(0,1)
              if(this.${conf.vModel}Value!=undefined){this.${conf.vModel}Value += "/"+ element.label
              }else{this.${conf.vModel}Value = element.label}
              if(this.${conf.vModel}CascaderOption!=null&&this.${conf.vModel}CascaderOption.length>0){
                this.${methodName}(element.value)
              }
            }
          });
        }
      });
    },`
    methodList.push(str)
  }
}


function buildOptionMethodAuditEdit(methodName, methodList,el,conf) {
  if (el.tag === 'el-cascader'&&(el.currentorder!=""&&el.currentorder!=undefined)) { //級聯  當前字段可編輯
    const str = `${methodName}() {
          this.getDicts("${el.dictType}").then(response => {
            this.${el.vModel}Options = response.data;
              if(response.data.length > 0){
                response.data.forEach(element => {
                   if(this.${conf.formModel}.${el.vModel}[0] == element.value){
                        this.${el.vModel}Value = element.label
                   }
                  let selectValue = JSON.stringify(this.${conf.formModel}.${el.vModel})
                  selectValue = JSON.parse(selectValue)
                  getOptionsAndValues(element,this,selectValue,this.${el.vModel}Value,"${el.vModel}Value");
                });
              }
        });
      },`

    const showStr = `${el.vModel}MainShowPannel(){
                           if(!this.${el.vModel}MainCascadePicker){
                              let _self = this
                              this.${el.vModel}MainCascadePicker = this.$createCascadePicker({
                                    title: '${el.label}',
                                    data: this.${el.vModel}Options,
                                    alias: {value:"value",text:"label"},
                                    swipeTime: 1000,
                                    onSelect:function(selectedVal, selectedIndex, selectedText){
                                      _self.formData.${el.vModel} = selectedVal
                                      _self.${el.vModel}Value = selectedText.join("/")
                                    },
                              })
                           }
                           this.${el.vModel}MainCascadePicker.show()
                    },
                    `
    methodList.push(str)
    methodList.push(showStr)
  }else if(el.tag === 'el-select'){
    const str = `${methodName}() {
        this.getDicts("${el.dictType}").then(response => {
            this.${el.vModel}Options = response.data;
            if(this.${conf.formModel}.${el.vModel}){
             this.${el.vModel}Value = this.${conf.formModel}.${el.vModel}
                response.data.forEach( item =>{
                       this.${el.vModel}Value = this.${el.vModel}Value.replace(item.value,item.label);
                })
            }
        });
    },`
    methodList.push(str)
    const showStr = `${el.vModel}MainShowPannel(){
                           if(!this.${el.vModel}MainPicker){
                               let _self = this
                               this.${el.vModel}MainPicker = this.$createPicker({
                                     title: '${el.label}',
                                     data: [this.${el.vModel}Options],
                                     alias: {value:"value",text:"label"},
                                     swipeTime: 1000,
                                     onSelect:function(selectedVal, selectedIndex, selectedText){
                                      _self.formData.${el.vModel} = selectedVal[0];
                                      _self.${el.vModel}Value = selectedText[0];
                                    },
                              })
                           }
                          this.${el.vModel}MainPicker.show()
                    },
                    `
    methodList.push(showStr)
  }else if((el.currentorder!=""&&el.currentorder!=undefined)){ //當前字段可編輯
    const str = `${methodName}() {
        this.getDicts("${el.dictType}").then(response => {
          this.${el.vModel}Options = response.data;
      });
    },`
    methodList.push(str)
  }

}



function buildOptionMethod(methodName, methodList,el,conf) {
  if (el.tag === 'el-cascader') { //級聯
    const str = `${methodName}() {
          this.getDicts("${el.dictType}").then(response => {
            this.${el.vModel}Options = response.data;
              if(response.data.length > 0){
                response.data.forEach(element => {
                   if(this.${conf.formModel}.${el.vModel}[0] == element.value){
                        this.${el.vModel}Value = element.label
                   }
                  getOptionsAndValues(element,this,this.${conf.formModel}.${el.vModel},this.${el.vModel}Value,"${el.vModel}Value");
                });
              }
        });
      },`

    const showStr = `${el.vModel}MainShowPannel(){
                           if(!this.${el.vModel}MainCascadePicker){
                              let _self = this
                              this.${el.vModel}MainCascadePicker = this.$createCascadePicker({
                                    title: '${el.label}',
                                    data: this.${el.vModel}Options,
                                    alias: {value:"value",text:"label"},
                                    swipeTime: 1000,
                                    onSelect:function(selectedVal, selectedIndex, selectedText){
                                      _self.formData.${el.vModel} = selectedVal
                                      _self.${el.vModel}Value = selectedText.join("/")
                                    },
                              })
                           }
                           this.${el.vModel}MainCascadePicker.show()
                    },
                    `
    methodList.push(str)
    methodList.push(showStr)
  }else if(el.tag === 'el-select'){
    const str = `${methodName}() {
        this.getDicts("${el.dictType}").then(response => {
            this.${el.vModel}Options = response.data;
            if(this.${conf.formModel}.${el.vModel}){
             this.${el.vModel}Value = this.${conf.formModel}.${el.vModel}
                response.data.forEach( item =>{
                       this.${el.vModel}Value = this.${el.vModel}Value.replace(item.value,item.label);
                })
            }
        });
    },`
    methodList.push(str)
    const showStr = `${el.vModel}MainShowPannel(){
                           if(!this.${el.vModel}MainPicker){
                               let _self = this
                               this.${el.vModel}MainPicker = this.$createPicker({
                                     title: '${el.label}',
                                     data: [this.${el.vModel}Options],
                                     alias: {value:"value",text:"label"},
                                     swipeTime: 1000,
                                     onSelect:function(selectedVal, selectedIndex, selectedText){
                                      _self.formData.${el.vModel} = selectedVal[0];
                                      _self.${el.vModel}Value = selectedText[0];
                                    },
                              })
                           }
                          this.${el.vModel}MainPicker.show()
                    },
                    `
    methodList.push(showStr)
  }else{
    const str = `${methodName}() {
        this.getDicts("${el.dictType}").then(response => {
          this.${el.vModel}Options = response.data;
      });
    },`
    methodList.push(str)
  }

}

function buildChildOptionMethodDetail(childTable, methodList,conf) {
  childTable.tableCols.forEach(column => {
    if(column.isAdd === '1' && column.dictType && column.dictType != ''){
      let methodName = 'get' + titleCase(column.javaField)+ childTable.className + 'Options'
      let changeSelect = ""
      if(column.htmlType === "select"||column.htmlType === "radio"){
        changeSelect = `this.${column.javaField}${childTable.className}Options.forEach(item=>{
                            this.${conf.formModel}.${childTable.entityName}Lists.forEach((selectInfo,n)=>{
                                if(item.value==selectInfo.${column.javaField}){
                                       this.${conf.formModel}.${childTable.entityName}Lists[n].${column.javaField}Value = item.label
                                }
                            })
                         })`
      }
      if(column.htmlType === "checkbox"){
        changeSelect = `this.${conf.formModel}.${childTable.entityName}Lists.forEach((selectInfo,n)=>{
                            if(selectInfo.${column.javaField}){
                               let selectValue = ''
                               if(typeof selectInfo.${column.javaField} == "string"){
                                  selectValue = JSON.parse(selectInfo.${column.javaField}).join()
                               }else{
                                  selectValue = (selectInfo.${column.javaField}).join()
                               }
                                this.${column.javaField}${childTable.className}Options.forEach(item=>{
                                    selectValue = selectValue.replace(item.value, item.label);
                                })
                             this.${conf.formModel}.${childTable.entityName}Lists[n].${column.javaField}Value = selectValue
                            }
                         })`
      }
      if(column.htmlType=="cascader"){
        let cascaderStr = `${column.javaField}${childTable.className}Format(value){
                                if(typeof value == "string"){
                                    value = JSON.parse(value)
                                }else{  //值為數組時 通過轉換json實現深拷貝與參數脫離關係
                                    value = JSON.stringify(value)
                                    value = JSON.parse(value)
                                }
                                let result = ""
                                if(value.length > 0&&this.${column.javaField}${childTable.className}Options.length>0){
                                    this.${column.javaField}${childTable.className}Options.forEach(item => {
                                        if(item.value == value[0]){
                                              this.${column.javaField}${childTable.className}Value = item.label
                                              value.splice(0, 1)
                                              if(value.length>0){
                                                  result = this.${column.javaField}${childTable.className}Cascader(item.children,value,result)
                                              }
                                        }
                                    })
                                }
                                return this.${column.javaField}${childTable.className}Value;
                             },
                             ${column.javaField}${childTable.className}Cascader(item,value,result) {
                                let returnInfo = ''
                                if(item){
                                    item.forEach(childItem => {
                                        if(childItem.value == value[0]){
                                            this.${column.javaField}${childTable.className}Value += "/" + childItem.label
                                            value.splice(0, 1)
                                            if(value.length>0){
                                                this.${column.javaField}${childTable.className}Cascader(childItem.children,value)
                                            }
                                            if(value.length>0){
                                                let childReturn = this.${column.javaField}${childTable.className}Cascader(childItem.children,value,result)
                                                if(childReturn){
                                                    returnInfo = childReturn
                                                }
                                            }else{
                                                returnInfo = result
                                            }
                                        }
                                    })
                                }
                                if(returnInfo!=''){
                                    return  returnInfo
                                }
                            },`
        changeSelect = `if(response.data.length > 0){
                                response.data.forEach(element => {
                                  this.getOptions(element);
                                });
                            }`
        methodList.push(cascaderStr)
      }
      const str = `${methodName}() {
          this.getDicts("${column.dictType}").then(response => {
            this.${column.javaField}${childTable.className}Options = response.data;
            ${changeSelect}
        });
      },`
      methodList.push(str)
    }
    if(column.htmlType=="time-range"||column.htmlType=="date-range"){
      let dataStr = `${column.javaField}${childTable.className}Format(value){
                          let returnInfo = ""
                            if(value&&value!="null"){
                                 if(typeof value == "string"){
                                     returnInfo = JSON.parse(value).join("  至  ")
                                 }else{
                                     returnInfo = value.join("  至  ")
                                 }
                            }
                          return returnInfo
                       },`

      methodList.push(dataStr)
    }
  })
}

function buildChildOptionMethod(childTable, methodList,conf,mountedList) {

  childTable.tableCols.forEach(column => {
    if(column.isAdd === '1' && column.dictType && column.dictType != ''){//下拉框 級聯下拉框  單選框 複選框
      let methodName = 'get' + titleCase(column.javaField) +childTable.className+ 'Options'
      if(column.htmlType=="select"){
        const str = `${methodName}() {
                        this.getDicts("${column.dictType}").then(response => {
                            this.${column.javaField}${childTable.className}Options = response.data;
                            let _self = this
                            if(this.${conf.formModel}.${childTable.entityName}Lists&&this.${conf.formModel}.${childTable.entityName}Lists.length>0){
                               for (let index in this.${conf.formModel}.${childTable.entityName}Lists) {
                                    response.data.forEach( item =>{
                                            if(item.value == this.${conf.formModel}.${childTable.entityName}Lists[index].${column.javaField}){
                                                 this.$set(this.${conf.formModel}.${childTable.entityName}Lists[index],"${column.javaField}${childTable.className}Value",item.label)
                                            }
                                     })
                               }
                            }
                            this.${column.javaField}${childTable.className}Picker = this.$createPicker({
                                title: '${column.columnComment}',
                                data: [this.${column.javaField}${childTable.className}Options],
                                alias: {
                                  value: "value",
                                  text: "label"
                                },
                                swipeTime: 1000,
                                onSelect: function(selectedVal, selectedIndex, selectedText) {
                                     _self.$set(_self.${conf.formModel}.${childTable.entityName}Lists[_self.${childTable.entityName}], "${column.javaField}${childTable.className}Value", selectedText[0])
                                     _self.${conf.formModel}.${childTable.entityName}Lists[_self.${childTable.entityName}].${column.javaField} = selectedVal[0]
                                },
                            })
                        });
                    },`
        const showStr = `${column.javaField}${childTable.className}ShowPannel(index,row){
                              this.${childTable.entityName} = index
                              this.${column.javaField}${childTable.className}Picker.show()
                        },`
        methodList.push(str)
        methodList.push(showStr)
      }else if(column.htmlType=="cascader"){
        const str = `${methodName}() {
              this.getDicts("${column.dictType}").then(response => {
                  this.${column.javaField}${childTable.className}Options = response.data;
                    if(response.data.length > 0){
                      response.data.forEach(element => {
                        this.getOptions(element);
                      });
                    }
                  });
               },`
        const showStr = `${column.javaField}${childTable.className}ShowPannel(index,rowInfo){
                             this.${childTable.entityName} =  index
                             if(!this.${column.javaField}${childTable.className}CascadePicker){
                                let _self = this
                                this.${column.javaField}${childTable.className}CascadePicker = this.$createCascadePicker({
                                      title: '${column.columnComment}',
                                      data: this.${column.javaField}${childTable.className}Options,
                                      alias: {value:"value",text:"label"},
                                      swipeTime: 1000,
                                      onSelect:function(selectedVal, selectedIndex, selectedText){
                                        _self.$set(_self.${conf.formModel}.${childTable.entityName}Lists[_self.${childTable.entityName}], "${column.javaField}${childTable.className}Value", selectedText.join("/"))
                                        _self.${conf.formModel}.${childTable.entityName}Lists[_self.${childTable.entityName}].${column.javaField} = selectedVal
                                      },
                                })
                             }
                             this.${column.javaField}${childTable.className}CascadePicker.show()
                      },
                      `
        methodList.push(str)
        methodList.push(showStr)
      }else{
        const str = `${methodName}() {
                        this.getDicts("${column.dictType}").then(response => {
                            this.${column.javaField}${childTable.className}Options = response.data;
                        });
                    },`
        methodList.push(str)
      }
    }
    if(column.htmlType=="time"||column.htmlType=="date"||column.htmlType=="datetime"){
      let formt = `startColumn: 'year',`
      let joinSymbols = `-`
      if(column.htmlType=="time"){
        formt = `startColumn: 'hour',
                 format: {
                    hour: 'hh',
                    minute: 'mm',
                    second: 'ss'
                 },`
        joinSymbols = `:`
      }
      let valueStr = `value = item.join("${joinSymbols}")`
      if(column.htmlType=="datetime"){
        formt = `columnCount: 6,
                 format: {
                    year:'yyyy',
                    month:'mm',
                    date:'dd',
                    hour: 'hh',
                    minute: 'mm',
                    second: 'ss'
                 },`
        valueStr =  `value = selectedTexts[0].splice(0,3).join("-")+' '+selectedTexts[0].join(":") `
      }

      let str = ` this.${column.javaField}${childTable.className}SegmentPicker = this.$createSegmentPicker({
                            data: [{
                                    is: 'cube-date-picker',
                                    title: '${column.columnComment}',
                                    value: new Date(),
                                    swipeTime: 1000,
                                    ${formt}
                                  }],
                            onSelect: (selectedDates, selectedVals, selectedTexts) => {
                                let value = ""
                                selectedTexts.forEach(item => {
                                   ${valueStr}
                                })
                                this.${conf.formModel}.${childTable.entityName}Lists[this.${childTable.entityName}].${column.javaField} = value
                            }
                          });
                          `
      let showStr = `${column.javaField}${childTable.className}ShowDate(index, row){
                              this.${childTable.entityName} = index
                              this.${column.javaField}${childTable.className}SegmentPicker.show()
                          },`
      mountedList.push(str)
      methodList.push(showStr)
    }
    if(column.htmlType=="time-range"||column.htmlType=="date-range"){
      let formt = `startColumn: 'year',
                   format: {
                      year: 'YYYY',
                      month: 'MM',
                      date: 'DD'
                    },`
      let joinSymbols = `-`
      if(column.htmlType=="time-range"){
        formt = `startColumn: 'hour',
                 format: {
                    hour: 'hh',
                    minute: 'mm',
                    second: 'ss'
                 },`
        joinSymbols = `:`
      }
      let str = ` this.${column.javaField}${childTable.className}SegmentPicker = this.$createSegmentPicker({
                            data: [{
                                    is: 'cube-date-picker',
                                    title: '開始時間',
                                    value: new Date(),
                                    swipeTime: 1000,
                                    ${formt}
                                  },{
                                    is: 'cube-date-picker',
                                    title: '結束時間',
                                    value: new Date(),
                                    swipeTime: 1000,
                                    ${formt}
                                  }],
                            onSelect: (selectedDates, selectedVals, selectedTexts) => {
                                 let value = new Array();
                                 selectedTexts.forEach(item=>{value.push(item.join("${joinSymbols}")) })
                                 this.${conf.formModel}.${childTable.entityName}Lists[this.${childTable.entityName}].${column.javaField} = value;
                                 this.$set(this.${conf.formModel}.${childTable.entityName}Lists[this.${childTable.entityName}], "${column.javaField}${childTable.className}Value", value.join("  至  "))
                            },
                          });
                          `
      let showStr = `${column.javaField}${childTable.className}ShowDate(index, row){
                              this.${childTable.entityName} = index
                              this.${column.javaField}${childTable.className}SegmentPicker.show()
                          },`
      mountedList.push(str)
      methodList.push(showStr)
    }


  })
}



function buildChildOptionMethodAudit(childTable, methodList,conf,mountedList,watchList) {

  childTable.tableCols.forEach(column => {
    if(column.isAdd === '1' && column.dictType && column.dictType != ''){//下拉框 級聯下拉框  單選框 複選框
      let methodName = 'get' + titleCase(column.javaField) +childTable.className+ 'NewObject'
      if(column.htmlType=="select"){
        const str = `${methodName}() {
                        this.getDicts("${column.dictType}").then(response => {
                            this.${column.javaField}${childTable.className}Options = response.data;
                            let _self = this
                            if(this.${conf.formModel}.${childTable.entityName}Lists&&this.${conf.formModel}.${childTable.entityName}Lists.length>0){
                               for (let index in this.${conf.formModel}.${childTable.entityName}Lists) {
                                    response.data.forEach( item =>{
                                            if(item.value == this.${conf.formModel}.${childTable.entityName}Lists[index].${column.javaField}){
                                                 this.$set(this.${conf.formModel}.${childTable.entityName}Lists[index],"${column.javaField}${childTable.className}Value",item.label)
                                            }
                                     })
                               }
                            }
                            this.${column.javaField}${childTable.className}Picker = this.$createPicker({
                                title: '${column.columnComment}',
                                data: [this.${column.javaField}${childTable.className}Options],
                                alias: {
                                  value: "value",
                                  text: "label"
                                },
                                swipeTime: 1000,
                                onSelect: function(selectedVal, selectedIndex, selectedText) {
                                     _self.$set(_self.${conf.formModel}.${childTable.entityName}Lists[_self.${childTable.entityName}], "${column.javaField}${childTable.className}Value", selectedText[0])
                                     _self.${conf.formModel}.${childTable.entityName}Lists[_self.${childTable.entityName}].${column.javaField} = selectedVal[0]
                                },
                            })
                        });
                    },`
        const showStr = `${column.javaField}${childTable.className}ShowPannel(index,row){
                              this.${childTable.entityName} = index
                              this.${column.javaField}${childTable.className}Picker.show()
                        },`
        methodList.push(str)
        methodList.push(showStr)
      }else if(column.htmlType=="cascader"){
        const str = `${methodName}() {
              this.getDicts("${column.dictType}").then(response => {
                  this.${column.javaField}${childTable.className}Options = response.data;
                    if(response.data.length > 0){
                      response.data.forEach(element => {
                        this.getOptions(element);
                      });
                    }
                  });
               },
              cloneObj(obj) {
                 if (typeof obj === 'object' && obj!=null) { //判断是对象，就进行循环复制
                    var o = Object.prototype.toString.call(obj).slice(8, -1) === "Array" ? [] : {}; // 区分是数组还是对象，创建空的数组或对象
                    for (var k in obj) {
                      if(typeof obj[k] === 'object' && obj[k]!=null){   // 如果属性对应的值为对象，则递归复制
                        o[k] = this.cloneObj(obj[k])
                      }else{
                        o[k] = obj[k];
                      }
                    }
                 }else{ //不为对象，直接把值返回
                    return obj;
                 }
                  return o;
              },
              getChildCascader(children,selectValues,value){
                selectValues.splice(0,1)
                let selectOption = ""
                children.forEach(item=>{
                   if(item.value == selectValues[0]){
                     value += "/" + item.label
                     selectOption = item.children
                   }
                })
                if(selectValues.length>1&&selectOption!=null){
                  value = this.getChildCascader(selectOption,selectValues,value)
                }
                return value
              },`
        const showStr = `${column.javaField}${childTable.className}ShowPannel(index,rowInfo){
                             this.${childTable.entityName} =  index
                             if(!this.${column.javaField}${childTable.className}CascadePicker){
                                let _self = this
                                this.${column.javaField}${childTable.className}CascadePicker = this.$createCascadePicker({
                                      title: '${column.columnComment}',
                                      data: this.${column.javaField}${childTable.className}Options,
                                      alias: {value:"value",text:"label"},
                                      swipeTime: 1000,
                                      onSelect:function(selectedVal, selectedIndex, selectedText){
                                        _self.$set(_self.${conf.formModel}.${childTable.entityName}Lists[_self.${childTable.entityName}], "${column.javaField}${childTable.className}Value", selectedText.join("/"))
                                        _self.${conf.formModel}.${childTable.entityName}Lists[_self.${childTable.entityName}].${column.javaField} = selectedVal
                                      },
                                })
                             }
                             this.${column.javaField}${childTable.className}CascadePicker.show()
                      },
                      `
        const watchStr = `${column.javaField}${childTable.className}Options: {
                              handler (val, olVal) {
                                if(this.${conf.formModel}.${childTable.entityName}Lists&&val){
                                  for(let itemIndex in this.${conf.formModel}.${childTable.entityName}Lists){
                                    for(let childValue of this.${conf.formModel}.${childTable.entityName}Lists[itemIndex].${column.javaField}){
                                      val.forEach(optionItem=>{
                                        if(childValue == optionItem.value){
                                          let selectValue = optionItem.label
                                          if(this.${conf.formModel}.${childTable.entityName}Lists[itemIndex].${column.javaField}!=null&&this.${conf.formModel}.${childTable.entityName}Lists[itemIndex].${column.javaField}.length>1&&optionItem.children!=null){
                                            selectValue = this.getChildCascader(optionItem.children,this.cloneObj(this.${conf.formModel}.${childTable.entityName}Lists[itemIndex].${column.javaField}),selectValue)
                                            this.$set(this.${conf.formModel}.${childTable.entityName}Lists[itemIndex],"${column.javaField}${childTable.className}Value", selectValue)
                                          }
                                        }
                                      })
                                    }
                                  }
                                }
                              },
                              deep:true
                            }`
        methodList.push(str)
        methodList.push(showStr)
        watchList.push(watchStr)
      }else{
        const str = `${methodName}() {
                        this.getDicts("${column.dictType}").then(response => {
                            this.${column.javaField}${childTable.className}Options = response.data;
                        });
                    },`
        methodList.push(str)
      }
    }
    if(column.htmlType=="time"||column.htmlType=="date"||column.htmlType=="datetime"){
      let formt = `startColumn: 'year',`
      let joinSymbols = `-`
      if(column.htmlType=="time"){
        formt = `startColumn: 'hour',
                 format: {
                    hour: 'hh',
                    minute: 'mm',
                    second: 'ss'
                 },`
        joinSymbols = `:`
      }
      let valueStr = `value = item.join("${joinSymbols}")`
      if(column.htmlType=="datetime"){
        formt = `columnCount: 6,
                 format: {
                    year:'yyyy',
                    month:'mm',
                    date:'dd',
                    hour: 'hh',
                    minute: 'mm',
                    second: 'ss'
                 },`
        valueStr =  `value = selectedTexts[0].splice(0,3).join("-")+' '+selectedTexts[0].join(":") `
      }

      let str = ` this.${column.javaField}${childTable.className}SegmentPicker = this.$createSegmentPicker({
                            data: [{
                                    is: 'cube-date-picker',
                                    title: '${column.columnComment}',
                                    value: new Date(),
                                    swipeTime: 1000,
                                    ${formt}
                                  }],
                            onSelect: (selectedDates, selectedVals, selectedTexts) => {
                                let value = ""
                                selectedTexts.forEach(item => {
                                   ${valueStr}
                                })
                                this.${conf.formModel}.${childTable.entityName}Lists[this.${childTable.entityName}].${column.javaField} = value
                            }
                          });
                          `
      let showStr = `${column.javaField}${childTable.className}ShowDate(index, row){
                              this.${childTable.entityName} = index
                              this.${column.javaField}${childTable.className}SegmentPicker.show()
                          },`
      mountedList.push(str)
      methodList.push(showStr)
    }
    if(column.htmlType=="time-range"||column.htmlType=="date-range"){
      let formt = `startColumn: 'year',
                   format: {
                      year: 'YYYY',
                      month: 'MM',
                      date: 'DD'
                    },`
      let joinSymbols = `-`
      if(column.htmlType=="time-range"){
        formt = `startColumn: 'hour',
                 format: {
                    hour: 'hh',
                    minute: 'mm',
                    second: 'ss'
                 },`
        joinSymbols = `:`
      }
      let str = ` this.${column.javaField}${childTable.className}SegmentPicker = this.$createSegmentPicker({
                            data: [{
                                    is: 'cube-date-picker',
                                    title: '開始時間',
                                    value: new Date(),
                                    swipeTime: 1000,
                                    ${formt}
                                  },{
                                    is: 'cube-date-picker',
                                    title: '結束時間',
                                    value: new Date(),
                                    swipeTime: 1000,
                                    ${formt}
                                  }],
                            onSelect: (selectedDates, selectedVals, selectedTexts) => {
                                 let value = new Array();
                                 selectedTexts.forEach(item=>{value.push(item.join("${joinSymbols}")) })
                                 this.${conf.formModel}.${childTable.entityName}Lists[this.${childTable.entityName}].${column.javaField} = value;
                                 this.$set(this.${conf.formModel}.${childTable.entityName}Lists[this.${childTable.entityName}], "${column.javaField}${childTable.className}Value", value.join("  至  "))
                            },
                          });
                          `
      let showStr = `${column.javaField}${childTable.className}ShowDate(index, row){
                              this.${childTable.entityName} = index
                              this.${column.javaField}${childTable.className}SegmentPicker.show()
                          },`
      mountedList.push(str)
      methodList.push(showStr)
    }


  })
}

//添加子表表單控件的集合
function buildChildOptions(childTable, optionsList,conf) {
  childTable.tableCols.forEach(column => {
    if(column.isAdd === '1' && column.dictType && column.dictType != ''){
      optionsList.push(`${column.javaField}${childTable.className}Options: [],`)
    }
  })
}



function buildCheckBoxObjMethod(conf,methodList,childList){
  let masterCheckBoxStr = ''
  let childCheckboxStr = ''
  childList.forEach(childTable => {
    childCheckboxStr = childCheckboxStr + buildCheckBoxParseMixinMethod(childTable,conf)
  })

  conf.fields.forEach(el => {
    if(el.tag === 'el-checkbox-group'){
      masterCheckBoxStr = masterCheckBoxStr + `
      if(this.${conf.formModel}.${el.vModel}&&this.${conf.formModel}.${el.vModel}!="null"){
        this.${el.vModel}Value = JSON.parse(this.${conf.formModel}.${el.vModel})
      }\n`
    }
  })
  const str = `checkBoxParse() {
                ${masterCheckBoxStr}\n
               ${childCheckboxStr}
              },
  `
  methodList.push(str)
}
function buildTimeRangObjMethod(conf,methodList,childList){
  let masterTimeRangStr = ''
  let childCheckboxStr = ''
  childList.forEach(childTable => {
    childCheckboxStr = childCheckboxStr + buildTimeRangMixinMethod(childTable,conf)
  })

  conf.fields.forEach(el => {
    if((el.tag === 'el-date-picker'&&el.type==='daterange')||(el.tag === 'el-time-picker'&&el["is-range"])){
      masterTimeRangStr = masterTimeRangStr + `
          if(this.${conf.formModel}.${el.vModel}&&this.${conf.formModel}.${el.vModel}!="null"){
            this.${conf.formModel}.${el.vModel} = JSON.parse(this.${conf.formModel}.${el.vModel})
            this.${el.vModel}Value = this.${conf.formModel}.${el.vModel}.join("  至  ")
          }\n`
    }
  })
  const str = `timeRangParse() {
                ${masterTimeRangStr}\n
                 ${childCheckboxStr}
              },`
  methodList.push(str)
}




function buildTimeRangObjMethodAudit(conf,methodList,childList){
  let masterTimeRangStr = ''
  let childCheckboxStr = ''
  childList.forEach(childTable => {
    childCheckboxStr = childCheckboxStr + buildTimeRangMixinMethodAudit(childTable,conf)
  })

  conf.fields.forEach(el => {
    if(((el.tag === 'el-date-picker'&&el.type==='daterange')||(el.tag === 'el-time-picker'&&el["is-range"]))){
      masterTimeRangStr = masterTimeRangStr + `
          if(this.${conf.formModel}.${el.vModel}&&this.${conf.formModel}.${el.vModel}!="null"){
            this.${el.vModel}Value = JSON.parse(this.${conf.formModel}.${el.vModel}).join("  至  ")
          }\n`
    }else if(((el.tag === 'el-date-picker'&&el.type!='daterange')||(el.tag === 'el-time-picker'&&!el["is-range"]))){
      masterTimeRangStr = masterTimeRangStr + `
          if(this.${conf.formModel}.${el.vModel}&&this.${conf.formModel}.${el.vModel}!="null"){
            this.${el.vModel}Value = this.${conf.formModel}.${el.vModel}
          }\n`
    }
  })
  const str = `timeRangParse() {
                ${masterTimeRangStr}\n
                 ${childCheckboxStr}
              },`
  methodList.push(str)
}

function buildCascaderObjMethod(conf,methodList,childList){
  let masterCascaderStr = ''
  let childCheckboxStr = ''
  childList.forEach(childTable => {
    childCheckboxStr = childCheckboxStr + buildCascaderMixinMethod(childTable,conf)
  })
  conf.fields.forEach(el => {
    if(el.tag === 'el-cascader'){
      masterCascaderStr = masterCascaderStr + `
      if(this.${conf.formModel}.${el.vModel}){
        this.${conf.formModel}.${el.vModel} = JSON.parse(this.${conf.formModel}.${el.vModel})
      }\n`
    }
  })
  const str = `cascaderParse() {
                ${masterCascaderStr}
                ${childCheckboxStr}
              },
  `
  methodList.push(str)
}
function buildFillAutoObjMethod(conf,methodList){

  // debugger
  //onChange事件帶出的方法集合
  let fillChangeMethods = {  };
  //所有需要產生@Change方法的控件
  let onChangeEls = []
  function addOnChangeStage(el,func){
    if(!el.onChangeStages){
      el.onChangeStages = []
    }
    el.onChangeStages.push(func)

  }
  conf.fields.forEach(el => {
    if (el.onchange !== '' && el.onchange !== undefined) {
      fillChangeMethods[`${el.onchange}`] = { key: el, els: [] }
      addOnChangeStage(el,(element)=>{
        let columnStr = ''
        let columnClearStr = ''
        fillChangeMethods[element.onchange].els.forEach(el => {
          columnStr += `this.${conf.formModel}.${el.el.vModel} = response.data.${el.fillColumn} \n`
          columnClearStr += `this.${conf.formModel}.${el.el.vModel} = '' \n`
          if(el.el.tag == "el-select" ){
            columnStr += `for(var item of this.${el.el.vModel}Options){
                            if(item.value == response.data.${el.fillColumn}){
                              this.${el.el.vModel}Value = item.label
                            }
                          }`
          }
        })
        if (element.onchange === 'getInfoUserByEmpno') {
          columnStr += `this.${conf.formModel}.applyFactoryId =  response.data.factoryid \n`
          columnClearStr += `this.${conf.formModel}.applyFactoryId =  ''`
        }
        let fillChangeMethodStr = `
          this.${element.onchange}(data).then(response => {
           if (response.code !== 0) {
              this.msgError(response.msg);
            } else {
              if(response.data != null){
                ${columnStr}
              }else{
                ${columnClearStr}
              }
            }
         });
         `
        return fillChangeMethodStr;
      })
    }
    if (conf.onChangeCalculateExpressionsMap[el.vModel]) {
      el.onChangeCalculateExpressions = conf.onChangeCalculateExpressionsMap[el.vModel]
      addOnChangeStage(el,(element)=>{
        var calculateExpressionChangeMethodStrs = ''
        el.onChangeCalculateExpressions.forEach(expression=>{
          calculateExpressionChangeMethodStrs += `
          if(${expression.isNumberValueCondition}){
            this.${conf.formModel}.${expression.key} = (${expression.calculateExpressions}).toFixed(${expression.calculateExpressionsDecimalScale})
          }else {
            this.${conf.formModel}.${expression.key} = ''
          } \n
          `
        })
        return calculateExpressionChangeMethodStrs
      })
    }
    if(el.onChangeStages){
      onChangeEls.push(el)
    }
  })
  conf.fields.forEach(el => {
    if (el.fillAuto !== '' && el.fillAuto !== undefined && el.fillAuto.length > 0) {
      if(fillChangeMethods[`${el.fillAuto[0]}`])
      {
        fillChangeMethods[`${el.fillAuto[0]}`].els.push({ 'fillColumn': `${el.fillAuto[1]}`, 'el': el })
      }
    }
  })
  onChangeEls.forEach(el =>{
    var changeMethodStr = `${el.vModel}_onchange(data){ \n`
    el.onChangeStages.forEach(stage => {
      let str = stage(el)
      changeMethodStr += str? str :''
    })
    changeMethodStr += ' \n},'
    methodList.push(changeMethodStr)
  })
}


function buildFillAutoObjMethodAudit(conf,methodList){

  // debugger
  //onChange事件帶出的方法集合
  let fillChangeMethods = {  };
  //所有需要產生@Change方法的控件
  let onChangeEls = []
  function addOnChangeStage(el,func){
    if(!el.onChangeStages){
      el.onChangeStages = []
    }
    el.onChangeStages.push(func)
  }

  conf.fields.forEach(el => {
    if (el.onchange !== '' && el.onchange !== undefined && (el.currentorder!=""&&el.currentorder!=undefined)) {  //當前字段可編輯
      fillChangeMethods[`${el.onchange}`] = { key: el, els: [] }
      addOnChangeStage(el,(element)=>{
        let columnStr = ''
        let columnClearStr = ''
        fillChangeMethods[element.onchange].els.forEach(el => {
          columnStr += `this.${conf.formModel}.${el.el.vModel} = response.data.${el.fillColumn} \n`
          columnClearStr += `this.${conf.formModel}.${el.el.vModel} = '' \n`
          if(el.el.tag == "el-select" ){
            columnStr += `for(var item of this.${el.el.vModel}Options){
                            if(item.value == response.data.${el.fillColumn}){
                              this.${el.el.vModel}Value = item.label
                            }
                          }`
          }
        })
        if (element.onchange === 'getInfoUserByEmpno') {
          columnStr += `this.${conf.formModel}.applyFactoryId =  response.data.factoryid \n`
          columnClearStr += `this.${conf.formModel}.applyFactoryId =  ''`
        }
        let fillChangeMethodStr = `
          this.${element.onchange}(data).then(response => {
           if (response.code !== 0) {
              this.msgError(response.msg);
            } else {
              if(response.data != null){
                ${columnStr}
              }else{
                ${columnClearStr}
              }
            }
         });
         `
        return fillChangeMethodStr;
      })
    }
    if (conf.onChangeCalculateExpressionsMap[el.vModel] && (el.currentorder!=""&&el.currentorder!=undefined)) { //當前字段可編輯
      el.onChangeCalculateExpressions = conf.onChangeCalculateExpressionsMap[el.vModel]
      addOnChangeStage(el,(element)=>{
        var calculateExpressionChangeMethodStrs = ''
        el.onChangeCalculateExpressions.forEach(expression=>{
          calculateExpressionChangeMethodStrs += `
          if(${expression.isNumberValueCondition}){
            this.${conf.formModel}.${expression.key} = (${expression.calculateExpressions}).toFixed(${expression.calculateExpressionsDecimalScale})
          }else {
            this.${conf.formModel}.${expression.key} = ''
          } \n
          `
        })
        return calculateExpressionChangeMethodStrs
      })
    }
    if(el.onChangeStages){
      onChangeEls.push(el)
    }
  })

  conf.fields.forEach(el => {
    if (el.fillAuto !== '' && el.fillAuto !== undefined && el.fillAuto.length > 0 && (el.currentorder!=""&&el.currentorder!=undefined)) { //當前字段可編輯
      if(fillChangeMethods[`${el.fillAuto[0]}`])
      {
        fillChangeMethods[`${el.fillAuto[0]}`].els.push({ 'fillColumn': `${el.fillAuto[1]}`, 'el': el })
      }
    }
  })
  onChangeEls.forEach(el =>{
    var changeMethodStr = `${el.vModel}_onchange(data){ \n`
    el.onChangeStages.forEach(stage => {
      let str = stage(el)
      changeMethodStr += str? str :''
    })
    changeMethodStr += ' \n},'
    methodList.push(changeMethodStr)
  })
}


function buildCheckPointObjMethod(conf,methodList){
  const methods = `onSignFormSelected(selectEmp,modelNo,modelName){
                      this.$set(this.${conf.formModel},modelNo,selectEmp.empNo)
                      this.$set(this.${conf.formModel},modelName,selectEmp.empName)
                  },`;
  methodList.push(methods);
}


function buildChildListMethod(childTable, methodList,conf) {
  // console.log(childTable)
  const dataList = []
  childTable.tableCols.forEach(column => {
    if(column.isAdd === '1'){
      buildChildData(column, dataList)
    }
  })
  var childTableElement = conf.fields.find(item => item.childTableName && item.childTableName === childTable.tableName)
  let buildChildOnChangeMethodObject = buildChildOnChangeMethod(childTableElement);
  // buildData(el, dataList)
  const str = `
  handleAdd_${childTable.tableName}() {
    const cloumn = {
      ${dataList}
    };
    this.${conf.formModel}.${childTable.entityName}Lists.splice(this.${conf.formModel}.${childTable.entityName}Lists.length, 0, cloumn);
    for (let index in this.${conf.formModel}.${childTable.entityName}Lists) {
      this.${conf.formModel}.${childTable.entityName}Lists[index].sort = parseInt(index) + 1;
    }
  },
  handleDel_${childTable.tableName}(index, row) {
    let functionName = this.$t('${conf.tableName}_${conf.dbId}.default.functionName');
    this.$confirm(this.$t('tips.deleteConfirm',['${childTable.tableComment}',row.sort]),  this.$t('tips.warm'), {
        confirmButtonText: this.$t("common.confirmTrim"),
        cancelButtonText: this.$t("common.cancelTrim"),
        type: "warning",
      }
    )
      .then(() => {
        this.${conf.formModel}.${childTable.entityName}Lists.splice(index, 1);
        this.$message({ showClose: true, message: this.$t("tips.deleteSuccess"), type: "success" ,offset:50});
      })
      .catch(function (err) {
        console.log(err);
      });
  },
   ${buildChildOnChangeMethodObject.methods}
  `
  methodList.push(str)
}
function buildChildOnChangeMethod(el)
{
  //console.log('22222')
  //所有需要產生@Change方法的控件
  let onChangeEls = []
  function addOnChangeStage(el,func){
    if(!el.onChangeStages){
      el.onChangeStages = []
    }
    el.onChangeStages.push(func)

  }
  let buildChildOnChangeMethodMap = new Map()
  buildChildOnChangeMethodMap.methods=``
  buildChildOnChangeMethodMap.methodNames=``
  let changeMethods = [];
  let childName = el.element.className
  for (let key in el.childAttributesMap) {
    var childAttribute = el.childAttributesMap[key]
    if (childAttribute.onchange) {
      changeMethods[`${childName}_${key}_onchange`] = {
        eventName: childAttribute.onchange,
        columnName: key,
        fillAutoStr: '',
        fillAutoClearStr: '',
      }
      childAttribute.changeMethod = changeMethods[`${childName}_${key}_onchange`]
      addOnChangeStage(childAttribute,(childAttribute)=>{
        return `
          this.${childAttribute.changeMethod.eventName}(item.${childAttribute.changeMethod.columnName}).then(responseInfo => {
            if (responseInfo.code !== 0) {
              this.msgError(responseInfo.msg);
            }
            else {
              if (responseInfo.data != null) {
                ${childAttribute.changeMethod.fillAutoStr}
              }else{
                ${childAttribute.changeMethod.fillAutoClearStr}
              }
            }
            });
         `
      })
    }
    //添加表達式的方法
    if (el.onChangeCalculateExpressionsMap && el.onChangeCalculateExpressionsMap[key]) {
      childAttribute.onChangeCalculateExpressions = el.onChangeCalculateExpressionsMap[key]
      addOnChangeStage(childAttribute,(childAttribute)=>{
        var calculateExpressionChangeMethodStrs = ''
        childAttribute.onChangeCalculateExpressions.forEach(expression=>{
          calculateExpressionChangeMethodStrs += `
          if(${expression.isNumberValueCondition}){
            item.${expression.key} = (${expression.calculateExpressions}).toFixed(${expression.calculateExpressionsDecimalScale})
          }else {
            item.${expression.key} = ''
          } \n
          `
        })
        return calculateExpressionChangeMethodStrs
      })
    }
    if(childAttribute.onChangeStages){
      childAttribute.methodName = `${childName}_${key}_onchange`
      onChangeEls.push(childAttribute)
    }
  }
  //onChangeCalculateExpressionsMap
  for (let key in el.childAttributesMap) {
    if (el.childAttributesMap[key].fillAuto !== '' && el.childAttributesMap[key].fillAuto !== undefined && el.childAttributesMap[key].fillAuto.length > 0) {
      for (let changeMethod in changeMethods) {
        if (el.childAttributesMap[key].fillAuto[0] == changeMethods[changeMethod].eventName)//&& key==changeMethods[changeMethod].columnName
        {
          changeMethods[changeMethod].fillAutoStr += `item.${key}=responseInfo.data.${el.childAttributesMap[key].fillAuto[1]} \n`
          changeMethods[changeMethod].fillAutoClearStr += `item.${key}='' \n`
        }
      }
    }
  }
  onChangeEls.forEach(onChangeEl =>{
    var changeMethodStr = `${onChangeEl.methodName}(item){ \n`
    onChangeEl.onChangeStages.forEach(stage => {
      let str = stage(onChangeEl)
      changeMethodStr += str? str :''
    })
    changeMethodStr += ' \n},'
    buildChildOnChangeMethodMap.methods += `${changeMethodStr} \n`
    buildChildOnChangeMethodMap.methodNames += `this.${onChangeEl.methodName}(item)\n`
  })
  return buildChildOnChangeMethodMap
}
function buildChildCreatedMethod(childTable, createdList,conf) {
  childTable.tableCols.forEach(column => {
    if(column.isAdd === '1' && column.dictType && column.dictType != ''){
      let methodName = 'get' + titleCase(column.javaField) +childTable.className+ 'Options'
      const str = `this.${methodName}() `
      createdList.push(str)
    }
  })
}


function buildChildCreatedMethodAudit(childTable, createdList,conf) {
  childTable.tableCols.forEach(column => {
    if(column.isAdd === '1' && column.dictType && column.dictType != ''){
      let methodName = 'get' + titleCase(column.javaField) +childTable.className+ 'NewObject'
      const str = `this.${methodName}() `
      createdList.push(str)
    }
  })
}

function buildCreatedMethod(methodName, createdList) {
  const str = `this.${methodName}()`
  createdList.push(str)
}
function buildImport(conf){
  const importStr = `import {get${conf.className}, add${conf.className}, edit${conf.className},add${conf.className}AndStartProcess,
  edit${conf.className}AndStartProcess,getSignConfigList} from "@/api/${conf.moduleName}/${conf.businessName}" \n`
    + `import '@/assets/styles/design-build/design-add-view.scss' \n`
    + `import {getAccessToken, getZltAccessToken} from "@/utils/auth"; \n`
    + `import {previewFile , getHeader} from "@/utils/entfrm"; \n`
    + `import {getOptionsAndValues} from "@/api/system/dictData"; \n`
    + `import {listFileInfo, getFileInfo, delFileInfo, addFileInfo, editFileInfo, getByKey} from "@/api/system/fileInfo"; \n`
  return importStr
}

function buildRejectImport(conf){
  const importStr = `import {get${conf.className}, add${conf.className}, edit${conf.className},getSignPathApp,
  edit${conf.className}AndResubmitProcess,getSignConfigList} from "@/api/${conf.moduleName}/${conf.businessName}" \n`
    + `import '@/assets/styles/design-build/design-add-view.scss' \n`
    + `import {listTask, getTask, checkTask, taskComment} from "@/api/activiti/task" \n`
    + `import {getAccessToken, getZltAccessToken} from "@/utils/auth"; \n`
    + `import {previewFile, getHeader} from "@/utils/entfrm"; \n`
    + `import {getOptionsAndValues} from "@/api/system/dictData"; \n`
    + `import {listFileInfo, getFileInfo, delFileInfo, addFileInfo, editFileInfo, getByKey} from "@/api/system/fileInfo"; \n`
  return importStr
}


function buildexport(conf, type, data, rules, selectOptions,childDragTableMobileClientWidths, uploadVar, props, methods,createds,mountedList,configData) {
  let checkboxStr = buildCheckBoxObjStr(conf)
  let cascaderStr = buildCascaderObjStr(conf)
  let timeRang = buildTimeRangObjStr(conf)
  let fileUploadStr = buildUploadStr(conf)
  let rangStr = ""
  let items = conf.fields;
  let dateCreateStr = ""
  items.forEach(item=>{
    if(item.tag=="el-time-picker"||item.tag=="el-date-picker"){

      let formatItem = {
        'yyyy':el => {return `year: 'YYYY'`},
        'MM':el => {return `month: 'MM'`},
        'dd':el => {return `date: 'DD'`},
        'DD':el => {return `date: 'DD'`},
        'WW':el => {return `date: 'DD'`},
        'HH':el => {return `hour: 'hh'`},
        'hh':el => {return `hour: 'hh'`},
        'mm':el => {return `minute: 'mm'`},
        'ss':el => {return `second: 'ss'`},
      }
      let formatIndex = {'yyyy': 1,'MM':2,'dd':3,'DD':3,'WW':3,'HH':4,'hh':4,'mm':5,'ss':6}
      let startColumn = ""  //格式開始
      let endColumn = ""    //格式結束
      let formats = new Array();
      for(let oneFormat in formatItem){
        if(item.format.indexOf(oneFormat)>=0){
          if(startColumn==""){
            startColumn =  oneFormat;
          }
          endColumn = oneFormat;
          formats.push(formatItem[oneFormat]())
        }
      }
      let format = `format: {${formats.join(",")}},`
      let columnCount = 6;
      if((formatIndex[startColumn]<4&&formatIndex[endColumn]<4)){
        columnCount = 3;
        startColumn = 'year'
      }else if((formatIndex[startColumn]>3&&formatIndex[endColumn]>3)){
        columnCount = 3;
        startColumn = 'hour'
      }else{
        startColumn = 'year'
      }
      rangStr += `${item.vModel}Value:undefined,`;
      if(item["is-range"]||item.type=="daterange"){ //範圍
        dateCreateStr+= `${item.vModel}DateValue :[{
                                is: 'cube-date-picker',
                                title: '${item["start-placeholder"]}',
                                ${format}
                                value: new Date(),
                                swipeTime :1000,
                                startColumn: '${startColumn}',
                                columnCount: ${columnCount}
                              },{
                                is: 'cube-date-picker',
                                title: '${item["end-placeholder"]}',
                                ${format}
                                value: new Date(),
                                swipeTime :1000,
                                startColumn: '${startColumn}',
                                columnCount: ${columnCount}
                              }],`
      }else{
        dateCreateStr+= `${item.vModel}DateValue :[{
                                is: 'cube-date-picker',
                                title: '${item.label}',
                                ${format}
                                value: new Date(),
                                swipeTime :1000,
                                startColumn: '${startColumn}',
                                columnCount: ${columnCount}
                              }],`
      }
    }
  })


  let dbAlias = conf.dbAlias?`alias:"${conf.dbAlias}"`:`alias:""`
  let buildImportStr=``
  if (!conf.isPreView)
  {
    buildImportStr=buildImport(conf)
  }
  const str = buildImportStr + `${exportDefault}{
  ${inheritAttrs[type]}
  components: {},
  props: [],
  data () {
    return {
      ${conf.formModel}: {
        ${data}
      },
      formConfigData: {
        ${configData}
      },
      tableHeight: document.documentElement.scrollHeight - 245 + "px",
      ${conf.formRules}: {
        ${rules}
      },
      ${uploadVar}
      ${selectOptions}
      ${props}
      ${childDragTableMobileClientWidths}
      isMobile: true,
      labelPosition: '${conf.labelPosition}',
      ${rangStr}
      ${dateCreateStr}
      ${dbAlias}
    }
  },
  computed: {},
  watch: {},
  created () {
    const id = this.$route.query.id;
    this.changeTagsView(this.$route.query);
    this.isMobile = true
    if(this.isMobile){
      this.labelPosition = 'left'
    }
    getSignConfigList().then(
      response =>{
        if (response.code === 0) {
          response.data.forEach(element => {
            const colKey = element.colKey.split('_')
            if (colKey.length > 1) {
              if (this.rules[colKey[0]] && colKey[1] === 'required') {
                this.rules[colKey[0]][0]['required'] = element.colValue === 'true'
                this.formConfigData[element.colKey] = element.colValue === 'true'
              }else{
                this.formConfigData[element.colKey] = element.colValue
              }
            }else{
              this.formConfigData[element.colKey] = element.colValue
            }
          });
        }
      }
    )

    if (id != null&&id!=undefined) {
      get${conf.className}(id).then(response => {
        this.${conf.formModel} = response.data;
        ${checkboxStr}
        ${cascaderStr}
        ${timeRang}
        ${fileUploadStr}
        ${createds}
      });
    }
    if(id == null||id==undefined){
      ${createds}
    }
  },
  mounted () {
    ${mountedList.join('\n')}
  },
  methods: {
    ${methods}
  }
}`
  return str
}
function buildUploadStr(conf){
  return `
  if (this.${conf.formModel}.attachids) {
    let a = this.${conf.formModel}.attachids.split(',');
    if (a.length > 0) {
      a.forEach(item => {
        if (item) {
          getByKey(item).then(response => {
            this.upload.fileList.push({name: response.data.orignalName, url: response.data.name});
          })
        }
      })
    }
  }`
}
function buildCheckBoxObjStr(conf){
  return 'this.checkBoxParse()'
}
function buildCascaderObjStr(conf){
  return 'this.cascaderParse()'
}
function buildTimeRangObjStr(conf){
  return 'this.timeRangParse()'
}
//生成渲染後,調整子表column寬度的字符串
function buildDragTableMobileClientWidthStr(conf){
  let childDragTableMobileClientWidthStr = ''
  conf.childTables.forEach(childTable => {
    childDragTableMobileClientWidthStr = childDragTableMobileClientWidthStr + ` this.${childTable.entityName}DragTableMobileClientWidth = this.$refs.${childTable.entityName}DragTableMobile.$el.clientWidth  \n`
  })
  return childDragTableMobileClientWidthStr
}




function buildRejectExport(conf, type, data, rules, selectOptions,childDragTableMobileClientWidths, uploadVar, props, methods,createds,configData) {
  let checkboxStr = buildCheckBoxObjStr(conf)
  let cascaderStr = buildCascaderObjStr(conf)
  let timeRang = buildTimeRangObjStr(conf)
  let fileUploadStr = buildUploadStr(conf)
  let rangStr = ""
  let items = conf.fields;
  let dateCreateStr = ""
  items.forEach(item=>{
    if(item.tag=="el-time-picker"||item.tag=="el-date-picker"){
      let format = `format: {year: 'YYYY', month: 'MM', date: 'DD'},`
      if(item.tag=="el-time-picker"){
        format = `format: {hour: 'hh', minute: 'mm', second: 'ss'},`
      }
      let startColumn = `startColumn: 'year',`
      if(item.tag === 'el-time-picker'){
        startColumn = `startColumn: 'hour',`
      }
      rangStr += `${item.vModel}Value:undefined,`;
      if(item["is-range"]||item.type=="daterange"){ //範圍
        dateCreateStr+= `${item.vModel}DateValue :[{
                                is: 'cube-date-picker',
                                title: '${item["start-placeholder"]}',
                                ${format}
                                value: new Date(),
                                swipeTime :1000,
                                ${startColumn}
                              },{
                                is: 'cube-date-picker',
                                title: '${item["end-placeholder"]}',
                                ${format}
                                value: new Date(),
                                swipeTime :1000,
                                ${startColumn}
                              }],`
      }else{
        dateCreateStr+= `${item.vModel}DateValue :[{
                                is: 'cube-date-picker',
                                title: '${item.label}',
                                ${format}
                                value: new Date(),
                                swipeTime :1000,
                                ${startColumn}
                              }],`
      }
    }
    if(item.tag=="el-cascader"){

    }
  })


  let dbAlias = conf.dbAlias?`alias:"${conf.dbAlias}"`:`alias:""`
  const str = buildRejectImport(conf) + `${exportDefault}{
  ${inheritAttrs[type]}
  components: {},
  props: [],
  data () {
    return {
      ${conf.formModel}: {
        ${data}
      },
      formConfigData: {
        ${configData}
      },
      tableHeight: document.documentElement.scrollHeight - 245 + "px",
      ${conf.formRules}: {
        ${rules}
      },
      ${uploadVar}
      ${selectOptions}
      ${props}
      ${childDragTableMobileClientWidths}
      isMobile: true,
      labelPosition: '${conf.labelPosition}',
      ${rangStr}
      ${dateCreateStr}
      ${dbAlias}
    }
  },
  computed: {},
  watch: {},
  created () {
    const id = this.$route.query.id;
    this.changeTagsView(this.$route.query);
    this.isMobile = true
    if(this.isMobile){
      this.labelPosition = 'left'
    }
    getSignConfigList().then(
      response =>{
        if (response.code === 0) {
          response.data.forEach(element => {
            const colKey = element.colKey.split('_')
            if (colKey.length > 1) {
              if (this.rules[colKey[0]] && colKey[1] === 'required') {
                this.rules[colKey[0]][0]['required'] = element.colValue === 'true'
                this.formConfigData[element.colKey] = element.colValue === 'true'
              }else{
                this.formConfigData[element.colKey] = element.colValue
              }
            }else{
              this.formConfigData[element.colKey] = element.colValue
            }
          });
        }
      }
    )

    if (id != null&&id!=undefined) {
      get${conf.className}(id).then(response => {
        this.${conf.formModel} = response.data;
        ${checkboxStr}
        ${cascaderStr}
        ${timeRang}
        ${fileUploadStr}
        ${createds}
      });
    }
    if(id == null||id==undefined){
      ${createds}
    }
  },
  mounted () {

  },
  methods: {
    ${methods}
  }
}`
  return str
}
