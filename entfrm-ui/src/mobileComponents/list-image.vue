<template>
  <div class="list-image-group" ref="group" >
    <slot>
      <div class="imgs-container">
        <img :src="img.barcodeUrl" v-for="(img, index) in options" :key="img.barcodeUrl" @click="handleImgsClick(index)" />
      </div>
    </slot>
  </div>
</template>
<script type="text/ecmascript-6">

const COMPONENT_NAME = 'list-image'
export default {
  name: COMPONENT_NAME,
  props: {
    options: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data () {
    return {
      _value: [],
      _checkboxGroup: true,
      images: []
    }
  },
  created(){
  },
  methods:{
    handleImgsClick(index) {
      this.initialIndex = index
      this.images = []
        this.options.forEach(img => {
          this.images.push(img.barcodeUrl)
      })
      const params = {
        $props: {
          imgs: this.images,
          initialIndex: 'initialIndex',
          loop: false
        },
        $events: {
          change: (i) => {
            this.initialIndex = i
          }
        }
      }
      this.$createImagePreview({ ...params }).show()
    },
  },
  components: {
  }

}
</script>
<style lang="stylus" rel="stylesheet/stylus">

.imgs-container
  margin-top 50px
  > img
    max-width: 100px
    height: 67px
    margin: 0 10px 16px 10px
    border-radius: 4px
</style>
