<template>
  <div class="cube-page" :class="type">
    <header class="header" >
      <h1>{{title}}</h1>
      <i @click="back" class="cubeic-back"></i>
    </header>
    <div class="wrapper">
      <main class="content">
        <slot name="content">{{content}}</slot>
      </main>
    </div>
  </div>
</template>

<script type="text/ecmascript-6">
  export default {
    props: {
      title: {
        type: String,
        default: '',
        required: true
      },
      type: {
        type: String,
        default: ''
      },
      desc: {
        type: String,
        default: ''
      },
      content: {
        type: String,
        default: ''
      },
      isPreView:{
        type: Boolean ,
        default: false,
      }
    },
    created() {
      let theme = localStorage.getItem('themeColor')
      if(!theme){
        theme = "default"
      }
      window.document.documentElement.setAttribute('data-theme', theme);
    },
    methods: {
      back() {
        if(this.isPreView){
          this.$message({ showClose: true, message: "預覽狀態返回按鈕不可用", type: "success", offset: 50});
        }else{
          this.$router.back()
        }
      }
    }
  }
</script>
<style lang="scss" scoped>
  @import "~@/assets/styles/mobileSkin/mobileMixin.scss";
  .cube-page{
    .header{
      z-index: 3000;
      @include backgroundColor('titleBgColor');
      @include fontColor('titleFontColor');
      .cubeic-back{
        @include fontColor('titleFontColor');
      }
    }
  }
</style>
<style  lang="stylus">
  .cube-page
    position: absolute
    z-index: 10
    top: 0
    left: 0
    width: 100%
    height: 100%
    background: #efeff4
    .header
      position: relative
      height: 44px
      line-height: 44px
      text-align: center
      background-color: white
      -webkit-backface-visibility: hidden
      backface-visibility: hidden
      z-index: 5
      h1
        font-size: 16px
        font-weight: 700
      .cubeic-back
        position: absolute
        top: 0
        left: 0
        padding: 0 15px
        color: #fc9153
    >.wrapper
      height: calc(100% - 44px)
      overflow-x: hidden
      overflow-y: auto
    &.option-demo
      .wrapper
        background-color: $color-white
      .title
        font-size: $fontsize-large
        font-weight: 500
        color: $color-dark-grey
        padding: 15px
        border-bottom: 1px solid rgba(0, 0, 0, .1)
        margin-bottom: 15px
      .options
        margin-bottom: 15px
      .option-list
        .group
          margin-bottom: 15px
          border: 1px solid rgba(0, 0, 0, .1)
          border-radius: $radius-size-medium
        .item
          height: 52px
          border-bottom: 1px solid rgba(0, 0, 0, .1)
          &.sub
            font-size: $fontsize-medium
            background-color: $color-light-grey-opacity
            &.first
              box-shadow: 0 1px 1px 1px #eee inset
            &.last
              border-bottom: none
      .demo
        margin-bottom: 15px
      .methods
        .method-list
          .group
            margin-bottom: 15px
            border: 1px solid rgba(0, 0, 0, .1)
            border-radius: $radius-size-medium
          .item
          button
            height: 40px
            font-size: $fontsize-large
          .item
            background-color: $color-active-light-gray
            border-bottom: 1px solid rgba(0, 0, 0, .1)
          button
            width: 100%
            border-bottom-left-radius: $radius-size-medium
            border-bottom-right-radius: $radius-size-medium
            background-color: $color-orange
            box-shadow: 0 0 0 1px $color-orange
            border: none
            outline: none
            color: $color-white
</style>
