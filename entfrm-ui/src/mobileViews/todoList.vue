<template>
  <cube-page type="repair-Repair-director-list" title="我的待辦">
    <div slot="content">
      <div class="tab-slide-container">

          <div class="button-audit-group">
            <cube-button @click="checkAll" :light="true" class="button-audit button-audit-css" :icon="chooseIcon">全選</cube-button>
            <cube-button primary @click="showActive" icon="cubeic-sad" class="button-audit button-audit-css">批量審核</cube-button>
<!--            <cube-button primary @click="handlTask(1)" style="background-color: #00afff" icon="cubeic-star">批量駁回</cube-button>-->
<!--            <el-button-group>-->
<!--              <el-button type="success" icon="el-icon-s-claim" size="mini" @click="checkAll">-->
<!--                全選-->
<!--              </el-button>-->
<!--              <el-button type="warning" icon="el-icon-s-release" size="mini" @click="handlTask(0)">-->
<!--                批量審核-->
<!--              </el-button>-->
<!--            </el-button-group>-->
          </div>

            <cube-scroll :data="taskList"
                         :options="scrollOptions"
                         ref="waitingForOrderScroll"
                         @pulling-down="onOrderTaskingPullingDown"
                         @pulling-up="onOrderTaskingPullingUp">
              <ul class="list-wrapper">
                <cube-checkbox-group v-model="checkList">
                  <li v-for="(item, index) in taskList" class="list-item" :key="index" style="border-bottom: 1px solid rgba(27,45,23,0.24)">
                    <div class="middle is-bold line-height">
                    <cube-checkbox v-model="checked" shape="square" style="width: 85%"
                                   :option="{disabled: item.canBatch==0?true:false,value:item.processId}"/>第{{index+1}}條
                    </div>
                    <div class="middle is-bold line-height is-black">
                      表單編號：{{ item.serialno }}
                    </div>
                    <div class="middle is-bold line-height is-black">
                      表單名稱：{{ item.wfName }}
                    </div>
                    <div class="middle is-bold line-height is-black">
                      填單人：{{ item.makerName }}
                    </div>
                    <div class="middle is-bold line-height is-black">
                      填單時間：{{ item.createTime }}
                    </div>
                    <div class="middle is-bold line-height is-black">
                      簽核節點：{{ item.taskName }}
                    </div>
                    <div class="middle is-bold line-height is-black">
                      表單狀態：{{ workStatusFormat(item.workStatus) }}
                    </div>
                    <div class="button-right">
                      <cube-button :inline="true"
                                   :primary="true"
                                   @click="viewDetail(item.bunessId,item.serialno,item.auditPath)" icon="cubeic-arrow"
                      >查看詳情
                      </cube-button>
                    </div>
                  </li>
                </cube-checkbox-group>
              </ul>
            </cube-scroll>


      </div>
    </div>
  </cube-page>
</template>

<script>
import CubePage from '@/mobileComponents/cube-page.vue'
import {checkBatchedTask, listTask} from "@/api/activiti/task";

export default {
  components: {
    CubePage
  },
  name: "todoList",
  data() {
    return {
      loop: false,
      autoPlay: false,
      chooseIcon: "cubeic-square-border",
      properTitle: '',
      checkList: [],
      checkAllList: [],
      showDots: false,
      form: {},
      scrollOptions: {
        /* lock x-direction when scrolling horizontally and  vertically at the same time */
        directionLockThreshold: 0,
        pullDownRefresh: {
          threshold: 60,
          stop: 40,
          txt: '刷新成功'
        },
        pullUpLoad: {
          hreshold: 0,
          txt: {more: '上拉加載更多', noMore: '沒有更多數據'},
          visible: true
        },
      },
      waitingForOrderTotal: '',
      taskList: [],
      workStatusOptions: [],
      queryParams: {
        current: 1,
        size: 5,
      },
      slideOptions: {
        listenScroll: true,
        probeType: 3,
        /* lock y-direction when scrolling horizontally and  vertically at the same time */
        directionLockThreshold: 0
      },
      checked: false,
    }
  },
  beforeCreate() {
    this.getDicts("table_type").then(response => {
      this.workStatusOptions = response.data;
    });
  },
  created() {
    let theme = localStorage.getItem('themeColor')
    if(!theme){
      theme = "default"
    }
    window.document.documentElement.setAttribute('data-theme', theme);
    this.getList();
  },
  methods: {
    /** 查询任务列表 */
    getList() {
      listTask(this.queryParams).then(response => {
          this.taskList = response.data;
        }
      );
    },
    changePage(current) {
      this.selectedLabel = this.tabLabels[current].label
      // console.log(current)
    },
    onOrderTaskingPullingDown() {
      this.checkList = [];
      this.queryParams.current = 1
      listTask(this.queryParams).then(response => {
        this.taskList = response.data;
        if (response.total !== 0) {
          this.waitingForOrderTotal = response.total
        } else {
          this.waitingForOrderTotal = ''
        }
          if (this.chooseIcon == "cubeic-square-right") {
            response.data.map(item => {
              if (item.canBatch == 1) {
                this.checkList.push(item.processId)
              }
            });
            if (this.checkList.length==0) {
              this.chooseIcon = "cubeic-square-border"
            };
          }
      })
    },
    onOrderTaskingPullingUp() {
      this.queryParams.current = this.queryParams.current + 1
      listTask(this.queryParams).then(response => {
        if (response.data.length > 0) {
          this.taskList.push(...response.data)
          if (this.chooseIcon == "cubeic-square-right") {
            response.data.map(item => {
              if (item.canBatch == 1) {
                this.checkList.push(item.processId)
              }
            });
          }
        } else {
          this.$refs.waitingForOrderScroll.forceUpdate();
          this.$refs.waitingForOrderScroll.refresh();
          this.queryParams.current = this.queryParams.current - 1
        }
      })
    },
    workStatusFormat(dictValue) {
      return this.selectDictLabel(this.workStatusOptions, dictValue);
    },
    showActive() {
      if (this.checkList.length==0) {
        this.$createToast({
          type: 'warn',
          time: 1000,
          mask: true,
          txt: `請至少選擇一項!`
        }).show()
        return;
      }
      this.$createActionSheet({
        title: '共'+this.taskList.length+'條數據，選擇了【'+this.checkList.length+'】條數據',
        maskClosable: true,
        data: [{
          content: '批量通過'
        },{
          content: '批量駁回'
        }],
        onSelect: (item, index) => {
          this.properTitle = item.content;
          if (index == 0) {
            this.handlTask(0);
          }else if (index == 1) {
            this.handlTask(1);
          }
        }
      }).show();
    },
    handlTask:function(pass) {
      if (this.checkList.length==0) {
        this.$createToast({
          type: 'warn',
          time: 1000,
          mask: true,
          txt: `請至少選擇一項!`
        }).show()
        return;
      }
      this.dialog = this.$createDialog({
        type: 'prompt',
        title: this.properTitle+'批註',
        prompt: {
          value: '',
          type: 'text',
          placeholder: '请输入'+this.properTitle+'批註內容'
        },
        onConfirm: (e, promptValue) => {
          if (pass == 1&&promptValue=='') {
            this.$createToast({
              type: 'warn',
              time: 1000,
              mask: true,
              txt: `駁回時批註必填!`
            }).show()
            return;
          }
          this.form.pass = pass;
          this.form.comment = promptValue
          const processIds = this.checkList;
          checkBatchedTask(this.form,processIds).then(response => {
            if (response.code === 0) {
              this.$createToast({
                type: 'correct',
                time: 1000,
                mask: true,
                txt: `操作成功!`
              }).show()
              this.getList()
            } else {
              this.$createToast({
                type: 'error',
                mask: true,
                time: 1000,
                txt: `操作失敗!`
              }).show()
              this.getList()
            }
          })
        },
        onCancel: (e) => {
          // console.log(this.dialog.prompt.placeholder);
          // console.log(this.dialog.prompt.value);
        }
      }).show()
    },
    checkAll() {
      this.chooseIcon = "cubeic-square-border";
      if (this.checkList.length > 0) {
        this.checkList = [];
      } else {
        this.taskList.map(item => {
          if (item.canBatch == 1) {
            this.checkList.push(item.processId)
          }
        });
        if (this.checkList.length > 0) {
          this.chooseIcon = "cubeic-square-right";
        }else{
          this.$createToast({
            type: 'correct',
            time: 1000,
            mask: true,
            txt: `當前頁沒有可選擇的項!`
          }).show()
        }
      }
    },
    viewDetail(id, serialno, path) {
      path = (path+"App").replace(/(.*)\//,"$1App\/");
      this.$router.push({path: path, query: {id: id, serialno: serialno}});
    },
  }
}
</script>
<style lang="scss" scoped>
  @import "~@/assets/styles/mobileSkin/mobileMixin.scss";
 .cube-btn-primary{
      @include backgroundColor('titleBgColor');
  }
  .cube-btn-light{
      @include fontColor('titleBgColor');
  }
  .cube-checkbox_checked{
    /deep/.cube-checkbox-ui {
      i {
        @include fontColor('titleBgColor');
      }
    }
  }

</style>
<style lang="stylus" rel="stylesheet/stylus">

/* 覆盖样式 */
.cube-page
&.repair-Repair-director-list
  > .wrapper
    > .content
      margin: 0
.button-audit-css{
    width:50%
    padding: 13px 16px
  //float: left;
}
.cube-btn-light {
  //margin-left :5px
  float: left;
}
.repair-Repair-director-list
  h2
    margin-top: 10px
    text-align: center
    font-size: 16px

  .cube-tab-bar
    background-color: white

  .cube-tab, .cube-tab_active
    color: black

  .cube-tab-bar-slider
    background-color: black

  .cube-slide-item
    width: 100%

  .tab-slide-container
    position: fixed
    top: 45px
    left: 0
    right: 0
    bottom: 0

  .list-wrapper
    overflow: hidden

    li
      padding: 5px 10px
      margin-top: 5px
      text-align: left
      background-color: white
      font-size: 14px
      color: #999
      white-space: normal

      .cube-rate-item
        padding: 0px 0px
        width: 14px

      .el-avatar
        float: right

      .is-back-red
        background: red

      .is-back-orange
        background: orange

      .line-height
        line-height: 1.5

      .is-black
        color: black

      .is-grey
        color: #999

      .is-red
        color: red

      .is-blue
        color: blue

      .is-bold
        font-weight: bold

      .top
        display: flex

        .avatar
          width: 15px
          height: 15px
          margin-right: 50px
          border-radius: 100%

        .time
          flex: 1

      .middle
        display: flex
        margin: 5px 0

      .button-right
        display: flex
        justify-content: flex-end
      .button-group
        display: inline-flex

  .hot-title
    display: flex
    align-items: center
    font-size: 12px

    .hot-sequence
      display: inline-block
      margin-right: 2px
      padding: 3px 6px
      border-radius: 2px
      background-color: darkgoldenrod
      color: white

      .hot-content
        margin-top: 15px
</style>
