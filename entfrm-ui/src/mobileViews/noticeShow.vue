<template>
    <div class="ant-modal-content" slot="content">
      <div class="ant-modal-body">
          <div class="title-content">
            {{formData.title}}
          </div>
          <div class="time-content">
            {{formData.startTime}}
          </div>
          <div class="detial-content" v-html="formData.description">{{formData.description}}</div>
      </div>
      <div v-if="formData.attachids!=null&&formData.attachids!=''">
        <div>附件：</div>
        <div class="file-style">
            <el-upload :file-list="upload.fileList" :show-file-list="true" :on-preview="handlePreview"
                       :headers="upload.headers" :action="upload.url" list-type="text" :disabled='true'>
            </el-upload>
        </div>
      </div>
    </div>
</template>
<script>
import {getNotice} from "@/api/caaesign/noticeShow";
import {getByKey} from "@/api/caaesign/wfcommonprocess";
import '@/assets/styles/design-build/design-add-view.scss'
import {getHeader} from "@/utils/entfrm";
export default {
  components: {},
  props: [],
  data() {
    return {
      formData: {
        description: undefined,
        title: undefined,
        status: undefined,
        startTime: undefined,
        endTime: undefined,
        type: undefined,
        attachids: undefined,
        createBy:undefined,
      },
      isMobile: true,
      labelPosition: 'left',
      upload: {
        // 是否显示弹出层
        open: false,
        fileList: [],
        fileNameList: [],
        // 弹出层标题
        title: "",
        multiple: false,
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: getHeader(),
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/fileInfo/upload"
      },
    }
  },
  computed: {},
  watch: {},
  created() {
    const id = this.$route.query.id;
    getNotice(id).then(response => {
       this.formData = JSON.parse(response.data);
       console.log(this.formData);
       if (this.formData.attachids) {
        let a = this.formData.attachids.split(',');
        if (a.length > 0) {
          // 使用 Promise.all 等待所有异步操作完成
          Promise.all(
            a.map(item => {
              if (item) {
                return getByKey(item).then(response => {
                  return {
                    name: response.data.name,
                    url: response.data.url,
                    id: response.data.id,
                    size: response.data.sizez,
                    type: response.data.type
                  };
                });
              }
              return null;
            })
          ).then(results => {
            // 过滤掉 null 值（即无效的 item）
            results = results.filter(result => result !== null);
            // 将结果赋值给 upload.fileList
            this.upload.fileList = results;
          });
        }
      }
    });
    this.isMobile = this.isMobileFun()
    ESignApp.onPageFinished();
  },
  methods: {
    closeForm() {

    },
    handlePreview(file) {
      if(file.type=='pdf'||file.type=='PDF'){
        ESignApp.previewPDF(process.env.VUE_APP_BASE_API +  '/caaesign/fileManager/downloadFile/' + file.id+"?name="+encodeURIComponent(file.name)+"&size="+file.size+"&extension="+file.type+"&encrypt=N");
      }else{
        ESignApp.previewOffice(process.env.VUE_APP_BASE_API +  '/caaesign/fileManager/downloadFile/' + file.id+"?name="+encodeURIComponent(file.name)+"&size="+file.size+"&extension="+file.type+"&encrypt=N");
      }
    },
  }
}

</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.ant-modal-content{
  background:#EAEEF9;
  height: 100%;
  padding:20px;
  font-size:3vw;
}
.ant-modal-body{
  display: flex;
  flex-direction: column;
  padding:0;
  background: white;
  border-radius: 10px;
  overflow: hidden;
  font-size:3vw;
  margin-bottom: 40px;
  .detial-content{
    padding:3vw 3vw 10vw 3vw;
    background: white;
    ::v-deep img{
      max-width:100%;
    }
  }
  .title-content{
    padding:3vw 3vw 1vw 3vw;
    text-align: center;
    font-weight: bold;
    font-size:4vw;
  }
  .time-content{
    text-align: center;
  }
}
.file-style{
  padding:0px 16px 10px 16px;

}
.file-style /deep/.el-icon-document{
  font-size:3vw;
}
</style>
