<template>
  <cube-index type="scroll-view" title="Scroll" class="mobileIndex">
    <template slot="content">
      <div class="content-head">
        <div class="content-head-bar">
             <div class="content-head-title">{{ $t('login.sysName') }}
                  <div class="user-info">{{this.$store.state.user.empNo}}-{{this.$store.state.user.name}}</div>
             </div>
             <div class="content-head-bar-shade"></div>
        </div>
        <div class="content-head-bar-img">
          <img :src="bannerUrl" style="width: 100%;">
          <div class="bar-img-name">{{ $t('login.sysName') }}</div>
        </div>
      </div>
      <div class="content-scroll-wrapper">
        <div class="content-scroll-list-wrap" ref="scrollWrapper">

          <cube-tab-panels v-model="selectedLabel">
            <cube-tab-panel :label="$t('mobile_index.footerTabs.home')" value="home"
                            class="{'cube-tab_active': true}">
              <cube-scroll>
                <ul class="item-list">
                  <li>
                    <div class="item"
                         @click="changeHandler('/mobileLayout/applyBySystem')">
                      <div style="width:50%;height: 100%;float: left;">
                        <img :src="require('../assets/images/apply.png')"
                             style="width: 15vw;transform:translateY(10vw);">
                      </div>
                      <div style="margin-left:50%;width:50%;">
                         <p>{{$t('mobile_index.index.wantToRepair')}}</p>
                      </div>
                    </div>
                  </li>
                  <li>
                    <div class="item"
                         @click="changeHandler('/mobileTaskList')">
                      <div style="width:50%;float: left;">
                        <img :src="require('../assets/images/myTask.png')"
                             style="width: 15vw;transform: translateY(10vw);">
                      </div>
                      <div style="margin-left:50%;width:50%;">
                        <el-badge :value="totalTodo" :max="99">
                          <p>{{$t('mobile_index.index.myRepair')}}</p>
                        </el-badge>
                      </div>
                    </div>
                  </li>
                </ul>
              </cube-scroll>
            </cube-tab-panel>
          </cube-tab-panels>
        </div>
      </div>
      <div class="bottom-bar">
        <svg-icon icon-class="twrap" class="twrap-btn" @click="changeShin"/>
      </div>
    </template>
  </cube-index>
</template>

<script>

import CubeIndex from '@/mobileComponents/cube-index.vue'
import {listTask} from "@/api/activiti/task";

export default {
  components: {
    CubeIndex
  },
  data() {
    return {
      selectedLabel: 'home',
      totalTodo: undefined,
      bannerUrl: require('../assets/images/banner.png'),
      tabs: [{
        label: this.$t('mobile_index.footerTabs.home'),
        value: 'home',
        icon: 'cubeic-home',
        path: '/mobileIndex',
        isActive: true
      }, {
        label: this.$t('mobile_index.footerTabs.person'),
        value: 'person',
        icon: 'cubeic-person',
        path: '/mobileMyInfo'
      }],
      options: {
        pullDownRefresh: {
          threshold: 60,
          // stop: 44,
          stopTime: 1000,
          txt: this.$t('mobile_index.common.updateSuccess')
        },
        pullUpLoad: true
      },
      todo: 10,
      isHidden: false,
      secondStop: 26,
    }
  },
  created() {
    this.getList()
    let theme = localStorage.getItem('themeColor')
    if(!theme){
      theme = "default"
    }
    window.document.documentElement.setAttribute('data-theme', theme);
  },
  methods: {
    changeHandler(path) {
      // if you clicked different tab, this methods can be emitted
      this.$router.push(path)
    },
    getList() {
      listTask().then(response => {
          this.totalTodo = response.total;
        }
      );
    },
    changeShin(){
      this.$router.push("/mobileLayout/skinChange")
    }
  },
  mounted() {
  }
}
</script>

<style lang="scss" scoped>
   @import "~@/assets/styles/mobileSkin/mobileMixin.scss";
   .content{
      .content-head{
        .content-head-bar{
          @include backgroundInfo('mainPageBgColor');
        }
        .content-head-bar-img{
          img{
            @include imgColor('mainImgBg');
          }
          .bar-img-name{
            position: relative;
            top: -35vw;
            text-align: center;
            font-size: 8vw;
            color: white;
            text-shadow: 0px 0px 20px #3998E2;
          }
        }
      }
      .bottom-bar{
        @include fontColor('titleBgColor');
      }
   }

</style>
<style lang="stylus" rel="stylesheet/stylus">
.mobileIndex
  .content
    height: 100%
    display: flex
    flex-flow: column
    background: #F6F6F6;
    .content-head{
      width:100%;
      height:65vw;
      overflow: hidden;
      .content-head-bar{
         height: 50vw;
         overflow: hidden;
         background: linear-gradient(to right, #0192EB, #03E0C1);
        .content-head-title{
            width:100%;
            font-size:6vw;
            color :#FFFFFF;
            top: 10vw;
            position: relative;
            padding-left: 6vw;
            position: absolute;
            .user-info{
              font-size:4vw;
              float:right;
              padding-right:5vw;
           }
        }
        .content-head-bar-shade{
          position: relative;
          border-radius: 100% 100% 0 0;
          width: 300vw;
          height: 300vw;
          margin-left: -100vw;
          top: 38vw;
          background: #F6F6F6;
        }
      }
      .content-head-bar-img{
        position: relative;
        margin-top: -32vw;
        z-index: 99;
        img{
          width: 100%;
          padding: 0 5.5vw;
        }
      }
    }
    .content-scroll-wrapper
      flex: 1
      position: relative

      .content-scroll-list-wrap
        width: 100%
        height: 100%
        transform: rotate(0deg) // fix 子元素超出边框圆角部分不隐藏的问题
        position: absolute
        top: 0
        bottom: 0
        overflow: hidden

        .cube-tab-bar
          background-color: #fff

        .cube-tab-panels
          width: 100%
          height: 100%


          .cube-tab-panels-group
            width: 100%
            height: 100%

            .cube-tab-panel
              width: 100%;
              -webkit-box-flex: 1;
              -webkit-flex: 1 0 auto;
              flex: 1 0 auto;

              .cube-scroll-wrapper
                width: 100%;

                .cube-scroll-content
                  width: 100%;

                  .cube-scroll-list-wrapper
                    width: 100%;
                    p
                    color:#000000;
            ul
              overflow: hidden
              font-size: 14px
              padding 0px
              line-height: 1.4
              border-top: 1px
              border-bottom: 1px

            li:first-child
              border-top-left-radius: 3vw;
              border-top-right-radius: 3vw;
            li:last-child
              border-bottom-left-radius: 3vw;
              border-bottom-right-radius: 3vw;
            li
              text-align: center
              margin: 4vw auto
              width: 80vw;
              overflow: hidden;
              background:#ffffff;
              .item {
                height: 30vw;
                text-align: center;
                border-radius: 8px;
                cursor: pointer;
                border 1px
                //background-color: white
                .el-badge{
                  float:left;
                }
                .el-badge__content.is-fixed {
                  top: 34px;
                  right: 10px;
                }
                .item-icon{
                  height:60px;
                  width:60px;
                  padding:10px;
                  color:#17E1DB;
                }
                svg {
                  font-size: 32px;
                  margin-bottom: 12px;
                  margin-top: 25px;
                  margin-left 70px
                  float: left
                }
              }

            //svg {
            //  font-size: 100px;
            //  margin-bottom: 12px;
            //  margin-top: 12px
            //}

            div
              p
                overflow: hidden
                white-space: nowrap
                transform: translateY(12.5vw);
                text-overflow: ellipsis
                font-size: 4.5vw;
                text-align: left
    .bottom-bar{
      width:100%;
      height:11vw;
      line-height:11vw;
      text-align :center;

      color:#17E1DB;
      background: white;
      bottom: 0;
      position: fixed;
      .twrap-btn{
        width: 8vw;
        height: 8vw;
        background: #fff;
        padding: 3vw;
        box-sizing: content-box;
        border-radius: 100%;
        margin-top: -5vw;
      }
    }
  .success-enter-active, .success-leave-active
    transition: width .5s

  .success-enter, .success-leave-to
    width: 70%

  .success-enter-to, .success-leave
    width: 100%

</style>
