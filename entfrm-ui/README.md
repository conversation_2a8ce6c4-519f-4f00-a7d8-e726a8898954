## 开发

```bash
# 克隆项目
git clone https://gitee.com/entfrm/entfrm-boot.git

# 进入项目目录
cd entfrm-ui

# 安装依赖
cnpm install

# 建议不要直接使用 cnpm 安装依赖，会有各种诡异的 bug。可以通过如下操作解决 npm 下载速度慢的问题
npm install --registry=https://registry.npm.taobao.org

# 启动服务
npm run dev
```

> 注：node-sass可能会安装失败，请安装Python 2，或者执行命令：cnpm install node-sass --save-dev

浏览器访问 http://localhost:80

## 发布

```bash
# 构建测试环境
npm run build:stage

# 构建生产环境
npm run build:prod
```

增加預覽功能的依賴包
npm install -s tiny-sass-compiler@^0.12.2
