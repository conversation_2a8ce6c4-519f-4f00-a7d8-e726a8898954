<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.entfrm</groupId>
    <artifactId>entfrm</artifactId>
    <packaging>pom</packaging>
    <version>1.1.0</version>

    <modules>
        <module>entfrm-core</module>
        <module>entfrm-auth</module>
        <module>entfrm-biz</module>
        <module>entfrm-web</module>
        <module>entfrm-commons</module>
        <module>fox-biz</module>
    </modules>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <java.version>8</java.version>
        <spring-boot.version>2.2.8.RELEASE</spring-boot.version>
        <security.oauth2.version>2.3.5.RELEASE</security.oauth2.version>
        <mybatis-plus.version>3.3.1</mybatis-plus.version>
        <hutool.version>5.2.0</hutool.version>
        <druid.version>1.1.21</druid.version>
        <oshi.version>3.9.1</oshi.version>
        <poi.version>4.1.0</poi.version>
        <activiti.version>5.22.0</activiti.version>
        <ip2region.version>1.7.2</ip2region.version>
        <commons-io.version>2.5</commons-io.version>
        <commons-lang3.version>3.3.2</commons-lang3.version>
        <mybatis.version>3.5.4</mybatis.version>
        <velocity.version>1.7</velocity.version>
        <pagehelper.boot.version>1.3.1</pagehelper.boot.version>
        <postgresql.version>42.5.1</postgresql.version>
        <swagger.version>2.9.2</swagger.version>
        <transmittable.version>2.11.5</transmittable.version>
        <spring-boot-admin.version>2.2.3</spring-boot-admin.version>
        <spring-cloud-dependencies.version>Hoxton.SR8</spring-cloud-dependencies.version>
        <spring-cloud-alibaba-dependencies.version>2.2.3.RELEASE</spring-cloud-alibaba-dependencies.version>
        <sftp-client.version>2.1.3</sftp-client.version>
        <knife4j.version>3.0.2</knife4j.version>
        <swagger-annotations.version>1.5.22</swagger-annotations.version>
<!--        <aws-java-sdk-s3.version>1.12.261</aws-java-sdk-s3.version>-->
        <aws-java-sdk-s3.version>1.12.40</aws-java-sdk-s3.version>
        <awsjavasdk.version>1.12.40</awsjavasdk.version>
<!--        <aws-java-sdk-s3.version>1.11.792</aws-java-sdk-s3.version>-->
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <!--            <scope>provided</scope>-->
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>transmittable-thread-local</artifactId>
            <version>${transmittable.version}</version>
        </dependency>
        <!-- ftps工具 -->
        <dependency>
            <groupId>com.xzixi</groupId>
            <artifactId>sftp-client-spring-boot-starter</artifactId>
            <version>${sftp-client.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-to-slf4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud-dependencies.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba-dependencies.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--监控中心-->
            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-server</artifactId>
                <version>${spring-boot-admin.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.security.oauth</groupId>
                <artifactId>spring-security-oauth2</artifactId>
                <version>${security.oauth2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${spring-boot.version}</version>
                <exclusions>
                    <!--排除tomcat依赖-->
                    <exclusion>
                        <artifactId>spring-boot-starter-tomcat</artifactId>
                        <groupId>org.springframework.boot</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.tika</groupId>
                <artifactId>tika-core</artifactId>
                <version>1.24.1</version>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-s3</artifactId>
                <version>${aws-java-sdk-s3.version}</version>
            </dependency>
            <dependency>
                <artifactId>aws-java-sdk-kms</artifactId>
                <groupId>com.amazonaws</groupId>
                <version>${aws-java-sdk-s3.version}</version>
            </dependency>
            <dependency>
                <artifactId>aws-java-sdk-core</artifactId>
                <groupId>com.amazonaws</groupId>
                <version>${aws-java-sdk-s3.version}</version>
            </dependency>
            <dependency>
                <artifactId>jmespath-java</artifactId>
                <groupId>com.amazonaws</groupId>
                <version>${aws-java-sdk-s3.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <repositories>
        <!--阿里云-->
        <repository>
            <id>aliyun</id>
            <name>aliyun</name>
            <url>http://maven.aliyun.com/nexus/content/groups/public</url>
        </repository>
    </repositories>

    <build>
        <plugins>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.0</version>
                <configuration>
                    <target>${maven.compiler.target}</target>
                    <source>${maven.compiler.source}</source>
                    <encoding>UTF-8</encoding>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
