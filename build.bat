@echo off
REM ========================================
REM EntFrm Boot ESGin 項目構建腳本
REM 此腳本用於構建前端UI並打包後端Web應用
REM ========================================
chcp 65001
setlocal enabledelayedexpansion
REM 設置項目根目錄變量，便於維護
set PROJECT_ROOT=%~dp0
set UI_DIR=%PROJECT_ROOT%entfrm-ui
set WEB_DIR=%PROJECT_ROOT%entfrm-web
set DIST_SOURCE=%UI_DIR%\dist
set STATIC_TARGET=%WEB_DIR%\src\main\resources\static\dist

echo [INFO] 開始構建 EntFrm Boot ESGin 項目...
echo [INFO] 項目根目錄: %PROJECT_ROOT%

echo [INFO] 正在更新項目項目...
git pull
REM ========================================
REM 第一步：構建前端UI項目
REM ========================================
echo [INFO] 正在構建前端UI項目...
pushd "%UI_DIR%"
if errorlevel 1 (
    echo [ERROR] 無法進入UI目錄: %UI_DIR%
    exit /b 1
)

REM 執行前端生產環境構建
call npm run build:prod
if errorlevel 1 (
    echo [ERROR] 前端構建失敗！
    popd
    exit /b 1
)
echo [INFO] 前端構建完成

REM 返回到原始目錄
popd

REM ========================================
REM 第二步：複製前端構建產物到後端靜態資源目錄
REM ========================================
echo [INFO] 正在複製前端構建產物...

REM 檢查構建產物是否存在
if not exist "%DIST_SOURCE%" (
    echo [ERROR] 前端構建產物不存在: %DIST_SOURCE%
    exit /b 1
)
echo [INFO] 正在複製前端構建產物...
REM 創建目標目錄（如果不存在）
if not exist "%STATIC_TARGET%" (
    mkdir "%STATIC_TARGET%"
)

REM 複製assets資源文件夾
echo [INFO] 複製assets資源文件...
XCOPY "%DIST_SOURCE%\dist\assets" "%STATIC_TARGET%\assets" /I /Y /D /E
if errorlevel 1 (
    echo [ERROR] 複製assets文件失敗！
    exit /b 1
)

REM 複製index.html文件
echo [INFO] 複製index.html文件...
XCOPY "%DIST_SOURCE%\index.html" "%STATIC_TARGET%\index.html" /I /Y /D
if errorlevel 1 (
    echo [ERROR] 複製index.html文件失敗！
    exit /b 1
)
echo [INFO] 前端資源複製完成

REM ========================================
REM 第三步：打包後端Web應用
REM ========================================
echo [INFO] 正在打包後端Web應用...
pushd "%PROJECT_ROOT%"
if errorlevel 1 (
    echo [ERROR] 無法進入Web目錄: %WEB_DIR%
    exit /b 1
)

REM 執行Maven打包，跳過測試
call mvn clean package -DskipTests=true
if errorlevel 1 (
    echo [ERROR] 後端打包失敗！
    popd
    exit /b 1
)
echo [INFO] 後端打包完成

REM 返回到原始目錄
popd

REM ========================================
REM 構建完成
REM ========================================
echo [SUCCESS] EntFrm Boot ESGin 項目構建成功完成！
::echo [INFO] Construct the product path:  %WEB_DIR%\target\pcms.war
start "" "%WEB_DIR%\target\"
