pushd D:\workspace\entfrm-boot-esgin\entfrm-ui
npm run build:prod
XCOPY D:\workspace\entfrm-boot-esgin\entfrm-ui\dist\assets D:\workspace\entfrm-boot-esgin\entfrm-web\src\main\resources\static\dist\assets /I /Y /D
XCOPY D:\workspace\entfrm-boot-esgin\entfrm-ui\dist\index.html D:\workspace\entfrm-boot-esgin\entfrm-web\src\main\resources\static\dist\index.html /I /Y /D
pushd D:\workspace\entfrm-boot-esgin\entfrm-web
mvn package -DskipTests=true
popd