<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.entfrm.biz.activiti.mapper.TQhAllRelationMapper">
    <resultMap type="com.entfrm.biz.activiti.entity.TQhAllRelation" id="TQhAllRelationResult">
        <result property="makerNo" column="maker_no"/>
        <result property="createTime" column="create_time"/>
        <result property="serialno" column="serialno"/>
        <result property="workflowid" column="workflowid"/>
        <result property="completTime" column="complet_time"/>
        <result property="wfName" column="WF_NAME"/>
        <result property="id" column="id"/>
        <result property="delFlag" column="del_flag"/>
        <result property="updateTime" column="update_time"/>
        <result property="makerName" column="maker_name"/>
        <result property="createBy" column="create_by"/>
        <result property="processId" column="process_id"/>
        <result property="updateBy" column="update_by"/>
        <result property="version" column="version"/>
        <result property="workStatus" column="work_status"/>
        <result property="dtoName" column="dto_name"/>
        <result property="formfrom" column="formfrom"/>
    </resultMap>
    <select id="queryMyDownTask" resultMap="TQhAllRelationResult">
        select tar.*
        from t_qh_all_relation tar
                     INNER JOIN (select distinct res.proc_inst_id_
                                 from ACT_HI_TASKINST RES
                                 WHERE RES.ASSIGNEE_ = #{assignee}
                                   and RES.END_TIME_ is not null) resp
                on resp.proc_inst_id_ = tar.process_id
        order by tar.create_time desc
    </select>
</mapper>
