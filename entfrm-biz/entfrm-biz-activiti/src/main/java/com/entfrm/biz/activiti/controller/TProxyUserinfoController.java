package com.entfrm.biz.activiti.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.entfrm.biz.activiti.entity.TProxyUserinfo;
import com.entfrm.biz.activiti.service.TProxyUserinfoService;
import com.entfrm.core.base.constant.SqlConstants;
import com.entfrm.core.data.annotation.DataFilter;
import com.entfrm.core.log.annotation.OperLog;
import com.entfrm.core.security.util.SecurityUtil;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.security.access.prepost.PreAuthorize;
import com.entfrm.core.base.api.R;
import com.entfrm.core.base.util.ExcelUtil;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020-11-18 10:22:38
 * @description 代理人Controller
 */
@Api("代理人管理")
@RestController
@AllArgsConstructor
@RequestMapping("/activiti/tProxyUserinfo")
public class TProxyUserinfoController {

	private final TProxyUserinfoService tProxyUserinfoService;
	private final JdbcTemplate jdbcTemplate;

	private QueryWrapper<TProxyUserinfo> getQueryWrapper(TProxyUserinfo tProxyUserinfo) {
		return new QueryWrapper<TProxyUserinfo>()
				.eq(StrUtil.isNotBlank(tProxyUserinfo.getSupplyreason()), "supplyreason", tProxyUserinfo.getSupplyreason())
				.eq(ObjectUtil.isNotNull(tProxyUserinfo.getSupplyend()), "supplyend", tProxyUserinfo.getSupplyend())
				.eq(ObjectUtil.isNotNull(tProxyUserinfo.getSupplybegin()), "supplybegin", tProxyUserinfo.getSupplybegin())
				.eq(StrUtil.isNotBlank(tProxyUserinfo.getSetmail()), "setmail", tProxyUserinfo.getSetmail())
				.eq(StrUtil.isNotBlank(tProxyUserinfo.getIsvalid()), "isvalid", tProxyUserinfo.getIsvalid())
				.eq(StrUtil.isNotBlank(tProxyUserinfo.getCancelmail()), "cancelmail", tProxyUserinfo.getCancelmail())
				.like(StrUtil.isNotBlank(tProxyUserinfo.getSupplyusername()), "supplyusername", tProxyUserinfo.getSupplyusername())
				.eq(StrUtil.isNotBlank(tProxyUserinfo.getSupplyempno()), "supplyempno", tProxyUserinfo.getSupplyempno())
				.like(StrUtil.isNotBlank(tProxyUserinfo.getSuppliedusername()), "suppliedusername", tProxyUserinfo.getSuppliedusername())
				.eq(StrUtil.isNotBlank(tProxyUserinfo.getSuppliedempno()), "suppliedempno", tProxyUserinfo.getSuppliedempno())
				.apply(StrUtil.isNotBlank(tProxyUserinfo.getSqlFilter()), tProxyUserinfo.getSqlFilter())
				.orderByDesc("create_time");
	}

	@ApiOperation("代理人列表")
//	@PreAuthorize("@ps.hasPerm('tProxyUserinfo_view')")
	@GetMapping("/list")
	@DataFilter
	public R list(Page page, TProxyUserinfo tProxyUserinfo) {
		IPage<TProxyUserinfo> tProxyUserinfoPage = tProxyUserinfoService.page(page, getQueryWrapper(tProxyUserinfo).eq("create_by",SecurityUtil.getUser().getUsername()));
		return R.ok(tProxyUserinfoPage.getRecords(), tProxyUserinfoPage.getTotal());
	}

	@ApiOperation("代理人查询")
	@GetMapping("/{id}")
	public R getById(@PathVariable("id") String id) {
		return R.ok(tProxyUserinfoService.getById(id));
	}

	@OperLog("代理人新增")
	@ApiOperation("代理人新增")
//	@PreAuthorize("@ps.hasPerm('tProxyUserinfo_add')")
	@PostMapping("/save")
	public R save(@Validated @RequestBody TProxyUserinfo tProxyUserinfo) {
		jdbcTemplate.update(SqlConstants.SET_PROX_INVALID, SecurityUtil.getUser().getUsername());
		tProxyUserinfo.setIsvalid("0");
		tProxyUserinfoService.save(tProxyUserinfo);
		return R.ok();
	}

	@OperLog("代理人修改")
	@ApiOperation("代理人修改")
//	@PreAuthorize("@ps.hasPerm('tProxyUserinfo_edit')")
	@PutMapping("/update")
	public R update(@Validated @RequestBody TProxyUserinfo tProxyUserinfo) {
		tProxyUserinfoService.updateById(tProxyUserinfo);
		return R.ok();
	}
	@PutMapping("/addDefault")
	public R addDefault() {
		List<TProxyUserinfo> tProxyUserinfoEd = tProxyUserinfoService.list(new QueryWrapper<TProxyUserinfo>().eq("suppliedempno", SecurityUtil.getUser().getUsername()));
		List<TProxyUserinfo> tProxyUserinfoEdOne = tProxyUserinfoEd.stream().sorted((o1, o2) -> {
			try {
				// 默认降序, 升序的话 把 dt1 和 dt2 调换位置
				return Long.compare(o2.getCreateTime().getTime(), o1.getCreateTime().getTime());
			} catch (Exception e) {
				e.printStackTrace();
			}
			return 0;
		}).limit(1).collect(Collectors.toList());
		TProxyUserinfo tProxyUserinfo = new TProxyUserinfo();
		if (tProxyUserinfoEdOne.size()>0) {
			tProxyUserinfo.setSupplyempno(tProxyUserinfoEdOne.get(0).getSupplyempno());
			tProxyUserinfo.setSupplyusername(tProxyUserinfoEdOne.get(0).getSupplyusername());
		}
		tProxyUserinfo.setSuppliedempno(SecurityUtil.getUser().getUsername());
		tProxyUserinfo.setSuppliedusername(SecurityUtil.getUser().getNickName());
		return R.ok(tProxyUserinfo);
	}

	@OperLog("代理人删除")
	@ApiOperation("代理人删除")
//	@PreAuthorize("@ps.hasPerm('tProxyUserinfo_del')")
	@DeleteMapping("/remove/{id}")
	public R remove(@PathVariable("id") String[] id) {
		return R.ok(tProxyUserinfoService.removeByIds(Arrays.asList(id)));
	}


	//	@PreAuthorize("@ps.hasPerm('tProxyUserinfo_export')")
	@GetMapping("/export")
	public R export(TProxyUserinfo tProxyUserinfo) {
		List<TProxyUserinfo> list = tProxyUserinfoService.list(getQueryWrapper(tProxyUserinfo));
		ExcelUtil<TProxyUserinfo> util = new ExcelUtil<TProxyUserinfo>(TProxyUserinfo.class);
		return util.exportExcel(list, "代理人数据");
	}
	@PostMapping("/setValid")
	public R setValid(String id, String isvalid) {
		jdbcTemplate.update(SqlConstants.SET_PROX_INVALID, SecurityUtil.getUser().getUsername());
		tProxyUserinfoService.updateById(tProxyUserinfoService.getById(id).setIsvalid(isvalid));
		return R.ok();
	}
}
