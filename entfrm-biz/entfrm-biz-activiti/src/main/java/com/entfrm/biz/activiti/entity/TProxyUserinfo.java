package com.entfrm.biz.activiti.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.entfrm.core.base.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.entfrm.core.data.entity.BaseBizEntity;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020-11-18 10:22:38
 *
 * @description 代理人对象 TProxyUserinfo
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_proxy_userinfo")
public class TProxyUserinfo extends BaseBizEntity{
    private static final long serialVersionUID = 1L;

    /** 代理原因 */
    @Excel(name = "代理原因")
    private String supplyreason;

    /** 代理結束時間 */
    @Excel(name = "代理結束時間", width = 30, dateFormat = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date supplyend;

    /** 代理開始時間 */
    @Excel(name = "代理開始時間", width = 30, dateFormat = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date supplybegin;

    /** 設置發送提醒郵件 */
    @Excel(name = "設置發送提醒郵件")
    private String setmail;

    /** 字典：DICT_ISVALID */
    @Excel(name = "字典：DICT_ISVALID")
    private String isvalid;

    /** 取消發送提醒郵件 */
    @Excel(name = "取消發送提醒郵件")
    private String cancelmail;

    /** 主鍵 */
    @TableId
    private String id;

    /** 代理人姓名 */
    @Excel(name = "代理人姓名")
    private String supplyusername;

    /** 代理人工號 */
    @Excel(name = "代理人工號")
    private String supplyempno;

    /** 被代理人姓名 */
    @Excel(name = "被代理人姓名")
    private String suppliedusername;

    /** 被代理人工號 */
    @Excel(name = "被代理人工號")
    private String suppliedempno;


}
