package com.entfrm.biz.activiti.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.entfrm.biz.activiti.dto.CommentDto;
import com.entfrm.biz.activiti.dto.LeaveDto;
import com.entfrm.biz.activiti.dto.TaskDto;
import com.entfrm.biz.activiti.entity.*;
import com.entfrm.biz.activiti.service.LeaveService;
import com.entfrm.biz.activiti.service.TProxyUserinfoService;
import com.entfrm.biz.activiti.service.TQhAllRelationService;
import com.entfrm.biz.activiti.service.WfConfigService;
import com.entfrm.biz.feign.FeignService;
import com.entfrm.biz.feign.dto.Dept;
import com.entfrm.biz.feign.dto.User;
import com.entfrm.core.base.constant.CommonConstants;
import com.entfrm.core.base.enums.TaskStatusEnum;
import com.entfrm.core.data.constant.CommonConstant;
import com.entfrm.core.security.entity.EntfrmUser;
import com.entfrm.core.security.util.SecurityUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.activiti.bpmn.model.Process;
import org.activiti.bpmn.model.*;
import org.activiti.engine.*;
import org.activiti.engine.history.*;
import org.activiti.engine.impl.RepositoryServiceImpl;
import org.activiti.engine.impl.bpmn.behavior.UserTaskActivityBehavior;
import org.activiti.engine.impl.cfg.ProcessEngineConfigurationImpl;
import org.activiti.engine.impl.context.Context;
import org.activiti.engine.impl.identity.Authentication;
import org.activiti.engine.impl.javax.el.ExpressionFactory;
import org.activiti.engine.impl.javax.el.ValueExpression;
import org.activiti.engine.impl.juel.ExpressionFactoryImpl;
import org.activiti.engine.impl.juel.SimpleContext;
import org.activiti.engine.impl.persistence.entity.CommentEntity;
import org.activiti.engine.impl.persistence.entity.ExecutionEntity;
import org.activiti.engine.impl.persistence.entity.ProcessDefinitionEntity;
import org.activiti.engine.impl.pvm.PvmActivity;
import org.activiti.engine.impl.pvm.PvmTransition;
import org.activiti.engine.impl.pvm.process.ActivityImpl;
import org.activiti.engine.impl.task.TaskDefinition;
import org.activiti.engine.repository.ProcessDefinition;
import org.activiti.engine.runtime.Execution;
import org.activiti.engine.runtime.ExecutionQuery;
import org.activiti.engine.runtime.ProcessInstance;
import org.activiti.engine.task.IdentityLink;
import org.activiti.engine.task.Task;
import org.activiti.engine.task.TaskQuery;
import org.activiti.image.ProcessDiagramGenerator;
import org.activiti.spring.ProcessEngineFactoryBean;
import org.apache.avalon.framework.service.ServiceException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.data.annotation.Transient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/4/4
 * @description 流程 service
 */
@Slf4j
@Service
@AllArgsConstructor
public class TaskServiceImpl implements com.entfrm.biz.activiti.service.TaskService {
	private static final String FLAG = "审批";
	private final LeaveService leaveService;
	private final TaskService taskService;
	private final RuntimeService runtimeService;
	private final RepositoryService repositoryService;
	private final HistoryService historyService;
	private final ProcessEngineFactoryBean processEngine;
	private final WfConfigService wfConfigService;
	private final IdentityService identityService;
	private final TQhAllRelationService tQhAllRelationService;
	private final TProxyUserinfoService tProxyUserinfoService;
	private final RabbitTemplate rabbitTemplate;
	@Resource
	private FeignService feignService;

	@Override
	public IPage list(Map<String, Object> params) {
		TaskQuery taskQuery = taskService.createTaskQuery()
				.taskCandidateOrAssigned(SecurityUtil.getUser().getUsername());
		String taskName = (String) params.get("taskName");
		if (StrUtil.isNotBlank(taskName)) {
			taskQuery.taskNameLike(taskName);
		}
//		String beginTime = (String) params.get("beginTime");
//		if (StrUtil.isNotBlank(beginTime)) {
//			taskQuery.taskCreatedAfter(DateUtil.parseDate(beginTime));
//		}
//		String endTime = (String) params.get("endTime");
//		if (StrUtil.isNotBlank(endTime)) {
//			taskQuery.taskCreatedBefore(DateUtil.parseDate(endTime));
//		}
		int page = MapUtil.getInt(params, CommonConstants.CURRENT);
		int limit = MapUtil.getInt(params, CommonConstants.SIZE);
		IPage result = new Page(page, limit);
		result.setTotal(taskQuery.count());
		List<TaskDto> taskDTOList = taskQuery.orderByTaskCreateTime().desc()
				.listPage((page - 1) * limit, limit).stream().map(task -> {
					TaskDto dto = new TaskDto();
					dto.setTaskId(task.getId());
					ProcessInstance pi = runtimeService.createProcessInstanceQuery()
							.processInstanceId(task.getProcessInstanceId())
							.singleResult();
					String businessKey = pi.getBusinessKey();
					if (StrUtil.isNotBlank(businessKey)) {
						dto.setProcessDefKey(businessKey.split(CommonConstant.BREAK_BUSINESS_KEY)[0]);
						dto.setBunessId(businessKey.split(CommonConstant.BREAK_BUSINESS_KEY)[1]);
						//查詢跳轉審核詳情
						WfConfig config = wfConfigService.getOne(new QueryWrapper<WfConfig>().eq("workflow_key", businessKey.split(CommonConstant.BREAK_BUSINESS_KEY)[0]));
						if (config != null) {
							dto.setAuditPath(config.getAuditAction());
							dto.setPdName(config.getWorkflowName());
							try {
								Map employer = wfConfigService.getMakerUserInfo(config.getDatasourceAlias(), config.getTableName(), businessKey.split(CommonConstant.BREAK_BUSINESS_KEY)[1]);
								if (ObjectUtil.isNotEmpty(employer)) {
									dto.setMakerNo(ObjectUtil.defaultIfNull(employer.get("makerno"), "") + "");
									dto.setMakerName(ObjectUtil.defaultIfNull(employer.get("makername"), "") + "");
									dto.setSerialno(ObjectUtil.defaultIfNull(employer.get("serialno"), "") + "");
									dto.setStatus(ObjectUtil.defaultIfNull(employer.get("workstatus"), "") + "");
									if (ObjectUtil.isNotEmpty(employer.get("createtime"))) {
										dto.setCreateTime(DateUtil.parse(employer.get("createtime") + ""));
									}
								}
							} catch (Exception e) {
								e.printStackTrace();
							}
						}
					}
					dto.setTaskName(task.getName());
					dto.setProcessInstanceId(task.getProcessInstanceId());
					dto.setNodeKey(task.getTaskDefinitionKey());
					dto.setCategory(task.getCategory());
//					dto.setStatus(task.isSuspended() ? "0" : "1");
//					dto.setCreateTime(task.getCreateTime());
					return dto;
				}).collect(Collectors.toList());
		result.setRecords(taskDTOList);
		return result;
	}

	/**
	 * 通过任务ID查询任务信息
	 *
	 * @param taskId
	 * @return
	 */
	@Override
	public LeaveDto getTaskById(String taskId) {
		Task task = taskService.createTaskQuery()
				.taskId(taskId)
				.singleResult();
		ProcessInstance pi = runtimeService.createProcessInstanceQuery()
				.processInstanceId(task.getProcessInstanceId())
				.singleResult();
		String businessKey = pi.getBusinessKey();
		if (StrUtil.isNotBlank(businessKey)) {
			businessKey = businessKey.split(CommonConstant.BREAK_BUSINESS_KEY)[1];
		}
		List<String> comeList = findOutFlagListByTaskId(task, pi);
		Leave leave = leaveService.getById(businessKey);
		LeaveDto leaveDto = new LeaveDto();
		BeanUtils.copyProperties(leave, leaveDto);
		leaveDto.setTaskId(taskId);
		leaveDto.setTaskName(task.getName());
		leaveDto.setTime(task.getCreateTime());
		leaveDto.setFlagList(comeList);
		return leaveDto;
	}

	/**
	 * 提交任务
	 *
	 * @param leaveDto
	 * @return
	 */
	@Override
	@Transient
	public Boolean checkTask(LeaveDto leaveDto) {

		String message = leaveDto.getComment();
		Task task = taskService.createTaskQuery()
				.processInstanceId(leaveDto.getProcessId())
				.singleResult();
		String taskId = task.getId();
		EntfrmUser user = SecurityUtil.getUser();
		//檢查該節點是否為本人審核
//		if(!user.getUsername().equals(task.getAssignee())){
//			return false;
//		}
		taskService.setVariable(taskId,"chargerno",SecurityUtil.getUser().getUsername());
		String processInstanceId = task.getProcessInstanceId();
		Authentication.setAuthenticatedUserId(SecurityUtil.getUser().getUsername());
		TQhAllRelation relation = tQhAllRelationService.getOne(new QueryWrapper<TQhAllRelation>().eq("process_id", leaveDto.getProcessId()));
//		if (TaskStatusEnum.RESUBMIT.getStatus().equals(relation.getWorkStatus())) {
//			message = "";
//		}
		if (TaskStatusEnum.RESUBMIT.getStatus().equals(leaveDto.getPass())) {
			taskService.addComment(taskId, processInstanceId, "重新提交");
		} else if(TaskStatusEnum.CANCLE.getStatus().equals(leaveDto.getPass())){
			taskService.addComment(taskId, processInstanceId, "取消申請");
		}else{
			taskService.addComment(taskId, processInstanceId, message == null ? "通過" : message);
		}
		//同步簽核記錄到ipebg電子簽核平台
		try {
			BizChargeLog bizChargeLog = new BizChargeLog();
			bizChargeLog.setChargename(SecurityUtil.getUser().getNickName());
			bizChargeLog.setChargeno(SecurityUtil.getUser().getUsername());
			bizChargeLog.setChargenode(task.getName());
			bizChargeLog.setSerialno(relation.getSerialno());
			if (TaskStatusEnum.RESUBMIT.getStatus().equals(leaveDto.getPass())) {
				bizChargeLog.setDecrib("重新提交");
			} else if(TaskStatusEnum.CANCLE.getStatus().equals(leaveDto.getPass())){
				bizChargeLog.setDecrib("取消申請");
			}else{
				bizChargeLog.setDecrib(message == null ? "通過" : message);
			}
			bizChargeLog.setIspass("0".equals(leaveDto.getPass()) ? "通過" : "駁回");
			bizChargeLog.setOperateip(leaveDto.getOperIp());
			bizChargeLog.setWorkflowid(relation.getWorkflowid());
			try {
				rabbitTemplate.convertAndSend("SignDirectExchange","Sign.ChargeLog.Routing", BeanUtil.beanToMap(bizChargeLog));
			} catch (Exception e) {
				e.printStackTrace();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		Map<String, Object> variables = new HashMap<>(1);
//        variables.put("flag" , leaveDto.getTaskFlag());
//        variables.put("type" , leaveDto.getType());
//        variables.put("days" , leaveDto.getDays());
		variables.put("pass", leaveDto.getPass());
		taskService.setVariableLocal(task.getId(), "IP", leaveDto.getOperIp());
		taskService.setVariableLocal(task.getId(), "STATUS", leaveDto.getPass());

		/**
		 * 查詢是否有代理，有則替換
		 */
//		TProxyUserinfo userinfo = tProxyUserinfoService.getOne(new QueryWrapper<TProxyUserinfo>().eq("isvalid", "0").eq("supplyempno", SecurityUtil.getUser().getUsername()).le("supplybegin", new Date()).ge("supplyend", new Date()));
		if (!user.getUsername().equals(task.getAssignee())) {
			User user11 = feignService.getOne(task.getAssignee());
			taskService.setVariableLocal(task.getId(), "AUDIT_USER",   user11.getUserName()+"/"+user11.getNickName()+"(" + SecurityUtil.getUser().getUsername() + "/" + SecurityUtil.getUser().getNickName()+ "代簽)");
		} else {
			User user11 = feignService.getOne(task.getAssignee());
			taskService.setVariableLocal(task.getId(), "AUDIT_USER", user11.getUserName()+"/"+user11.getNickName());
		}
		if (StrUtil.equals(TaskStatusEnum.CANCLE.getStatus(),leaveDto.getPass())) {
			//結束任務
//			taskService.complete(taskId);
//			taskService.deleteTask(taskId);
			runtimeService.deleteProcessInstance(task.getProcessInstanceId(),"取消申請");
		}else {
			//任務流轉
			if (StrUtil.equals(TaskStatusEnum.BUTTON_REJECT.getStatus(), leaveDto.getPass())) {
				variables.put("condition","reject");
			}else{
				variables.put("condition","pass");
			}
			taskService.complete(taskId, variables);
		}
		ProcessInstance pi = runtimeService.createProcessInstanceQuery()
				.processInstanceId(processInstanceId)
				.singleResult();
		if (pi != null) {
			String businessKey = pi.getBusinessKey();
			String processKey = "";
			if (StrUtil.isNotBlank(businessKey)) {
				processKey = businessKey.split(CommonConstant.BREAK_BUSINESS_KEY)[0];
				businessKey = businessKey.split(CommonConstant.BREAK_BUSINESS_KEY)[1];
			}
			List<HistoricTaskInstance> historicTaskInstanceList =historyService.createHistoricTaskInstanceQuery().processInstanceId(processInstanceId).orderByHistoricTaskInstanceStartTime().asc().list();
			task = taskService.createTaskQuery()
					.processInstanceId(leaveDto.getProcessId())
					.singleResult();
			if (StrUtil.equals(TaskStatusEnum.BUTTON_REJECT.getStatus()
					, leaveDto.getPass())) {
				if (ObjectUtil.isNotNull(historicTaskInstanceList) && !StrUtil.equals(task.getTaskDefinitionKey(), historicTaskInstanceList.get(0).getTaskDefinitionKey())) {
					relation.setWorkStatus(Integer.parseInt(TaskStatusEnum.CHECKING.getStatus()));
				}else{
					relation.setWorkStatus(Integer.parseInt(TaskStatusEnum.REJECT.getStatus()));
				}
				tQhAllRelationService.updateById(relation);
			} else {
				relation.setWorkStatus(Integer.parseInt(TaskStatusEnum.CHECKING.getStatus()));
				tQhAllRelationService.updateById(relation);
			}
			WfConfig config = wfConfigService.getOne(new QueryWrapper<WfConfig>().eq("workflow_key", processKey));
			if (StrUtil.equals(leaveDto.getPass(),TaskStatusEnum.BUTTON_REJECT.getStatus())&&ObjectUtil.isNotNull(historicTaskInstanceList) && !StrUtil.equals(task.getTaskDefinitionKey(), historicTaskInstanceList.get(0).getTaskDefinitionKey())) {
				wfConfigService.updateFormStatusByBusinessId(config.getDatasourceAlias(), config.getTableName(), TaskStatusEnum.CHECKING.getStatus(), businessKey);
			}else {
				wfConfigService.updateFormStatusByBusinessId(config.getDatasourceAlias(), config.getTableName(), StrUtil.equals(TaskStatusEnum.BUTTON_PASS.getStatus()
						, leaveDto.getPass()) || StrUtil.equals(TaskStatusEnum.CHECKING.getStatus(), leaveDto.getPass()) || StrUtil.equals(TaskStatusEnum.RESUBMIT.getStatus()
						, leaveDto.getPass())
						? TaskStatusEnum.CHECKING.getStatus()
						: TaskStatusEnum.REJECT.getStatus(), businessKey);
			}
			//更新業務庫中間表狀態
			try {
				Map<String, String> paramsMap = MapUtil.newHashMap();
				paramsMap.put("serialno",relation.getSerialno());
				if (StrUtil.equals(leaveDto.getPass(),TaskStatusEnum.BUTTON_REJECT.getStatus())&&ObjectUtil.isNotNull(historicTaskInstanceList) && !StrUtil.equals(task.getTaskDefinitionKey(), historicTaskInstanceList.get(0).getTaskDefinitionKey())) {
					paramsMap.put("workstatus", TaskStatusEnum.CHECKING.getStatus());
				}else {
					paramsMap.put("workstatus", StrUtil.equals(TaskStatusEnum.BUTTON_PASS.getStatus()
							, leaveDto.getPass()) || StrUtil.equals(TaskStatusEnum.CHECKING.getStatus(), leaveDto.getPass()) || StrUtil.equals(TaskStatusEnum.RESUBMIT.getStatus()
							, leaveDto.getPass())
							? TaskStatusEnum.CHECKING.getStatus()
							: TaskStatusEnum.REJECT.getStatus());
				}
				paramsMap.put("nodename",task.getName());
				rabbitTemplate.convertAndSend("SignDirectExchange","Sign.Finished.Routing", paramsMap);
			} catch (Exception e) {
				e.printStackTrace();
			}

		} else if (StrUtil.equals(TaskStatusEnum.CANCLE.getStatus(),leaveDto.getPass())) {
			HistoricProcessInstance historicProcessInstance =
					historyService.createHistoricProcessInstanceQuery()
							.processInstanceId(processInstanceId).singleResult();
			String businessKey = historicProcessInstance.getBusinessKey();
			String processKey = "";
			if (StrUtil.isNotBlank(businessKey)) {
				processKey = businessKey.split(CommonConstant.BREAK_BUSINESS_KEY)[0];
				businessKey = businessKey.split(CommonConstant.BREAK_BUSINESS_KEY)[1];
			}
			relation.setWorkStatus(Integer.parseInt(TaskStatusEnum.CANCLE.getStatus()));
			tQhAllRelationService.updateById(relation);
			WfConfig config = wfConfigService.getOne(new QueryWrapper<WfConfig>().eq("workflow_key", processKey));
			wfConfigService.updateFormStatusByBusinessId(config.getDatasourceAlias(), config.getTableName(), TaskStatusEnum.CANCLE.getStatus(), businessKey);
			//更新業務庫中間表狀態
			try {
				Map<String, String> paramsMap = MapUtil.newHashMap();
				paramsMap.put("serialno",relation.getSerialno());
				paramsMap.put("workstatus",TaskStatusEnum.CANCLE.getStatus());
				rabbitTemplate.convertAndSend("SignDirectExchange","Sign.Finished.Routing", paramsMap);
			} catch (Exception e) {
				e.printStackTrace();
			}
		} else {
			relation.setWorkStatus(Integer.parseInt(TaskStatusEnum.COMPLETED.getStatus()));
			relation.setCompletTime(new Date());
			tQhAllRelationService.updateById(relation);

			HistoricProcessInstance historicProcessInstance =
					historyService.createHistoricProcessInstanceQuery()
							.processInstanceId(processInstanceId).singleResult();
			String businessKey = historicProcessInstance.getBusinessKey();
			WfConfig config = wfConfigService.getOne(new QueryWrapper<WfConfig>().eq("workflow_key", businessKey.split(CommonConstant.BREAK_BUSINESS_KEY)[0]));
			wfConfigService.updateFormStatusByBusinessIds(config.getDatasourceAlias(), config.getTableName(), TaskStatusEnum.COMPLETED.getStatus(), businessKey.split(CommonConstant.BREAK_BUSINESS_KEY)[1]);
			try {
				Map<String, String> paramsMap = MapUtil.newHashMap();
				paramsMap.put("serialno",relation.getSerialno());
				paramsMap.put("workstatus",TaskStatusEnum.COMPLETED.getStatus());
				rabbitTemplate.convertAndSend("SignDirectExchange","Sign.Finished.Routing", paramsMap);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		//更新郵件發送記錄，邏輯刪除
		try {
			feignService.removeAfterAudit(processInstanceId, SecurityUtil.getUser().getUsername());
		} catch (Exception e) {

		}
		return true;
	}
	@Override
	@Transient
	public Boolean checkBachTask(LeaveDto leaveDto) {

		String message = leaveDto.getComment();
		Task task = taskService.createTaskQuery()
				.processInstanceId(leaveDto.getProcessId())
				.singleResult();
		String taskId = task.getId();
		taskService.setVariable(taskId,"chargerno",leaveDto.getChargNo());
		String processInstanceId = task.getProcessInstanceId();
		Authentication.setAuthenticatedUserId(leaveDto.getChargNo());

		TQhAllRelation relation = tQhAllRelationService.getOne(new QueryWrapper<TQhAllRelation>().eq("process_id", leaveDto.getProcessId()));
//		if (TaskStatusEnum.RESUBMIT.getStatus().equals(relation.getWorkStatus())) {
//			message = "";
//		}
		if (TaskStatusEnum.RESUBMIT.getStatus().equals(leaveDto.getPass())) {
			taskService.addComment(taskId, processInstanceId, "重新提交");
		} else if(TaskStatusEnum.CANCLE.getStatus().equals(leaveDto.getPass())){
			taskService.addComment(taskId, processInstanceId, "取消申請");
		}else{
			taskService.addComment(taskId, processInstanceId, message == null ? "通過" : message);
		}
		//同步簽核記錄到ipebg電子簽核平台
		try {
			BizChargeLog bizChargeLog = new BizChargeLog();
			bizChargeLog.setChargename(leaveDto.getChargName());
			bizChargeLog.setChargeno(leaveDto.getChargNo());
			bizChargeLog.setChargenode(task.getName());
			bizChargeLog.setSerialno(relation.getSerialno());
			if (TaskStatusEnum.RESUBMIT.getStatus().equals(leaveDto.getPass())) {
				bizChargeLog.setDecrib("重新提交");
			} else if(TaskStatusEnum.CANCLE.getStatus().equals(leaveDto.getPass())){
				bizChargeLog.setDecrib("取消申請");
			}else{
				bizChargeLog.setDecrib(message == null ? "通過" : message);
			}
			bizChargeLog.setIspass("0".equals(leaveDto.getPass()) ? "通過" : "駁回");
			bizChargeLog.setOperateip(leaveDto.getOperIp());
			bizChargeLog.setWorkflowid(relation.getWorkflowid());
			try {
				rabbitTemplate.convertAndSend("SignDirectExchange","Sign.ChargeLog.Routing", BeanUtil.beanToMap(bizChargeLog));
			} catch (Exception e) {
				e.printStackTrace();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		Map<String, Object> variables = new HashMap<>(1);
//        variables.put("flag" , leaveDto.getTaskFlag());
//        variables.put("type" , leaveDto.getType());
//        variables.put("days" , leaveDto.getDays());
		variables.put("pass", leaveDto.getPass());
		taskService.setVariableLocal(task.getId(), "IP", leaveDto.getOperIp());
		taskService.setVariableLocal(task.getId(), "STATUS", leaveDto.getPass());

		/**
		 * 查詢是否有代理，有則替換
		 */
		TProxyUserinfo userinfo = tProxyUserinfoService.getOne(new QueryWrapper<TProxyUserinfo>().eq("isvalid", "0").eq("supplyempno", leaveDto.getChargNo()).le("supplybegin", new Date()).ge("supplyend", new Date()));
		if (userinfo != null && !this.getTaskCandidate(taskId).contains(leaveDto.getChargNo())) {
			taskService.setVariableLocal(task.getId(), "AUDIT_USER",   leaveDto.getChargNo() + "/" + leaveDto.getChargName() + "代簽"+ "(" +userinfo.getSuppliedempno() + "/" + userinfo.getSuppliedusername()+")");
		} else {
			taskService.setVariableLocal(task.getId(), "AUDIT_USER", leaveDto.getChargNo() + "/" + leaveDto.getChargName());
		}
		if (StrUtil.equals(TaskStatusEnum.CANCLE.getStatus(),leaveDto.getPass())) {
			//結束任務
//			taskService.complete(taskId);
//			taskService.deleteTask(taskId);
			runtimeService.deleteProcessInstance(task.getProcessInstanceId(),"取消申請");
		}else {
			//任務流轉
			if (StrUtil.equals(TaskStatusEnum.BUTTON_REJECT.getStatus(), leaveDto.getPass())) {
				variables.put("condition","reject");
			}else{
				variables.put("condition","pass");
			}
			taskService.complete(taskId, variables);
		}
		ProcessInstance pi = runtimeService.createProcessInstanceQuery()
				.processInstanceId(processInstanceId)
				.singleResult();
		if (pi != null) {
			String businessKey = pi.getBusinessKey();
			String processKey = "";
			if (StrUtil.isNotBlank(businessKey)) {
				processKey = businessKey.split(CommonConstant.BREAK_BUSINESS_KEY)[0];
				businessKey = businessKey.split(CommonConstant.BREAK_BUSINESS_KEY)[1];
			}
			if (StrUtil.equals(TaskStatusEnum.BUTTON_REJECT.getStatus()
					, leaveDto.getPass())) {
				relation.setWorkStatus(Integer.parseInt(TaskStatusEnum.REJECT.getStatus()));
				tQhAllRelationService.updateById(relation);
			} else {
				relation.setWorkStatus(Integer.parseInt(TaskStatusEnum.CHECKING.getStatus()));
				tQhAllRelationService.updateById(relation);
			}
			WfConfig config = wfConfigService.getOne(new QueryWrapper<WfConfig>().eq("workflow_key", processKey));
			wfConfigService.updateFormStatusByBusinessId(config.getDatasourceAlias(), config.getTableName(), StrUtil.equals(TaskStatusEnum.BUTTON_PASS.getStatus()
					, leaveDto.getPass()) || StrUtil.equals(TaskStatusEnum.CHECKING.getStatus(), leaveDto.getPass()) || StrUtil.equals(TaskStatusEnum.RESUBMIT.getStatus()
					, leaveDto.getPass())
					? TaskStatusEnum.CHECKING.getStatus()
					: TaskStatusEnum.REJECT.getStatus(), businessKey);

			//更新業務庫中間表狀態
			try {
				Map<String, String> paramsMap = MapUtil.newHashMap();
				paramsMap.put("serialno",relation.getSerialno());
				paramsMap.put("workstatus",StrUtil.equals(TaskStatusEnum.BUTTON_PASS.getStatus()
						, leaveDto.getPass()) || StrUtil.equals(TaskStatusEnum.CHECKING.getStatus(), leaveDto.getPass()) || StrUtil.equals(TaskStatusEnum.RESUBMIT.getStatus()
						, leaveDto.getPass())
						? TaskStatusEnum.CHECKING.getStatus()
						: TaskStatusEnum.REJECT.getStatus());
				rabbitTemplate.convertAndSend("SignDirectExchange","Sign.Finished.Routing", paramsMap);
			} catch (Exception e) {
				e.printStackTrace();
			}

		} else if (StrUtil.equals(TaskStatusEnum.CANCLE.getStatus(),leaveDto.getPass())) {
			HistoricProcessInstance historicProcessInstance =
					historyService.createHistoricProcessInstanceQuery()
							.processInstanceId(processInstanceId).singleResult();
			String businessKey = historicProcessInstance.getBusinessKey();
			String processKey = "";
			if (StrUtil.isNotBlank(businessKey)) {
				processKey = businessKey.split(CommonConstant.BREAK_BUSINESS_KEY)[0];
				businessKey = businessKey.split(CommonConstant.BREAK_BUSINESS_KEY)[1];
			}
			relation.setWorkStatus(Integer.parseInt(TaskStatusEnum.CANCLE.getStatus()));
			tQhAllRelationService.updateById(relation);
			WfConfig config = wfConfigService.getOne(new QueryWrapper<WfConfig>().eq("workflow_key", processKey));
			wfConfigService.updateFormStatusByBusinessId(config.getDatasourceAlias(), config.getTableName(), TaskStatusEnum.CANCLE.getStatus(), businessKey);
			//更新業務庫中間表狀態
			try {
				Map<String, String> paramsMap = MapUtil.newHashMap();
				paramsMap.put("serialno",relation.getSerialno());
				paramsMap.put("workstatus",TaskStatusEnum.CANCLE.getStatus());
				rabbitTemplate.convertAndSend("SignDirectExchange","Sign.Finished.Routing", paramsMap);
			} catch (Exception e) {
				e.printStackTrace();
			}
		} else {
			relation.setWorkStatus(Integer.parseInt(TaskStatusEnum.COMPLETED.getStatus()));
			relation.setCompletTime(new Date());
			tQhAllRelationService.updateById(relation);

			HistoricProcessInstance historicProcessInstance =
					historyService.createHistoricProcessInstanceQuery()
							.processInstanceId(processInstanceId).singleResult();
			String businessKey = historicProcessInstance.getBusinessKey();
			WfConfig config = wfConfigService.getOne(new QueryWrapper<WfConfig>().eq("workflow_key", businessKey.split(CommonConstant.BREAK_BUSINESS_KEY)[0]));
			wfConfigService.updateFormStatusByBusinessIds(config.getDatasourceAlias(), config.getTableName(), TaskStatusEnum.COMPLETED.getStatus(), businessKey.split(CommonConstant.BREAK_BUSINESS_KEY)[1]);
			try {
				Map<String, String> paramsMap = MapUtil.newHashMap();
				paramsMap.put("serialno",relation.getSerialno());
				paramsMap.put("workstatus",TaskStatusEnum.COMPLETED.getStatus());
				rabbitTemplate.convertAndSend("SignDirectExchange","Sign.Finished.Routing", paramsMap);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		//更新郵件發送記錄，邏輯刪除
		try {
			feignService.removeAfterAudit(processInstanceId, leaveDto.getChargNo());
		} catch (Exception e) {

		}
		return null;
	}
	@Override
	public List<CommentDto> commitList(String processId) {
		//使用当前任务ID，获取当前任务对象
//		Task task = taskService.createTaskQuery().processInstanceId(processId)
//				.singleResult();
//		task = historyService.createHistoricTaskInstanceQuery().processInstanceId(processId).list();
		//获取流程实例ID
		List<CommentDto> commentDtoList = taskService
				.getProcessInstanceComments(processId)
				.stream().sorted(Comparator.comparing(c -> c.getTime())).map(comment -> {
							CommentEntity commentEntity = (CommentEntity) comment;
							CommentDto commentDto = new CommentDto();
							commentDto.setId(historyService.createHistoricTaskInstanceQuery().taskId(comment.getTaskId()).singleResult().getName());
							commentDto.setTime(comment.getTime());
							commentDto.setType(comment.getType());
							commentDto.setTaskId(comment.getTaskId());
							commentDto.setUserId(historyService.createHistoricVariableInstanceQuery().taskId(comment.getTaskId()).variableName("AUDIT_USER").singleResult().getValue().toString());
							commentDto.setFullMessage(commentEntity.getMessage());
							HistoricVariableInstance varIp = historyService//
									.createHistoricVariableInstanceQuery().taskId(comment.getTaskId()) // 创建一个历史的流程变量查询对象
									.variableName("IP").singleResult();
							HistoricVariableInstance varStatus = historyService//
									.createHistoricVariableInstanceQuery().taskId(comment.getTaskId()) // 创建一个历史的流程变量查询对象
									.variableName("STATUS").singleResult();
							commentDto.setOperIp(varIp == null ? "" : varIp.getValue().toString());
							commentDto.setStatus(varStatus == null ? "" : varStatus.getValue().toString());
							commentDto.setProcessInstanceId(comment.getProcessInstanceId());
							return commentDto;
						}
				).collect(Collectors.toList());
		return commentDtoList;
	}

	/**
	 * 追踪图片节点
	 *
	 * @param id
	 */
	@Override
	public InputStream trackImage(String id) {
		//使用当前任务ID，获取当前任务对象
		String processInstanceId = id;
		ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
				.processInstanceId(processInstanceId).singleResult();
		HistoricProcessInstance historicProcessInstance =
				historyService.createHistoricProcessInstanceQuery()
						.processInstanceId(processInstanceId).singleResult();
		String processDefinitionId = null;
		List<String> executedActivityIdList = new ArrayList<>();
		if (processInstance != null) {
			processDefinitionId = processInstance.getProcessDefinitionId();
			executedActivityIdList = this.runtimeService.getActiveActivityIds(processInstance.getId());
		} else if (historicProcessInstance != null) {
			processDefinitionId = historicProcessInstance.getProcessDefinitionId();
			executedActivityIdList = historyService.createHistoricActivityInstanceQuery()
					.processInstanceId(processInstanceId)
					.orderByHistoricActivityInstanceId().asc().list()
					.stream().map(HistoricActivityInstance::getActivityId)
					.collect(Collectors.toList());
		}
		BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinitionId);
		ProcessEngineConfiguration processEngineConfiguration = processEngine.getProcessEngineConfiguration();
		Context.setProcessEngineConfiguration((ProcessEngineConfigurationImpl) processEngineConfiguration);
		ProcessDiagramGenerator diagramGenerator = processEngineConfiguration.getProcessDiagramGenerator();
		return diagramGenerator.generateDiagram(
				bpmnModel, "png",
				executedActivityIdList, Collections.emptyList(),
				processEngine.getProcessEngineConfiguration().getActivityFontName(),
				processEngine.getProcessEngineConfiguration().getLabelFontName(),
				"宋体",
				null, 1.0);
	}

	private List<String> findOutFlagListByTaskId(Task task, ProcessInstance pi) {
		//查询ProcessDefinitionEntiy对象
		ProcessDefinitionEntity processDefinitionEntity = (ProcessDefinitionEntity) repositoryService
				.getProcessDefinition(task.getProcessDefinitionId());
		ActivityImpl activityImpl = processDefinitionEntity.findActivity(pi.getActivityId());
		//获取当前活动完成之后连线的名称
		List<String> nameList = activityImpl.getOutgoingTransitions().stream()
				.map(pvm -> {
					String name = (String) pvm.getProperty("name");
					return StrUtil.isNotBlank(name) ? name : FLAG;
				}).collect(Collectors.toList());
		return nameList;
	}

	/**
	 * 获取下一个用户任务用户组信息
	 *
	 * @return 下一个用户任务用户组信息
	 * @throws Exception
	 */
	public TaskDefinition getNextTaskGroup(String processInstanceId) throws Exception {
		ProcessDefinitionEntity processDefinitionEntity = null;
		String id = null;
		TaskDefinition task = null;
		//获取流程实例Id信息
		//获取流程发布Id信息
		String definitionId = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult().getProcessDefinitionId();
		processDefinitionEntity = (ProcessDefinitionEntity) ((RepositoryServiceImpl) repositoryService)
				.getDeployedProcessDefinition(definitionId);
		ExecutionEntity execution = (ExecutionEntity) runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
		//当前流程节点Id信息
		String activitiId = execution.getActivityId();
		//获取流程所有节点信息
		List<ActivityImpl> activitiList = processDefinitionEntity.getActivities();
		//遍历所有节点信息
		for (ActivityImpl activityImpl : activitiList) {
			id = activityImpl.getId();
			// 找到当前节点信息
			if (activitiId.equals(id)) {
				//获取下一个节点信息
				task = nextTaskDefinition(activityImpl, activityImpl.getId(), null, processInstanceId);
				break;
			}
		}
		return task;
	}

	/**
	 * 下一个任务节点信息,
	 * <p>
	 * 如果下一个节点为用户任务则直接返回,
	 * <p>
	 * 如果下一个节点为排他网关, 获取排他网关Id信息, 根据排他网关Id信息和execution获取流程实例排他网关Id为key的变量值,
	 * 根据变量值分别执行排他网关后线路中的el表达式, 并找到el表达式通过的线路后的用户任务信息
	 *
	 * @param activityImpl      流程节点信息
	 * @param activityId        当前流程节点Id信息
	 * @param elString          排他网关顺序流线段判断条件, 例如排他网关顺序留线段判断条件为${money>1000}, 若满足流程启动时设置variables中的money>1000, 则流程流向该顺序流信息
	 * @param processInstanceId 流程实例Id信息
	 * @return
	 */
	private TaskDefinition nextTaskDefinition(ActivityImpl activityImpl, String activityId, String elString, String processInstanceId) {
		PvmActivity ac = null;
		Object s = null;
		//如果遍历节点为用户任务并且节点不是当前节点信息
		if ("userTask".equals(activityImpl.getProperty("type")) && !activityId.equals(activityImpl.getId())) {
			//获取该节点下一个节点信息
			TaskDefinition taskDefinition = ((UserTaskActivityBehavior) activityImpl.getActivityBehavior()).getTaskDefinition();
			return taskDefinition;
		} else {
			//获取节点所有流向线路信息
			List<PvmTransition> outTransitions = activityImpl.getOutgoingTransitions();
			List<PvmTransition> outTransitionsTemp = null;
			for (PvmTransition tr : outTransitions) {
				ac = tr.getDestination(); //获取线路的终点节点
				//如果流向线路为排他网关
				if ("exclusiveGateway".equals(ac.getProperty("type"))) {
					outTransitionsTemp = ac.getOutgoingTransitions();
					//如果网关路线判断条件为空信息
					if (StringUtils.isEmpty(elString)) {
						//获取流程启动时设置的网关判断条件信息
						elString = getGatewayCondition(ac.getId(), processInstanceId);
					}
					//如果排他网关只有一条线路信息
					if (outTransitionsTemp.size() == 1) {
						return nextTaskDefinition((ActivityImpl) outTransitionsTemp.get(0).getDestination(), activityId, elString, processInstanceId);
					} else if (outTransitionsTemp.size() > 1) {  //如果排他网关有多条线路信息
						for (PvmTransition tr1 : outTransitionsTemp) {
							s = tr1.getProperty("conditionText");  //获取排他网关线路判断条件信息
							//判断el表达式是否成立
							if (isCondition(ac.getId(), StringUtils.trim(s.toString()), elString)) {
								return nextTaskDefinition((ActivityImpl) tr1.getDestination(), activityId, elString, processInstanceId);
							}
						}
					}
				} else if ("userTask".equals(ac.getProperty("type"))) {
					return ((UserTaskActivityBehavior) ((ActivityImpl) ac).getActivityBehavior()).getTaskDefinition();
				} else {
				}
			}
			return null;
		}
	}

	/**
	 * 查询流程启动时设置排他网关判断条件信息
	 *
	 * @param gatewayId         排他网关Id信息, 流程启动时设置网关路线判断条件key为网关Id信息
	 * @param processInstanceId 流程实例Id信息
	 * @return
	 */
	public String getGatewayCondition(String gatewayId, String processInstanceId) {
		Execution execution = runtimeService.createExecutionQuery().processInstanceId(processInstanceId).singleResult();
		return runtimeService.getVariable(execution.getId(), gatewayId).toString();
	}

	/**
	 * 根据key和value判断el表达式是否通过信息
	 *
	 * @param key   el表达式key信息
	 * @param el    el表达式信息
	 * @param value el表达式传入值信息
	 * @return
	 */
	public boolean isCondition(String key, String el, String value) {
		ExpressionFactory factory = new ExpressionFactoryImpl();
		SimpleContext context = new SimpleContext();
		context.setVariable(key, factory.createValueExpression(value, String.class));
		ValueExpression e = factory.createValueExpression(context, el, boolean.class);
		return (Boolean) e.getValue(context);
	}

	/**
	 * 方法描述: 查詢歷史任務
	 *
	 * @Author: S6114648
	 * @CreateDate: 2020/8/14  13:57
	 * @Return
	 **/

	public IPage listHistory(Map<String, Object> params) {
		HistoricTaskInstanceQuery taskQuery = historyService.createHistoricTaskInstanceQuery()
				.taskCandidateUser(SecurityUtil.getUser().getUsername()).finished().orderByHistoricTaskInstanceEndTime().desc();
		String taskName = (String) params.get("taskName");
		if (StrUtil.isNotBlank(taskName)) {
			taskQuery.taskNameLike(taskName);
		}
		int page = MapUtil.getInt(params, CommonConstants.CURRENT);
		int limit = MapUtil.getInt(params, CommonConstants.SIZE);
		IPage result = new Page(page, limit);
		result.setTotal(taskQuery.count());
		List<TaskDto> taskDTOList = taskQuery.orderByTaskCreateTime().desc()
				.listPage((page - 1) * limit, limit).stream().map(task -> {
					TaskDto dto = new TaskDto();
					dto.setTaskId(task.getId());
					HistoricProcessInstance pi = historyService.createHistoricProcessInstanceQuery()
							.processInstanceId(task.getProcessInstanceId())
							.singleResult();
					String businessKey = pi.getBusinessKey();
					if (StrUtil.isNotBlank(businessKey)) {
						dto.setProcessDefKey(businessKey.split(CommonConstant.BREAK_BUSINESS_KEY)[0]);
						dto.setBunessId(businessKey.split(CommonConstant.BREAK_BUSINESS_KEY)[1]);
						//查詢跳轉審核詳情
						WfConfig config = wfConfigService.getOne(new QueryWrapper<WfConfig>().eq("workflow_key", businessKey.split(CommonConstant.BREAK_BUSINESS_KEY)[0]));
						if (config != null) {
							dto.setAuditPath(config.getAuditAction());
							dto.setAuditDetail(config.getDetailAction());
							dto.setPdName(config.getWorkflowName());
							try {
								Map employer = wfConfigService.getMakerUserInfo(config.getDatasourceAlias(), config.getTableName(), businessKey.split(CommonConstant.BREAK_BUSINESS_KEY)[1]);
								if (ObjectUtil.isNotEmpty(employer)) {
									dto.setMakerNo(ObjectUtil.defaultIfNull(employer.get("makerno"), "") + "");
									dto.setMakerName(ObjectUtil.defaultIfNull(employer.get("makername"), "") + "");
									dto.setSerialno(ObjectUtil.defaultIfNull(employer.get("serialno"), "") + "");
									dto.setStatus(ObjectUtil.defaultIfNull(employer.get("workstatus"), "") + "");
//									dto.setCreateTime(DateUtil.parse(employer.get("createTime") + ""));
								}
							} catch (Exception e) {
								e.printStackTrace();
							}
						}
					}
					dto.setTaskName(task.getName());
					dto.setProcessInstanceId(task.getProcessInstanceId());
					dto.setNodeKey(task.getTaskDefinitionKey());
					dto.setCategory(task.getCategory());
					dto.setCreateTime(task.getEndTime());
					return dto;
				}).collect(Collectors.toList());
		result.setRecords(taskDTOList);
		return result;
	}

	public FlowElement getNextUserFlowElement(String processInstanceId) throws ServiceException {
		Task task = taskService.createTaskQuery().processInstanceId(processInstanceId).singleResult();
		if (task == null) {
			throw new ServiceException("流程未启动或已执行完成");
		}
		// 取得已提交的任务
		HistoricTaskInstance historicTaskInstance = historyService.createHistoricTaskInstanceQuery()
				.taskId(task.getId()).singleResult();
		// 取得正在流转的流程实例,若已完成则为null
//getRuntimeProcessInstance是自己封装的获取流程实例的方法
		ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceBusinessKey(historicTaskInstance.getProcessInstanceId()).singleResult();
		//每个流程实例只有一个executionId
		//获得流程定义
		ProcessDefinition processDefinition = repositoryService.getProcessDefinition(historicTaskInstance.getProcessDefinitionId());

		//获得当前流程的活动ID
		ExecutionQuery executionQuery = runtimeService.createExecutionQuery();
		Execution execution = executionQuery.executionId(historicTaskInstance.getExecutionId()).singleResult();
		String activityId = execution.getActivityId();
		UserTask userTask = null;
		while (true) {
			//根据活动节点获取当前的组件信息
			FlowNode flowNode = getFlowNode(processDefinition.getId(), activityId);
			//获取该流程组件的之后/之前的组件信息
			List<SequenceFlow> sequenceFlowListOutGoing = flowNode.getOutgoingFlows();
//        List<SequenceFlow> sequenceFlowListIncoming=flowNode.getIncomingFlows();

			//获取的下个节点不一定是userTask的任务节点，所以要判断是否是任务节点
			//sequenceFlowListOutGoing数量可能大于1,可以自己做判断,此处只取第一个
			FlowElement flowElement = sequenceFlowListOutGoing.get(0);
			if (flowElement instanceof UserTask) {
				userTask = (UserTask) flowElement;
				System.out.println("获取该任务节点的审批信息...");
				break;
			} else {
				//下一节点不是任务userTask的任务节点,所以要获取再下一个节点的信息,直到获取到userTask任务节点信息
				String flowElementId = flowElement.getId();
				activityId = flowElementId;
				continue;
			}
		}
		return userTask;
	}
	//根据活动节点和流程定义ID获取该活动节点的组件信息

	public FlowNode getFlowNode(String processDefinitionId, String activityId) {
		BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinitionId);
		FlowNode flowNode = (FlowNode) bpmnModel.getMainProcess().getFlowElement(activityId);
		return flowNode;
	}

	public Set getTaskCandidate(String taskId) {
		Set users = new HashSet();
		List identityLinkList = taskService.getIdentityLinksForTask(taskId);
		if (identityLinkList != null && identityLinkList.size() > 0) {
			for (Iterator iterator = identityLinkList.iterator(); iterator
					.hasNext(); ) {
				IdentityLink identityLink = (IdentityLink) iterator.next();
				if (identityLink.getUserId() != null) {
					String user = identityLink.getUserId();
					if (user != null)
						users.add(user);
				}
				if (identityLink.getGroupId() != null) {
					// 根据组获得对应人员
					List userList = identityService.createUserQuery()
							.memberOfGroup(identityLink.getGroupId()).list();
					if (userList != null && userList.size() > 0)
						users.addAll(userList);
				}
			}
		}
		return users;
	}

	public List<UserTask> getAllTasks(String processId) {
		HistoricProcessInstance historicProcessInstance =
				historyService.createHistoricProcessInstanceQuery()
						.processInstanceId(processId).singleResult();
		String processDefinitionId = historicProcessInstance.getProcessDefinitionId();
		BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinitionId);
		Process process = bpmnModel.getProcesses().get(0);
//获取所有节点
		List<UserTask> userTaskList = process.findFlowElementsOfType(UserTask.class);
		return userTaskList;
	}

	public String getSignPath(String processId) {
		//流程定义id
		HistoricProcessInstance historicProcessInstance =
				historyService.createHistoricProcessInstanceQuery()
						.processInstanceId(processId).singleResult();
		String businessKey = historicProcessInstance.getBusinessKey();
		String processDefinitionId = historicProcessInstance.getProcessDefinitionId();
		BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinitionId);
		Process process = bpmnModel.getProcesses().get(0);

		Task task = taskService.createTaskQuery().processInstanceId(processId).singleResult();
//获取所有节点
		List<UserTask> UserTaskList = process.findFlowElementsOfType(UserTask.class);
		List userList = new ArrayList();
		UserTaskList.stream().forEach(userTask -> {
					List<String> assigness = null;
					StringBuffer sb = new StringBuffer();
					TQhAllRelation relation = tQhAllRelationService.getOne(new QueryWrapper<TQhAllRelation>().eq("process_id", processId));
					Map<String, List<ExtensionElement>> stringListMap = userTask.getExtensionElements();
					if (stringListMap.get("responsibledepartment") != null) {
						assigness = feignService.getAssignees(stringListMap.get("responsibledepartment").get(0).getElementText());
					}
					if (stringListMap.get("responsiblepost") != null) {
						WfConfig config = wfConfigService.getOne(new QueryWrapper<WfConfig>().eq("workflow_key", businessKey.split(CommonConstant.BREAK_BUSINESS_KEY)[0]));
						String employer = feignService.getCreateBy(config.getDatasourceAlias(), config.getTableName(), businessKey.split(CommonConstant.BREAK_BUSINESS_KEY)[1]);
						Dept dept = feignService.selectDeptById(feignService.getOne(employer).getDeptId());
						assigness = feignService.getAssignees(dept.getCode(), stringListMap.get("responsiblepost").get(0).getElementText());
					}
					if(assigness != null) {
						if (task!=null&&userTask.getName().equals(task.getName())) {
							//添加當前節點高亮顯示
							userList.add(sb.append("<font color=\"red\" size=\"4\"><strong>").append(userTask.getName()).append(this.getSignUser(assigness)).append("</strong></font>"));
						} else {
							userList.add(sb.append(userTask.getName()).append(this.getSignUser(assigness)));
						}
					}
				}
		);
		return ArrayUtil.join(userList.toArray(), "-->");
	}

	private String getSignUser(List<String> assigness) {
		StringBuffer sb = new StringBuffer();
		List userList = new ArrayList();
		sb.append("(").append(ArrayUtil.join(assigness.toArray(), ",")).append("/");
		assigness.stream().forEach(assignes -> {
			User user = feignService.getOne(assignes);
			userList.add(user.getNickName());
		});
		sb.append(ArrayUtil.join(userList.toArray(), ",")).append(")");
		return sb.toString();
	}
}
