package com.entfrm.biz.activiti.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.entfrm.core.base.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.entfrm.core.data.entity.BaseBizEntity;
import org.springframework.data.annotation.Transient;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020-11-12 13:27:22
 *
 * @description 中間表对象 TQhAllRelation
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_qh_all_relation")
public class TQhAllRelation extends BaseBizEntity{
    private static final long serialVersionUID = 1L;

    /** 填單人工號 */
    private String makerNo;

    /** 表單編號 */
    @Excel(name = "表單編號")
    private String serialno;

    /** 流程編碼 */
    @Excel(name = "流程編碼")
    private String workflowid;

    /** 完成時間 */
    @Excel(name = "完成時間", width = 30, dateFormat = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date completTime;

    /** 流程名稱 */
    @Excel(name = "流程名稱")
    private String wfName;

    /** 编号 */
    @TableId
    private String id;

    /** 填單人姓名 */
    private String makerName;

    /** 流程定義標示 */
    private String processId;

    /** 流程版本 */
    @Excel(name = "流程版本")
    private String version;

    /** 審核狀態 */
    @Excel(name = "審核狀態")
    private Integer workStatus;

    private String classPackage;
    /** 表單來源 */
    private String formfrom;
    /** 表對應實體名稱 */
    @Excel(name = "表對應實體名稱")
    private String dtoName;
    @TableField(exist = false)
    private String auditPath;
    @TableField(exist = false)
    private String bunessId;
    @TableField(exist = false)
    private String taskName;
    @TableField(exist = false)
    private String signPerson;
    @TableField(exist = false)
    private int canBatch;
    @TableField(exist = false)
    private String nodename;
    @TableField(exist = false)
    private String deptNo;


}
