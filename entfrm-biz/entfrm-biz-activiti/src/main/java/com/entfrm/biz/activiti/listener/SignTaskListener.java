package com.entfrm.biz.activiti.listener;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONException;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.entfrm.biz.activiti.dto.CommentDto;
import com.entfrm.biz.activiti.entity.BizChargeLog;
import com.entfrm.biz.activiti.entity.TQhAllRelation;
import com.entfrm.biz.activiti.entity.WfConfig;
import com.entfrm.biz.activiti.service.TQhAllRelationService;
import com.entfrm.biz.activiti.service.WfConfigService;
import com.entfrm.biz.activiti.service.impl.TaskServiceImpl;
import com.entfrm.biz.feign.FeignService;
import com.entfrm.core.base.annotation.SignNode;
import com.entfrm.core.base.enums.TaskStatusEnum;
import com.entfrm.core.base.util.SpringContextUtil;
import com.entfrm.core.data.constant.CommonConstant;
import com.entfrm.core.security.util.SecurityUtil;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.EngineServices;
import org.activiti.engine.HistoryService;
import org.activiti.engine.TaskService;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.ExecutionListener;
import org.activiti.engine.delegate.TaskListener;
import org.activiti.engine.task.Task;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateSettings;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 类的描述：
 *
 * @Author: S6114648
 * @Email: <EMAIL>
 * @CreateDate: 2021年06月02日  10:29
 * @version: v1.0
 **/
@Slf4j
@Service
public class SignTaskListener implements TaskListener, ExecutionListener {
    @Resource
    private FeignService feignService;
    @Resource
    private com.entfrm.biz.activiti.service.TaskService taskServiceLocal;
    @Resource
    private WfConfigService wfConfigService;
    @Resource
    private TQhAllRelationService tQhAllRelationService;
    @Resource
    private RabbitTemplate rabbitTemplate;

    @Override
    public void notify(DelegateExecution execution) throws Exception {
        String emailAddress = feignService.getByKey("send.mail.address").getData().toString();
//			String ：duserNo//填單人工號
//			String：chargerno//審核人工號
//			String：empno//下一節點審核人工號
//			String ：workflowName//表單名稱
//			String ：serialNo//表單號
//			String ：status//狀態
        try {
            HttpUtil.post(emailAddress, JSONUtil.createObj().put("duserNo", execution.getVariable("maker")).put("chargerno", execution.getVariable("chargerno"))
                    .put("empno", execution.getVariable("maker")).put("workflowName", execution.getVariable("workflowName")).put("serialNo", execution.getVariable("serialNo")).put("status", "3"));
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void notify(DelegateTask delegateTask) {
        EngineServices engineServices = delegateTask.getExecution().getEngineServices();
        TaskService taskService = engineServices.getTaskService();
        String businessKey = delegateTask.getExecution().getProcessBusinessKey();
        WfConfig config = wfConfigService.getOne(new QueryWrapper<WfConfig>().eq("workflow_key", businessKey.split(CommonConstant.BREAK_BUSINESS_KEY)[0]));
        TQhAllRelation tQhAllRelation = tQhAllRelationService.getOne(new QueryWrapper<TQhAllRelation>().eq("process_id", delegateTask.getProcessInstanceId()));
        List<Field> fieldsList;
        Boolean flag = false;
        try {
            if (ObjectUtil.isNotNull(tQhAllRelation)) {
                Class c1 = Class.forName(tQhAllRelation.getClassPackage());
                Field fields[] = c1.getDeclaredFields();
                fieldsList = new ArrayList();
                Arrays.stream(fields).forEach(field -> {
                            SignNode signNode = field.getAnnotation(SignNode.class);
                            if (ObjectUtil.isNotEmpty(signNode)) {
                                fieldsList.add(field);
                            }
                        }
                );
                for (Field field : fieldsList) {
                    if (field.getName().equals(delegateTask.getTaskDefinitionKey())) {
                        flag = ((SignNode) field.getAnnotation(SignNode.class)).canBatch() == 1;
                        break;
                    }
                }
            }
        } catch (ClassNotFoundException e) {
            throw new RuntimeException(e);
        }
        //如果沒有設置簽核人或者為第一個節點時，則跳過概節點
        String pass = delegateTask.getVariable("pass") + "";
        if (("maker".equals(delegateTask.getTaskDefinitionKey()) && !StrUtil.equals("1", pass)) || StrUtil.equals("AUTO_SIGN", delegateTask.getAssignee())) {
            Map<String, Object> variables = new HashMap<>(1);
            variables.put("pass", "0");
            taskService.complete(delegateTask.getId(), variables);
        } else if (flag && StrUtil.equals(config.getAutoAudit(), "Y")&& !StrUtil.equals("1", pass)) {
            List<CommentDto> commentDtoList = taskServiceLocal.commitList(delegateTask.getProcessInstanceId());
            Integer lastRejectIndex = -1;
            for (int i = 0; i < commentDtoList.size(); i++) {
                if (commentDtoList.get(i).getStatus().equals("1")||commentDtoList.get(i).getStatus().equals("5")) {
                    lastRejectIndex = i;
                }
            }
            if (lastRejectIndex >= 0) {
                commentDtoList = commentDtoList.stream().skip((lastRejectIndex + 1)).collect(Collectors.toList());
            }
            List<CommentDto> entityList1 = commentDtoList.stream().filter(entir -> entir.getUserId().split("/")[0].equals(delegateTask.getAssignee())).collect(Collectors.toList());
            if (entityList1.size() > 0) {
//                taskService.addComment(delegateTask.getId(), delegateTask.getProcessInstanceId(), "通過");
                delegateTask.setVariableLocal("IP", entityList1.get(0).getOperIp());
                delegateTask.setVariableLocal("STATUS", entityList1.get(0).getStatus());
                delegateTask.setVariable("chargerno", entityList1.get(0).getUserId().split("/")[0]);
                delegateTask.setVariableLocal("AUDIT_USER", entityList1.get(0).getUserId());
                taskService.addComment(delegateTask.getId(), delegateTask.getProcessInstanceId(), "系統簽核");

                Map<String, Object> variables = new HashMap<>(1);
                variables.put("pass", "0");
                taskService.complete(delegateTask.getId(), variables);
                try {
                    BizChargeLog bizChargeLog = new BizChargeLog();
                    bizChargeLog.setChargename(entityList1.get(0).getUserId().split("/")[1]);
                    bizChargeLog.setChargeno(entityList1.get(0).getUserId().split("/")[0]);
                    bizChargeLog.setChargenode(delegateTask.getName());
                    bizChargeLog.setSerialno(tQhAllRelation.getSerialno());
                    bizChargeLog.setIspass("通過");
                    bizChargeLog.setDecrib("通過");
                    bizChargeLog.setOperateip(entityList1.get(0).getOperIp());
                    bizChargeLog.setWorkflowid(tQhAllRelation.getWorkflowid());
                    try {
                        rabbitTemplate.convertAndSend("SignDirectExchange","Sign.ChargeLog.Routing", BeanUtil.beanToMap(bizChargeLog));
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        } else {
            String emailAddress = feignService.getByKey("send.mail.address").getData().toString();
//			String ：duserNo//填單人工號
//			String：chargerno//審核人工號
//			String：empno//下一節點審核人工號
//			String ：workflowName//表單名稱
//			String ：serialNo//表單號
//			String ：status//狀態
//			System.out.println(delegateTask.getVariable("chargerno"));
            try {
                HttpUtil.post(emailAddress,
                        JSONUtil.createObj()
                                .put("duserNo", delegateTask.getVariable("maker"))
                                .put("chargerno", delegateTask.getVariable("chargerno") != null ? delegateTask.getVariable("chargerno") : delegateTask.getVariable("maker"))
                                .put("empno", delegateTask.getAssignee())
                                .put("workflowName", delegateTask.getVariable("workflowName"))
                                .put("serialNo", delegateTask.getVariable("serialNo")).put("status", StrUtil.equals("1", pass) ? "4" : "2"));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}
