package com.entfrm.biz.design.tool.query;

import com.entfrm.biz.design.dto.DesignTableDto;
import com.entfrm.biz.design.entity.DesignColumn;
import com.entfrm.biz.design.entity.DesignTable;
import com.entfrm.core.base.util.StrUtil;
import com.entfrm.core.data.annotation.DataResourceFilter;
import com.entfrm.core.data.constant.CommonConstant;
import com.entfrm.core.data.datasource.DSContextHolder;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * TODO
 *
 * <AUTHOR>
 * @ClassName PostgresqlQueryTool
 * @Version 1.0
 * @since 2019/8/2 11:28
 */
@Component
public class PostgresqlQueryTool extends BaseQueryTool {
    public static final String sqlForGetColumn = new StringBuilder().append( " SELECT " )
            .append( "   attname AS COLUMN_NAME, " )
            .append( "   nullvalue AS is_required, " )
            .append( "   is_pk, " )
            .append( "   sort, " )
            .append( "   column_comment, " )
            .append( "   case jdbc_type  " )
            .append( "     when 'numeric' then 'numeric(' || substr( SPLIT_PART( c_length, ',', 1 ), 2, LENGTH ( SPLIT_PART( c_length, ',', 1 ) ) ) || ')' " )
            .append( "     else	jdbc_type end " )
            .append( "     column_type " )
            .append( " FROM " )
            .append( "   ( " )
            .append( "   SELECT A " )
            .append( "     .attname, " )
            .append( "     case A.attnotnull :: VARCHAR when 'true' then '0' else NULL END  as nullvalue, " )
            .append( "     A.attnum * 10 AS SORT, " )
            .append( "     col_description ( A.attrelid, A.attnum ) AS column_comment, " )
            .append( "     case T.typname " )
            .append( "      when 'timestamp' then 'timestamp(' || A.attlen || ')' " )
            .append( "      when 'numeric' then 'numeric' " )
            .append( "      else concat_ws ( '', T.typname, SUBSTRING ( format_type ( A.atttypid, A.atttypmod ) FROM '\\(.*\\)' ) ) end " )
            .append( "        AS jdbc_type, " )
            .append( "     SUBSTRING ( format_type ( A.atttypid, A.atttypmod ) FROM '\\(.*\\)' ) c_length , " )
            .append( "     (case " )
            .append( " when (select count(*) from pg_constraint where conrelid = a.attrelid and conkey[1]=attnum and contype='p')>0  then '1' " )
            .append( " else '0' " )
            .append( " end) as is_pk " )
            .append( "   FROM " )
            .append( "     pg_class AS C, " )
            .append( "     pg_attribute AS A, " )
            .append( "     pg_tables AS B, " )
            .append( "     pg_type T  " )
            .append( "   WHERE " )
            .append( "     B.tableowner = CURRENT_USER  " )
            .append( "     AND B.schemaname <> 'pgagent'  " )
            .append( "     AND C.relname = B.tablename  " )
            .append( "     AND UPPER ( B.tablename ) = UPPER ( ? )  " )
            .append( "     AND A.attrelid = C.oid  " )
            .append( "     AND A.atttypid = T.oid  " )
            .append( "   AND A.attnum > 0  " )
            .append( "   ) A " ).toString();
//    private PostgresqlQueryTool() {
////        System.out.println("single");
//    }
//
//    private static class Inner {
//        private static PostgresqlQueryTool postgresqlQueryTool = new PostgresqlQueryTool();
//    }
//    public static PostgresqlQueryTool getInstance() {
//        return PostgresqlQueryTool.Inner.postgresqlQueryTool;
//    }

    @Override
    @DataResourceFilter
    public List<DesignTableDto> getTables(String alias, String tableName) {

            StringBuilder sql = new StringBuilder();
            sql.append("SELECT  relname AS tableName,  CAST ( obj_description ( relfilenode, 'pg_class' ) AS VARCHAR ) AS tableComment  FROM  pg_class C  WHERE  relkind = 'r'  AND relname NOT LIKE'pg_%'  AND relname NOT LIKE'sql_%' ")
                    .append(" and relname not like 'sys_%' and relname not like 'qrtz_%' and relname not like 'act_%' and relname not like 'dev_%' ");

            if (StrUtil.isNotBlank(tableName)) {
                sql.append(" and relname like '%" + tableName + "%'");
            }
            sql.append(" ORDER BY relname");
            return jdbcTemplate.query(sql.toString(),new BeanPropertyRowMapper<DesignTableDto>(DesignTableDto.class));

    }
    @Override
    @DataResourceFilter
    public List<DesignColumn> getColumns(String alias, String tableName) {
        return jdbcTemplate.query(sqlForGetColumn, new BeanPropertyRowMapper<DesignColumn>(DesignColumn.class), tableName);

    }

    //创建表
    @Override
    public String createTable(DesignTable table) {
        StringBuilder sb = new StringBuilder();
        sb.append("CREATE TABLE " + table.getTableName().toLowerCase(Locale.ENGLISH) + "  (");
        if (table.getColumns() != null && table.getColumns().size() > 0) {
            for (int i = 0; i < table.getColumns().size(); i++) {
                DesignColumn column = table.getColumns().get(i);
                if(null != column.getColumnName()&&null != column.getColumnType()) {
                    if (i == table.getColumns().size() - 1) {
                        sb.append(fieldCreateScript(column));
                    } else {
                        sb.append(fieldCreateScript(column) + ",");
                    }
                }else{
                    if (i == table.getColumns().size() - 1) {
                        sb.deleteCharAt(sb.length() - 1);
                    }
                }
            }
        }
        sb.append(");");
        sb.append(primaryKey(table));
        sb.append(commentList(table));
        return sb.toString();
    }

    //字段信息转sql脚本(创建)
    private String fieldCreateScript(DesignColumn column) {
        String columnName = null != column.getColumnName()?column.getColumnName().toLowerCase(Locale.ENGLISH):"";
        String script = columnName + " " + column.getColumnType() + " " + ((("1").equals(column.getIsRequired()) || "1".equals(column.getIsPk())) ? "NOT NULL" : "NULL");
//        script += (StrUtil.isNotEmpty(column.getColumnComment()) ? " COMMENT '" + column.getColumnComment() + "'" : " ");
        script += (StrUtil.isNotEmpty(column.getDefValue()) ? " DEFAULT " + column.getDefValue() : " ");
//        if ("id".equalsIgnoreCase(column.getColumnName()) && "1".equals(column.getIsPk())) {
//            script += " primary key AUTO_INCREMENT";
//        }
        return script;
    }
    private String primaryKey(DesignTable table){
        String script = "";
        for (int i = 0; i < table.getColumns().size(); i++) {
            DesignColumn column = table.getColumns().get(i);
            if ("id".equalsIgnoreCase(column.getColumnName()) && "1".equals(column.getIsPk())) {
//                script += "ALTER TABLE "+ table.getTableName().toLowerCase(Locale.ENGLISH) + " ADD CONSTRAINT "+ table.getTableName().toLowerCase(Locale.ENGLISH) + "pkey PRIMARY KEY (id);";
                script += "ALTER TABLE "+ table.getTableName().toLowerCase(Locale.ENGLISH) + " ADD PRIMARY KEY (id);";

            }
        }
        return script;
    }
    private String commentList(DesignTable table){
        String script = "";
        for (int i = 0; i < table.getColumns().size(); i++) {
            DesignColumn column = table.getColumns().get(i);
            if(null != column.getColumnName()&&null != column.getColumnType()) {
                script += "COMMENT ON COLUMN " + table.getTableName().toLowerCase(Locale.ENGLISH) + "." + column.getColumnName().toLowerCase(Locale.ENGLISH) + " IS '" + column.getColumnComment() + "';";
            }
        }
        script += "COMMENT ON TABLE " + table.getTableName().toLowerCase(Locale.ENGLISH) + " IS '"+ table.getTableComment()+"';";
        return script;
    }
    //字段信息转sql脚本(创建)ALTER TABLE "public"."act_ru_job" ADD CONSTRAINT "act_ru_job_pkey" PRIMARY KEY ("id_");
//    private String fieldCreateScript(DesignColumn column) {
//        String script = column.getColumnName() + " " + column.getColumnType() + " " + ((("1").equals(column.getIsRequired()) || "1".equals(column.getIsPk())) ? "NOT NULL" : "NULL");
//        script += (StrUtil.isNotEmpty(column.getColumnComment()) ? " COMMENT '" + column.getColumnComment() + "'" : " ");
//        script += (StrUtil.isNotEmpty(column.getDefValue()) ? " DEFAULT " + column.getDefValue() : " ");
//        if ("id".equalsIgnoreCase(column.getColumnName()) && "1".equals(column.getIsPk())) {
//            script += " primary key AUTO_INCREMENT";
//        }
//        return script;
//    }

    @Override
    public  List<DesignColumn> initColumns(){
        List<DesignColumn> list = new ArrayList<>();
        DesignColumn column1 = new DesignColumn();
        column1.setColumnName("id");
        column1.setColumnComment("编号");
        column1.setColumnType("varchar(64)");
        column1.setJavaType("String");
        column1.setJavaField("id");
        column1.setIsPk("1");
        column1.setIsIncrement("1");
//        column1.setIsList("1");
        column1.setQueryType("eq");
        column1.setHtmlType("input");
        column1.setId("1");
        column1.setDisabled(CommonConstant.TRUE);
        column1.setShowMove(CommonConstant.FALSE);
        list.add(column1);
        DesignColumn column2 = new DesignColumn();
        column2.setColumnName("create_by");
        column2.setColumnComment("創建人");
        column2.setColumnType("varchar(60)");
        column2.setJavaType("String");
        column2.setJavaField("createBy");
        column2.setQueryType("eq");
        column2.setHtmlType("input");
        column2.setId("2");
        column2.setDisabled(CommonConstant.TRUE);
        column2.setShowMove(CommonConstant.FALSE);
        list.add(column2);
        DesignColumn column3 = new DesignColumn();
        column3.setColumnName("create_time");
        column3.setColumnComment("創建時間");
        column3.setColumnType("timestamp(6)");
        column3.setJavaType("Date");
        column3.setJavaField("createTime");
        column3.setIsQuery("1");
        column3.setIsList("1");
        column3.setQueryType("between");
        column3.setHtmlType("datetime");
        column3.setId("3");
        column3.setDisabled(CommonConstant.TRUE);
        column3.setShowMove(CommonConstant.FALSE);
        list.add(column3);
        DesignColumn column4 = new DesignColumn();
        column4.setColumnName("update_by");
        column4.setColumnComment("更新人");
        column4.setColumnType("varchar(60)");
        column4.setJavaType("String");
        column4.setJavaField("updateBy");
        column4.setQueryType("eq");
        column4.setHtmlType("input");
        column4.setId("4");
        column4.setDisabled(CommonConstant.TRUE);
        column4.setShowMove(CommonConstant.FALSE);
        list.add(column4);
        DesignColumn column5 = new DesignColumn();
        column5.setColumnName("update_time");
        column5.setColumnComment("更新時間");
        column5.setColumnType("timestamp(6)");
        column5.setJavaType("Date");
        column5.setJavaField("updateTime");
        column5.setQueryType("between");
        column5.setHtmlType("datetime");
        column5.setId("5");
        column5.setDisabled(CommonConstant.TRUE);
        column5.setShowMove(CommonConstant.FALSE);
        list.add(column5);
        DesignColumn column6 = new DesignColumn();
        column6.setColumnName("remarks");
        column6.setColumnComment("備註");
        column6.setColumnType("varchar(255)");
        column6.setJavaType("String");
        column6.setJavaField("remarks");
        column6.setIsAdd("1");
        column6.setIsEdit("1");
        column6.setQueryType("eq");
        column6.setHtmlType("textarea");
        column6.setId("6");
        column6.setDisabled(CommonConstant.TRUE);
        column6.setShowMove(CommonConstant.FALSE);
        list.add(column6);
        DesignColumn column7 = new DesignColumn();
        column7.setColumnName("del_flag");
        column7.setColumnComment("刪除標識（0-正常，1-删除）");
        column7.setColumnType("char(1)");
        column7.setJavaType("String");
        column7.setJavaField("delFlag");
        column7.setDefValue("0");
        column7.setQueryType("eq");
        column7.setHtmlType("input");
        column7.setId("7");
        column7.setDisabled(CommonConstant.TRUE);
        column7.setShowMove(CommonConstant.FALSE);
        list.add(column7);

        DesignColumn column8 = new DesignColumn();
        column8.setColumnName("dept_id");
        column8.setColumnComment("機構ID");
        column8.setColumnType("varchar(64)");
        column8.setJavaType("String");
        column8.setJavaField("deptId");
        column8.setQueryType("eq");
        column8.setHtmlType("input");
        column8.setId("8");
        column8.setDisabled(CommonConstant.TRUE);
        column8.setShowMove(CommonConstant.FALSE);
        list.add(column8);

        DesignColumn column9 = new DesignColumn();
        column9.setColumnName("process_id");
        column9.setColumnComment("流程定義標示");
        column9.setColumnType("varchar(50)");
        column9.setJavaType("String");
        column9.setJavaField("processId");
        column9.setQueryType("eq");
        column9.setHtmlType("input");
        column9.setId("9");
        column9.setDisabled(CommonConstant.TRUE);
        column9.setShowMove(CommonConstant.FALSE);
        list.add(column9);

        DesignColumn column10 = new DesignColumn();
        column10.setColumnName("work_status");
        column10.setColumnComment("審核狀態");
        column10.setIsList("1");
        column10.setIsQuery("1");
        column10.setColumnType("char(1)");
        column10.setJavaType("String");
        column10.setJavaField("workStatus");
        column10.setQueryType("eq");
        column10.setHtmlType("select");
        column10.setDictType("work_status");
        column10.setId("10");
        column10.setIsExcel("1");
        column10.setDisabled(CommonConstant.TRUE);
        column10.setShowMove(CommonConstant.FALSE);
        list.add(column10);

        DesignColumn column11 = new DesignColumn();
        column11.setColumnName("serialno");
        column11.setColumnComment("表單編號");
        column11.setIsList("1");
        column11.setIsQuery("1");
        column11.setColumnType("varchar(100)");
        column11.setJavaType("String");
        column11.setJavaField("serialno");
        column11.setQueryType("eq");
        column11.setHtmlType("input");
        column11.setId("11");
        column11.setIsExcel("1");
        column11.setDisabled(CommonConstant.TRUE);
        column11.setShowMove(CommonConstant.FALSE);
        list.add(column11);

        DesignColumn column12 = new DesignColumn();
        column12.setColumnName("maker_no");
        column12.setColumnComment("填單人工號");
        column12.setIsList("1");
        column12.setIsQuery("1");
        column12.setColumnType("varchar(20)");
        column12.setJavaType("String");
        column12.setJavaField("makerNo");
        column12.setQueryType("eq");
        column12.setHtmlType("input");
        column12.setId("12");
        column12.setDisabled(CommonConstant.TRUE);
        column12.setShowMove(CommonConstant.FALSE);
        list.add(column12);

        DesignColumn column13 = new DesignColumn();
        column13.setColumnName("maker_name");
        column13.setColumnComment("填單人姓名");
        column13.setIsList("1");
        column13.setColumnType("varchar(30)");
        column13.setJavaType("String");
        column13.setJavaField("makerName");
        column13.setQueryType("eq");
        column13.setHtmlType("input");
        column13.setId("13");
        column13.setDisabled(CommonConstant.TRUE);
        column13.setShowMove(CommonConstant.FALSE);
        list.add(column13);

        DesignColumn column14 = new DesignColumn();
        column14.setColumnName("complet_time");
        column14.setColumnComment("完成時間");
        column14.setIsList("1");
        column14.setColumnType("timestamp(6)");
        column14.setJavaType("String");
        column14.setJavaField("completTime");
        column14.setQueryType("between");
        column14.setHtmlType("datetime");
        column14.setId("14");
        column14.setIsExcel("1");
        column14.setDisabled(CommonConstant.TRUE);
        column14.setShowMove(CommonConstant.FALSE);
        list.add(column14);

        DesignColumn column15 = new DesignColumn();
        column15.setColumnName("attachids");
        column15.setColumnComment("附件ids");
        column15.setColumnType("varchar(300)");
        column15.setJavaType("String");
        column15.setJavaField("attachids");
        column15.setQueryType("like");
        column15.setHtmlType("input");
        column15.setId("15");
        column15.setDisabled(CommonConstant.TRUE);
        column15.setShowMove(CommonConstant.FALSE);
        list.add(column15);

        DesignColumn column16 = new DesignColumn();
        column16.setColumnName("sign_person");
        column16.setColumnComment("當前簽核人");
        column16.setIsList("1");
        column16.setColumnType("varchar(500)");
        column16.setJavaType("String");
        column16.setJavaField("signPerson");
        column16.setQueryType("like");
        column16.setHtmlType("input");
        column16.setId("16");
        column16.setIsExcel("1");
        column16.setDisabled(CommonConstant.TRUE);
        column16.setShowMove(CommonConstant.FALSE);
        list.add(column16);

        DesignColumn column17 = new DesignColumn();
        column17.setColumnName("sign_node");
        column17.setColumnComment("當前簽核節點");
        column17.setIsList("1");
        column17.setColumnType("varchar(500)");
        column17.setJavaType("String");
        column17.setJavaField("signNode");
        column17.setQueryType("like");
        column17.setHtmlType("input");
        column17.setId("17");
        column17.setIsExcel("1");
        column17.setDisabled(CommonConstant.TRUE);
        column17.setShowMove(CommonConstant.FALSE);
        list.add(column17);


        DesignColumn column18 = new DesignColumn();
        column18.setColumnName("makerdeptno");
        column18.setColumnComment("填單人所在部門");
        column18.setIsList("1");
        column18.setIsAdd("1");
        column18.setIsEdit("1");
        column18.setColumnType("varchar(50)");
        column18.setJavaType("String");
        column18.setJavaField("makerdeptno");
        column18.setQueryType("=");
        column18.setHtmlType("input");
        column18.setId("18");
        column18.setDisabled(CommonConstant.TRUE);
        column18.setShowMove(CommonConstant.FALSE);
        column18.setSort(8);
        list.add(column18);


        DesignColumn column19 = new DesignColumn();
        column19.setColumnName("makerfactoryid");
        column19.setColumnComment("所在廠區");
        column19.setIsList("1");
        column19.setIsAdd("1");
        column19.setIsEdit("1");
        column19.setColumnType("varchar(50)");
        column19.setJavaType("String");
        column19.setJavaField("makerfactoryid");
        column19.setDictType("caaesign_factory");
        column19.setQueryType("=");
        column19.setHtmlType("select");
        column19.setId("19");
        column19.setDisabled(CommonConstant.TRUE);
        column19.setShowMove(CommonConstant.FALSE);
        column19.setSort(9);
        list.add(column19);

        DesignColumn column20 = new DesignColumn();
        column20.setColumnName("data_source");
        column20.setColumnComment("數據來源(pc、app)");
        column20.setIsList("0");
        column20.setIsAdd("0");
        column20.setIsEdit("0");
        column20.setColumnType("varchar(6)");
        column20.setJavaType("String");
        column20.setJavaField("dataSource");
        column20.setQueryType("=");
        column20.setHtmlType("input");
        column20.setId("20");
        column20.setDisabled(CommonConstant.TRUE);
        column20.setShowMove(CommonConstant.FALSE);
        list.add(column20);
//        DesignColumn column18 = new DesignColumn();
//        column18.setColumnName("factory_code");
//        column18.setColumnComment("廠區");
//        column18.setColumnType("varchar(30)");
//        column18.setJavaType("String");
//        column18.setJavaField("factoryCode");
//        column18.setQueryType("like");
//        column18.setHtmlType("input");
//        column18.setId("18");
//        column18.setDisabled(CommonConstant.TRUE);
//        column18.setShowMove(CommonConstant.FALSE);
//        column18.setSort(5);
//        list.add(column18);
        return list;
    }
}
