package ${packageName}.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
    #if($table.tplCategory == 'tree')
    import java.util.ArrayList;
    import java.util.Iterator;
    import java.util.List;
    #end
import com.baomidou.mybatisplus.annotation.TableName;
import com.entfrm.biz.activiti.dto.WfConfigDto;
import com.entfrm.biz.activiti.entity.TQhAllRelation;
import com.entfrm.biz.activiti.service.TQhAllRelationService;
import com.entfrm.biz.activiti.service.WfConfigService;
import com.entfrm.biz.activiti.service.TaskService;
import com.entfrm.biz.activiti.dto.LeaveDto;
import com.entfrm.core.base.annotation.SignNode;
import com.entfrm.core.security.util.SecurityUtil;
import com.google.common.collect.Maps;
import org.activiti.engine.task.Task;
import org.springframework.stereotype.Service;
import ${packageName}.mapper.${ClassName}Mapper;
import ${packageName}.entity.${ClassName};
import ${packageName}.service.${ClassName}Service;
import com.entfrm.core.base.util.PinyinUtil;
import com.entfrm.core.data.util.SerialNumberTool;
import cn.hutool.core.date.DateUtil;
import lombok.AllArgsConstructor;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.runtime.ProcessInstance;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.transaction.annotation.Transactional;
import com.entfrm.core.base.enums.TaskStatusEnum;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Map;
import com.entfrm.core.data.constant.CommonConstant;

/**
 * <AUTHOR>
 * @date ${datetime}
 *
 * @description ${functionName}Service业务层
 */
@Service
@AllArgsConstructor
public class ${ClassName}ServiceImpl extends ServiceImpl<${ClassName}Mapper, ${ClassName}> implements ${ClassName}Service {
	private final RuntimeService runtimeService;
	private final WfConfigService wfConfigService;
	private final TQhAllRelationService allRelationService;
	private final RabbitTemplate rabbitTemplate;
	private final TaskService taskService;
    private final org.activiti.engine.TaskService actTaskService;
    #if($table.tplCategory == 'tree')
        /**
      * 构建树
      *
      * @param list     分类表
      * @param parentId 传入的父节点ID
      * @return String
      */
        public List<${ClassName}> buildTree(List<${ClassName}> list, String parentId) {
            List<${ClassName}> ${className}List = new ArrayList<>();
            for (Iterator<${ClassName}> iterator = list.iterator(); iterator.hasNext(); ) {
                ${ClassName} t = (${ClassName}) iterator.next();
                if (t.getParentId().equls(parentId)) {
                    recursion(list, t);
                        ${className}List.add(t);
                }
            }
            return ${className}List;
        }

        /**
       * 递归列表
       *
       * @param list
       * @param t
       */
        private void recursion(List<${ClassName}> list, ${ClassName} t) {
            // 得到子节点列表
            List<${ClassName}> childList = getChildList(list, t);
            t.setChildren(childList);
            for (${ClassName} tChild : childList) {
                if (hasChild(list, tChild)) {
                    // 判断是否有子节点
                    Iterator<${ClassName}> it = childList.iterator();
                    while (it.hasNext()) {
                        ${ClassName} n = (${ClassName}) it.next();
                        recursion(list, n);
                    }
                }
            }
        }

        /**
         * 得到子节点列表
         */
        private List<${ClassName}> getChildList(List<${ClassName}> list, ${ClassName} t) {
            List<${ClassName}> tlist = new ArrayList<${ClassName}>();
            Iterator<${ClassName}> it = list.iterator();
            while (it.hasNext()) {
                ${ClassName} n = (${ClassName}) it.next();
                if (n.getParentId().intValue() == t.getId().intValue()) {
                    tlist.add(n);
                }
            }
            return tlist;
        }

        /**
         * 判断是否有子节点
         */
        private boolean hasChild(List<${ClassName}> list, ${ClassName} t) {
            return getChildList(list, t).size() > 0 ? true : false;
        }

    #end
#if(!$isChildTable)
	/**
	 * 启动流程
	 *
	 * @param ${className}
	 * @return
	 */
	@Override
##	@Transactional(rollbackFor = Exception.class)
	public Boolean startProcess(${ClassName} ${className}) {
        ${className}.setWorkStatus(Integer.parseInt(TaskStatusEnum.CHECKING.getStatus()));
		String tableName = ${ClassName}.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
		String procDefKey = wfConfigDto.getProcDefKey();
		String businessKey = procDefKey + CommonConstant.BREAK_BUSINESS_KEY + ${className}.getId();
		Field fields[] = ${className}.getClass().getDeclaredFields();
        Map variables = Maps.newHashMap();
        //設置填單人
        variables.put("maker", SecurityUtil.getUser().getUsername());
        variables.put("serialNo", ${className}.getSerialno());
        variables.put("workflowName", wfConfigDto.getFormName());
        Arrays.stream(fields).forEach(field -> {
                    SignNode signNode = field.getAnnotation(SignNode.class);
                    if (ObjectUtil.isNotEmpty(signNode)) {
                        if (signNode.type().equals(SignNode.Type.COMMON)) {
                            variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(${className}, field)) ? "AUTO_SIGN" : ReflectUtil.getFieldValue(${className}, field));
                        } else if (signNode.type().equals(SignNode.Type.HUIQIAN)) {
                            variables.put(field.getName(), ObjectUtil.isEmpty(ReflectUtil.getFieldValue(${className}, field)) ? Arrays.asList("AUTO_SIGN") : Arrays.asList(ReflectUtil.getFieldValue(${className}, field).toString().split(",")));
                        }
                    }
                }
        );
		ProcessInstance pi = runtimeService.startProcessInstanceByKey(procDefKey, businessKey,variables);
        ${className}.setProcessId(pi.getProcessInstanceId());
		super.updateById(${className});

		TQhAllRelation relation = allRelationService.getOne(new QueryWrapper<TQhAllRelation>().eq("serialno",${className}.getSerialno()));
		relation.setWorkStatus(${className}.getWorkStatus());
		relation.setProcessId(pi.getProcessInstanceId());
        Task task = actTaskService.createTaskQuery().processInstanceId(pi.getProcessInstanceId()).singleResult();
        relation.setNodename(task.getName());
        relation.setDeptNo(${className}.getMakerdeptno());
		allRelationService.updateById(relation);
		//添加mq
		rabbitTemplate.convertAndSend("SignDirectExchange", "Sign.Direct.Routing", BeanUtil.beanToMap(relation));
		return Boolean.TRUE;
	}

	/**
     * 插入一条记录（选择字段，策略插入）
     *
     */
	@Override
	public boolean save(${ClassName} ${className}) {
		boolean rs = super.save(${className});
		String tableName = ${ClassName}.class.getAnnotation(TableName.class).value();
        WfConfigDto wfConfigDto = wfConfigService.getTableKeyToWfConfigDto(tableName);
		TQhAllRelation relation = new TQhAllRelation();
		relation.setDtoName(tableName);
		relation.setMakerNo(${className}.getMakerNo());
		relation.setMakerName(${className}.getMakerName());
		relation.setSerialno(${className}.getSerialno());
		relation.setWorkflowid(wfConfigDto.getProcDefKey());
		relation.setWfName(wfConfigDto.getFormName());
		relation.setClassPackage(${className}.getClass().getName());
		relation.setWorkStatus(0);
		allRelationService.save(relation);
		return rs;
	}
	@Override
	public Boolean updateAndCheck(${ClassName} ${className}, LeaveDto leaveDto) {
		try {
			this.updateById(${className});
			taskService.checkTask(leaveDto);
		} catch (Exception e) {
			return false;
		}
		return true;
	}
#end
}
