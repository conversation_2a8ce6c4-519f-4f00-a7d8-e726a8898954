export default [
    {
        path: '/${table.moduleName}/${table.businessName}',
        component: () => import('@/layout'),
        hidden: true,
        meta: {title: '${table.functionName}'},
        children: [
            {
                path: '${table.ClassName}Add',
                component: () => import('@/views/${table.moduleName}/${table.businessName}/pc/${table.ClassName}Add'),
                name: '${table.moduleName}_${table.businessName}_${table.ClassName}Add',
                meta: {title: '${table.functionName}新增'}
            },
            {
                path: '${table.ClassName}Edit',
                    component: () => import('@/views/${table.moduleName}/${table.businessName}/pc/${table.ClassName}Add'),
                name: '${table.moduleName}_${table.businessName}_${table.ClassName}Edit',
                meta: {title: '${table.functionName}修改'}
            },
            {
                path: '${table.ClassName}Audit',
                component: () => import('@/views/${table.moduleName}/${table.businessName}/pc/${table.ClassName}Audit'),
                name: '${table.moduleName}_${table.businessName}_${table.ClassName}Audit',
                meta: { title: '${table.functionName}审核' }
            },
            {
                path: '${table.ClassName}Detail',
                component:() => import('@/views/${table.moduleName}/${table.businessName}/pc/${table.ClassName}Detail'),
                name:'${table.moduleName}_${table.businessName}_${table.ClassName}Detail',
                meta:{title: '${table.functionName}详情'}
            },
            {
                path: '${table.ClassName}Reject',
                    component:() => import('@/views/${table.moduleName}/${table.businessName}/pc/${table.ClassName}Reject'),
                name:'${table.moduleName}_${table.businessName}_${table.ClassName}Reject',
                meta:{title: '${table.functionName}修改'}
            }
        ]
    }
]
  