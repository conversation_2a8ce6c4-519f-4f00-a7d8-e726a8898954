export default [
    {
        path: '/${table.moduleName}/${table.businessName}App',
        component: () => import('@/mobileLayout'),
        hidden: true,
        meta: {title: '${table.functionName}'},
        children: [
            {
                path: '${table.ClassName}AddApp',
                component: () => import('@/views/${table.moduleName}/${table.businessName}/app/${table.ClassName}AddApp'),
                name: '${table.moduleName}_${table.businessName}_${table.ClassName}AddApp',
                meta: {title: '${table.functionName}新增'}
            },
            {
                path: '${table.ClassName}EditApp',
                    component: () => import('@/views/${table.moduleName}/${table.businessName}/app/${table.ClassName}AddApp'),
                name: '${table.moduleName}_${table.businessName}_${table.ClassName}EditApp',
                meta: {title: '${table.functionName}修改'}
            },
            {
                path: '${table.ClassName}AuditApp',
                component: () => import('@/views/${table.moduleName}/${table.businessName}/app/${table.ClassName}AuditApp'),
                name: '${table.moduleName}_${table.businessName}_${table.ClassName}AuditApp',
                meta: { title: '${table.functionName}审核' }
            },
            {
                path: '${table.ClassName}DetailApp',
                component:() => import('@/views/${table.moduleName}/${table.businessName}/app/${table.ClassName}DetailApp'),
                name:'${table.moduleName}_${table.businessName}_${table.ClassName}DetailApp',
                meta:{title: '${table.functionName}详情'}
            },
            {
                path: '${table.ClassName}RejectApp',
                    component:() => import('@/views/${table.moduleName}/${table.businessName}/app/${table.ClassName}RejectApp'),
                name:'${table.moduleName}_${table.businessName}_${table.ClassName}RejectApp',
                meta:{title: '${table.functionName}修改'}
            }
        ]
    }
]
  