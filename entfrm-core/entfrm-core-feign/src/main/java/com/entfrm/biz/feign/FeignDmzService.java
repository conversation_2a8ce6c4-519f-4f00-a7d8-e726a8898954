package com.entfrm.biz.feign;

import com.entfrm.biz.feign.config.MultipartSupportConfig;
import com.entfrm.biz.feign.dto.SsoAccountInfo;
import com.entfrm.biz.feign.fallback.FeignDmzServiceFallbackFactory;
import com.entfrm.core.base.api.R;
import com.entfrm.core.data.constant.ServiceNameConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @date 2020-08-10 09:08:13
 * @description 流程信息配置Service接口
 */

@FeignClient(
        value = ServiceNameConstants.DMZ_SERVICE,
        url = "${entfrm.feignDmzUrl}",
        fallbackFactory = FeignDmzServiceFallbackFactory.class,
        decode404 = true,
        configuration =  MultipartSupportConfig.class)
public interface FeignDmzService {

    @GetMapping("/apiForDmz/ssoLogin")
    public R<Boolean> ssoLogin(@RequestParam("username") String username,
                               @RequestParam("password") String password,
                               @RequestParam("clientIp") String clientIp);
    @GetMapping("/apiForDmz/getAccount")
    public R<SsoAccountInfo> getAccount(@RequestParam("username") String username);

    @PostMapping(value = "/apiForDmz/upload",consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<String> upload(@RequestPart("file") MultipartFile file,@RequestParam("fileName") String fileName);

    @GetMapping("/apiForDmz/download")
    public R<byte[]> download(@RequestParam("filePath") String filePath,@RequestParam("fileName") String fileName);


    @PostMapping(value = "/apiForDmz/newEsign/uploadSignature",consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<String> newEsignUploadSignature(@RequestParam("ftpIp") String ftpIp,
                                    @RequestParam("ftpPort") Integer ftpPort,
                                    @RequestParam("ftpUser") String ftpUser,
                                    @RequestParam("ftpPass") String ftpPass,
                                    @RequestParam("rootPath") String rootPath,
                                    @RequestParam("filePath") String filePath,
                                    @RequestParam("uploadFileName") String uploadFileName,
                                    @RequestPart("file") MultipartFile file);

    @PostMapping(value = "/apiForDmz/newEsign/upload",consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<String> newEsignUpload(@RequestParam("ftpIp") String ftpIp,
                                             @RequestParam("ftpPort") Integer ftpPort,
                                             @RequestParam("ftpUser") String ftpUser,
                                             @RequestParam("ftpPass") String ftpPass,
                                             @RequestParam("rootPath") String rootPath,
                                             @RequestParam("filePath") String filePath,
                                             @RequestParam("uploadFileName") String uploadFileName,
                                             @RequestPart("file") MultipartFile file);


    @PostMapping(value = "/apiForDmz/uploadWithOos",consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<String> uploadWithOos(@RequestPart("file") MultipartFile file,
                                   @RequestParam("fileName") String fileName,
                                   @RequestParam("directory") String directory);



    @PostMapping(value = "/apiForDmz/newEsign/uploadSignatureWithOos",consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<String> newEsignUploadSignatureWithOos(@RequestPart("file") MultipartFile file,
                                             @RequestParam("fileName") String fileName,
                                             @RequestParam("directory") String directory);

    @PostMapping(value = "/apiForDmz/newEsign/uploadWithOos",consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<String> newEsignUploadWithOos(@RequestPart("file") MultipartFile file,
                                           @RequestParam("fileName") String fileName,
                                           @RequestParam("directory") String directory);
}
