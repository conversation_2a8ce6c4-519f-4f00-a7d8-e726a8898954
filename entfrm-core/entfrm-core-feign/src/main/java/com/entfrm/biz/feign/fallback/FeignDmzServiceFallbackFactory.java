package com.entfrm.biz.feign.fallback;

import com.entfrm.biz.feign.FeignDmzService;
import com.entfrm.core.base.api.R;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

/**
 * menuService降级工场
 *
 * <AUTHOR>
 * @date 2019/1/18
 */
@Slf4j
@Component
public class FeignDmzServiceFallbackFactory implements FallbackFactory<FeignDmzService> {
    @Override
    public FeignDmzService create(Throwable throwable) {
        return new FeignDmzService() {

            @Override
            public R<Boolean> ssoLogin(@RequestParam("username") String username,
                                       @RequestParam("password") String password,
                                       @RequestParam("clientIp") String clientIp) {
                return R.error("登錄失敗");
            }

            @Override
            public R getAccount(String username) {
                return R.error("調用失敗");
            }

            @Override
            public R<String> upload(MultipartFile file, String fileName) {
                return R.error("調用失敗");
            }

            @Override
            public R<byte[]> download(String filePath, String fileName) {
                return R.error(new byte[0]);
            }

            @Override
            public R<String> newEsignUploadSignature(String ftpIp, Integer ftpPort, String ftpUser, String ftpPass, String rootPath, String filePath, String uploadFileName, MultipartFile file) {
                return R.error("調用上傳附件失敗");
            }

            @Override
            public R<String> newEsignUpload(String ftpIp, Integer ftpPort, String ftpUser, String ftpPass, String rootPath, String filePath, String uploadFileName, MultipartFile file) {
                return R.error("調用上傳附件失敗");
            }

            @Override
            public R<String> uploadWithOos(MultipartFile file, String fileName, String directory) {
                return R.error("調用uploadWithOos失敗");
            }

            @Override
            public R<String> newEsignUploadSignatureWithOos(MultipartFile file, String fileName, String directory) {
                return R.error("調用newEsignUploadSignatureWithOos失敗");
            }

            @Override
            public R<String> newEsignUploadWithOos(MultipartFile file, String fileName, String directory) {
                return R.error("調用newEsignUploadWithOos失敗");
            }


        };
    }
}
