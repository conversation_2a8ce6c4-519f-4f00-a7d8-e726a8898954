package com.entfrm.auth.config;

import com.entfrm.core.base.config.GlobalConfig;
import com.entfrm.auth.filter.CaptchaFilter;
import com.entfrm.core.base.intercept.RepeatSubmitInterceptor;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableResourceServer;
import org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfigurerAdapter;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.servlet.config.annotation.*;

/**
 * <AUTHOR>
 * @date 2020/3/10
 * @description 资源服务器配置
 */
@Configuration
@EnableResourceServer
@AllArgsConstructor
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class ResourceServerConfig extends ResourceServerConfigurerAdapter implements WebMvcConfigurer {
    private final RedisTemplate redisTemplate;
    private final RepeatSubmitInterceptor repeatSubmitInterceptor;

    @Override
    @SneakyThrows
    public void configure(HttpSecurity http) {
        http
                // CRSF禁用，因为不使用session
                .csrf().disable()
                .authorizeRequests()
                .antMatchers(
                        "/oauth/**","/system/fileInfo/download", "/common/**", "/cms/article/doc/**", "/activiti/service/**", "/activiti/task/track/**",
                        "/devtool/dataset/api/**", "/activiti/process/resource","/token/thirdApplicationMobileLogin/**", "/actuator/**",
                        "/token/thirdApplicationLogin/**",
                        "/apiForDmz/**"
                        ,"/system/application/**","/token/emailLogin/**","/system/config/getByKey/**","/task/**"
                        ,"/login/**","/webApp/**","/token/ssoLogin/**","/sysinfo/versionUpdate").permitAll()
                .anyRequest().authenticated()
                .and().addFilterBefore(new CaptchaFilter(redisTemplate), UsernamePasswordAuthenticationFilter.class);
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        /** 本地文件上传路径 */
        registry.addResourceHandler("/profile/**").addResourceLocations("file:" + GlobalConfig.getProfile() + "/");

        //配置静态资源映射，将css请求映射到/dist/css/目录下
//        registry.addResourceHandler("/static/css/**").addResourceLocations("classpath:/dist/static/css/");
//        //配置静态资源映射，将js请求映射到/dist/js/目录下
//        registry.addResourceHandler("/static/js/**").addResourceLocations("classpath:/dist/static/js/");
//        registry.addResourceHandler("/static/fonts/**").addResourceLocations("classpath:/dist/static/fonts/");
//        registry.addResourceHandler("/static/img/**").addResourceLocations("classpath:/dist/static/img/");
    }
    @Override
    public void addViewControllers(ViewControllerRegistry registry) {
        registry.addViewController("/login").setViewName("/dist/index.html");//默认视图跳转,打开dist/index.html
        registry.addViewController("/index").setViewName("/dist/index.html");//默认视图跳转,打开dist/index.html
//        registry.addViewController("/webApp/login").setViewName("/dist/index.html");//默认视图跳转,打开dist/index.html
//        registry.addViewController("/webApp/index").setViewName("/dist/index.html");//默认视图跳转,打开dist/index.html
//        registry.addViewController("/webApp/webLogin").setViewName("/dist/index.html");//默认视图跳转,打开dist/index.html
//        registry.addViewController("/webApp/mobileIndex").setViewName("/dist/index.html");//默认视图跳转,打开dist/index.html
//        registry.addViewController("/webApp/mobileLogin").setViewName("/dist/index.html");//默认视图跳转,打开dist/index.html
        registry.addViewController("/webApp/**").setViewName("/dist/index.html");//默认视图跳转,打开dist/index.html
    }
    /**
     * 自定义拦截规则
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry)
    {
        registry.addInterceptor(repeatSubmitInterceptor).addPathPatterns("/**");
    }


    @Override
    public void configurePathMatch(PathMatchConfigurer configurer) {
        //setUseSuffixPatternMatch:设置是否遵循后缀匹配模式，如“/user”是否匹配/user.*，为true时就匹配;
        configurer.setUseSuffixPatternMatch(true)
                //setUseTrailingSlashMatch,设置是否自动后缀留级匹配模式，如“/user”是否匹配“/user/”，为true是即匹配
                .setUseTrailingSlashMatch(true);
    }

}

