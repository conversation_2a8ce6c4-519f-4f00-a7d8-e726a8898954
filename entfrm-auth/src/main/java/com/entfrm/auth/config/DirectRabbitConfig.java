package com.entfrm.auth.config;

import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 类的描述：
 *
 * @Author: S6114648
 * @Email: <EMAIL>
 * @CreateDate: 2021年06月07日  15:55
 * @version: v1.0
 **/

@Configuration
public class DirectRabbitConfig {
	@Bean
	public Queue signDirectQueue() {
		// durable:是否持久化,默认是false,持久化队列：会被存储在磁盘上，当消息代理重启时仍然存在，暂存队列：当前连接有效
		// exclusive:默认也是false，只能被当前创建的连接使用，而且当连接关闭后队列即被删除。此参考优先级高于durable
		// autoDelete:是否自动删除，当没有生产者或者消费者使用此队列，该队列会自动删除。
		//   return new Queue("TestDirectQueue",true,true,false);

		//一般设置一下队列的持久化就好,其余两个就是默认false
		return new Queue("SignDirectQueue11",true);
	}
	@Bean
	public Queue signDirectForODSMSQueue() {
		// durable:是否持久化,默认是false,持久化队列：会被存储在磁盘上，当消息代理重启时仍然存在，暂存队列：当前连接有效
		// exclusive:默认也是false，只能被当前创建的连接使用，而且当连接关闭后队列即被删除。此参考优先级高于durable
		// autoDelete:是否自动删除，当没有生产者或者消费者使用此队列，该队列会自动删除。
		//   return new Queue("TestDirectQueue",true,true,false);

		//一般设置一下队列的持久化就好,其余两个就是默认false
		return new Queue("SignDirectForODSMSQueue11",true);
	}
    @Bean
	public Queue signChargeLogQueue() {
	    return new Queue("SignChargeLogQueue", true);
	}
	@Bean
	public Queue signFinishedQueue() {
		return new Queue("SignFinishedQueue11", true);
	}
	//Direct交换机 起名：TestDirectExchange
	@Bean
	DirectExchange signDirectExchange() {
		//  return new DirectExchange("TestDirectExchange",true,true);
		return new DirectExchange("SignDirectExchange",true,false);
	}

//	@Bean
//	DirectExchange signChargeLogExchange() {
//		//  return new DirectExchange("TestDirectExchange",true,true);
//		return new DirectExchange("SignChargeLogExchange",true,false);
//	}
	//绑定  将队列和交换机绑定, 并设置用于匹配键：TestDirectRouting
	@Bean
	Binding bindingDirect() {
		return BindingBuilder.bind(signDirectQueue()).to(signDirectExchange()).with("Sign.Direct.Routing");
	}
	@Bean
	Binding bindingDirectForODSMS() {
		return BindingBuilder.bind(signDirectForODSMSQueue()).to(signDirectExchange()).with("Sign.Direct.odsms.Routing");
	}
    @Bean
	Binding signChargeLog() {
		return BindingBuilder.bind(signChargeLogQueue()).to(signDirectExchange()).with("Sign.ChargeLog.Routing");
	}
	@Bean
	Binding signFinished() {
		return BindingBuilder.bind(signFinishedQueue()).to(signDirectExchange()).with("Sign.Finished.Routing");
	}



//	@Bean
//	DirectExchange lonelyDirectExchange() {
//		return new DirectExchange("lonelyDirectExchange");
//	}
}
