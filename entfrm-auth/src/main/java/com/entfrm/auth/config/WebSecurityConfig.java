package com.entfrm.auth.config;

import com.entfrm.auth.openid.OpenIdAuthenticationSecurityConfig;
import com.entfrm.auth.service.EntfrmUserDetailService;
import com.entfrm.auth.sso.SSOAuthenticationSecurityConfig;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;

/**
 * <AUTHOR>
 * @date 2020/3/10
 * @description 认证相关配置
 */
@Configuration
@EnableWebSecurity
@AllArgsConstructor
public class WebSecurityConfig extends WebSecurityConfigurerAdapter {

    private final EntfrmUserDetailService userDetailService;
    private final OpenIdAuthenticationSecurityConfig openIdAuthenticationSecurityConfig;
    private final SSOAuthenticationSecurityConfig ssoAuthenticationSecurityConfig;
    private final PasswordEncoder passwordEncoder;


    @Bean
    @Override
    public AuthenticationManager authenticationManagerBean() throws Exception {
        return super.authenticationManagerBean();
    }

    @Override
    protected void configure(AuthenticationManagerBuilder auth) throws Exception {
        auth.userDetailsService(userDetailService).passwordEncoder(passwordEncoder);
    }


    /***设置不拦截规则*/
    @Override
    public void configure(WebSecurity web) {
        web.ignoring().antMatchers("/static/**", "/druid/**", "/ip2region/**", "/processes", "/profile/**", "/stencilset.json"
                        ,"/editor-app/**","/lib/js/**","/js/**", "/modeler.html", "/favicon.ico","/captcha/**")
                .antMatchers("/doc.html","/swagger-ui.html", "/webjars/**", "/v2/**", "/swagger-resources/**"
                        ,"/dist/**"
                        ,"/webApp/**","/index");
    }
    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http.authorizeRequests()
                .anyRequest()
                //授权服务器关闭basic认证
                .permitAll()
                .and().apply(openIdAuthenticationSecurityConfig)
                .and().apply(ssoAuthenticationSecurityConfig);
    }
}
