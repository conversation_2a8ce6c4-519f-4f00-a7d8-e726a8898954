package com.entfrm.auth.controller;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.entfrm.auth.dto.SsoInfoDto;
import com.entfrm.biz.system.service.ConfigService;
import com.entfrm.core.base.api.R;
import com.foxconn.entity.AccountInfo;
import com.foxconn.so.LibSsoUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 一賬通登陸功能
 * <AUTHOR>
 * @date 2020/3/14
 * @description token 管理
 */
@RestController
@AllArgsConstructor
@RequestMapping("/apiForDmz")
@Slf4j
public class DmzLoginController {
    private final ConfigService configService;

    public static final String AUTHENTICATE = "/authenticate";
    public static final String GET_ACCOUNT = "/getAccount";
    public static final String SSO_DOMAIN_URL = "https://sso-g-zzk.ipebg.efoxconn.com";
    /**
     * 方法描述: 通過郵箱登錄
     * @Author: S6114648
     * @CreateDate:   2020/9/2  8:35
     * @Return
     **/
    @GetMapping("/ssoLogin")
    @ResponseBody
    public R ssoLogin(String username, String password,String clientIp) {
        try {
//          使用一賬通登錄
            boolean isAuth = authenticateHttps(username,password,clientIp);
            if (isAuth) {
                return R.ok(isAuth);
            } else {
                return R.error(isAuth);
            }
        } catch (Exception e) {
            return R.error(false);
        }
    }

    /**
     * 方法描述: 通過一賬通獲取用戶信息
     * @Author: S6114648
     * @CreateDate:   2020/9/2  8:35
     * @Return
     **/
    @GetMapping("/getAccount")
    @ResponseBody
    public R getAccount(String username) {
        try {
//          使用一賬通登錄
            String ssoKey = configService.getValueByKey("CAA_SSO_KEY");
            String tenantCode = configService.getValueByKey("CAA_SSO_TENANT_CODE", "iPEBG");
            String ssoDomainUrl = configService.getValueByKey("SSO_DOMAIN_URL", SSO_DOMAIN_URL);

            Map<String, Object> param = new HashMap<>();
            param.put("empNo", username);
            param.put("authNo", ssoKey);
            param.put("tenantCode", tenantCode);
            // 發送請求，并獲取結果
            String resultStr = HttpUtil.post(ssoDomainUrl + GET_ACCOUNT, param);
            SsoInfoDto ssoInfoDto = JSONUtil.toBean(resultStr, SsoInfoDto.class);

            if("0000".equals(ssoInfoDto.getCode())){
                AccountInfo accountInfo = JSONUtil.toBean(ssoInfoDto.getData(), AccountInfo.class);
                return R.ok(accountInfo);
            }else{
                return R.error();
            }
        } catch (Exception e) {
            return R.error(null);
        }
    }


    /**
     * 使用一帳通Http登錄
     * @param empNo 賬號
     * @param passWord 密碼
     * @return
     */
    public boolean authenticateHttps(String empNo, String passWord,String clientIp){
        if(StringUtils.isEmpty(empNo)|| StringUtils.isEmpty(passWord)|| org.apache.commons.lang3.StringUtils.isBlank(empNo)|| org.apache.commons.lang3.StringUtils.isBlank(passWord)){
            log.error("賬號或者密碼為空，不允許登錄.");
            return false;
        }
        boolean result = false;
        try {
            //一賬通接口調用iPEBG
            String ssoKey = configService.getValueByKey("CAA_SSO_KEY");
            String tenantCode = configService.getValueByKey("CAA_SSO_TENANT_CODE", "iPEBG");
            String ssoDomainUrl = configService.getValueByKey("SSO_DOMAIN_URL", SSO_DOMAIN_URL);
            // 傳遞APPID獲取報表集合
            Map<String, Object> param = new HashMap<>();
            param.put("empNo", empNo);
            param.put("pwd", passWord);
            param.put("userIP", clientIp);
            param.put("authNo", ssoKey);
            param.put("tenantCode", tenantCode);
            // 發送請求，并獲取結果
            String resultStr = HttpUtil.post(ssoDomainUrl + AUTHENTICATE, param);
            SsoInfoDto ssoInfoDto = JSONUtil.toBean(resultStr, SsoInfoDto.class);
//            log.error(resultStr);
            if("0000".equals(ssoInfoDto.getCode())){
//                SsoUserInfoDto ssoUserInfoDto = JSONUtil.toBean(ssoInfoDto.getData(), SsoUserInfoDto.class);
                return true;
            }else{
                return false;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return result;
    }
}
