package com.entfrm.auth.sso;

import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.entfrm.auth.config.UserSecurityConfig;
import com.entfrm.biz.feign.FeignDmzService;
import com.entfrm.biz.system.service.ConfigService;
import com.entfrm.core.base.api.R;
import com.entfrm.core.base.constant.CommonConstants;
import com.entfrm.core.security.exception.UserLockedException;
import com.foxconn.so.LibSsoUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

public class SSOAuthenticationProvider extends DaoAuthenticationProvider {

    private ConfigService configService;
    RedisTemplate redisTemplate;
    UserSecurityConfig userSecurityConfig;

    private FeignDmzService feignDmzService;

    public static final String SSO_LOGIN_URL = "/apiForDmz/ssoLogin";

//
//    @Resource(name = "userDTOServiceImpl")
//    private  UserDTOService userDTOServiceImpl;

    //    @Resource
//    private UserDTOService userDTOServiceImpl;


    @Override
    protected void additionalAuthenticationChecks(UserDetails userDetails, UsernamePasswordAuthenticationToken authentication) {
        String username = authentication.getPrincipal().toString();
        String password = authentication.getCredentials().toString();

        if (isAccountLocked(username)) {
            throw new UserLockedException("您的密碼輸入錯誤超過 " + userSecurityConfig.getMaxRetryCount() + " 次,鎖定 " + userSecurityConfig.getLockMinute() + " 分鐘");
        }
//        try {
//            super.additionalAuthenticationChecks(userDetails, authentication);
//        }catch (AuthenticationException ae){

            try {
//          使用一賬通登錄
//                boolean isAuth = LibSsoUtil.authenticate(username, password, "", configService.getValueByKey("CAA_SSO_KEY"));

//          使用一賬通登錄
//                String ssoEndpoint = configService.getValueByKey("MRMS_ENDPOINT");
//                Map<String, Object> param = new HashMap<>();
//                param.put("username", username);
//                param.put("password", password);
//                String body = HttpRequest.get(ssoEndpoint + SSO_LOGIN_URL).header(Header.ACCEPT, "*/*").form(param).execute().body();
//                R<Boolean> r = JSONUtil.toBean(body, R.class);
                HttpServletRequest request = ((ServletRequestAttributes) Objects
                        .requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
                String userIp = ServletUtil.getClientIP(request);
                R<Boolean> r = feignDmzService.ssoLogin(username,password,userIp);

                if (CommonConstants.FAIL == r.getCode()) {
                    updateFailedAttepts(username);
                    // 非CAA用戶或者使用非人事密碼登陸
                    throw new AuthenticationServiceException("登錄失敗");
                } else {
//                    if(CommonConstant.TRUE.equals(r.getData().toString())){
                    if (r.getData()) {
                        clearFailedAttempts(username);
                        logger.info(username + "CAA  login 一賬通驗證通過");
                        //登錄成功直接放過
                    } else {
                        updateFailedAttepts(username);
                        // 非CAA用戶或者使用非人事密碼登陸
                        throw new AuthenticationServiceException("登錄失敗");
                    }
                }
            } catch (Exception e) {
                logger.debug(username + "一賬通登錄失敗，密碼錯誤");
                throw new AuthenticationServiceException("登錄失敗");
            }
//        }
    }


    private boolean isAccountLocked(String username) {
        final String o = redisTemplate.boundValueOps("failedAttempts:" + username).get(0,-1);
        if(StringUtils.isNotBlank(o)) {
            Integer failedAttempts = Integer.parseInt(o);
            return failedAttempts != null && failedAttempts >= userSecurityConfig.getMaxRetryCount();
        }
        return false;
    }

    private void clearFailedAttempts(String username) {
        redisTemplate.delete("failedAttempts:" + username);
    }

    private void updateFailedAttepts(String username) {
        redisTemplate.opsForValue().increment("failedAttempts:" + username);
        redisTemplate.expire("failedAttempts:" + username, userSecurityConfig.getLockMinute(), TimeUnit.MINUTES);

    }

    public ConfigService getConfigService() {
        return configService;
    }

    public void setConfigService(ConfigService configService) {
        this.configService = configService;
    }

    public RedisTemplate getRedisTemplate() {
        return redisTemplate;
    }

    public void setRedisTemplate(RedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    public UserSecurityConfig getUserSecurityConfig() {
        return userSecurityConfig;
    }

    public void setUserSecurityConfig(UserSecurityConfig userSecurityConfig) {
        this.userSecurityConfig = userSecurityConfig;
    }

    public FeignDmzService getFeignDmzService() {
        return feignDmzService;
    }

    public void setFeignDmzService(FeignDmzService feignDmzService) {
        this.feignDmzService = feignDmzService;
    }
}

