//package com.entfrm.test;
//
//import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import com.entfrm.biz.system.entity.User;
//import com.entfrm.web.WebApplication;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.context.annotation.Bean;
//import org.springframework.data.redis.core.RedisTemplate;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import java.util.ArrayList;
//import java.util.List;
//import java.util.concurrent.TimeUnit;
//
///**
// * @Auther F1858847 王磊
// * @Date 2022/11/4 15:12
// */
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = WebApplication.class)
//public class RedisTest {
//    @Autowired
//    private RedisTemplate redisTemplate;
//    public static String key = "F1188990";
////    @Test
//    public void testIds(){
////        Long increment = redisTemplate.opsForValue().increment(key, 1);
////        System.out.println(increment);
//
//        List<Thread> threads = new ArrayList<Thread>();
//
//        for(int i=0; i<100; i++) {
//            threads.add(new Thread("thread-"+i) {
//                public void run() {
//                    for (int i = 0; i < 100; i++) {
//                        readId();
//                    }
//                }
//            });
//
//        }
//
//        threads.forEach((o)->o.start());
//
//        threads.forEach((o)->{
//            try {
//                o.join();
//            } catch (InterruptedException e) {
//                e.printStackTrace();
//            }
//        });
//    }
//
//    public void readId(){
//        Long increment = redisTemplate.opsForValue().increment(key, 1);
//        if(1 == increment){
//            redisTemplate.expire(key, 1, TimeUnit.DAYS);
//        }
//    }
//}
