
package com.entfrm.test;

import com.entfrm.biz.feign.ch.ChOpenapiFeignService;
import com.entfrm.biz.feign.ch.dto.ChResult;
import com.entfrm.biz.feign.ch.dto.EntMachineCheckDto;
import com.entfrm.biz.feign.ch.dto.EntMachineItemsDto;
import com.entfrm.biz.system.util.JsonUtils;
import com.entfrm.core.base.api.R;
import com.entfrm.web.WebApplication;
import org.apache.commons.codec.binary.Base64;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;


/**
 * @Auther F1858847 王磊
 * @Date 2022/11/4 15:12
 */

@RunWith(SpringRunner.class)
@SpringBootTest(classes = WebApplication.class)
public class ChFeginTest {
    private Logger logger = LoggerFactory.getLogger(ChFeginTest.class);
    @Autowired
    private ChOpenapiFeignService chOpenapiFeignService;
    @Test
    public void saveZhubiaoImportTest(){
        EntMachineCheckDto entMachineCheckDto = new EntMachineCheckDto();
        entMachineCheckDto.setApplyEmpno("1111");
        entMachineCheckDto.setApplyEmpname("1111");
        ChResult save = chOpenapiFeignService.saveZhubiaoImport(entMachineCheckDto);
        logger.error(save.toString());
    }
    @Test
    public void saveEsignImportTest(){
        EntMachineItemsDto entMachineItemsDto = new EntMachineItemsDto();
        entMachineItemsDto.setCheckFloor("111");
        ChResult save = chOpenapiFeignService.saveEsignImport(entMachineItemsDto);
        logger.error(save.toString());
    }
}

