
package com.entfrm.test;

import com.entfrm.biz.feign.consumables.ConsumablesFeignService;
import com.entfrm.biz.feign.consumables.dto.ConsumablesResult;
import com.entfrm.biz.feign.consumables.dto.UserInfoDto;
import com.entfrm.biz.system.util.JsonUtils;
import com.entfrm.web.WebApplication;
import org.apache.commons.codec.binary.Base64;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;


/**
 * @Auther F1858847 王磊
 * @Date 2022/11/4 15:12
 */

@RunWith(SpringRunner.class)
@SpringBootTest(classes = WebApplication.class)
public class FeginTest {
    private Logger logger = LoggerFactory.getLogger(FeginTest.class);
    @Autowired
    private ConsumablesFeignService consumablesFeignService;

//    @Autowired
//    private ConsumablesEncryptedFeignService consumablesEncryptedFeignService;
    @Test
    public void testIds(){
        ConsumablesResult<UserInfoDto> consumablesResult = consumablesFeignService.getUserInfo("S6666666");
        if(200 == consumablesResult.getCode()){
            logger.error(consumablesResult.getData().toString());
        }else{
            logger.error(consumablesResult.getMsg());
        }
    }


    @Test
    public void userManagementAdd(){
        List<UserInfoDto> userInfoDtoList = new ArrayList<>();
        UserInfoDto userInfoDto = new UserInfoDto();
        userInfoDto.setDepartCode("TOT16107A1(IPEZBQ)");
        userInfoDto.setDepartName("統籌規劃課");
        userInfoDto.setEmail("<EMAIL>");
        userInfoDto.setIpControl("1");
        userInfoDto.setLoginIp("**************");
        userInfoDto.setNickName("張三");
        userInfoDto.setPhone("63616");
        userInfoDto.setRoleName("gdd,交管");
        userInfoDto.setSite("ZZK,ZZC");
        userInfoDto.setType("2");
        userInfoDto.setUserName("S777777");
        userInfoDtoList.add(userInfoDto);

        UserInfoDto userInfoDto2 = new UserInfoDto();
        userInfoDto2.setDepartCode("TOT16107A1(IPEZBQ)");
        userInfoDto2.setDepartName("統籌規劃課");
        userInfoDto2.setEmail("<EMAIL>");
        userInfoDto2.setIpControl("1");
        userInfoDto2.setLoginIp("**************");
        userInfoDto2.setNickName("李四");
        userInfoDto2.setPhone("63616");
        userInfoDto2.setRoleName("gdd,交管");
        userInfoDto2.setSite("ZZK,ZZC");
        userInfoDto2.setType("2");
        userInfoDto2.setUserName("S888888");
        userInfoDtoList.add(userInfoDto2);

        String s = JsonUtils.toJsonString(userInfoDtoList);
        String base = new Base64().encodeToString(s.getBytes());

        ConsumablesResult<UserInfoDto> consumablesResult = consumablesFeignService.userManagement(base);
        if(200 == consumablesResult.getCode()){
            logger.error(consumablesResult.toString());
        }else{
            logger.error(consumablesResult.getMsg());
        }
    }

    @Test
    public void restTest() {
        MultiValueMap<String, String> paramsMap = new LinkedMultiValueMap<>();

        // 构造头部信息(若有需要)
        HttpHeaders headers = new HttpHeaders();
        headers.add("Authorization", "Basic ZW50ZnJtOmVudGZybQ==");
        headers.add("Content-Type", "application/json;charset=UTF-8");

        // 构造请求的实体。包含body和headers的内容
        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity(paramsMap, headers);

        // 声明 restTemplateAuth（用作请求）
        RestTemplate restTemplateAuth = new RestTemplate();
        String userName = "F1858847";
        // 进行请求，并返回数据
        ResponseEntity<String> response = restTemplateAuth.exchange("http://10.213.132.226:8888/oauth/token?grant_type=openId&openId=" + userName, HttpMethod.GET, request, String.class);
        String str = response.getBody();
        System.out.println(str);

    }
}

