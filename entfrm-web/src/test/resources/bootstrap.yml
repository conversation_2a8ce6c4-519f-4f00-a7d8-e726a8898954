spring:
  application:
    name: entfrm-server
  profiles:
    active: dev

#  cloud:
#    nacos:
#      server-addr: 10.185.103.147:30112
#      discovery:
##        namespace: dea734d9-1aef-4782-a2f2-64073ccd92c3
#        namespace: d0956bb2-d9e5-4b63-b4a4-e74c1490e5b4

element:
  trigger:
    el-input: blur
    el-input-number: blur
    el-select: change
    el-radio-group: change
    el-checkbox-group: change
    el-cascader: change
    el-time-picker: change
    el-date-picker: change
    el-rate: change
##### spring-boot-actuator配置
management:
  endpoint:
    health:
      show-details: always
  endpoints:
    web:
      exposure:
        include: "*"