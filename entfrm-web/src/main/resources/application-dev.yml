
spring:
  # redis 配置
  redis:
    # 地址
    host: **************
    # 端口，默认为6379
    port: 30379
    # 密码
    password: foxconn.88
    database: 8
  datasource:
    druid:
      driver-class-name: org.postgresql.Driver
      jdbc-url: *******************************************************************
      username: entfrm
      password: Entfrm_123
      # 最大连接池数量
      maxActive: 20
  rabbitmq:
    host: **************
    port: 5672
    username: guest
    password: guest
ftps-client:
  # 主机ip
  host: **************
  # 端口号
  port: 990
  # 用户名
  username: ftpuser
  # 密码
  password: Foxconn88
  #主目錄
  rootPath: upload
  #mybatis-plus:
#mybatis-plus:
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

entfrm:
  # feign调用地址
  #  feignUrl: https://localhost:444/esign
#  feignUrl: http://localhost:80/esign
  feignUrl: http://localhost:8888/esign
  # Consumables系統接口地址，From:冬冬http://*************:8080
  consumablesFeignUrl: http://cias.ipebg.efoxconn.com

  chOpenapifeignUrl: *************:8080/openApi

  feignDmzUrl: https://**************:444/esignDmz
#  feignDmzUrl: http://localhost:8888/esign
#  feignDmzUrl: https://**************:444/esignDmz
#  feignDmzUrl: https://esgc.ipebg.efoxconn.com:9098/esign
  file-server:
    type: s3
    s3:
      access-key: AKIDpSmnUrtbz3nDK6ZiX2C2XhX3F6dqiFSK
      accessKeySecret: dvtWomQlmSopWtvcz3nDI3VcPr0oISHm
      endpoint: http://cos.zhengzhou.fii-foxconn.com
      bucketName: newesign-1255000011
      pathStyleAccessEnabled: true
      maxConnections: 100
      fileSize: 20971520
      directory: dev/www/qianhe/upload
