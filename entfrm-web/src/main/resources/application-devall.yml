
spring:
  # redis 配置
  redis:
    # 地址
    host: **************
    # 端口，默认为6379
    port: 30379
    # 密码
    password: foxconn.88
    database: 8
  datasource:
    druid:
      driver-class-name: org.postgresql.Driver
      jdbc-url: *******************************************************************
      username: entfrm
      password: entfrm.2131
      # 最大连接池数量
      maxActive: 20
  rabbitmq:
    host: **************
    port: 5672
    username: guest
    password: guest
ftps-client:
  # 主机ip
  host: **************
  # 端口号
  port: 990
  # 用户名
  username: ftpuser
  # 密码
  password: Foxconn88
  #主目錄
  rootPath: upload
#mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
## 开发环境配置
#server:
#  servlet:
#    # 项目contextPath
#    context-path: /pro