
spring:
  # redis 配置
  redis:
    # 地址
    #    host: **************
    host: sigc-by.ipebg.efoxconn.com
    # 端口，默认为走網關
    port: 9097
    # 密码
#    password: foxconn.88
    password: iPEBG-eSign
  #    database: 8
  datasource:
    druid:
      driver-class-name: org.postgresql.Driver
      jdbc-url: ******************************************************************
      username: entfrm
      password: entfrm#0713

      # 最大连接池数量
      maxActive: 20
  rabbitmq:
    #    host: **************
    host: esgc.ipebg.efoxconn.com
    port: 9097
#    username: guest
#    password: guest
    username: admin
    password: Foxconn#88
ftps-client:
  # 主机ip
  host: ************
  # 端口号
  port: 990
  # 用户名
  username: entfrm_esign
  # 密码
  password: Entfrm_Esign
  #主目錄
  rootPath: entfrm_esign


entfrm:
  # feign调用地址
#  feignUrl: http://localhost:8888/esign
#  feignUrl: https://localhost/esign
  feignUrl: https://esign.foxconn.com/esign
  # Consumables系統接口地址，From:冬冬
  consumablesFeignUrl: http://cias.ipebg.efoxconn.com

  chOpenapifeignUrl: *************:8080/openApi

  feignDmzUrl: https://esgc.ipebg.efoxconn.com:9098/esign

  file-server:
    type: s3
    s3:
      access-key: AKIDpSmnUrtbz3nDK6ZiX2C2XhX3F6dqiFSK
      accessKeySecret: dvtWomQlmSopWtvcz3nDI3VcPr0oISHm
      endpoint: http://cos.zhengzhou.fii-foxconn.com
      bucketName: newesign-1255000011
      pathStyleAccessEnabled: true
      maxConnections: 100
      fileSize: 20971520
      directory: www/qianhe/upload