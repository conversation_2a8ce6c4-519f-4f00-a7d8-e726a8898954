# 项目配置
entfrm:
  # 名称
  name: entfrm开发平台
  # 版本
  version: 1.1.0
  # 文件上传路径
  profile: C:/profile/
  # 获取ip地址开关
  addressSwitch: true
  # redis开关
  redisSwitch: true
  #en_US 英语  zh_CN 简体中文
  lang: zh_TW
  swagger:
    title: 管理后台
    description: 提供管理员管理的所有功能
    version: ${entfrm.version}
    base-package: com.entfrm
  cache-manager:
    configs:
      - key: userInfo
        second: 1800
  file-server:
    type: s3
    s3:
      access-key: ${entfrm.s3.access-key}
      accessKeySecret: ${entfrm.s3.accessKeySecret}
      endpoint: ${entfrm.s3.endpoint}
      bucketName: ${entfrm.s3.bucketName}
      pathStyleAccessEnabled: ${entfrm.s3.pathStyleAccess}
      maxConnections: ${entfrm.s3.maxConnections}
      fileSize: ${entfrm.s3.fileSize}
      directory: ${entfrm.s3.directory}
  ##### 负载均衡隔离(version隔离，只适用于开发环境)
#  ribbon:
#    isolation:
#      enabled: false
# 开发环境配置
server:
  # 服务端口
  port: 8888
  servlet:
    # 项目contextPath
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # tomcat最大线程数，默认为200
    max-threads: 800
    # Tomcat启动初始化的线程数，默认值25
    min-spare-threads: 30

# 用户配置
user:
  password:
    # 密码错误{maxRetryCount}次锁定10分钟
    maxRetryCount: 5
    lockMinute: 3

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
    encoding: utf-8
  # 序列化
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  profiles:
    active: druid
  # 文件上传
  servlet:
     multipart:
       max-file-size:  50MB
       max-request-size:  30MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # redis 配置
  redis:
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
      filter:
        stat:
        # 慢SQL记录
        log-slow-sql: true
        slow-sql-millis: 1000
        merge-sql: true
      wall:
        config:
          multi-statement-allow: true
#          log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  ##### sentinel配置
#  cloud:
#    sentinel:
#      transport:
#        dashboard: *
#      eager: true


# MyBatis Plus
mybatis-plus:
  mapper-locations: classpath*:mapper/**/*.xml
  typeAliasesPackage: com.*.biz.**.entity
  global-config:
    # 不显示banner
    banner: false
    # 数据库相关配置
    db-config:
      #主键类型  AUTO:"数据库ID自增", INPUT:"用户输入ID",ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
      id-type: UUID
  configuration:
    call-setters-on-nulls: true

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /cms/article/*,/system/datasource/save,/system/datasource/update,/devtool/dataset/*
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/quartz/*,/devtool/*,/activiti/*,/cms/*,/msg/*,/LJAPPLY/*

# 日志配置
logging:
  level:
    com.entfrm: error
    org.springframework: WARN
    org.spring.springboot.dao: error

##### feign配置
feign:
  sentinel:
    enabled: true
  hystrix: false
  okhttp:
    enabled: true
  httpclient:
    enabled: false
    max-connections: 1000
    max-connections-per-route: 100
  client:
    config:
      feignName:
        connectTimeout: 30000
        readTimeout: 30000
  ## 开启Feign请求响应压缩
  compression:
    request:
      enabled: true
      ## 配置压缩文档类型及最小压缩的文档大小
      mime-types: text/xml,application/xml,application/json
      min-request-size: 2048
    response:
      enabled: true
ftps-client:
  # 主机ip
  host: **************
  # 端口号
  port: 990
  # 用户名
  username: ftpuser
  # 密码
  password: Foxconn88
  #主目錄
  rootPath: upload
knife4j:
  # 开启增强配置
  enable: true
  basic:
    enable: true
    # Basic认证用户名
    username: admin
    # Basic认证密码
    password: 123456
