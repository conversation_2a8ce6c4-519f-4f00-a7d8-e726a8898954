<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>entfrm</artifactId>
        <groupId>com.entfrm</groupId>
        <version>1.1.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <packaging>jar</packaging>
    <artifactId>entfrm-web</artifactId>

    <dependencies>
<!--        <dependency>-->
<!--            <groupId>com.alibaba.cloud</groupId>-->
<!--            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.entfrm</groupId>
            <artifactId>entfrm-auth</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.entfrm</groupId>
            <artifactId>entfrm-biz-system</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.entfrm</groupId>
            <artifactId>entfrm-biz-monitor</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.entfrm</groupId>
            <artifactId>entfrm-biz-quartz</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.entfrm</groupId>
            <artifactId>entfrm-biz-devtool</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.entfrm</groupId>
            <artifactId>entfrm-biz-activiti</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.entfrm</groupId>
            <artifactId>entfrm-biz-cms</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.entfrm</groupId>
            <artifactId>entfrm-biz-msg</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>com.entfrm</groupId>
            <artifactId>entfrm-biz-LJAPPLY</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.entfrm</groupId>
            <artifactId>entfrm-biz-custom</artifactId>
            <version>${project.version}</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.entfrm</groupId>-->
<!--            <artifactId>fox-biz</artifactId>-->
<!--            <version>${project.version}</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.entfrm</groupId>
            <artifactId>fox-biz-dept1</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.entfrm</groupId>
            <artifactId>fox-biz-caaesign</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.entfrm</groupId>
            <artifactId>entfrm-biz-design</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!--undertow容器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>

<!--        监控中心-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-to-slf4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


<!--熔断-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-datasource-nacos</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webflux</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.eclipse.jgit</groupId>
            <artifactId>org.eclipse.jgit</artifactId>
            <version>5.9.0.202009080501-r</version>
        </dependency>
        <dependency>
            <groupId>org.eclipse.jgit</groupId>
            <artifactId>org.eclipse.jgit.junit</artifactId>
            <version>5.9.0.202009080501-r</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.vaadin.external.google</groupId>
                    <artifactId>android-json</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test</artifactId>
            <version>2.3.0.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <version>5.2.8.RELEASE</version>
        </dependency>
    </dependencies>
    <repositories>
        <repository>
            <id>jgit-snapshot-repository</id>
            <url>https://repo.eclipse.org/content/groups/jgit/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <configuration>
                    <fork>true</fork> <!-- 如果没有该配置，devtools不会生效 -->
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <finalName>${project.artifactId}</finalName>
    </build>
</project>
