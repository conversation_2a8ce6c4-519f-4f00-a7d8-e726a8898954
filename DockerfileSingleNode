#V1.0   Dockerfile文件编写
# 基础镜像
FROM hub.ipebg.com/library/jdk8u181:v7
# 维护者信息
MAINTAINER <EMAIL>

#构建参数
ARG NACOS_SERVER_ADDR
ARG NACOS_NAMESPACE
ARG ENTFRM_SERVER_NAME
ARG WORK_PATH="/usr/local/entfrm-server/"
ARG FONT_PATH="/usr/share/fonts/"

#设置参数
ENV NACOS_SERVER_ADDR=${NACOS_SERVER_ADDR} \
NACOS_NAMESPACE=${NACOS_NAMESPACE} \
ENTFRM_SERVER_NAME=${ENTFRM_SERVER_NAME}

#设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime \
&&  echo $TZ > /etc/timezone


#添加应用
ADD entfrm-web/target/entfrm-web.jar $WORK_PATH/
#添加字體
ADD resourcesFiles/font/ $FONT_PATH
#添加一賬通目錄
RUN mkdir -p $WORK_PATH/entfrm-web.jar'!'/BOOT-INF/lib
#添加一賬通依賴
ADD ssoLib/libsso.so $WORK_PATH/entfrm-web.jar'!'/BOOT-INF/lib



WORKDIR $WORK_PATH

ENTRYPOINT exec java \
 -Dskywalking.trace.ignore_path=/actuator/** \
 -Dspring.profiles.active=test \
 -Dspring.application.name=entfrm-server$ENTFRM_SERVER_NAME \
 -Dspring.cloud.nacos.server-addr=$NACOS_SERVER_ADDR  \
 -Dans.namespace=$NACOS_NAMESPACE \
 -jar entfrm-web.jar
#ENTRYPOINT exec java -Dspring.profiles.active=dev -jar entfrm-web.jar

#相关脚本
#docker stop entfrm
#docker rm entfrm
#docker rmi $(docker images | grep "entfrm" | awk '{print $3}') 
#docker build -t entfrm:v1.0
#docker run -d -p 8088:80 --name entfrm entfrm:v1.0
#docker run -d -p 8088:80 -p 8888:8888 --link mysql:mysql --link redis:redis --name entfrm entfrm:v1.0
