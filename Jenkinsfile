pipeline {
    agent any
    environment {
        HARBOR_CREDS = credentials('jenkins-harbor-creds')
        K8S_CONFIG = credentials('jenkins-k8s-config')
        BUILD_ENTFRM_SERVER = false
        BUILD_ENTFRM_UI = false
        SERVER_IMAGE_URL_TAG = ""
        UI_IMAGE_URL_TAG = ""
        ENTFRM_SERVER_NAME = ""
    }
    parameters {
        string(name: 'HARBOR_HOST', defaultValue: 'hub.ipebg.com', description: 'harbor仓库地址')
        //string(name: 'DOCKER_PROJECT', defaultValue: 'entfrm', description: 'docker项目名称')
        string(name: 'DOCKER_IMAGE_DIR', defaultValue: 'server', description: 'docker镜像目录名称')
        string(name: 'NACOS_SERVER_ADDR', defaultValue: 'nacos-nodeport:8848', description: 'docker镜像目录名称')
        string(name: 'NACOS_NAMESPACE', defaultValue: '24dfbe7f-cf6e-41c7-a89f-514e78ae05ac', description: 'docker镜像目录名称')
        string(name: 'APP_NAME_SERVER', defaultValue: 'entfrm-server', description: 'server在k8s中标签名')
        string(name: 'APP_NAME_UI', defaultValue: 'entfrm-ui', description: 'ui在k8s中标签名')
        string(name: 'K8S_NAMESPACE', defaultValue: 'default', description: 'k8s的namespace名称')
    }
    stages {
        stage('init'){
            steps{
                script{
                    if(params.GIT_BRANCH_NAME == "Master"){
                        ENTFRM_SERVER_NAME = ""
                    }else{
                        ENTFRM_SERVER_NAME = "-${params.GIT_BRANCH_NAME}"
                    }
                    def apps = "${deliver_item}".split(",")
                        sh 'echo ${deliver_item}'
                    if(apps.contains("server")){
                        BUILD_ENTFRM_SERVER = true
                        SERVER_IMAGE_URL_TAG = "${params.HARBOR_HOST}/${DOCKER_PROJECT}/${params.DOCKER_IMAGE_DIR}/entfrm-server${ENTFRM_SERVER_NAME}:${BUILD_NUMBER}"
                        //sh 'echo BUILD_ENTFRM_SERVER'
                    }
                    if(apps.contains("web")){
                        BUILD_ENTFRM_UI = true
                        UI_IMAGE_URL_TAG = "${params.HARBOR_HOST}/${DOCKER_PROJECT}/${params.DOCKER_IMAGE_DIR}/entfrm-ui${ENTFRM_SERVER_NAME}:${BUILD_NUMBER}"
                        //sh 'echo BUILD_ENTFRM_UI'
                    }
                }
            }
        }
        stage('Server Maven Build') {
            when {
                expression { BUILD_ENTFRM_SERVER == true  }
            }
            agent {
                docker {
                    image 'hub.ipebg.com/library/maven:3.6.3-jdk-8'
                    args '-v /root/.m2:/root/.m2'
                }
            }
            steps {
                sh "mvn -B -gs maven-setting.xml -DskipTests=true clean package"
            }
        }
        stage('Server Docker Build') {
            when {
                expression { BUILD_ENTFRM_SERVER == true  }
            }
            agent any
            steps {
                    sh "docker login -u ${HARBOR_CREDS_USR} -p ${HARBOR_CREDS_PSW} ${params.HARBOR_HOST}"
                    sh "docker build \
                       --build-arg NACOS_SERVER_ADDR=${NACOS_SERVER_ADDR} \
                       --build-arg NACOS_NAMESPACE=${NACOS_NAMESPACE} \
                       --build-arg ENTFRM_SERVER_NAME=${ENTFRM_SERVER_NAME} \
                       -t ${SERVER_IMAGE_URL_TAG} ."
                    sh "docker push ${SERVER_IMAGE_URL_TAG}"
                    sh "docker rmi ${SERVER_IMAGE_URL_TAG}"
            }
        }
        stage('web Npm Build') {
            when {
                expression { BUILD_ENTFRM_UI == true  }
            }
            agent {
                docker {
                    image 'hub.ipebg.com/library/node:12.19.0-buster'
                    //args '-p 3000:3000'
                }
            }
            steps {
                sh '''
                    cd entfrm-ui
                    if [ ! -d "node_modules" ];then
                        tar -xzf ./node_modules_linux.tar.gz
                    fi
                '''
                sh '''
                    cd entfrm-ui
                    npm run build:prod
                '''
            }
        }
        stage('web Docker Build') {
            when {
                expression { BUILD_ENTFRM_UI == true  }
            }
            agent any
            steps {
                sh "docker login -u ${HARBOR_CREDS_USR} -p ${HARBOR_CREDS_PSW} ${params.HARBOR_HOST}"
                sh "docker build \
                --build-arg ENTFRM_SERVER_NAME=${ENTFRM_SERVER_NAME} \
                -t ${UI_IMAGE_URL_TAG} ./entfrm-ui/"

                sh "docker push ${UI_IMAGE_URL_TAG}"
                sh "docker rmi ${UI_IMAGE_URL_TAG}"
            }
        }
        stage('Deploy Server') {
            when {
                expression { BUILD_ENTFRM_SERVER == true  }
            }
            agent {
                docker {
                    image 'hub.ipebg.com/library/helm-kubectl-docker:v1.18.10-v3.4.0'
                }
            }
            steps {
                sh "mkdir -p ~/.kube"
                sh "echo ${K8S_CONFIG} | base64 -d > ~/.kube/config"
                sh "sed -e 's#{IMAGE_URL}#${SERVER_IMAGE_URL_TAG}#g;s#{APP_NAME}#${params.APP_NAME_SERVER}${ENTFRM_SERVER_NAME}#g;s#{REPLICAS}#${params.REPLICAS}#g' jenkins/k8s-deployment/entfrm-server.tpl > jenkins/k8s-deployment/entfrm-server.yml"
                sh "kubectl apply -f jenkins/k8s-deployment/entfrm-server.yml --namespace=${params.K8S_NAMESPACE}"
            }
        }
        stage('Deploy UI') {
            when {
                expression { BUILD_ENTFRM_UI == true  }
            }
            agent {
                docker {
                    image 'hub.ipebg.com/library/helm-kubectl-docker:v1.18.10-v3.4.0'
                }
            }
            steps {
                //30800-30900
                sh "mkdir -p ~/.kube"
                sh "echo ${K8S_CONFIG} | base64 -d > ~/.kube/config"
                sh "echo ${K8S_CONFIG} | base64 -d > ~/.kube/config"
                sh "sed -e 's#{IMAGE_URL}#${UI_IMAGE_URL_TAG}#g;s#{APP_NAME}#${params.APP_NAME_UI}${ENTFRM_SERVER_NAME}#g;s#{APP_NODE_PORT}#${params.APP_NODE_PORT}#g;s#{REPLICAS}#${params.REPLICAS}#g' jenkins/k8s-deployment/entfrm-ui.tpl > jenkins/k8s-deployment/entfrm-ui.yml"
                sh "kubectl apply -f jenkins/k8s-deployment/entfrm-ui.yml --namespace=${params.K8S_NAMESPACE}"
            }
        }
    }
}
